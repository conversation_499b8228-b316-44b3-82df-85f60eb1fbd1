@model WebMenuItemDto

@if (Model.IsContainer && Model.Children.Any())
{
    <!-- Container with children (collapsible section) -->
    <li class="nav-item">
        @if (Model.ParentMenuId == null)
        {
            <hr class="text-white-50">
        }
        <button class="nav-link sidebar-section-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#<EMAIL>">
            @if (!string.IsNullOrEmpty(Model.Icon))
            {
                <i class="@Model.Icon"></i>
            }
            @Model.DisplayName
            <i class="fas fa-chevron-down float-end toggle-icon"></i>
        </button>
        <div class="collapse" id="<EMAIL>">
            <ul class="nav flex-column ms-3">
                @foreach (var child in Model.Children.OrderBy(c => c.DisplayOrder))
                {
                    @await Html.PartialAsync("_MenuItem", child)
                }
            </ul>
        </div>
    </li>
}
else if (!Model.IsContainer)
{
    <!-- Regular menu item (link) -->
    <li class="nav-item">
        <a class="nav-link" href="@Model.Route">
            @if (!string.IsNullOrEmpty(Model.Icon))
            {
                <i class="@Model.Icon"></i>
            }
            @Model.DisplayName
        </a>
    </li>
}

