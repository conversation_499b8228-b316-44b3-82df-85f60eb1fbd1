-- Check the actual time value for invoice 89
-- This will help us understand why the time is showing as 00:00

-- Check invoice 89 details including time
SELECT 
    'Invoice 89 Details' as Section,
    TrxNo,
    TrxDate,
    CAST(TrxDate AS TIME) as TimeOnly,
    DATEPART(HOUR, TrxDate) as Hour,
    DATEPART(MINUTE, TrxDate) as Minute,
    DATEPART(SECOND, TrxDate) as Second,
    Cashier,
    PartnerName,
    TrxTotal,
    TrxNetAmount
FROM tblStockMovHeader 
WHERE TrxNo = 89 AND TrxType = 'مبيعات';

-- Check if there are any other invoices with proper time values
SELECT TOP 5
    'Recent Invoices with Time' as Section,
    TrxNo,
    TrxDate,
    CAST(TrxDate AS TIME) as TimeOnly,
    DATEPART(HOUR, TrxDate) as Hour,
    DATEPART(MINUTE, TrxDate) as Minute
FROM tblStockMovHeader 
WHERE TrxType = 'مبيعات'
ORDER BY TrxNo DESC;

-- Check if the issue is with the TrxDate field itself
SELECT 
    'Date Field Analysis' as Section,
    COUNT(*) as TotalInvoices,
    COUNT(CASE WHEN CAST(TrxDate AS TIME) = '00:00:00' THEN 1 END) as InvoicesWithZeroTime,
    COUNT(CASE WHEN CAST(TrxDate AS TIME) != '00:00:00' THEN 1 END) as InvoicesWithRealTime
FROM tblStockMovHeader 
WHERE TrxType = 'مبيعات';
