﻿Imports System.IO
Imports System.Text
Imports System.Data.SqlClient

Module Module2

    Public sellername As String = ""
    Public vatregistration As String = ""
    Public timestamp As String = ""
    Public invoiceamount As String = ""
    Public vatAmount As String = ""

    Public Class SaudiConvertion
        Public Function GetData() As String

            Return getBase64(sellername, vatregistration, timestamp, invoiceamount, vatAmount)
        End Function

        Public Function getBase64(ByVal sellername As String, ByVal vatregistration As String, ByVal timestamp As String, ByVal invoiceamount As String, ByVal vatamoun As String) As String
            Dim ltr As String = (ChrW(&H200E)).ToString()
            Dim seller = getTlvVAlue("1", sellername)
            Dim vatno = getTlvVAlue("2", vatregistration)
            Dim time = getTlvVAlue("3", timestamp)
            Dim invamt = getTlvVAlue("4", invoiceamount)
            Dim vatamt = getTlvVAlue("5", vatamoun)
            Dim result = seller.Concat(vatno).Concat(time).Concat(invamt).Concat(vatamt).ToArray()
            Console.WriteLine(result)
            Console.WriteLine(result.ToString())
            Dim output = Convert.ToBase64String(result)
            Console.WriteLine(output)
            Return output
        End Function

        Public Function getTlvVAlue(ByVal tagnums As String, ByVal tagvalue As String) As Byte()
            Dim tagnums_array As String() = {tagnums}
            Dim tagvalue1 = tagvalue
            Dim tagnum = tagnums_array.[Select](Function(s) Byte.Parse(s)).ToArray()
            Dim tagvalueb = Encoding.UTF8.GetBytes(tagvalue1)
            Dim taglengths As String() = {tagvalueb.Length.ToString()}
            Dim tagvaluelengths = taglengths.[Select](Function(s) Byte.Parse(s)).ToArray()
            Dim tlvVAlue = tagnum.Concat(tagvaluelengths).Concat(tagvalueb).ToArray()
            Return tlvVAlue
        End Function
    End Class
    Public Function ImageToByteArray(image As Image) As Byte()
        Using ms As New MemoryStream()
            image.Save(ms, System.Drawing.Imaging.ImageFormat.Png)
            Return ms.ToArray()
        End Using
    End Function

End Module
