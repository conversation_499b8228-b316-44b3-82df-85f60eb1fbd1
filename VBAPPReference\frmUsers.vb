﻿Imports System.Data.SqlClient
Public Class frmUsers
    Dim type As String = ""

    Private Sub btncrtusr_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btncrtusr.Click
        'change to group id
        'If rbadmin.Checked = True Then
        '    type = "Admin"
        'ElseIf rbLimited.Checked = True Then
        '    type = "Limited"
        'ElseIf rbLimitedEntry.Checked = True Then
        '    type = "LimitedEntry"
        'End If
        If txtUser.Text <> "" Then
            Dim checksts As New SQLCommand("select username from tblusers where username like '" & txtUser.Text.Trim() & "'", Con)
            Dim dr2 As SQLDataReader
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            dr2 = checksts.ExecuteReader
            If dr2.Read Then
                MsgBox("عفوًا اسم المستخدم موجود مسبقًا", MsgBoxStyle.Critical, "شاشة المستخدمين")
                txtUser.Focus()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                dr2.Close()
            Else
                dr2.Close()
                Dim insusrcmd As New SqlCommand("INSERT INTO [tblUsers] ([Username], [Password], [GroupID], [DefaultStore], [StoreChange], [DefaultCustomer], [CustomerChange], [DefaultCashier], [CashierChange],ChangeInvoicePrice,MaxDiscountPercent, [CreatedBy], [CreatedOn]) VALUES (@Username, @Password, @GroupID, @DefaultStore, @StoreChange, @DefaultCustomer, @CustomerChange, @DefaultCashier, @CashierChange,@ChangeInvoicePrice,@MaxDiscountPercent, @CreatedBy, @CreatedOn)", Con)

                ' Adding parameters to prevent SQL injection
                insusrcmd.Parameters.AddWithValue("@Username", txtUser.Text.Trim())
                insusrcmd.Parameters.AddWithValue("@Password", txtPass.Text.GetHashCode()) ' Consider using proper hashing
                If cmbxGroupID.SelectedValue IsNot Nothing Then
                    insusrcmd.Parameters.AddWithValue("@GroupID", Convert.ToInt32(cmbxGroupID.SelectedValue.ToString()))
                Else
                    insusrcmd.Parameters.AddWithValue("@GroupID", DBNull.Value)
                End If


                insusrcmd.Parameters.AddWithValue("@DefaultStore", cmbxStore.Text.Trim())
                insusrcmd.Parameters.AddWithValue("@StoreChange", ckbxStoreChange.Checked)

                If cmbxPartnerNo.SelectedValue IsNot Nothing Then
                    insusrcmd.Parameters.AddWithValue("@DefaultCustomer", Convert.ToInt32(cmbxPartnerNo.SelectedValue.ToString()))
                Else
                    insusrcmd.Parameters.AddWithValue("@DefaultCustomer", DBNull.Value)
                End If


                insusrcmd.Parameters.AddWithValue("@CustomerChange", ckbxCustomerChange.Checked)
                If cmbxCashier.SelectedValue IsNot Nothing Then
                    insusrcmd.Parameters.AddWithValue("@DefaultCashier", Convert.ToInt32(cmbxCashier.SelectedValue.ToString()))
                Else
                    insusrcmd.Parameters.AddWithValue("@DefaultCashier", DBNull.Value)
                End If
                insusrcmd.Parameters.AddWithValue("@CashierChange", ckbxCashierChange.Checked)

                insusrcmd.Parameters.AddWithValue("@ChangeInvoicePrice", ckbxChangeInvoicePrice.Checked)
                insusrcmd.Parameters.AddWithValue("@CreatedBy", UserName)
                insusrcmd.Parameters.AddWithValue("@CreatedOn", DateTime.Now)

                insusrcmd.Parameters.AddWithValue("@MaxDiscountPercent", Val(txtMaxDiscount.Text))


                If txtPass2.Text.Trim() = txtPass.Text.Trim() Then
                    If Con.State <> ConnectionState.Open Then
                        Con.Open()
                    End If
                    insusrcmd.ExecuteNonQuery()
                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                    MsgBox("تم انشاء المستخدم بنجاح", MsgBoxStyle.Information, "شاشة المستخدمين")
                    ClearFields()
                Else
                    MsgBox("عفوًا كلمة المرور غير متطابقة", MsgBoxStyle.Critical, "شاشة المستخدمين")
                    txtPass.Clear()
                    txtPass2.Clear()
                    txtPass.Focus()
                End If
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If

        Else
            MsgBox("فضلاً اختر اسم المستخدم أولاً", MsgBoxStyle.Critical, "شاشة المستخدمين")
            txtUser.Focus()
        End If
        Refreshlist()
        UserList()
    End Sub

    Private Sub btnsv_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsv.Click
        'change to group id
        'If rbadmin.Checked = True Then
        '    type = "Admin"
        'ElseIf rbLimited.Checked = True Then
        '    type = "Limited"
        'ElseIf rbLimitedEntry.Checked = True Then
        '    type = "LimitedEntry"
        'End If
        If txtUser.Text <> "" Then
            Dim checksts As New SQLCommand("select * from tblUsers where username like '" & txtUser.Text.Trim() & "'", Con)
            Dim dr3 As SQLDataReader
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            dr3 = checksts.ExecuteReader
            If dr3.HasRows Then
                Dim updtpasscmd2 As New SqlCommand("UPDATE tblUsers set GroupID = @GroupID, DefaultStore = @DefaultStore, StoreChange = @StoreChange, DefaultCustomer = @DefaultCustomer, CustomerChange = @CustomerChange, DefaultCashier = @DefaultCashier, CashierChange = @CashierChange,ChangeInvoicePrice=@ChangeInvoicePrice,MaxDiscountPercent=@MaxDiscountPercent, ModifiedBy=@ModifiedBy, ModifiedOn=GETDATE() WHERE Username LIKE @Username AND Username <> 'admin'", Con)
                updtpasscmd2.Parameters.AddWithValue("@Username", txtUser.Text.Trim())

                ' Handling nullable GroupID
                If cmbxGroupID.SelectedValue IsNot Nothing Then
                    updtpasscmd2.Parameters.AddWithValue("@GroupID", Convert.ToInt32(cmbxGroupID.SelectedValue.ToString()))
                Else
                    updtpasscmd2.Parameters.AddWithValue("@GroupID", DBNull.Value)
                End If

                updtpasscmd2.Parameters.AddWithValue("@DefaultStore", cmbxStore.Text.Trim())
                updtpasscmd2.Parameters.AddWithValue("@StoreChange", ckbxStoreChange.Checked)

                ' Handling nullable DefaultCustomer
                If cmbxPartnerNo.SelectedValue IsNot Nothing Then
                    updtpasscmd2.Parameters.AddWithValue("@DefaultCustomer", Convert.ToInt32(cmbxPartnerNo.SelectedValue.ToString()))
                Else
                    updtpasscmd2.Parameters.AddWithValue("@DefaultCustomer", DBNull.Value)
                End If

                updtpasscmd2.Parameters.AddWithValue("@CustomerChange", ckbxCustomerChange.Checked)

                ' Handling nullable DefaultCashier
                If cmbxCashier.SelectedValue IsNot Nothing Then
                    updtpasscmd2.Parameters.AddWithValue("@DefaultCashier", Convert.ToInt32(cmbxCashier.SelectedValue.ToString()))
                Else
                    updtpasscmd2.Parameters.AddWithValue("@DefaultCashier", DBNull.Value)
                End If

                updtpasscmd2.Parameters.AddWithValue("@CashierChange", ckbxCashierChange.Checked)
                updtpasscmd2.Parameters.AddWithValue("@ChangeInvoicePrice", ckbxChangeInvoicePrice.Checked)
                updtpasscmd2.Parameters.AddWithValue("@ModifiedBy", UserName)


                updtpasscmd2.Parameters.AddWithValue("@MaxDiscountPercent", Val(txtMaxDiscount.Text))


                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                dr3.Close()
                updtpasscmd2.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If

                MsgBox("تم تغيير مستوى الصلاحية بنجاح", MsgBoxStyle.Information, "شاشة المستخدمين")
                ClearFields()

                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            Else
                MsgBox("فضلاً تحقق من اسم المستخدم أولاً", MsgBoxStyle.Critical, "شاشة المستخدمين")
                dr3.Close()
            End If
        Else
            MsgBox("فضلاً اختر اسم المستخدم أولاً", MsgBoxStyle.Critical, "شاشة المستخدمين")
        End If

        Refreshlist()
    End Sub

    Private Sub frmUsers_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        ' frmSettings.Enabled = True
    End Sub

    Private Sub frmUsers_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        UserList()
        Refreshlist()
        CustomerLoad()
        StoresLoad()
        CashierLoad()
        GroupsAuthLoad()
        Me.BringToFront()
        Me.CenterToScreen()
        rbUserNew.Checked = True
        txtUser.DropDownStyle = ComboBoxStyle.DropDown
    End Sub
    Sub Refreshlist()
        Dim ds As DataSet = New DataSet
        Dim usercmd As New SqlCommand("select SN as [م],Username as [اسم المستخدم] from tblUsers where Username <> 'admin'", Con)
        Dim da As New SQLDataAdapter(usercmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub
    Sub StoresLoad()
        Dim CMD As New SqlCommand("Select Store from tblStores Order by Store", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxStore.Items.Clear()
        Do While reader.Read
            cmbxStore.Items.Add(reader.Item(0).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Sub CustomerLoad()
        Dim CMD As New SqlCommand("  Select AccountCode,AccountName from tbl_Acc_Accounts where ParentAccountCode = (Select AccountNo from tblGLConfig where EntryReferenceModule = 'عملاء') order by AccountCode", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountName + ' - ' + CONVERT(AccountCode, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxPartnerNo.DataSource = dt
        cmbxPartnerNo.DisplayMember = "DisplayText" ' Show Name
        cmbxPartnerNo.ValueMember = "AccountCode" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Private Sub btnCustomerSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCustomerSearch.Click
        CustomerSearchForm = "frmUsers"
        frmCustomerSearch.ShowDialog()
    End Sub
    Sub CashierLoad()
        Dim CMD As New SqlCommand("SELECT AccountCode,AccountName from tbl_Acc_Accounts WHERE ParentAccountCode = (SELECT AccountNo FROM tblGLConfig WHERE EntryReferenceModule = 'نقدية') ORDER BY AccountName", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()

        dt.Load(reader)
        reader.Close()

        ' Bind the ComboBox to DataTable
        cmbxCashier.DataSource = dt
        cmbxCashier.DisplayMember = "AccountName" ' Show Name
        cmbxCashier.ValueMember = "AccountCode" ' Store ID

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Sub GroupsAuthLoad()
        Dim CMD As New SqlCommand("select GroupID,GroupName from tblGroupsAuth order by GroupName", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()

        dt.Load(reader)
        reader.Close()

        ' Bind the ComboBox to DataTable
        cmbxGroupID.DataSource = dt
        cmbxGroupID.DisplayMember = "GroupName" ' Show Name
        cmbxGroupID.ValueMember = "GroupID" ' Store ID

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If

    End Sub

    Private Sub rbUserType_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbUserType.CheckedChanged
        cmbxGroupID.Enabled = True
        btncrtusr.Enabled = False
        btndel.Enabled = False
        btnchngpass.Enabled = False
        btnsv.Enabled = True
        txtPass.Enabled = False
        txtPass2.Enabled = False
        txtUser.Enabled = True
        txtUser.DropDownStyle = ComboBoxStyle.DropDownList
    End Sub

    Private Sub rbUserNew_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbUserNew.CheckedChanged
        btnchngpass.Enabled = False
        btndel.Enabled = False
        btnsv.Enabled = False
        btncrtusr.Enabled = True
        txtPass.Enabled = True
        txtPass2.Enabled = True
        txtUser.Enabled = True
        cmbxGroupID.Enabled = True
        txtUser.DropDownStyle = ComboBoxStyle.DropDown
    End Sub

    Private Sub rbUserPasswordChange_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbUserPasswordChange.CheckedChanged
        btncrtusr.Enabled = False
        btndel.Enabled = False
        btnsv.Enabled = False
        btnchngpass.Enabled = True
        txtPass.Enabled = True
        txtPass2.Enabled = True
        txtUser.Enabled = True
        cmbxGroupID.Enabled = False
        txtUser.DropDownStyle = ComboBoxStyle.DropDownList
    End Sub

    Private Sub rbUserDelete_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbUserDelete.CheckedChanged
        cmbxGroupID.Enabled = False
        btncrtusr.Enabled = False
        btndel.Enabled = True
        btnchngpass.Enabled = False
        btnsv.Enabled = False
        txtPass.Enabled = False
        txtPass2.Enabled = False
        txtUser.Enabled = True
        txtUser.DropDownStyle = ComboBoxStyle.DropDownList
    End Sub

    Sub ClearFields()
        txtUser.Text = ""
        txtPass.Clear()
        txtPass2.Clear()
        cmbxGroupID.SelectedIndex = -1
        cmbxPartnerNo.SelectedIndex = -1
        cmbxStore.SelectedIndex = -1
        cmbxCashier.SelectedIndex = -1
        ckbxCustomerChange.Checked = False
        ckbxStoreChange.Checked = False
        ckbxCashierChange.Checked = False
        ckbxChangeInvoicePrice.Checked = False
        txtMaxDiscount.Clear()
    End Sub

    Private Sub btnchngpass_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnchngpass.Click
        If txtUser.Text <> "" Then
            If txtPass.Text = txtPass2.Text Then
                Dim searCmduser As New SQLCommand("select * from tblUsers where UserName like '" & txtUser.Text & "' ", Con)
                Dim DrCmd As SQLDataReader
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                DrCmd = searCmduser.ExecuteReader
                If DrCmd.HasRows Then
                    Dim updtpasscmd As New SQLCommand(" update tblUsers set [password]= '" & txtPass.Text.GetHashCode() & "' where username like '" & txtUser.Text & "' and Username <> 'admin'", Con)
                    If Con.State <> ConnectionState.Open Then
                        Con.Open()
                    End If
                    DrCmd.Close()
                    updtpasscmd.ExecuteNonQuery()
                    MsgBox("تم تغيير كلمة المرور بنجاح", MsgBoxStyle.Information, "شاشة المستخدمين")
                    ClearFields()
                Else
                    DrCmd.Close()
                    MsgBox("اسم المستخدم خطأ", MsgBoxStyle.Critical, "شاشة المستخدمين")
                    txtUser.Focus()
                End If

            Else
                MsgBox("عفوًا كلمة المرور غير متطابقة", MsgBoxStyle.Critical, "شاشة المستخدمين")
                txtPass.Clear()
                txtPass2.Clear()
                txtPass.Focus()
            End If
        Else
            MsgBox("فضلاً اختر اسم المستخدم أولاً", MsgBoxStyle.Critical, "شاشة المستخدمين")
            txtUser.Focus()
        End If

        Refreshlist()
    End Sub

    Private Sub btndel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btndel.Click
        If txtUser.Text <> "" Then
            Dim checksts As New SQLCommand("select username from tblUsers where username like '" & txtUser.Text.Trim() & "'", Con)
            Dim dr2 As SQLDataReader
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            dr2 = checksts.ExecuteReader
            If dr2.Read Then
                If txtUser.Text.Trim() <> UserName Then
                    If MsgBox("هل أنت متأكد من حذف المستخدم ؟", MsgBoxStyle.OkCancel, "شاشة المستخدمين") = MsgBoxResult.Ok Then
                        Dim delcmd As New SqlCommand("delete from tblUsers where username like '" & txtUser.Text & "' and Username <> 'admin'", Con)
                        If Con.State <> ConnectionState.Open Then
                            Con.Open()
                        End If
                        dr2.Close()
                        delcmd.ExecuteNonQuery()
                        MsgBox("تم حذف المستخدم بنجاح", MsgBoxStyle.Information, "شاشة المستخدمين")
                        ClearFields()

                        If Con.State <> ConnectionState.Closed Then
                            Con.Close()
                        End If

                    Else
                        dr2.Close()
                    End If
                Else
                    MsgBox("لا يمكنك حذف حسابك", MsgBoxStyle.Critical, "شاشة المستخدمين")
                End If

            Else
                MsgBox("فضلاً تحقق من اسم المستخدم أولاً", MsgBoxStyle.Critical, "شاشة المستخدمين")
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End If
        Refreshlist()
        UserList()
    End Sub
    Sub UserList()
        txtUser.Items.Clear()
        Dim usercmd As New SQLCommand("select Username from tblUsers where Username <> 'admin'", Con)
        Dim dr2 As SQLDataReader
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        dr2 = usercmd.ExecuteReader
        Do While dr2.Read
            txtUser.Items.Add(dr2.Item("Username").ToString)
        Loop
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    'Private Sub DataGridView1_CellClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellClick
    '    If DataGridView1.SelectedRows.Count - 1 >= 0 Then
    '        txtUser.Text = DataGridView1.SelectedRows(0).Cells(1).Value
    '    End If
    'End Sub
    Private Sub DataGridView1_CellDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If Val(DataGridView1.CurrentRow.Cells(0).Value) <> 0 Then
            txtUser.Text = DataGridView1.CurrentRow.Cells(1).Value
        End If
    End Sub
    Private Sub txtUser_TextChanged(sender As Object, e As EventArgs) Handles txtUser.TextChanged
        Try
            If Trim(txtUser.Text) <> "" And rbUserNew.Checked <> True Then
                Dim SearchCMD As New SqlCommand("select * from tblUsers where Username = '" & Trim(txtUser.Text) & "' ", Con)

                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                Dim reader As SqlDataReader = SearchCMD.ExecuteReader
                If reader.Read Then
                    cmbxGroupID.SelectedValue = reader("GroupID").ToString()
                    cmbxPartnerNo.SelectedValue = Val(reader("DefaultCustomer").ToString())
                    ckbxCustomerChange.Checked = reader("CustomerChange").ToString()
                    txtMaxDiscount.Text = reader("MaxDiscountPercent").ToString()
                    cmbxStore.Text = reader("DefaultStore").ToString()
                    ckbxStoreChange.Checked = reader("StoreChange").ToString()
                    cmbxCashier.SelectedValue = reader("DefaultCashier").ToString()
                    ckbxCashierChange.Checked = reader("CashierChange").ToString()
                    'cmbxEmployee.SelectedValue = If(IsDBNull(reader("EmployeeNo")), 0, CInt(reader("EmployeeNo")))

                Else
                    ClearFields()
                End If
                If reader.IsClosed Then
                Else
                    reader.Close()
                End If
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If
        Catch ex As Exception
            Exit Sub
        End Try
    End Sub
End Class