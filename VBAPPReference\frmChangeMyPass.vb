﻿Imports System.Data.SqlClient
Public Class frmChangeMyPass

    Private Sub btnChange_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnChange.Click
        If txtNewPass.Text = txtNewPass2.Text Then
            Dim searCmduser As SQLCommand = New SQLCommand("select sn from tblUsers where Username like '" & UserName & "' and password like '" & txtOldPass.Text.Trim.GetHashCode & "' ", Con)
            Dim sn As Int64 = 0
            Dim DrCmd As SQLDataReader
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            DrCmd = searCmduser.ExecuteReader
            If DrCmd.Read Then
                sn = DrCmd.Item("SN").ToString
                'sn = 4
                Dim updtpasscmd As New SQLCommand("update tblUsers set [password]= '" & txtNewPass.Text.GetHashCode & "' where SN = " & sn & "", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                If DrCmd.IsClosed Then
                Else
                    DrCmd.Close()
                End If

                updtpasscmd.ExecuteNonQuery()
                MsgBox("تم تغيير كلمة المرور بنجاح", "نظام السلطان")
                txtOldPass.Clear()
                txtNewPass.Clear()
                txtNewPass2.Clear()
                txtOldPass.Focus()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                    DrCmd.Close()
                End If
            Else
                DrCmd.Close()
                MsgBox("كلمة المرور خاطئة", "نظام السلطان")
                txtOldPass.Focus()
            End If
        Else
            MsgBox("عفوا كلمة المرور الجديدة غير متطابقة", "نظام السلطان")
            txtNewPass.Clear()
            txtNewPass2.Clear()
            txtNewPass.Focus()
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub frmChangeMyPass_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        '         frmSettings.Enabled = True
    End Sub
End Class