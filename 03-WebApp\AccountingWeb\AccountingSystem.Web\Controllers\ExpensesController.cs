using System;
using System.Linq;
using System.Threading.Tasks;
using AccountingSystem.Services;
using AccountingSystem.Web.ViewModels.Expenses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class ExpensesController : Controller
    {
        private readonly IExpensesService _expensesService;
        private readonly IVendorService _vendorService;
        private readonly IEmployeeService _employeeService;
        private readonly IWarehouseService _warehouseService;

        public ExpensesController(
            IExpensesService expensesService,
            IVendorService vendorService,
            IEmployeeService employeeService,
            IWarehouseService warehouseService)
        {
            _expensesService = expensesService;
            _vendorService = vendorService;
            _employeeService = employeeService;
            _warehouseService = warehouseService;
        }

        [HttpGet]
        public async Task<IActionResult> Create()
        {
            var model = new ExpenseCreateViewModel();
            await PopulateDropdowns(model);
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(ExpenseCreateViewModel model)
        {
            if (!ModelState.IsValid)
            {
                await PopulateDropdowns(model);
                return View(model);
            }

            // Server-side validations similar to VB
            if (model.TaxAmount > 0 && string.IsNullOrWhiteSpace(model.VendorVATRN))
            {
                ModelState.AddModelError(nameof(model.VendorVATRN), "لم يتم ادخال الرقم الضريبي للمورد؟");
                await PopulateDropdowns(model);
                return View(model);
            }

            var createdBy = User?.Identity?.Name ?? "System";

            var result = await _expensesService.InsertExpenseAsync(
                model.InvoiceDate,
                model.CreditCashier,
                model.DebitExpense,
                model.InvoiceAmount,
                model.TaxAmount,
                model.VendorNo,
                model.VendorInvoiceNo,
                model.VendorName,
                model.VendorVATRN,
                model.EmployeeBuyer,
                model.Notes,
                model.Store,
                createdBy,
                model.IgnoreDuplicate,
                model.PhotoFile);

            if (!string.IsNullOrWhiteSpace(result.DuplicateWarning) && !model.IgnoreDuplicate)
            {
                // Present warning and allow confirmation
                model.DuplicateWarning = result.DuplicateWarning;
                model.ResultMessage = result.Message;
                model.SaveSuccess = false;
                ModelState.AddModelError("", result.DuplicateWarning + " — اضغط حفظ مرة أخرى لتأكيد التجاوز.");
                model.IgnoreDuplicate = true; // Next submit will pass ignore
                await PopulateDropdowns(model);
                return View(model);
            }

            model.SaveSuccess = result.Success;
            model.ResultMessage = result.Message;
            if (result.Success)
            {
                TempData["Success"] = result.Message;
                // Printing later
                return RedirectToAction(nameof(Create));
            }
            else
            {
                ModelState.AddModelError("", result.Message);
                await PopulateDropdowns(model);
                return View(model);
            }
        }

        private async Task PopulateDropdowns(ExpenseCreateViewModel model)
        {
            var cashiers = await _expensesService.GetCashierAccountsAsync();
            var expenses = await _expensesService.GetExpenseAccountsAsync();
            var vendors = await _vendorService.GetVendorsAsync("");
            var employees = await _employeeService.GetEmployeesAsync();
            var warehouses = await _warehouseService.GetWarehousesAsync();

            model.CashierAccounts = cashiers.Select(c => (c.AccountCode, c.AccountName)).ToList();
            model.ExpenseAccounts = expenses.Select(c => (c.AccountCode, c.AccountName)).ToList();
            model.Vendors = vendors.OrderBy(v => v.VendorNo).Select(v => (v.VendorNo, v.VendorName ?? string.Empty, v.VATRegNo)).ToList();
            model.Employees = employees.OrderBy(e => e.Id).Select(e => (e.Id, e.Name ?? string.Empty)).ToList();
            model.Warehouses = warehouses.Select(w => w.WarehouseName).ToList();
        }
    }
}

