using AccountingSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public interface IItemsService
    {
        Task<List<Item>> GetItemsAsync(string searchTerm = "");
        Task<Item?> GetItemByIdAsync(long id);
        Task<Item> CreateItemAsync(Item item);
        Task<Item> UpdateItemAsync(Item item);
        Task DeleteItemAsync(long id);
        Task<List<string>> GetUofMsAsync();
        Task<List<ItemsCategory>> GetCategoriesAsync();
        Task<List<string>> GetItemTypesAsync();
        Task<List<decimal>> GetTaxesAsync();
        Task<List<string>> GetBrandsAsync();
        Task<List<Store>> GetShopsAsync();
    }
} 