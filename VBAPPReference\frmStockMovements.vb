﻿Imports System.Data.SqlClient
Public Class frmStockMovements
    Private Sub frmStockMovements_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Load the stores into the combo box
        LoadStores()
        ForceGregorianForAllPickers(Me)
    End Sub
    Private Sub LoadStores()
        Dim cmd As New SqlCommand("SELECT SN, Store FROM tblStores ORDER BY Store", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = cmd.ExecuteReader()
        cmbxStore.Items.Clear()
        While reader.Read()
            cmbxStore.Items.Add(reader.Item(1).ToString())
        End While
        If Not reader.IsClosed Then
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Private Sub btnPreview_Click(sender As Object, e As EventArgs) Handles btnPreview.Click
        If cmbxStore.Text.Trim() <> "" Then
            PrintType = "StockMovements"
            DateStrFrom = dtpFrom.Value.ToString("yyyy-MM-dd")
            DateStrTo = dtpTo.Value.ToString("yyyy-MM-dd")
            StoreForReport = cmbxStore.Text.Trim
            frmPrintPreview.MdiParent = frmMain
            frmPrintPreview.Show()
        Else
            MsgBox("فضلا اختر المخزن", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

End Class