﻿Imports System.Data.SqlClient
Public Class frmCashStatement

    Private Sub frmCashStatement_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        ' frmReports.Enabled = True
    End Sub

    Private Sub frmCashStatement_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        CashierLoad()
        ForceGregorianForAllPickers(Me)
        cmbxCashier.SelectedIndex = 0

    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If cmbxCashier.SelectedValue <> 0 Then
            AccNo = cmbxCashier.SelectedValue
            'dtp.CustomFormat = "dd/MM/yyyy"
            DateFrom = dtpFrom.Value.Date
            DateTo = dtpTo.Value.Date
            DateStrFrom = dtpFrom.Value.ToString("yyyy-MM-dd")
            DateStrTo = dtpTo.Value.ToString("yyyy-MM-dd")
            PrintType = "JESN"
            frmPrintPreview.MdiParent = frmMain
            frmPrintPreview.Show()
        Else
            MsgBox("فضلا اختر الصندوق", MsgBoxStyle.Critical, "نظام السلطان")
        End If

    End Sub
    Sub CashierLoad()
        Dim CMD As New SqlCommand("SELECT AccountCode,AccountName from tbl_Acc_Accounts WHERE ParentAccountCode = (SELECT AccountNo FROM tblGLConfig WHERE EntryReferenceModule = 'نقدية') ORDER BY AccountName", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()

        dt.Load(reader)
        reader.Close()

        ' Bind the ComboBox to DataTable
        cmbxCashier.DataSource = dt
        cmbxCashier.DisplayMember = "AccountName" ' Show Name
        cmbxCashier.ValueMember = "AccountCode" ' Store ID

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Me.Close()
    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        'If CheckBox1.Checked = True Then
        '    cmbxCashier.Enabled = False
        'Else
        '    cmbxCashier.Enabled = True
        'End If
    End Sub
End Class