using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Models;
using AccountingSystem.Data;
using AccountingSystem.Web.ViewModels;
using Microsoft.EntityFrameworkCore;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly IApplicationStateService _appState;
        private readonly AccountingDbContext _context;
        private readonly ILogger<DashboardController> _logger;

        public DashboardController(
            IApplicationStateService appState,
            AccountingDbContext context,
            ILogger<DashboardController> logger)
        {
            _appState = appState;
            _context = context;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var username = User.Identity?.Name ?? "Unknown";
                var userGroup = User.FindFirst("UserGroup")?.Value ?? "";
                var fullName = User.FindFirst("FullName")?.Value ?? username;
                var storeName = _appState.StoreName ?? "Alsultan Accounting System";

                // Create dashboard view model
                var viewModel = new DashboardViewModel
                {
                    Username = fullName,
                    UserGroup = userGroup,
                    StoreName = storeName,
                    LoginTime = DateTime.Now,
                    UserPermissions = await GetUserPermissionsAsync(),
                    DashboardStats = await GetDashboardStatisticsAsync(),
                    QuickActions = GetQuickActions()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard for user {Username}", User.Identity?.Name);
                ViewBag.ErrorMessage = "حدث خطأ أثناء تحميل لوحة التحكم";
                return View(new DashboardViewModel());
            }
        }

        private async Task<List<UserPermissionViewModel>> GetUserPermissionsAsync()
        {
            var permissions = new List<UserPermissionViewModel>();

            try
            {
                var username = User.Identity?.Name;
                if (string.IsNullOrEmpty(username)) return permissions;

                // Get user's group ID
                var user = await _context.Users
                    .Include(u => u.Group)
                    .FirstOrDefaultAsync(u => u.Username == username);

                if (user?.GroupID == null) return permissions;

                // Get group permissions
                var groupPermissions = await _context.GroupFormPermissions
                    .Include(gfp => gfp.Form)
                    .Where(gfp => gfp.GroupID == user.GroupID)
                    .ToListAsync();

                foreach (var gp in groupPermissions)
                {
                    if (gp.Form != null)
                    {
                        permissions.Add(new UserPermissionViewModel
                        {
                            FormName = gp.Form.FormName,
                            FormTitle = gp.Form.FormTitle ?? gp.Form.FormName,
                            ModuleName = gp.Form.ModuleName ?? "General",
                            CanView = gp.CanView,
                            CanAdd = gp.CanAdd,
                            CanEdit = gp.CanEdit,
                            CanDelete = gp.CanDelete,
                            CanPrint = gp.CanPrint
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions for {Username}", User.Identity?.Name);
            }

            return permissions;
        }

        private async Task<DashboardStatisticsViewModel> GetDashboardStatisticsAsync()
        {
            var stats = new DashboardStatisticsViewModel();

            try
            {
                // Get basic counts from available tables
                stats.TotalCustomers = await _context.Users.CountAsync();
                stats.TotalVendors = await _context.UserGroups.CountAsync();
                
                // Set some default values for now
                stats.TodaySales = 0;
                stats.TodayPurchases = 0;
                stats.LowStockItems = 0;
                stats.PendingInvoices = 0;
                stats.CashBalance = 0;
                stats.TotalInventoryValue = 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard statistics");
            }

            return stats;
        }

        private List<QuickActionViewModel> GetQuickActions()
        {
            return new List<QuickActionViewModel>
            {
                new QuickActionViewModel
                {
                    Title = "إدارة المستخدمين",
                    Description = "إضافة وتعديل المستخدمين",
                    Icon = "fas fa-users",
                    Color = "primary",
                    Action = "/Users/<USER>"
                },
                new QuickActionViewModel
                {
                    Title = "إعدادات النظام",
                    Description = "تكوين إعدادات النظام",
                    Icon = "fas fa-cog",
                    Color = "secondary",
                    Action = "/Settings/Index"
                },
                new QuickActionViewModel
                {
                    Title = "التقارير",
                    Description = "عرض التقارير المالية",
                    Icon = "fas fa-chart-bar",
                    Color = "info",
                    Action = "/Reports/Index"
                },
                new QuickActionViewModel
                {
                    Title = "النسخ الاحتياطي",
                    Description = "إنشاء نسخة احتياطية",
                    Icon = "fas fa-database",
                    Color = "warning",
                    Action = "/Backup/Index"
                }
            };
        }
    }
}
