using System.ComponentModel.DataAnnotations;

namespace AccountingSystem.Web.ViewModels
{
    // Login ViewModel
    public class LoginViewModel
    {
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; }
    }

    // Dashboard ViewModels
    public class DashboardViewModel
    {
        public string Username { get; set; } = string.Empty;
        public string UserGroup { get; set; } = string.Empty;
        public string StoreName { get; set; } = string.Empty;
        public DateTime LoginTime { get; set; }
        public List<UserPermissionViewModel> UserPermissions { get; set; } = new();
        public DashboardStatisticsViewModel DashboardStats { get; set; } = new();
        public List<QuickActionViewModel> QuickActions { get; set; } = new();
    }

    public class UserPermissionViewModel
    {
        public string FormName { get; set; } = string.Empty;
        public string FormTitle { get; set; } = string.Empty;
        public string ModuleName { get; set; } = string.Empty;
        public bool CanView { get; set; }
        public bool CanAdd { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public bool CanPrint { get; set; }
    }

    public class DashboardStatisticsViewModel
    {
        public decimal TodaySales { get; set; }
        public decimal TodayPurchases { get; set; }
        public int TotalCustomers { get; set; }
        public int TotalVendors { get; set; }
        public int LowStockItems { get; set; }
        public int PendingInvoices { get; set; }
        public decimal CashBalance { get; set; }
        public decimal TotalInventoryValue { get; set; }
    }

    public class QuickActionViewModel
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
    }

    public class ModulePermissionViewModel
    {
        public string ModuleName { get; set; } = string.Empty;
        public string ModuleTitle { get; set; } = string.Empty;
        public string ModuleIcon { get; set; } = string.Empty;
        public List<UserPermissionViewModel> Forms { get; set; } = new();
    }

    // User Management ViewModels
    public class UserManagementViewModel
    {
        public List<UserViewModel> Users { get; set; } = new();
        public List<UserGroupViewModel> Groups { get; set; } = new();
    }

    public class UserViewModel
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
        public string Username { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "الاسم الكامل يجب أن يكون أقل من 100 حرف")]
        public string? FullName { get; set; }

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        public string? Email { get; set; }

        public int? GroupID { get; set; }
        public string? GroupName { get; set; }

        public bool IsLocked { get; set; }
        public int FailedLoginAttempts { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }

        [DataType(DataType.Password)]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "كلمة المرور يجب أن تكون بين 6 و 100 حرف")]
        public string? Password { get; set; }

        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "كلمة المرور وتأكيد كلمة المرور غير متطابقتين")]
        public string? ConfirmPassword { get; set; }
    }

    public class UserGroupViewModel
    {
        public int Id { get; set; }
        public int GroupID { get; set; }

        [Required(ErrorMessage = "اسم المجموعة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المجموعة يجب أن يكون أقل من 100 حرف")]
        public string GroupName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UserCount { get; set; }
    }

    public class GroupPermissionManagementViewModel
    {
        public int GroupID { get; set; }
        public string GroupName { get; set; } = string.Empty;
        public List<ModulePermissionViewModel> Modules { get; set; } = new();
    }

    public class FormPermissionViewModel
    {
        public int FormID { get; set; }
        public string FormName { get; set; } = string.Empty;
        public string FormTitle { get; set; } = string.Empty;
        public string ModuleName { get; set; } = string.Empty;
        public bool CanView { get; set; }
        public bool CanAdd { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public bool CanPrint { get; set; }
        public bool HasPermission { get; set; }
    }

    public class ChangePasswordViewModel
    {
        [Required(ErrorMessage = "كلمة المرور الحالية مطلوبة")]
        [DataType(DataType.Password)]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور الجديدة مطلوبة")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "كلمة المرور يجب أن تكون بين 6 و 100 حرف")]
        [DataType(DataType.Password)]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
        [DataType(DataType.Password)]
        [Compare("NewPassword", ErrorMessage = "كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    // System Configuration ViewModels
    public class SystemConfigurationViewModel
    {
        [Required(ErrorMessage = "اسم المتجر مطلوب")]
        [StringLength(200, ErrorMessage = "اسم المتجر يجب أن يكون أقل من 200 حرف")]
        public string StoreName { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "رقم التسجيل الضريبي يجب أن يكون أقل من 50 حرف")]
        public string VATRegistration { get; set; } = string.Empty;

        [Range(400, 2000, ErrorMessage = "ارتفاع النموذج يجب أن يكون بين 400 و 2000")]
        public int FormHeight { get; set; } = 600;

        [Range(600, 3000, ErrorMessage = "عرض النموذج يجب أن يكون بين 600 و 3000")]
        public int FormWidth { get; set; } = 800;

        [Range(400, 2000, ErrorMessage = "ارتفاع التقرير يجب أن يكون بين 400 و 2000")]
        public int ReportHeight { get; set; } = 600;

        [Range(600, 3000, ErrorMessage = "عرض التقرير يجب أن يكون بين 600 و 3000")]
        public int ReportWidth { get; set; } = 800;

        [StringLength(500, ErrorMessage = "مسار النسخ الاحتياطي يجب أن يكون أقل من 500 حرف")]
        public string DatabaseBackupLocation { get; set; } = string.Empty;

        public bool LinkStoreCash { get; set; }

        [StringLength(100, ErrorMessage = "مستوى التصنيف الأول يجب أن يكون أقل من 100 حرف")]
        public string ItemClassL1 { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "مستوى التصنيف الثاني يجب أن يكون أقل من 100 حرف")]
        public string ItemClassL2 { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "مستوى التصنيف الثالث يجب أن يكون أقل من 100 حرف")]
        public string ItemClassL3 { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "مستوى التصنيف الرابع يجب أن يكون أقل من 100 حرف")]
        public string ItemClassL4 { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "مستوى التصنيف الخامس يجب أن يكون أقل من 100 حرف")]
        public string ItemClassL5 { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "طابعة سندات الصرف يجب أن تكون أقل من 100 حرف")]
        public string CashPayPrint { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "طابعة سندات القبض يجب أن تكون أقل من 100 حرف")]
        public string CashRecPrint { get; set; } = string.Empty;

        public bool IsTestDeploy { get; set; }
    }
}
