﻿
Imports System.Data.SqlClient
    Imports Excel = Microsoft.Office.Interop.Excel
    Imports System.Runtime.InteropServices

    Public Class frmSearchStock

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Dim CMDString As String = ""
        If rdbStock.Checked = True Then
            CMDString = "Select Store as [المخزن],ItemNo as [رقم الصنف],ItemDescription as [الوصف],UofM as [الوحدة],StockBalance as [الكمية],StockValue as [القيمة] from StockBalanceView where ItemNo <> 0"
            If Trim(cmbxStore.Text) <> "" Then
                CMDString += " And Store = '" & Trim(cmbxStore.Text) & "'"
            End If
            If Trim(txtSearch.Text) <> "" Then
                CMDString += " and ItemNo = '" & Trim(txtSearch.Text) & "'"
            End If
            CMDString += "  order by Store,ItemNo"
        Else
            Dim trxType As String = ""

            If cmbxTrxType.Text.Trim = "توريد" Then
            trxType = "In"
        ElseIf cmbxTrxType.Text.Trim = "صرف" Then
            trxType = "Out"
        Else
            trxType = cmbxTrxType.Text.Trim
        End If


        CMDString = "Select Store as المستودع,DocNo as [رقم المستند],LineSN [رقم السطر],TrxDate التاريخ,Case when TrxType ='Out' then 'صرف' else 'توريد' end as [نوع الحركة],SourceType [نوع المرجع],ItemNo [رقم الصنف],ItemDescription as [وصف الصنف],UofM as [الوحدة],QTYIn as [الكمية الواردة], QTYOut as [الكمية المصروفة], UnitPurchasePrice as [سعر وحدة الشراء],CreatedBy as بواسطة from StockMoveView where TrxDate >= '" & Format(dtpFrom.Value.Date, "yyyy-MM-dd") & "' and TrxDate <= '" & Format(dtpTo.Value.Date, "yyyy-MM-dd") & "'"

        If Val(txtInvoiceNo.Text) <> 0 Then
            CMDString += " and DocNo = " & Val(txtInvoiceNo.Text) & ""
        End If
        If Trim(cmbxStore.Text) <> "" Then
            CMDString += " And Store = '" & Trim(cmbxStore.Text) & "'"
        End If
        If trxType <> "" Then
            CMDString += " and TrxType = '" & trxType & "'"

        End If
        If Trim(cmbxUser.Text) <> "" Then
            CMDString += " and CreatedBy = '" & Trim(cmbxUser.Text) & "'"
        End If

        If Trim(cmbxInvoiceType.Text) <> "" Then
            CMDString += " and SourceType = '" & Trim(cmbxInvoiceType.Text) & "'"
        End If

        If Trim(txtSearch.Text) <> "" Then
            CMDString += " and ItemNo = '" & Trim(txtSearch.Text) & "'"
        End If

        CMDString += "  order by TrxDate,DocNo"
        End If
        If CMDString <> "" Then
            Dim ds As DataSet = New DataSet
            Dim CMD As New SqlCommand
            CMD.CommandText = CMDString
            CMD.Connection = Con
            Dim da As New SqlDataAdapter(CMD)
            ds.Clear()
            da.Fill(ds)
            DataGridView1.DataSource = ds.Tables(0)
        Else
            DataGridView1.DataSource = ""
        End If



    End Sub

    Private Sub frmSearch_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
            'frmMain.Enabled = True
        End Sub

    Private Sub frmSearch_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        rdbStock.Checked = True
        StoresLoad()
        DateLoad()
        ForceGregorianForAllPickers(Me)
        cmbxInvoiceType.SelectedIndex = 0
            If UserType = "Limited" Then
                cmbxUser.Items.Clear()
                cmbxUser.Items.Add(UserName)
                cmbxUser.Text = UserName
                cmbxUser.Enabled = False
            Else
                UsersLoad()
                cmbxUser.Enabled = True
            End If
        End Sub
        Sub UsersLoad()
            Dim CMD As New SqlCommand("Select Distinct([المستخدم]) from InvoicesForSearch order by [المستخدم]", Con)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CMD.ExecuteReader
            cmbxUser.Items.Clear()
            Do While reader.Read
                cmbxUser.Items.Add(reader.Item(0).ToString)
            Loop
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End Sub


    Sub StoresLoad()
            Dim CMD As New SqlCommand("Select Store from tblStores Order by Store", Con)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CMD.ExecuteReader
            cmbxStore.Items.Clear()
            Do While reader.Read
                cmbxStore.Items.Add(reader.Item("Store").ToString)
            Loop
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End Sub
        Sub DateLoad()
            Dim CMD As New SqlCommand("Select Min([تاريخ الفاتورة]) from InvoicesForSearch", Con)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CMD.ExecuteReader
            If reader.Read Then
                If Not IsDBNull(reader.Item(0)) Then
                    dtpFrom.Value = CDate(reader.Item(0))
                Else
                    dtpFrom.Value = Date.Today ' or any default value you prefer
                End If
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End Sub



    Private Sub btnItemSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnItemSearch.Click
        SearchItemFor = "StockSearch"
        frmItemSearch.ShowDialog()

        End Sub


    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
            ExportToExcelAndSave()
        End Sub


        Private Sub ExportToExcelAndSave()
            If DataGridView1.Rows.Count = 0 Then
                MessageBox.Show("لا يوجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            Dim saveFileDialog As New SaveFileDialog()
            saveFileDialog.Filter = "Excel Workbook|*.xlsx"
        saveFileDialog.Title = "حدد مكان حفظ الملف"
        If rdbStock.Checked = True Then
            saveFileDialog.FileName = "تقرير رصيد المخزون.xlsx"
        Else
            saveFileDialog.FileName = "تقرير حركة المخزون.xlsx"
        End If


        If saveFileDialog.ShowDialog() = DialogResult.OK Then
                Dim xlApp As Excel.Application = Nothing
                Dim xlWorkbook As Excel.Workbook = Nothing
                Dim xlWorksheet As Excel.Worksheet = Nothing

                Try
                    xlApp = New Excel.Application()
                    xlWorkbook = xlApp.Workbooks.Add()
                    xlWorksheet = CType(xlWorkbook.Sheets(1), Excel.Worksheet)

                    ' Header row
                    Dim colIndex As Integer = 1
                    For Each col As DataGridViewColumn In DataGridView1.Columns
                        If col.Visible Then
                            xlWorksheet.Cells(1, colIndex).Value = col.HeaderText
                            colIndex += 1
                        End If
                    Next

                    ' Data rows
                    Dim rowIndex As Integer = 2
                    For Each row As DataGridViewRow In DataGridView1.Rows
                        If Not row.IsNewRow Then
                            colIndex = 1
                            For Each col As DataGridViewColumn In DataGridView1.Columns
                                If col.Visible Then
                                    xlWorksheet.Cells(rowIndex, colIndex).Value = row.Cells(col.Index).Value?.ToString()
                                    colIndex += 1
                                End If
                            Next
                            rowIndex += 1
                        End If
                    Next

                    ' Save the file
                    xlWorkbook.SaveAs(saveFileDialog.FileName)
                    MessageBox.Show("تم حفظ الملف بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)

                Catch ex As Exception
                    MessageBox.Show("خطأ أثناء التصدير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Finally
                    ' Cleanup Excel COM objects
                    If Not IsNothing(xlWorkbook) Then xlWorkbook.Close(False)
                    If Not IsNothing(xlApp) Then xlApp.Quit()
                    Marshal.ReleaseComObject(xlWorksheet)
                    Marshal.ReleaseComObject(xlWorkbook)
                    Marshal.ReleaseComObject(xlApp)
                End Try
            End If
        End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        If rdbStock.Checked = True Then
            PrintType = "ItemStock"
            ItemNoForReport = Val(txtSearch.Text)
        Else
            PrintType = "StockMovements"
        End If

        DateStrFrom = dtpFrom.Value.ToString("yyyy-MM-dd")
        DateStrTo = dtpTo.Value.ToString("yyyy-MM-dd")
        StoreForReport = cmbxStore.Text.Trim
        frmPrintPreview.MdiParent = frmMain
        frmPrintPreview.Show()
    End Sub

End Class