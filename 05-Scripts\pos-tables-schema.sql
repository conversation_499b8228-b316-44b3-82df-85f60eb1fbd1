-- =====================================================
-- POS System Tables Schema Documentation
-- Focused on POS-related tables and their structure
-- =====================================================

USE [SULTDB]
GO

-- =====================================================
-- POS CORE TABLES
-- =====================================================

-- 1. POS Sessions (tblPOSSessions)
SELECT 
    'POS SESSIONS' AS TableGroup,
    'tblPOSSessions' AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    CASE 
        WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
        WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
        ELSE ''
    END AS Size,
    c.IS_NULLABLE AS IsNullable,
    c.COLUMN_DEFAULT AS DefaultValue,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        ELSE ''
    END AS KeyType,
    ep.value AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = 'tblPOSSessions'
) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('tblPOSSessions') 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE c.TABLE_NAME = 'tblPOSSessions'
ORDER BY c.ORDINAL_POSITION

UNION ALL

-- 2. POS Invoices (tblStockMovHeader)
SELECT 
    'POS INVOICES' AS TableGroup,
    'tblStockMovHeader' AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    CASE 
        WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
        WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
        ELSE ''
    END AS Size,
    c.IS_NULLABLE AS IsNullable,
    c.COLUMN_DEFAULT AS DefaultValue,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        ELSE ''
    END AS KeyType,
    ep.value AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = 'tblStockMovHeader'
) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('tblStockMovHeader') 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE c.TABLE_NAME = 'tblStockMovHeader'
ORDER BY c.ORDINAL_POSITION

UNION ALL

-- 3. POS Invoice Items (tblStockMovement)
SELECT 
    'POS INVOICE ITEMS' AS TableGroup,
    'tblStockMovement' AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    CASE 
        WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
        WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
        ELSE ''
    END AS Size,
    c.IS_NULLABLE AS IsNullable,
    c.COLUMN_DEFAULT AS DefaultValue,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        WHEN fk.COLUMN_NAME IS NOT NULL THEN 'FK'
        ELSE ''
    END AS KeyType,
    ep.value AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = 'tblStockMovement'
) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'FOREIGN KEY' AND tc.TABLE_NAME = 'tblStockMovement'
) fk ON c.COLUMN_NAME = fk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('tblStockMovement') 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE c.TABLE_NAME = 'tblStockMovement'
ORDER BY c.ORDINAL_POSITION

UNION ALL

-- 4. POS Payments (tblPayMethodTrx)
SELECT 
    'POS PAYMENTS' AS TableGroup,
    'tblPayMethodTrx' AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    CASE 
        WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
        WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
        ELSE ''
    END AS Size,
    c.IS_NULLABLE AS IsNullable,
    c.COLUMN_DEFAULT AS DefaultValue,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        WHEN fk.COLUMN_NAME IS NOT NULL THEN 'FK'
        ELSE ''
    END AS KeyType,
    ep.value AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = 'tblPayMethodTrx'
) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'FOREIGN KEY' AND tc.TABLE_NAME = 'tblPayMethodTrx'
) fk ON c.COLUMN_NAME = fk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('tblPayMethodTrx') 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE c.TABLE_NAME = 'tblPayMethodTrx'
ORDER BY c.ORDINAL_POSITION

UNION ALL

-- 5. Items (tblItems)
SELECT 
    'ITEMS' AS TableGroup,
    'tblItems' AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    CASE 
        WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
        WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
        ELSE ''
    END AS Size,
    c.IS_NULLABLE AS IsNullable,
    c.COLUMN_DEFAULT AS DefaultValue,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        ELSE ''
    END AS KeyType,
    ep.value AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = 'tblItems'
) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('tblItems') 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE c.TABLE_NAME = 'tblItems'
ORDER BY c.ORDINAL_POSITION

UNION ALL

-- 6. Stores (tblShops)
SELECT 
    'STORES' AS TableGroup,
    'tblShops' AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    CASE 
        WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
        WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
        ELSE ''
    END AS Size,
    c.IS_NULLABLE AS IsNullable,
    c.COLUMN_DEFAULT AS DefaultValue,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        ELSE ''
    END AS KeyType,
    ep.value AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = 'tblShops'
) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('tblShops') 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE c.TABLE_NAME = 'tblShops'
ORDER BY c.ORDINAL_POSITION

UNION ALL

-- 7. Users (tblUsers)
SELECT 
    'USERS' AS TableGroup,
    'tblUsers' AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    CASE 
        WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
        WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
        ELSE ''
    END AS Size,
    c.IS_NULLABLE AS IsNullable,
    c.COLUMN_DEFAULT AS DefaultValue,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        ELSE ''
    END AS KeyType,
    ep.value AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = 'tblUsers'
) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('tblUsers') 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE c.TABLE_NAME = 'tblUsers'
ORDER BY c.ORDINAL_POSITION

UNION ALL

-- 8. User Groups (tblGroupsAuth)
SELECT 
    'USER GROUPS' AS TableGroup,
    'tblGroupsAuth' AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    CASE 
        WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
        WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
        ELSE ''
    END AS Size,
    c.IS_NULLABLE AS IsNullable,
    c.COLUMN_DEFAULT AS DefaultValue,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        ELSE ''
    END AS KeyType,
    ep.value AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = 'tblGroupsAuth'
) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('tblGroupsAuth') 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE c.TABLE_NAME = 'tblGroupsAuth'
ORDER BY c.ORDINAL_POSITION

UNION ALL

-- 9. Customers (tblChartOfAccounts)
SELECT 
    'CUSTOMERS' AS TableGroup,
    'tblChartOfAccounts' AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    CASE 
        WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
        WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
        ELSE ''
    END AS Size,
    c.IS_NULLABLE AS IsNullable,
    c.COLUMN_DEFAULT AS DefaultValue,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        ELSE ''
    END AS KeyType,
    ep.value AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = 'tblChartOfAccounts'
) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('tblChartOfAccounts') 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE c.TABLE_NAME = 'tblChartOfAccounts'
ORDER BY c.ORDINAL_POSITION

UNION ALL

-- 10. Barcode Settings (tblBarcodeSettings)
SELECT 
    'BARCODE SETTINGS' AS TableGroup,
    'tblBarcodeSettings' AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    CASE 
        WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
        WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
        ELSE ''
    END AS Size,
    c.IS_NULLABLE AS IsNullable,
    c.COLUMN_DEFAULT AS DefaultValue,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        ELSE ''
    END AS KeyType,
    ep.value AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' AND tc.TABLE_NAME = 'tblBarcodeSettings'
) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('tblBarcodeSettings') 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE c.TABLE_NAME = 'tblBarcodeSettings'
ORDER BY c.ORDINAL_POSITION

ORDER BY TableGroup, TableName, ColumnName;

-- =====================================================
-- POS SYSTEM SUMMARY
-- =====================================================
SELECT 
    'POS SYSTEM SUMMARY' AS InfoType,
    'Total POS Sessions' AS Metric,
    CAST((SELECT COUNT(*) FROM tblPOSSessions) AS VARCHAR(20)) AS Value
UNION ALL
SELECT 
    'POS SYSTEM SUMMARY',
    'Total POS Invoices',
    CAST((SELECT COUNT(*) FROM tblStockMovHeader WHERE TrxType = 'مبيعات') AS VARCHAR(20))
UNION ALL
SELECT 
    'POS SYSTEM SUMMARY',
    'Total POS Invoice Items',
    CAST((SELECT COUNT(*) FROM tblStockMovement WHERE TrxType = 'مبيعات') AS VARCHAR(20))
UNION ALL
SELECT 
    'POS SYSTEM SUMMARY',
    'Total POS Payments',
    CAST((SELECT COUNT(*) FROM tblPayMethodTrx) AS VARCHAR(20))
UNION ALL
SELECT 
    'POS SYSTEM SUMMARY',
    'Total Items',
    CAST((SELECT COUNT(*) FROM tblItems) AS VARCHAR(20))
UNION ALL
SELECT 
    'POS SYSTEM SUMMARY',
    'Total Stores',
    CAST((SELECT COUNT(*) FROM tblShops) AS VARCHAR(20))
UNION ALL
SELECT 
    'POS SYSTEM SUMMARY',
    'Total Users',
    CAST((SELECT COUNT(*) FROM tblUsers) AS VARCHAR(20))
UNION ALL
SELECT 
    'POS SYSTEM SUMMARY',
    'Total User Groups',
    CAST((SELECT COUNT(*) FROM tblGroupsAuth) AS VARCHAR(20))
UNION ALL
SELECT 
    'POS SYSTEM SUMMARY',
    'Total Customers',
    CAST((SELECT COUNT(*) FROM tblChartOfAccounts) AS VARCHAR(20))
UNION ALL
SELECT 
    'POS SYSTEM SUMMARY',
    'Total Barcode Settings',
    CAST((SELECT COUNT(*) FROM tblBarcodeSettings) AS VARCHAR(20)); 