﻿Imports System.Data.SqlClient
Imports System.Reflection
Imports System.Runtime.InteropServices
Imports Azure.Identity
Imports Excel = Microsoft.Office.Interop.Excel
Public Class frmSearchJE

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click

        Dim CMDString As String = ""

        CMDString = "SELECT JEHeaderID AS [رقم القيد], AccountCode AS [رقم الحساب], AccountName AS [اسم الحساب], EntryReference AS البيان, EntryDate AS التاريخ, EntryType AS [نوع القيد], EntryReferenceTrxNo AS [رقم الفاتورة], Credit AS دائن, Debit AS مدين, Description AS الوصف, CreatedBy AS بواسطة FROM Accounts_JE_View where EntryDate >= '" & Format(dtpFrom.Value.Date, "yyyy-MM-dd") & "' and EntryDate <= '" & Format(dtpTo.Value.Date, "yyyy-MM-dd") & "'"

        If Val(txtInvoiceNo.Text) <> 0 Then
            CMDString += " and EntryReferenceTrxNo = " & Val(txtInvoiceNo.Text) & ""
        End If
        If Val(cmbxAccount.SelectedValue) <> 0 Then
            CMDString += " and AccountCode = '" & cmbxAccount.SelectedValue & "'"
        End If
        If Val(txtCredit.Text) <> 0 Then
            CMDString += " and Credit = " & Val(txtCredit.Text) & ""
        End If
        If Val(txtDebit.Text) <> 0 Then
            CMDString += " and Debit = " & Val(txtDebit.Text) & ""
        End If
        If Val(txtJEID.Text) <> 0 Then
            CMDString += " and JEHeaderID = " & Val(txtJEID.Text) & ""
        End If
        If Trim(cmbxEntryType.Text) <> "" Then
            CMDString += " and EntryType = '" & Trim(cmbxEntryType.Text) & "'"
        End If
        If Trim(cmbxUser.Text) <> "" Then
            CMDString += " and CreatedBy = '" & Trim(cmbxUser.Text) & "'"
        End If
        If Val(txtJEID.Text) <> 0 Then
            CMDString += " and JEHeaderID = " & Val(txtJEID.Text) & ""
        End If


        CMDString += "  order by JEHeaderID"

        If CMDString <> "" Then
            Dim ds As DataSet = New DataSet
            Dim CMD As New SqlCommand
            CMD.CommandText = CMDString
            CMD.Connection = Con
            Dim da As New SqlDataAdapter(CMD)
            ds.Clear()
            da.Fill(ds)
            DataGridView1.DataSource = ds.Tables(0)
        Else
            DataGridView1.DataSource = ""
        End If

    End Sub

    Private Sub frmSearch_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        'frmMain.Enabled = True
    End Sub

    Private Sub frmSearch_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AccountsLoad()
        EntryTypeLoad()
        DateLoad()

        'cmbxEntryType.SelectedIndex = 0
        If UserType = "Limited" Then
            cmbxUser.Items.Clear()
            cmbxUser.Items.Add(UserName)
            cmbxUser.Text = UserName
            cmbxUser.Enabled = False
        Else
            UsersLoad()
            cmbxUser.Enabled = True
        End If
        ForceGregorianForAllPickers(Me)
        If UserType = "admin" Then
            btnDelete.Visible = True
            btnEdit.Visible = True
        Else
            btnDelete.Visible = False
            btnEdit.Visible = False
        End If

    End Sub
    Sub UsersLoad()
        Try
            Dim CMD As New SqlCommand("Select Distinct([المستخدم]) from InvoicesForSearch order by [المستخدم]", Con)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CMD.ExecuteReader
            cmbxUser.Items.Clear()
            Do While reader.Read
                cmbxUser.Items.Add(reader.Item(0).ToString)
            Loop
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try

    End Sub



    Sub AccountsLoad()
        Dim CMD As New SqlCommand("Select AccountNo,AccountDescription from AccountChart order by ParentID,AccountNo", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountDescription + ' - ' + CONVERT(AccountNo, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxAccount.DataSource = dt
        cmbxAccount.DisplayMember = "DisplayText" ' Show Name
        cmbxAccount.ValueMember = "AccountNo" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        cmbxAccount.Text = ""
    End Sub
    Sub EntryTypeLoad()
        Dim CMD As New SqlCommand("Select Distinct(EntryType) from Accounts_JE_View where EntryType is not Null order by EntryType", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxEntryType.Items.Clear()
        Do While reader.Read
            cmbxEntryType.Items.Add(reader.Item("EntryType").ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Sub DateLoad()
        Try
            Dim CMD As New SqlCommand("Select Min([تاريخ الفاتورة]) from InvoicesForSearch", Con)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CMD.ExecuteReader
            If reader.Read Then
                If Not IsDBNull(reader.Item(0)) Then
                    dtpFrom.Value = CDate(reader.Item(0))
                Else
                    dtpFrom.Value = Date.Today ' or any default value you prefer
                End If
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try

    End Sub

    Private Sub btnPrintVoucher_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrintVoucher.Click
        If DataGridView1.SelectedRows.Count - 1 >= 0 Then
            If Val(DataGridView1.SelectedRows(0).Cells(0).Value) <> 0 Then
                If DataGridView1.SelectedRows(0).Cells(5).Value = "دفع مبيعات" Or DataGridView1.SelectedRows(0).Cells(5).Value = "قيد يدوي قبض" Then
                    PrintType = "CashRec"
                ElseIf DataGridView1.SelectedRows(0).Cells(5).Value = "قيد يدوي صرف" Or DataGridView1.SelectedRows(0).Cells(5).Value = "مدفوعات مورد" Then
                    PrintType = "CashPay"
                Else
                    PrintType = ""
                End If
            End If

            If PrintType <> "" Then
                JESN = DataGridView1.SelectedRows(0).Cells(0).Value
                frmPrintPreview.MdiParent = frmMain
                frmPrintPreview.Show()
            End If
        Else
            MsgBox("يجب تحديد سطر واحد فقط لطباعة الإيصال", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

    Private Sub btnPrintJE_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrintJE.Click
        If DataGridView1.SelectedRows.Count - 1 >= 0 Then
            If Val(DataGridView1.SelectedRows(0).Cells(0).Value) <> 0 Then
                PrintType = "GLTrx"
            Else
                PrintType = ""
            End If
            If PrintType <> "" Then
                JESN = DataGridView1.SelectedRows(0).Cells(0).Value
                frmPrintPreview.MdiParent = frmMain
                frmPrintPreview.Show()
            End If
        Else
            MsgBox("يجب تحديد سطر واحد فقط لطباعة القيد", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub


    'Private Sub btnPrintPOSInvoice_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrintPOSInvoice.Click
    '    If DataGridView1.SelectedRows.Count - 1 >= 0 Then
    '        If DataGridView1.SelectedRows(0).Cells(1).Value <> "مبيعات" Then
    '            MsgBox("يمكن طباعة فاتورة المبيعات المبسطة فقط", MsgBoxStyle.Critical, "نظام السلطان")
    '            Exit Sub
    '        End If
    '        'If DataGridView1.SelectedRows(0).Cells(4).Value <> " " Then
    '        '    MsgBox("يمكن طباعة فاتورة المبيعات المبسطة فقط", MsgBoxStyle.Critical, "نظام السلطان")
    '        '    Exit Sub
    '        'End If
    '        If Not IsDBNull(DataGridView1.SelectedRows(0).Cells(4).Value) AndAlso
    '            Not String.IsNullOrWhiteSpace(DataGridView1.SelectedRows(0).Cells(4).Value.ToString()) Then

    '            MsgBox("يمكن طباعة فاتورة المبيعات المبسطة فقط", MsgBoxStyle.Critical, "نظام السلطان")
    '            Exit Sub
    '        End If


    '        PrintType = "POSInvoice"
    '        InvNoForPrint = DataGridView1.SelectedRows(0).Cells(2).Value
    '        frmPrintPreview.MdiParent = frmMain
    '        frmPrintPreview.Show()
    '    Else
    '        MsgBox("يجب تحديد سطر واحد فقط على الأقل لطباعة نسخة من الفاتورة", MsgBoxStyle.Critical, "نظام السلطان")
    '    End If
    'End Sub

    Private Sub btnAccountSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAccountSearch.Click
        AccountSearchForm = "frmSearchJE"
        frmAccountSearch.ShowDialog()

    End Sub


    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        ExportToExcelAndSave()
    End Sub


    Private Sub ExportToExcelAndSave()
        If DataGridView1.Rows.Count = 0 Then
            MessageBox.Show("لا يوجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim saveFileDialog As New SaveFileDialog()
        saveFileDialog.Filter = "Excel Workbook|*.xlsx"
        saveFileDialog.Title = "حدد مكان حفظ الملف"
        saveFileDialog.FileName = "تقرير السندات.xlsx"

        If saveFileDialog.ShowDialog() = DialogResult.OK Then
            Dim xlApp As Excel.Application = Nothing
            Dim xlWorkbook As Excel.Workbook = Nothing
            Dim xlWorksheet As Excel.Worksheet = Nothing

            Try
                xlApp = New Excel.Application()
                xlWorkbook = xlApp.Workbooks.Add()
                xlWorksheet = CType(xlWorkbook.Sheets(1), Excel.Worksheet)

                ' Header row
                Dim colIndex As Integer = 1
                For Each col As DataGridViewColumn In DataGridView1.Columns
                    If col.Visible Then
                        xlWorksheet.Cells(1, colIndex).Value = col.HeaderText
                        colIndex += 1
                    End If
                Next

                ' Data rows
                Dim rowIndex As Integer = 2
                For Each row As DataGridViewRow In DataGridView1.Rows
                    If Not row.IsNewRow Then
                        colIndex = 1
                        For Each col As DataGridViewColumn In DataGridView1.Columns
                            If col.Visible Then
                                xlWorksheet.Cells(rowIndex, colIndex).Value = row.Cells(col.Index).Value?.ToString()
                                colIndex += 1
                            End If
                        Next
                        rowIndex += 1
                    End If
                Next

                ' Save the file
                xlWorkbook.SaveAs(saveFileDialog.FileName)
                MessageBox.Show("تم حفظ الملف بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)

            Catch ex As Exception
                MessageBox.Show("خطأ أثناء التصدير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Finally
                ' Cleanup Excel COM objects
                If Not IsNothing(xlWorkbook) Then xlWorkbook.Close(False)
                If Not IsNothing(xlApp) Then xlApp.Quit()
                Marshal.ReleaseComObject(xlWorksheet)
                Marshal.ReleaseComObject(xlWorkbook)
                Marshal.ReleaseComObject(xlApp)
            End Try
        End If
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrintReport.Click
        'InvoiceType = Trim(cmbxEntryType.Text)
        'If InvoiceType = "" Then
        '    MsgBox("يجب تحديد نوع الفاتورة قبل طباعة التقرير", MsgBoxStyle.Critical, "نظام السلطان")
        '    Exit Sub
        'End If
        'If rdbInvoiceLines.Checked = True Then
        '    PrintType = "SalesLines"
        'ElseIf rdbInvoices.Checked = True Then
        '    PrintType = "SalesHeader"
        'End If

        'DateStrFrom = dtpFrom.Value.ToString("yyyy-MM-dd")
        'DateStrTo = dtpTo.Value.ToString("yyyy-MM-dd")
        'InvoiceType = cmbxEntryType.Text
        'frmPrintPreview.MdiParent = frmMain
        'frmPrintPreview.Show()


    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If DataGridView1.SelectedRows.Count - 1 >= 0 Then
            JESN = DataGridView1.SelectedRows(0).Cells(0).Value
            Dim DeleteReason As String = ""
            If MsgBox("هل أنت متأكد من حذف الإيصال " & JESN & "؟", MsgBoxStyle.YesNo, "تأكيد الحذف") = MsgBoxResult.No Then
                Exit Sub
            End If
            DeleteReason = Trim(InputBox("يرجى إدخال سبب الحذف:", "سبب الحذف"))
            If DeleteReason = "" Then
                MsgBox("يجب إدخال سبب الحذف", MsgBoxStyle.Critical, "نظام السلطان")
                Exit Sub
            End If

            Try
                Using Con As New SqlConnection(ConStr)
                    Using cmd As New SqlCommand("sp_DeleteJEEntry", Con)
                        cmd.CommandType = CommandType.StoredProcedure
                        cmd.Parameters.AddWithValue("@JEHeaderID", JESN)
                        cmd.Parameters.AddWithValue("@DeleteReason", DeleteReason)
                        cmd.Parameters.AddWithValue("@DeletedBy", UserName)
                        If Con.State <> ConnectionState.Open Then
                            Con.Open()
                        End If
                        cmd.ExecuteNonQuery()
                        If Con.State <> ConnectionState.Closed Then
                            Con.Close()
                        End If
                    End Using
                End Using
            Catch ex As Exception
                MessageBox.Show("خطأ أثناء حذف الإيصال: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Exit Sub
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End Try
            MsgBox("تم حذف الإيصال بنجاح", MsgBoxStyle.Information, "نظام السلطان")

        Else
            MsgBox("يجب تحديد سطر واحد فقط لحذف الإيصال", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If DataGridView1.SelectedRows.Count - 1 >= 0 Then
            JESN = DataGridView1.SelectedRows(0).Cells(0).Value
            Dim OriginalRef As String = DataGridView1.SelectedRows(0).Cells(3).Value.ToString()
            Dim OriginalAmount As Decimal = Val(DataGridView1.SelectedRows(0).Cells(7).Value) + Val(DataGridView1.SelectedRows(0).Cells(8).Value)

            Dim NewRef As String = InputBox("تعديل نص المرجع:", "تعديل المرجع", OriginalRef)
            If NewRef.Trim() = "" Then
                MsgBox("يجب إدخال نص المرجع", MsgBoxStyle.Critical, "نظام السلطان")
                Exit Sub
            End If

            Dim NewAmountStr As String = InputBox("تعديل المبلغ:", "تعديل المبلغ", OriginalAmount.ToString())
            Dim NewAmount As Decimal
            If Not Decimal.TryParse(NewAmountStr, NewAmount) Or NewAmount <= 0 Then
                MsgBox("المبلغ غير صحيح", MsgBoxStyle.Critical, "نظام السلطان")
                Exit Sub
            End If

            Try
                Using Con As New SqlConnection(ConStr)
                    Using cmd As New SqlCommand("sp_ModifyJEEntry", Con)
                        cmd.CommandType = CommandType.StoredProcedure
                        cmd.Parameters.AddWithValue("@JEHeaderID", JESN)
                        cmd.Parameters.AddWithValue("@NewEntryReference", NewRef)
                        cmd.Parameters.AddWithValue("@NewAmount", NewAmount)
                        cmd.Parameters.AddWithValue("@ModifiedBy", UserName)
                        If Con.State <> ConnectionState.Open Then
                            Con.Open()
                        End If
                        cmd.ExecuteNonQuery()
                        If Con.State <> ConnectionState.Closed Then
                            Con.Close()
                        End If
                    End Using
                End Using
            Catch ex As Exception
                MessageBox.Show("خطأ أثناء تعديل الإيصال: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Exit Sub
            End Try

            MsgBox("تم تعديل الإيصال بنجاح", MsgBoxStyle.Information, "نظام السلطان")
        Else
            MsgBox("يجب تحديد سطر واحد لتعديل الإيصال", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

End Class