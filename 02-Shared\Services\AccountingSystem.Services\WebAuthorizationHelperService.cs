using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;

namespace AccountingSystem.Services
{
    /// <summary>
    /// Helper service for checking web authorization permissions
    /// Used in views and controllers to check if current user has access
    /// </summary>
    public class WebAuthorizationHelperService
    {
        private readonly AccountingDbContext _context;
        private readonly IMemoryCache _cache;
        private const string CACHE_KEY_USER_GROUP = "UserGroup_";
        private const string CACHE_KEY_USER_MENUS = "UserMenus_";
        private const string CACHE_KEY_USER_QUICKACTIONS = "UserQuickActions_";
        private const int CACHE_DURATION_MINUTES = 30;

        public WebAuthorizationHelperService(AccountingDbContext context, IMemoryCache cache)
        {
            _context = context;
            _cache = cache;
        }

        /// <summary>
        /// Get user's group ID by username
        /// </summary>
        public async Task<int?> GetUserGroupIdAsync(string username)
        {
            if (string.IsNullOrEmpty(username))
                return null;

            var cacheKey = $"{CACHE_KEY_USER_GROUP}{username}";

            if (_cache.TryGetValue(cacheKey, out int? cachedGroupId))
            {
                return cachedGroupId;
            }

            var user = await _context.Users
                .Where(u => u.Username == username)
                .Select(u => u.GroupID)
                .FirstOrDefaultAsync();

            if (user.HasValue)
            {
                _cache.Set(cacheKey, user, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
            }

            return user;
        }

        /// <summary>
        /// Check if user can access a specific route
        /// </summary>
        public async Task<bool> CanAccessRouteAsync(string username, string route)
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(route))
                return false;

            var groupId = await GetUserGroupIdAsync(username);
            if (!groupId.HasValue)
                return false;

            // Admin group (GroupID = 1) has access to everything
            if (groupId.Value == 1)
                return true;

            var cacheKey = $"{CACHE_KEY_USER_MENUS}{username}";

            HashSet<string>? allowedRoutes;
            if (!_cache.TryGetValue(cacheKey, out allowedRoutes) || allowedRoutes == null)
            {
                // Load all allowed routes for this user
                var routes = await _context.WebGroupMenuPermissions
                    .Where(p => p.GroupID == groupId.Value && p.CanView)
                    .Include(p => p.Menu)
                    .Select(p => p.Menu.Route)
                    .ToListAsync();

                allowedRoutes = routes.ToHashSet();
                _cache.Set(cacheKey, allowedRoutes, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
            }

            return allowedRoutes.Contains(route);
        }

        /// <summary>
        /// Check if user has specific permission on a route
        /// </summary>
        public async Task<MenuPermissionDto?> GetRoutePermissionsAsync(string username, string route)
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(route))
                return null;

            var groupId = await GetUserGroupIdAsync(username);
            if (!groupId.HasValue)
                return null;

            // Admin group (GroupID = 1) has full permissions
            if (groupId.Value == 1)
            {
                return new MenuPermissionDto
                {
                    MenuId = 0,
                    CanView = true,
                    CanAdd = true,
                    CanEdit = true,
                    CanDelete = true,
                    CanPrint = true
                };
            }

            var menu = await _context.WebFormMenus
                .Where(m => m.Route == route && m.IsActive)
                .FirstOrDefaultAsync();

            if (menu == null)
                return null;

            var permission = await _context.WebGroupMenuPermissions
                .Where(p => p.GroupID == groupId.Value && p.MenuId == menu.Id)
                .FirstOrDefaultAsync();

            if (permission == null)
                return null;

            return new MenuPermissionDto
            {
                MenuId = menu.Id,
                CanView = permission.CanView,
                CanAdd = permission.CanAdd,
                CanEdit = permission.CanEdit,
                CanDelete = permission.CanDelete,
                CanPrint = permission.CanPrint
            };
        }

        /// <summary>
        /// Get all menu items accessible by user
        /// </summary>
        public async Task<List<WebMenuItemDto>> GetUserMenuItemsAsync(string username)
        {
            if (string.IsNullOrEmpty(username))
                return new List<WebMenuItemDto>();

            var groupId = await GetUserGroupIdAsync(username);
            if (!groupId.HasValue)
                return new List<WebMenuItemDto>();

            try
            {
                // First check if tables exist
                var tableExists = await _context.Database.ExecuteSqlRawAsync(
                    "SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'WebFormMenus'") > 0;

                if (!tableExists)
                {
                    // Tables don't exist yet, return empty list
                    return new List<WebMenuItemDto>();
                }

                // Use simple query first to test
                var allMenus = await _context.WebFormMenus
                    .AsNoTracking()
                    .Where(m => m.IsActive)
                    .OrderBy(m => m.DisplayOrder)
                    .ToListAsync();

                // If admin, return all menus
                if (groupId.Value == 1)
                {
                    return BuildMenuHierarchy(allMenus, null);
                }

                // For non-admin users, filter by permissions
                var permissionMenuIds = await _context.WebGroupMenuPermissions
                    .AsNoTracking()
                    .Where(p => p.GroupID == groupId.Value && p.CanView)
                    .Select(p => p.MenuId)
                    .ToListAsync();

                var accessibleMenus = allMenus
                    .Where(m => permissionMenuIds.Contains(m.Id))
                    .ToList();

                // Add parent containers if children are accessible
                var parentIds = accessibleMenus
                    .Where(m => m.ParentMenuId.HasValue)
                    .Select(m => m.ParentMenuId.Value)
                    .Distinct()
                    .ToHashSet();

                if (parentIds.Any())
                {
                    var parents = allMenus
                        .Where(m => parentIds.Contains(m.Id) && !accessibleMenus.Any(am => am.Id == m.Id))
                        .ToList();

                    accessibleMenus.AddRange(parents);
                }

                return BuildMenuHierarchy(accessibleMenus, null);
            }
            catch (Exception ex)
            {
                // Log the exception for debugging
                System.Diagnostics.Debug.WriteLine($"WebAuthorizationHelperService.GetUserMenuItemsAsync error: {ex.Message}");
                
                // Fallback: return empty list
                return new List<WebMenuItemDto>();
            }
        }

        /// <summary>
        /// Get all quick actions accessible by user
        /// </summary>
        public async Task<List<WebQuickActionDto>> GetUserQuickActionsAsync(string username)
        {
            if (string.IsNullOrEmpty(username))
                return new List<WebQuickActionDto>();

            var groupId = await GetUserGroupIdAsync(username);
            if (!groupId.HasValue)
                return new List<WebQuickActionDto>();

            var cacheKey = $"{CACHE_KEY_USER_QUICKACTIONS}{username}";

            if (_cache.TryGetValue(cacheKey, out List<WebQuickActionDto>? cachedQuickActions) && cachedQuickActions != null)
            {
                return cachedQuickActions;
            }

            try
            {
                // First check if tables exist
                var tableExists = await _context.Database.ExecuteSqlRawAsync(
                    "SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'WebQuickActions'") > 0;

                if (!tableExists)
                {
                    // Tables don't exist yet, return empty list
                    return new List<WebQuickActionDto>();
                }

                // Use simple query first to test
                var allQuickActions = await _context.WebQuickActions
                    .AsNoTracking()
                    .Where(qa => qa.IsActive)
                    .OrderBy(qa => qa.DisplayOrder)
                    .ToListAsync();

                List<WebQuickActionDto> quickActions;

                // If admin, return all quick actions
                if (groupId.Value == 1)
                {
                    quickActions = allQuickActions.Select(qa => new WebQuickActionDto
                    {
                        Id = qa.Id,
                        DisplayName = qa.DisplayName,
                        DisplayNameEn = qa.DisplayNameEn,
                        Route = qa.Route,
                        Icon = qa.Icon,
                        ColorClass = qa.ColorClass,
                        DisplayOrder = qa.DisplayOrder,
                        Category = qa.Category,
                        IsActive = qa.IsActive
                    }).ToList();
                }
                else
                {
                    // For non-admin users, filter by permissions
                    var accessibleQuickActionIds = await _context.WebGroupQuickActionPermissions
                        .AsNoTracking()
                        .Where(p => p.GroupID == groupId.Value && p.IsVisible)
                        .Select(p => p.QuickActionId)
                        .ToListAsync();

                    quickActions = allQuickActions
                        .Where(qa => accessibleQuickActionIds.Contains(qa.Id))
                        .Select(qa => new WebQuickActionDto
                        {
                            Id = qa.Id,
                            DisplayName = qa.DisplayName,
                            DisplayNameEn = qa.DisplayNameEn,
                            Route = qa.Route,
                            Icon = qa.Icon,
                            ColorClass = qa.ColorClass,
                            DisplayOrder = qa.DisplayOrder,
                            Category = qa.Category,
                            IsActive = qa.IsActive
                        }).ToList();
                }

                _cache.Set(cacheKey, quickActions, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
                return quickActions;
            }
            catch (Exception ex)
            {
                // Log the exception for debugging
                System.Diagnostics.Debug.WriteLine($"WebAuthorizationHelperService.GetUserQuickActionsAsync error: {ex.Message}");
                
                // Fallback: return empty list
                return new List<WebQuickActionDto>();
            }
        }

        /// <summary>
        /// Clear cache for a specific user
        /// </summary>
        public void ClearUserCache(string username)
        {
            if (string.IsNullOrEmpty(username))
                return;

            _cache.Remove($"{CACHE_KEY_USER_GROUP}{username}");
            _cache.Remove($"{CACHE_KEY_USER_MENUS}{username}");
            _cache.Remove($"{CACHE_KEY_USER_QUICKACTIONS}{username}");
        }

        /// <summary>
        /// Build hierarchical menu structure
        /// </summary>
        private List<WebMenuItemDto> BuildMenuHierarchy(List<WebFormMenu> allMenus, int? parentId)
        {
            return allMenus
                .Where(m => m.ParentMenuId == parentId)
                .Select(m => new WebMenuItemDto
                {
                    Id = m.Id,
                    DisplayName = m.DisplayName,
                    DisplayNameEn = m.DisplayNameEn,
                    Route = m.Route,
                    Icon = m.Icon,
                    ParentMenuId = m.ParentMenuId,
                    DisplayOrder = m.DisplayOrder,
                    ModuleName = m.ModuleName,
                    IsContainer = m.IsContainer,
                    IsActive = m.IsActive,
                    Children = BuildMenuHierarchy(allMenus, m.Id)
                })
                .OrderBy(m => m.DisplayOrder)
                .ToList();
        }
    }
}

