﻿Imports System.Data.SqlClient
Public Class frmCustomerSearch
    Private Sub frmCustomerSearch_Load(sender As Object, e As EventArgs) Handles MyBase.Load
    End Sub

    Sub SearchBox()
        Dim keyword As String = txtSearch.Text.Trim()
        If keyword = "" Then
            CustomersLoad()
            Return
        End If

        Dim ds As DataSet = New DataSet
        Dim cmd As New SqlClient.SqlCommand("Select CustomerNo as [رقم العميل],CustomerName as [الاسم التجاري],FirstName as [الاسم الأول],LastName as [الاسم الاخير] from tblCustomers where CAST(CustomerNo AS NVARCHAR) like @kw or CustomerName LIKE @kw or FirstName LIKE @kw or LastName LIKE @kw order by CustomerNo", New SqlClient.SqlConnection(Constr))
        cmd.Parameters.AddWithValue("@kw", "%" & keyword & "%")
        Dim da As New SqlClient.SqlDataAdapter(cmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Sub CustomersLoad()
        Dim ds As DataSet = New DataSet
        Dim usercmd As New SqlCommand("Select CustomerNo as [رقم العميل], CustomerName as [الاسم التجاري], FirstName as [الاسم الأول], LastName as [الاسم الاخير] FROM tblCustomers ORDER BY CustomerNo", Con)
        Dim da As New SqlDataAdapter(usercmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        SearchBox()
    End Sub

    Private Sub DataGridView1_CellDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If Val(DataGridView1.CurrentRow.Cells(0).Value) <> 0 Then
            CustNo = DataGridView1.CurrentRow.Cells(0).Value
            DataGridView1.DataSource = ""
            txtSearch.Text = ""
            Me.Close()
            If CustomerSearchForm = "frmSalesInvoiceTrx" Then
                frmSalesInvoiceTrx.BringToFront()
                frmSalesInvoiceTrx.cmbxPartnerNo.SelectedValue = CustNo
                CustomerSearchForm = ""
            ElseIf CustomerSearchForm = "frmCustomerStatement" Then
                frmCustomerStatement.BringToFront()
                frmCustomerStatement.cmbxPartnerNo.SelectedValue = CustNo
                CustomerSearchForm = ""
            ElseIf CustomerSearchForm = "frmCustomerInvoices" Then
                frmCustomerInvoices.BringToFront()
                frmCustomerInvoices.cmbxPartnerNo.SelectedValue = CustNo
                CustomerSearchForm = ""
            ElseIf CustomerSearchForm = "frmSalesReturnInvoiceTrx" Then
                frmSalesReturnInvoiceTrx.BringToFront()
                frmSalesReturnInvoiceTrx.cmbxPartnerNo.SelectedValue = CustNo
                CustomerSearchForm = ""

            ElseIf CustomerSearchForm = "frmUsers" Then
                frmUsers.BringToFront()
                frmUsers.cmbxPartnerNo.SelectedValue = CustNo
                CustomerSearchForm = ""

            ElseIf CustomerSearchForm = "frmPOS" Then
                frmPOS.BringToFront()
                frmPOS.cmbxPartnerNo.SelectedValue = CustNo
                CustomerSearchForm = ""

            ElseIf CustomerSearchForm = "frmCustomerBalances" Then
                frmCustomerBalances.BringToFront()
                frmCustomerBalances.cmbxPartnerNo.SelectedValue = CustNo
                CustomerSearchForm = ""

            End If
        End If
    End Sub
End Class