using System.Globalization;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace AccountingSystem.Web.ModelBinders
{
    /// <summary>
    /// Model binder that parses decimal values in a culture-invariant way, accepting both ',' and '.' as decimal separators.
    /// </summary>
    public sealed class InvariantDecimalModelBinder : IModelBinder
    {
        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            if (bindingContext == null)
            {
                throw new ArgumentNullException(nameof(bindingContext));
            }

            var valueProviderResult = bindingContext.ValueProvider.GetValue(bindingContext.ModelName);
            if (valueProviderResult == ValueProviderResult.None)
            {
                return Task.CompletedTask;
            }

            bindingContext.ModelState.SetModelValue(bindingContext.ModelName, valueProviderResult);

            var value = valueProviderResult.FirstValue;
            if (string.IsNullOrWhiteSpace(value))
            {
                // Allow empty values for nullable decimals
                bindingContext.Result = ModelBindingResult.Success(null);
                return Task.CompletedTask;
            }

            // Normalize decimal separator to '.' and try parse using invariant culture
            var normalized = value.Replace(',', '.');

            if (decimal.TryParse(normalized, NumberStyles.Number, CultureInfo.InvariantCulture, out var result))
            {
                bindingContext.Result = ModelBindingResult.Success(result);
            }
            else
            {
                bindingContext.ModelState.TryAddModelError(bindingContext.ModelName, $"قيمة غير صالحة: {value}");
            }

            return Task.CompletedTask;
        }
    }
}

