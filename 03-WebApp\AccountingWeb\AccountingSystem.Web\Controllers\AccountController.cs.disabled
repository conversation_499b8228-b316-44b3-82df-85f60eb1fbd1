using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using System.Security.Claims;
using System.ComponentModel.DataAnnotations;
using AccountingSystem.Models;
using AccountingSystem.Web.Models;
using AuthService = AccountingSystem.Services.IAuthenticationService;

namespace AccountingSystem.Web.Controllers
{
    public class AccountController : Controller
    {
        private readonly AuthService _authService;
        private readonly ILogger<AccountController> _logger;

        public AccountController(AuthService authService, ILogger<AccountController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        [HttpGet]
        public IActionResult Login(string? returnUrl = null)
        {
            // If user is already authenticated, redirect to main page
            if (User.Identity?.IsAuthenticated == true)
            {
                return RedirectToAction("Index", "Dashboard");
            }

            ViewData["ReturnUrl"] = returnUrl;
            return View(new LoginViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginViewModel model, string? returnUrl = null)
        {
            ViewData["ReturnUrl"] = returnUrl;

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                // Get machine information
                var ipAddress = AccountingSystem.Services.MachineInfoHelper.GetLocalIPAddress(HttpContext);
                var userAgent = AccountingSystem.Services.MachineInfoHelper.GetUserAgent(HttpContext);
                var machineName = AccountingSystem.Services.MachineInfoHelper.GetMachineName();
                var machineUser = AccountingSystem.Services.MachineInfoHelper.GetMachineUser();

                // Authenticate user
                var authResult = await _authService.AuthenticateAsync(
                    model.Username,
                    model.Password,
                    ipAddress,
                    userAgent,
                    machineName,
                    machineUser);

                if (authResult.IsSuccess && authResult.User != null)
                {
                    // Create claims for the authenticated user
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.Name, authResult.User.Username),
                        new Claim(ClaimTypes.NameIdentifier, authResult.User.Id.ToString()),
                        new Claim("FullName", authResult.User.FullName ?? authResult.User.Username),
                        new Claim("UserGroup", authResult.User.Group?.GroupName ?? ""),
                        new Claim("GroupID", authResult.User.GroupID?.ToString() ?? ""),
                        new Claim("LoginTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                    };

                    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                    var authProperties = new AuthenticationProperties
                    {
                        IsPersistent = model.RememberMe,
                        ExpiresUtc = DateTimeOffset.UtcNow.AddHours(8)
                    };

                    await HttpContext.SignInAsync(
                        CookieAuthenticationDefaults.AuthenticationScheme,
                        new ClaimsPrincipal(claimsIdentity),
                        authProperties);

                    _logger.LogInformation("User {Username} logged in successfully from {IPAddress}", 
                        model.Username, ipAddress);

                    // Redirect to return URL or dashboard
                    if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
                    {
                        return Redirect(returnUrl);
                    }

                    return RedirectToAction("Index", "Dashboard");
                }
                else
                {
                    ModelState.AddModelError(string.Empty, authResult.ErrorMessage ?? "فشل في تسجيل الدخول");
                    _logger.LogWarning("Failed login attempt for user {Username} from {IPAddress}: {Error}", 
                        model.Username, ipAddress, authResult.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء تسجيل الدخول");
                _logger.LogError(ex, "Login error for user {Username}", model.Username);
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            var username = User.Identity?.Name;
            
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            
            _logger.LogInformation("User {Username} logged out", username);
            
            return RedirectToAction("Login");
        }

        public IActionResult AccessDenied()
        {
            return View();
        }
    }
}
