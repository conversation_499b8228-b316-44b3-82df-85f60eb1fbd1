using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Web.Utilities;
using AccountingSystem.Models;
using AccountingSystem.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic; // Added missing import for List

using Microsoft.EntityFrameworkCore;
using System;

namespace AccountingSystem.Web.Controllers
{
    [Authorize(Roles = "admin")]
    [Route("Admin/QuickActions")]
    public class AdminQuickActionsController : Controller
    {
        private readonly AccountingDbContext _context;
        public AdminQuickActionsController(AccountingDbContext context)
        {
            _context = context;
        }

        [HttpGet("")]
        public IActionResult Index(string role = null)
        {
            var allItems = SidebarItemRegistry.AllItems;
            var groups = _context.UserGroups.ToList();
            if (string.IsNullOrEmpty(role) && groups.Count > 0)
                role = groups[0].GroupName;
            var enabledRoutes = _context.DashboardQuickActionConfigs
                .Where(q => q.RoleName == role && q.IsEnabled)
                .Select(q => q.SidebarItemRoute)
                .ToHashSet();
            ViewBag.Role = role;
            ViewBag.Groups = groups;
            return View((allItems, enabledRoutes));
        }

        [HttpPost("Save")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Save(string role, List<string> enabledRoutes)
        {
            var configs = _context.DashboardQuickActionConfigs.Where(q => q.RoleName == role).ToList();
            // Disable all first
            foreach (var config in configs)
            {
                config.IsEnabled = enabledRoutes.Contains(config.SidebarItemRoute);
            }
            // Add new enabled routes if not exist
            var existingRoutes = configs.Select(c => c.SidebarItemRoute).ToHashSet();
            foreach (var route in enabledRoutes)
            {
                if (!existingRoutes.Contains(route))
                {
                    _context.DashboardQuickActionConfigs.Add(new DashboardQuickActionConfig
                    {
                        RoleName = role,
                        SidebarItemRoute = route,
                        IsEnabled = true
                    });
                }
            }
            await _context.SaveChangesAsync();
            TempData["SuccessMessage"] = "تم حفظ الإعدادات بنجاح.";
            return RedirectToAction("Index", new { role });
        }

        [HttpPost("FixTodayTrxTypes")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> FixTodayTrxTypes()
        {
            // Correct any purchase invoices incorrectly tagged as sales (and vice versa) for today only
            try
            {
                var today = DateTime.Today;
                var sql = @"UPDATE tblStockMovHeader
                             SET TrxType = CASE WHEN TrxType = N'مبيعات' AND EXISTS(
                                 SELECT 1 FROM tblStockMovement m WHERE m.DocNo = tblStockMovHeader.TrxNo AND m.TrxType = N'مشتريات') THEN N'مشتريات'
                               WHEN TrxType = N'مشتريات' AND EXISTS(
                                 SELECT 1 FROM tblStockMovement m WHERE m.DocNo = tblStockMovHeader.TrxNo AND m.TrxType = N'مبيعات') THEN N'مبيعات'
                               ELSE TrxType END
                             WHERE TrxDate IS NOT NULL AND CAST(TrxDate AS DATE) = @today";

                var conn = _context.Database.GetDbConnection();
                await conn.OpenAsync();
                using var cmd = conn.CreateCommand();
                cmd.CommandText = sql;
                var p = cmd.CreateParameter();
                p.ParameterName = "@today";
                p.Value = today;
                cmd.Parameters.Add(p);
                await cmd.ExecuteNonQueryAsync();
                await conn.CloseAsync();

                TempData["SuccessMessage"] = "تم تصحيح أنواع الحركات لليوم بنجاح.";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = ex.Message;
                return RedirectToAction("Index");
            }
        }

    }
}
