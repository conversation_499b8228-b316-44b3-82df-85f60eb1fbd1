@model AccountingSystem.Web.ViewModels.Expenses.ExpenseCreateViewModel
@{
    ViewData["Title"] = "إنشاء مصروف";
}
<div class="container mt-3">
    <h3>المصروفات - إنشاء</h3>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success">@TempData["Success"]</div>
    }

    @if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger">
            الرجاء تصحيح الأخطاء التالية.
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.DuplicateWarning))
    {
        <div class="alert alert-warning">
            @Model.DuplicateWarning
        </div>
    }

    <form asp-action="Create" method="post" enctype="multipart/form-data" class="row g-3">
        <div class="col-md-3">
            <label asp-for="InvoiceDate" class="form-label"></label>
            <input asp-for="InvoiceDate" class="form-control" type="date" />
            <span asp-validation-for="InvoiceDate" class="text-danger"></span>
        </div>
        <div class="col-md-3">
            <label class="form-label">المستودع</label>
            <select asp-for="Store" class="form-select">
                <option value="">-- اختر --</option>
                @foreach (var s in Model.Warehouses) { <option value="@s">@s</option> }
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">الصندوق/الكاشير (دائن)</label>
            <select asp-for="CreditCashier" class="form-select">
                <option value="">-- اختر --</option>
                @foreach (var c in Model.CashierAccounts) { <option value="@c.code">@c.code - @c.name</option> }
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">حساب المصروف (مدين)</label>
            <select asp-for="DebitExpense" class="form-select">
                @foreach (var e in Model.ExpenseAccounts) { <option value="@e.code">@e.code - @e.name</option> }
            </select>
            <span asp-validation-for="DebitExpense" class="text-danger"></span>
        </div>

        <div class="col-md-3">
            <label asp-for="InvoiceAmount" class="form-label"></label>
            <input asp-for="InvoiceAmount" class="form-control" />
            <span asp-validation-for="InvoiceAmount" class="text-danger"></span>
        </div>
        <div class="col-md-3">
            <label asp-for="TaxAmount" class="form-label"></label>
            <input asp-for="TaxAmount" class="form-control" />
            <small class="text-muted">إذا كانت الضريبة > 0 يجب إدخال الرقم الضريبي للمورد</small>
        </div>

        <div class="col-md-3">
            <label asp-for="VendorNo" class="form-label"></label>
            <select asp-for="VendorNo" class="form-select">
                <option value="">-- بدون مورد --</option>
                @foreach (var v in Model.Vendors) { <option value="@v.id" data-name="@v.name" data-vat="@v.vat">@v.id - @v.name</option> }
            </select>
        </div>
        <div class="col-md-3">
            <label asp-for="VendorInvoiceNo" class="form-label"></label>
            <input asp-for="VendorInvoiceNo" class="form-control" />
        </div>

        <div class="col-md-4">
            <label asp-for="VendorName" class="form-label"></label>
            <input asp-for="VendorName" class="form-control" />
        </div>
        <div class="col-md-4">
            <label asp-for="VendorVATRN" class="form-label"></label>
            <input asp-for="VendorVATRN" class="form-control" />
            <span asp-validation-for="VendorVATRN" class="text-danger"></span>
        </div>
        <div class="col-md-4">
            <label asp-for="EmployeeBuyer" class="form-label"></label>
            <select asp-for="EmployeeBuyer" class="form-select">
                <option value="">-- بدون موظف --</option>
                @foreach (var emp in Model.Employees) { <option value="@emp.id">@emp.id - @emp.name</option> }
            </select>
        </div>

        <div class="col-12">
            <label asp-for="Notes" class="form-label"></label>
            <textarea asp-for="Notes" class="form-control" rows="2"></textarea>
        </div>

        <div class="col-md-6">
            <label asp-for="PhotoFile" class="form-label"></label>
            <input asp-for="PhotoFile" class="form-control" type="file" accept="image/*,application/pdf,.pdf" />
        </div>

        <input type="hidden" asp-for="IgnoreDuplicate" />

        <div class="col-12">
            <button type="submit" class="btn btn-primary">حفظ</button>
            <a asp-action="Create" class="btn btn-secondary">تفريغ</a>
        </div>
    </form>
</div>

@section Scripts{
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const amt = document.getElementById('InvoiceAmount');
            const vat = document.getElementById('TaxAmount');
            const vendorSel = document.getElementById('VendorNo');
            const vendorName = document.getElementById('VendorName');
            const vendorVat = document.getElementById('VendorVATRN');

            if (amt && vat) {
                const recalc = () => {
                    const v = parseFloat((amt.value || '').toString().replace(',', '.'));
                    if (!isNaN(v)) {
                        const calc = Math.round(v * 0.15 * 100) / 100; // 15%
                        vat.value = calc.toFixed(2);
                    }
                };
                amt.addEventListener('input', recalc);
                amt.addEventListener('change', recalc);
            }

            if (vendorSel && vendorName && vendorVat) {
                vendorSel.addEventListener('change', function () {
                    const opt = vendorSel.options[vendorSel.selectedIndex];
                    vendorName.value = opt?.dataset?.name || '';
                    vendorVat.value = opt?.dataset?.vat || '';
                });
            }
        });
    </script>
}

