@model AccountingSystem.Models.Vendor

@{
    ViewData["Title"] = "حذف المورد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-trash me-2"></i>
                        حذف المورد
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هل أنت متأكد من حذف هذا المورد؟ هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">المعلومات الأساسية</h6>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-4">رقم المورد:</dt>
                                        <dd class="col-sm-8">@Model.VendorNo</dd>

                                        <dt class="col-sm-4">اسم المورد:</dt>
                                        <dd class="col-sm-8">@Model.VendorName</dd>

                                        <dt class="col-sm-4">الاسم الأول:</dt>
                                        <dd class="col-sm-8">@Model.FirstName</dd>

                                        <dt class="col-sm-4">الاسم الأخير:</dt>
                                        <dd class="col-sm-8">@Model.LastName</dd>

                                        <dt class="col-sm-4">الجوال:</dt>
                                        <dd class="col-sm-8">@Model.Mobile</dd>

                                        <dt class="col-sm-4">الهاتف:</dt>
                                        <dd class="col-sm-8">@Model.Phone</dd>

                                        <dt class="col-sm-4">البريد الإلكتروني:</dt>
                                        <dd class="col-sm-8">@Model.Email</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">معلومات العنوان والأعمال</h6>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-4">العنوان:</dt>
                                        <dd class="col-sm-8">@Model.StreetAddress1</dd>

                                        <dt class="col-sm-4">المدينة:</dt>
                                        <dd class="col-sm-8">@Model.City</dd>

                                        <dt class="col-sm-4">المنطقة:</dt>
                                        <dd class="col-sm-8">@Model.Region</dd>

                                        <dt class="col-sm-4">طريقة الدفع:</dt>
                                        <dd class="col-sm-8">@Model.PaymentMethod</dd>

                                        <dt class="col-sm-4">الحد الائتماني:</dt>
                                        <dd class="col-sm-8">@Model.CreditLimit?.ToString("C")</dd>

                                        <dt class="col-sm-4">الحالة:</dt>
                                        <dd class="col-sm-8">
                                            @if (!string.IsNullOrEmpty(Model.Status))
                                            {
                                                <span class="badge bg-@(Model.Status == "نشط" ? "success" : "secondary")">
                                                    @Model.Status
                                                </span>
                                            }
                                        </dd>

                                        <dt class="col-sm-4">نوع المورد:</dt>
                                        <dd class="col-sm-8">@Model.LocalVendor</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">الملاحظات</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>@Model.Notes</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <div class="row mt-4">
                        <div class="col-12">
                            <form asp-action="Delete" method="post" class="d-inline">
                                <input type="hidden" asp-for="VendorNo" />
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        العودة للقائمة
                                    </a>
                                    <div>
                                        <a asp-action="Edit" asp-route-vendorNo="@Model.VendorNo" class="btn btn-warning me-2">
                                            <i class="fas fa-edit me-2"></i>
                                            تعديل بدلاً من الحذف
                                        </a>
                                        <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المورد؟')">
                                            <i class="fas fa-trash me-2"></i>
                                            تأكيد الحذف
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
