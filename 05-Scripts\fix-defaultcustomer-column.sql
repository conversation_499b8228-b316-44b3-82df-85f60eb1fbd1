-- Fix DefaultCustomer column type mismatch
-- This script ensures the DefaultCustomer column is BIGINT to match the database schema

USE SULTDB;
GO

-- Check if the column exists and is BIGINT
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'tblUsers' 
           AND COLUMN_NAME = 'DefaultCustomer' 
           AND DATA_TYPE = 'bigint')
BEGIN
    PRINT 'DefaultCustomer column is already BIGINT - no change needed';
END
ELSE IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'tblUsers' 
                AND COLUMN_NAME = 'DefaultCustomer' 
                AND DATA_TYPE = 'int')
BEGIN
    -- Change the column type from INT to BIGINT
    ALTER TABLE tblUsers ALTER COLUMN DefaultCustomer BIGINT NULL;
    PRINT 'Changed DefaultCustomer column from INT to BIGINT';
END
ELSE
BEGIN
    PRINT 'DefaultCustomer column does not exist - it will be created by the add-pos-user-columns.sql script';
END 