﻿Imports System.Data.SqlClient
Public Class frmPOSDevices

    Private Sub frmPOSDevices_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadShops()
        LoadDevices()
    End Sub

    Private Sub LoadShops()
        Dim dt As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT SN, Shop_Text FROM tblShops ORDER BY Shop_Text", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dt)
        End Using
        cmbShop.DataSource = dt
        cmbShop.DisplayMember = "Shop_Text"
        cmbShop.ValueMember = "SN"
        cmbShop.SelectedIndex = -1
    End Sub

    'Private Sub LoadDevices()
    '    Dim dt As New DataTable()
    '    Using con As New SqlConnection(ConStr)
    '        Dim cmd As New SqlCommand("SELECT DeviceID, DeviceName, Description, ShopID, IsActive FROM tblPOSDevices ORDER BY DeviceID DESC", con)
    '        Dim da As New SqlDataAdapter(cmd)
    '        da.Fill(dt)
    '    End Using
    '    dgvDevices.DataSource = dt
    'End Sub

    Private Sub LoadDevices()
        Dim dt As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT d.DeviceID, d.DeviceName, d.Description, d.ShopID, s.Shop_Text As ShopName, d.IsActive FROM tblPOSDevices d LEFT JOIN tblShops s ON d.ShopID = s.SN ORDER BY d.DeviceID DESC", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dt)
        End Using
        dgvDevices.DataSource = dt
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If txtDeviceName.Text.Trim = "" OrElse cmbShop.SelectedIndex = -1 Then
            MessageBox.Show("يرجى إدخال اسم الجهاز واختيار الفرع", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        Using con As New SqlConnection(ConStr)
            Dim query As String = ""

            query &= "IF EXISTS (SELECT 1 FROM tblPOSDevices WHERE DeviceName = @DeviceName) "
            query &= "UPDATE tblPOSDevices SET Description=@Desc, ShopID=@ShopID, IsActive=@Active,ModifiedBy=@ModifiedBy,ModifiedOn=@ModifiedOn WHERE DeviceName=@DeviceName "
            query &= "ELSE "
            query &= "INSERT INTO tblPOSDevices (DeviceName, Description, ShopID, IsActive,CreatedOn,CreatedBy) VALUES (@DeviceName, @Desc, @ShopID, @Active,@CreatedOn,@CreatedBy)"


            Dim cmd As New SqlCommand(query, con)

            cmd.Parameters.AddWithValue("@DeviceName", txtDeviceName.Text.Trim())
            cmd.Parameters.AddWithValue("@Desc", txtDescription.Text.Trim())
            cmd.Parameters.AddWithValue("@ShopID", cmbShop.SelectedValue)
            cmd.Parameters.AddWithValue("@Active", chkIsActive.Checked)
            cmd.Parameters.AddWithValue("@CreatedOn", DateTime.Now)
            cmd.Parameters.AddWithValue("@CreatedBy", UserName)
            cmd.Parameters.AddWithValue("@ModifiedOn", DateTime.Now)
            cmd.Parameters.AddWithValue("@ModifiedBy", UserName)

            con.Open()
            cmd.ExecuteNonQuery()
        End Using

        LoadDevices()
        ClearFields()
        MessageBox.Show("تم حفظ الجهاز بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        txtDeviceName.Clear()
        txtDescription.Clear()
        cmbShop.SelectedIndex = -1
        chkIsActive.Checked = True
    End Sub
    Private Sub ClearFields()
        txtDeviceName.Clear()
        txtDescription.Clear()
        cmbShop.SelectedIndex = -1
        chkIsActive.Checked = True
    End Sub
    Private Sub dgvDevices_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgvDevices.CellDoubleClick
        If e.RowIndex >= 0 Then
            Dim row As DataGridViewRow = dgvDevices.Rows(e.RowIndex)
            txtDeviceName.Text = row.Cells("DeviceName").Value.ToString()
            txtDescription.Text = row.Cells("Description").Value.ToString()
            cmbShop.SelectedValue = row.Cells("ShopID").Value
            chkIsActive.Checked = Convert.ToBoolean(row.Cells("IsActive").Value)
        End If
    End Sub

End Class