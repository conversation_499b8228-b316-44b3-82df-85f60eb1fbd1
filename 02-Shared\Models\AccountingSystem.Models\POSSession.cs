using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    public class POSSession
    {
        [Key]
        public int SessionID { get; set; }

        [Required]
        [StringLength(50)]
        public string SessionSN { get; set; } = string.Empty;

        public int? ShopID { get; set; }

        public int? DeviceID { get; set; }

        public int? ShiftID { get; set; }

        [StringLength(50)]
        public string? OpenedBy { get; set; }

        public DateTime? OpenTime { get; set; }

        [StringLength(50)]
        public string? ClosedBy { get; set; }

        public DateTime? CloseTime { get; set; }

        [StringLength(20)]
        public string? Status { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? ClosingCash { get; set; }

        [StringLength(500)]
        public string? Note { get; set; }

        // Navigation properties
        public virtual Store? Shop { get; set; }
        public virtual POSDevice? Device { get; set; }
        public virtual POSShift? Shift { get; set; }
    }

    public class POSDevice
    {
        [Key]
        public int DeviceID { get; set; }

        [Required]
        [StringLength(100)]
        public string DeviceName { get; set; } = string.Empty;
    }

    public class POSShift
    {
        [Key]
        public int ShiftID { get; set; }

        [Required]
        [StringLength(100)]
        public string ShiftName { get; set; } = string.Empty;
    }

    public class POSSessionDetail
    {
        [Key]
        public int DetailID { get; set; }

        public int SessionID { get; set; }

        [Required]
        [StringLength(50)]
        public string PaymentMethod { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        // Navigation property
        public virtual POSSession Session { get; set; }
    }
} 