@model List<AccountingSystem.Models.ItemsCategory>
@{
    ViewData["Title"] = "تصنيفات الأصناف";
    Layout = "_Layout";
    var allCategories = ViewBag.AllCategories as List<AccountingSystem.Models.ItemsCategory> ?? new List<AccountingSystem.Models.ItemsCategory>();
}

<style>
    .tree-container ul {
        padding-right: 20px;
        list-style-type: none;
    }
    .tree-container li {
        margin: 5px 0;
    }
    .tree-item {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .tree-item .actions {
        display: none;
    }
    .tree-item:hover .actions {
        display: inline-block;
    }
</style>

<div class="container-fluid" dir="rtl">
    <div class="page-header">
        <h1 class="page-title"><i class="fas fa-sitemap"></i> @ViewData["Title"]</h1>
        <div class="page-options">
            <button type="button" class="btn btn-primary" onclick="openCreateModal(null)">
                <i class="fas fa-plus"></i> إضافة تصنيف رئيسي
            </button>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success">@TempData["SuccessMessage"]</div>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger">@TempData["ErrorMessage"]</div>
        }
    </div>

    <div class="card">
        <div class="card-header"><h3 class="card-title">الهيكل الشجري للتصنيفات</h3></div>
        <div class="card-body">
            <div class="tree-container">
                <ul>
                    @foreach (var category in Model)
                    {
                        @await Html.PartialAsync("_CategoryTreeItem", category)
                    }
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Category Modal (for Create/Edit) -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="categoryForm" method="post">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="categoryId" name="Id" />
                    <input type="hidden" id="parentId" name="ParentId" />
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">اسم التصنيف</label>
                        <input type="text" class="form-control" id="categoryName" name="Name" required />
                    </div>
                    <div id="parentInfo" class="alert alert-info d-none"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Form -->
<form id="deleteForm" asp-controller="ItemsCategories" asp-action="DeleteCategory" method="post" class="d-none">
    <input type="hidden" id="deleteCategoryId" name="id" />
</form>

@section Scripts {
    <script>
        var myModal = new bootstrap.Modal(document.getElementById('categoryModal'));

        function openCreateModal(parentId) {
            $('#categoryForm').attr('action', '/ItemsCategories/Create');
            $('#modalTitle').text('إضافة تصنيف جديد');
            $('#categoryId').val('0');
            $('#parentId').val(parentId || '');
            $('#categoryName').val('');
            
            if (parentId) {
                const parentName = '@(allCategories.FirstOrDefault(c => c.Id == -1)?.Name)'.replace('-1', parentId);
                 $('#parentInfo').text(`تابع للتصنيف: ${parentName}`).removeClass('d-none');
            } else {
                 $('#parentInfo').addClass('d-none');
            }
            myModal.show();
        }

        function openEditModal(id, name) {
            $('#categoryForm').attr('action', '/ItemsCategories/Edit');
            $('#modalTitle').text('تعديل التصنيف');
            $('#categoryId').val(id);
            $('#parentId').val(''); // Parent change is not supported from UI for simplicity
            $('#categoryName').val(name);
            $('#parentInfo').addClass('d-none');
            myModal.show();
        }

        function confirmDelete(id) {
            if (confirm('هل أنت متأكد من حذف هذا التصنيف؟')) {
                $('#deleteCategoryId').val(id);
                $('#deleteForm').submit();
            }
        }

        setTimeout(() => $('.alert').fadeOut(), 5000);
    </script>
} 