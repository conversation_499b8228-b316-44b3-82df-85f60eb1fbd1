﻿Imports System.Data.SqlClient

Public Class frmGroupPermissions

    Private Sub frmGroupPermissions_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadGroups()

        ' Initialize TreeView
        InitializeTreeView()

        ' Check for unmaintained buttons after loading groups
        'CheckForUnmaintainedButtonsWithHierarchy()
        'CleanupOrphanedForms()
    End Sub

    Private Sub InitializeTreeView()
        ' Set TreeView properties for better UX
        With tvPermissions
            .CheckBoxes = True
            .ShowLines = True
            .ShowPlusMinus = True
            .ShowRootLines = True
            .HideSelection = False
            .FullRowSelect = True
            .Sorted = False
            .ShowNodeToolTips = True
        End With

    End Sub

    Private Sub LoadGroups()
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Using cmd As New SqlCommand("SELECT GroupID, GroupName FROM tblGroupsAuth ORDER BY GroupID", Con)
            Using da As New SqlDataAdapter(cmd)
                Dim dt As New DataTable()
                da.Fill(dt)
                dgvGroups.DataSource = dt

                ' Set the column names
                dgvGroups.Columns("GroupID").HeaderText = "م"
                dgvGroups.Columns("GroupName").HeaderText = "اسم المجموعة"
            End Using
        End Using
    End Sub

    Private Sub dgvGroups_SelectionChanged(sender As Object, e As EventArgs) Handles dgvGroups.SelectionChanged
        If dgvGroups.SelectedRows.Count > 0 Then
            Dim groupId As Integer = Convert.ToInt32(dgvGroups.SelectedRows(0).Cells(0).Value)
            LoadPermissionTreeView(groupId)
        End If
    End Sub

    Private Sub LoadPermissionTreeView(groupID As Integer)
        Try
            tvPermissions.Nodes.Clear()

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            ' Get all forms with hierarchy - SORTED BY SortOrder
            Dim formsData As New DataTable()
            Dim query As String = "SELECT FormID, FormName, DisplayText, ParentFormID, IsContainer, SortOrder " &
                                "FROM tblForms " &
                                "ORDER BY " &
                                "CASE WHEN ParentFormID IS NULL THEN SortOrder ELSE 999 END, " &
                                "SortOrder, FormName"

            Using cmd As New SqlCommand(query, Con)
                Using da As New SqlDataAdapter(cmd)
                    da.Fill(formsData)
                End Using
            End Using

            ' Get group permissions
            Dim groupPermissions As New HashSet(Of Integer)
            Dim permQuery As String = "SELECT FormID FROM tblGroupFormPermissions WHERE GroupID = @GroupID"
            Using cmd As New SqlCommand(permQuery, Con)
                cmd.Parameters.AddWithValue("@GroupID", groupID)
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    While reader.Read()
                        groupPermissions.Add(Convert.ToInt32(reader("FormID")))
                    End While
                End Using
            End Using

            ' Build tree structure
            Dim nodeMap As New Dictionary(Of Integer, TreeNode)
            Dim rootNodes As New List(Of TreeNode)

            ' First pass: create all nodes
            For Each row As DataRow In formsData.Rows
                Dim formID As Integer = Convert.ToInt32(row("FormID"))
                Dim formName As String = row("FormName").ToString()
                Dim displayText As String = row("DisplayText").ToString()
                Dim isContainer As Boolean = Convert.ToBoolean(row("IsContainer"))
                Dim sortOrder As Integer = Convert.ToInt32(row("SortOrder"))

                ' Skip frmGroupPermissions buttons, excluded buttons, and maintenance-excluded buttons
                If IsExcludedButton(formName) OrElse IsGroupPermissionButton(formName) OrElse IsMaintenanceExcludedButton(formName) Then
                    Continue For
                End If

                Dim node As New TreeNode(displayText)
                node.Tag = New FormNodeInfo With {
                    .FormID = formID,
                    .FormName = formName,
                    .IsContainer = isContainer,
                    .SortOrder = sortOrder
                }
                ' Enable tooltip for long text
                If TextRenderer.MeasureText(displayText, tvPermissions.Font).Width > tvPermissions.Width - 40 Then
                    node.ToolTipText = displayText
                End If

                ' Set node appearance
                If isContainer Then
                    'node.NodeFont = New Font(tvPermissions.Font, FontStyle.Bold)
                    node.ForeColor = Color.DarkBlue
                End If

                ' Set checked state
                node.Checked = groupPermissions.Contains(formID)

                nodeMap(formID) = node
            Next

            ' Second pass: build hierarchy with proper sorting
            For Each row As DataRow In formsData.Rows
                Dim formID As Integer = Convert.ToInt32(row("FormID"))
                Dim parentFormID As Object = row("ParentFormID")
                Dim formName As String = row("FormName").ToString()

                ' Skip excluded buttons
                If IsExcludedButton(formName) OrElse IsGroupPermissionButton(formName) OrElse IsMaintenanceExcludedButton(formName) Then
                    Continue For
                End If

                If nodeMap.ContainsKey(formID) Then
                    Dim currentNode As TreeNode = nodeMap(formID)

                    If IsDBNull(parentFormID) Then
                        ' Root level node
                        rootNodes.Add(currentNode)
                    Else
                        ' Child node
                        Dim parentID As Integer = Convert.ToInt32(parentFormID)
                        If nodeMap.ContainsKey(parentID) Then
                            nodeMap(parentID).Nodes.Add(currentNode)
                        End If
                    End If
                End If
            Next

            ' Sort root nodes by SortOrder
            rootNodes.Sort(Function(x, y)
                               Dim xInfo As FormNodeInfo = DirectCast(x.Tag, FormNodeInfo)
                               Dim yInfo As FormNodeInfo = DirectCast(y.Tag, FormNodeInfo)
                               Return xInfo.SortOrder.CompareTo(yInfo.SortOrder)
                           End Function)

            ' Add sorted root nodes to TreeView
            For Each rootNode As TreeNode In rootNodes
                tvPermissions.Nodes.Add(rootNode)
                ' Sort child nodes recursively
                SortChildNodes(rootNode)
            Next

            ' Collapse all nodes by default
            tvPermissions.CollapseAll()

        Catch ex As Exception
            MessageBox.Show($"Error loading permission tree: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Recursively sort child nodes by SortOrder
    Private Sub SortChildNodes(parentNode As TreeNode)
        If parentNode.Nodes.Count <= 1 Then Return

        ' Convert to list for sorting
        Dim childNodes As New List(Of TreeNode)
        For Each node As TreeNode In parentNode.Nodes
            childNodes.Add(node)
        Next

        ' Sort by SortOrder
        childNodes.Sort(Function(x, y)
                            Dim xInfo As FormNodeInfo = DirectCast(x.Tag, FormNodeInfo)
                            Dim yInfo As FormNodeInfo = DirectCast(y.Tag, FormNodeInfo)
                            Return xInfo.SortOrder.CompareTo(yInfo.SortOrder)
                        End Function)

        ' Clear and re-add in sorted order
        parentNode.Nodes.Clear()
        For Each node As TreeNode In childNodes
            parentNode.Nodes.Add(node)
            ' Recursively sort children
            SortChildNodes(node)
        Next
    End Sub

    Private Sub tvPermissions_AfterCheck(sender As Object, e As TreeViewEventArgs) Handles tvPermissions.AfterCheck
        ' Prevent recursive calls
        If tvPermissions.Tag IsNot Nothing AndAlso CBool(tvPermissions.Tag) Then
            Return
        End If

        tvPermissions.Tag = True ' Set flag to prevent recursion

        Try
            ' Auto-check/uncheck children
            CheckUncheckChildren(e.Node, e.Node.Checked)

            ' Auto-check parents if any child is checked
            If e.Node.Checked Then
                CheckParents(e.Node.Parent)
            Else
                ' Uncheck parents if no siblings are checked
                UncheckParentsIfNoSiblingsChecked(e.Node.Parent)
            End If

        Finally
            tvPermissions.Tag = Nothing ' Clear flag
        End Try
    End Sub

    Private Sub CheckUncheckChildren(node As TreeNode, isChecked As Boolean)
        For Each childNode As TreeNode In node.Nodes
            childNode.Checked = isChecked
            CheckUncheckChildren(childNode, isChecked) ' Recursive for nested children
        Next
    End Sub

    Private Sub CheckParents(parentNode As TreeNode)
        If parentNode IsNot Nothing Then
            parentNode.Checked = True
            CheckParents(parentNode.Parent) ' Recursive up the tree
        End If
    End Sub

    Private Sub UncheckParentsIfNoSiblingsChecked(parentNode As TreeNode)
        If parentNode IsNot Nothing Then
            ' Check if any sibling is checked
            Dim anyChildChecked As Boolean = False
            For Each siblingNode As TreeNode In parentNode.Nodes
                If siblingNode.Checked Then
                    anyChildChecked = True
                    Exit For
                End If
            Next

            If Not anyChildChecked Then
                parentNode.Checked = False
                UncheckParentsIfNoSiblingsChecked(parentNode.Parent)
            End If
        End If
    End Sub

    ' Double-click event for editing form names/descriptions
    Private Sub tvPermissions_NodeMouseDoubleClick(sender As Object, e As TreeNodeMouseClickEventArgs) Handles tvPermissions.NodeMouseDoubleClick
        If e.Node IsNot Nothing AndAlso e.Node.Tag IsNot Nothing Then
            EditFormDescription(e.Node)
        End If
    End Sub

    ' Edit form description
    Private Sub EditFormDescription(node As TreeNode)
        Try
            Dim nodeInfo As FormNodeInfo = DirectCast(node.Tag, FormNodeInfo)
            Dim currentDescription As String = node.Text
            Dim formID As Integer = nodeInfo.FormID
            Dim formName As String = nodeInfo.FormName

            Dim newDescription As String = InputBox($"تعديل وصف النموذج:{vbNewLine}الاسم: {formName}",
                                                  "تعديل الوصف",
                                                  currentDescription)

            If Not String.IsNullOrWhiteSpace(newDescription) AndAlso newDescription <> currentDescription Then
                ' Update in database
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If

                Dim updateQuery As String = "UPDATE tblForms SET DisplayText = @DisplayText WHERE FormID = @FormID"
                Using cmd As New SqlCommand(updateQuery, Con)
                    cmd.Parameters.AddWithValue("@DisplayText", newDescription)
                    cmd.Parameters.AddWithValue("@FormID", formID)
                    cmd.ExecuteNonQuery()
                End Using

                ' Update the node text
                node.Text = newDescription

                MessageBox.Show("تم تحديث الوصف بنجاح", "تم التحديث", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            MessageBox.Show($"خطأ في تعديل الوصف: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If dgvGroups.SelectedRows.Count > 0 Then
            Dim groupId As Integer = Convert.ToInt32(dgvGroups.SelectedRows(0).Cells(0).Value)
            SavePermissionsFromTreeView(groupId)
        End If
    End Sub

    Private Sub SavePermissionsFromTreeView(groupID As Integer)
        If groupID = 0 Then Return

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim transaction As SqlTransaction = Con.BeginTransaction()

        Try
            ' Delete existing permissions for this group
            Dim deleteQuery As String = "DELETE FROM tblGroupFormPermissions WHERE GroupID = @GroupID"
            Using deleteCmd As New SqlCommand(deleteQuery, Con, transaction)
                deleteCmd.Parameters.AddWithValue("@GroupID", groupID)
                deleteCmd.ExecuteNonQuery()
            End Using

            ' Collect all checked nodes
            Dim checkedFormIDs As New List(Of Integer)
            CollectCheckedNodes(tvPermissions.Nodes, checkedFormIDs)

            ' Insert new permissions
            For Each formID As Integer In checkedFormIDs
                Dim insertQuery As String = "INSERT INTO tblGroupFormPermissions (GroupID, FormID) VALUES (@GroupID, @FormID)"
                Using insertCmd As New SqlCommand(insertQuery, Con, transaction)
                    insertCmd.Parameters.AddWithValue("@GroupID", groupID)
                    insertCmd.Parameters.AddWithValue("@FormID", formID)
                    insertCmd.ExecuteNonQuery()
                End Using
            Next

            ' Commit the transaction
            transaction.Commit()

            MessageBox.Show("تم حفظ الصلاحيات بنجاح", "نجح الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            ' Rollback the transaction on error
            transaction.Rollback()
            MessageBox.Show($"خطأ في حفظ الصلاحيات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub CollectCheckedNodes(nodes As TreeNodeCollection, checkedFormIDs As List(Of Integer))
        For Each node As TreeNode In nodes
            If node.Checked AndAlso node.Tag IsNot Nothing Then
                Dim nodeInfo As FormNodeInfo = DirectCast(node.Tag, FormNodeInfo)
                checkedFormIDs.Add(nodeInfo.FormID)
            End If

            ' Recursively collect from child nodes
            If node.Nodes.Count > 0 Then
                CollectCheckedNodes(node.Nodes, checkedFormIDs)
            End If
        Next
    End Sub

    Private Sub btnCheckAll_Click(sender As Object, e As EventArgs) Handles btnCheckAll.Click
        CheckUncheckAllNodes(tvPermissions.Nodes, True)
    End Sub

    Private Sub btnUncheckAll_Click(sender As Object, e As EventArgs) Handles btnUncheckAll.Click
        CheckUncheckAllNodes(tvPermissions.Nodes, False)
    End Sub

    Private Sub CheckUncheckAllNodes(nodes As TreeNodeCollection, isChecked As Boolean)
        tvPermissions.Tag = True ' Prevent recursion

        Try
            For Each node As TreeNode In nodes
                node.Checked = isChecked
                If node.Nodes.Count > 0 Then
                    CheckUncheckAllNodes(node.Nodes, isChecked)
                End If
            Next
        Finally
            tvPermissions.Tag = Nothing
        End Try
    End Sub

    ' Expand/Collapse All buttons
    Private Sub btnExpandAll_Click(sender As Object, e As EventArgs) ' Handles btnExpandAll.Click
        tvPermissions.ExpandAll()
    End Sub

    Private Sub btnCollapseAll_Click(sender As Object, e As EventArgs) ' Handles btnCollapseAll.Click
        tvPermissions.CollapseAll()
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If dgvGroups.SelectedRows.Count > 0 Then
            Dim groupId As Integer = Convert.ToInt32(dgvGroups.SelectedRows(0).Cells(0).Value)
            If Not IsGroupLinkedWithUsers(groupId) Then
                DeleteGroup(groupId)
                LoadGroups() ' Refresh the groups list
            Else
                MsgBox("هذه المجموعة نشطة حاليًا يرجى فك ارتباط المستخدمين بها واعادة المحاولة", MsgBoxStyle.Critical, "نظام السلطان")
            End If
        End If
    End Sub

    Private Function IsGroupLinkedWithUsers(groupID As Integer) As Boolean
        Dim query As String = "SELECT COUNT(*) FROM tblUsers WHERE GroupID = @GroupID"

        Using cmd As New SqlCommand(query, Con)
            cmd.Parameters.AddWithValue("@GroupID", groupID)
            Dim result As Integer = Convert.ToInt32(cmd.ExecuteScalar())
            Return result > 0
        End Using
    End Function

    Private Sub DeleteGroup(groupID As Integer)
        Try
            Dim query2 As String = "DELETE FROM tblGroupsAuth WHERE GroupId = @GroupId"
            Dim query As String = "DELETE FROM tblGroupFormPermissions WHERE GroupID = @GroupID"

            Using cmd As New SqlCommand(query, Con)
                cmd.Parameters.AddWithValue("@GroupID", groupID)
                cmd.ExecuteNonQuery()
            End Using

            Using cmd2 As New SqlCommand(query2, Con)
                cmd2.Parameters.AddWithValue("@GroupID", groupID)
                cmd2.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            MessageBox.Show("هناك خطأ: " & ex.Message, "نظام السلطان")
        End Try

        MsgBox("تم الحذف بنجاح", MsgBoxStyle.Information, "نظام السلطان")
    End Sub

    Private Sub btnCreate_Click(sender As Object, e As EventArgs) Handles btnCreate.Click
        Dim groupName As String = InputBox("يرجى ادخال اسم المجموعة الجديدة:", "مجموعة صلاحيات جديدة")

        If Not String.IsNullOrWhiteSpace(groupName) Then
            CreateNewGroup(groupName)
            LoadGroups() ' Refresh the groups list to show the new group
        Else
            MsgBox("لم يتم ادخال الاسم، يرجى ادخال اسم المجموعة الجديدة", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

    Private Sub CreateNewGroup(groupName As String)
        If String.IsNullOrWhiteSpace(groupName) Then
            MsgBox("لم يتم ادخال اسم للمجموعة الجديدة", MsgBoxStyle.Critical, "نظام السلطان")
            Return
        End If

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim insertQuery As String = "INSERT INTO tblGroupsAuth (GroupName) VALUES (@GroupName)"

        Using cmd As New SqlCommand(insertQuery, Con)
            cmd.Parameters.AddWithValue("@GroupName", groupName)
            cmd.ExecuteNonQuery()
        End Using
    End Sub

    ' ========== ENHANCED MAINTENANCE FUNCTIONS ==========

    ' Enhanced maintenance functions for hierarchy support
    Private Sub CheckForUnmaintainedButtonsWithHierarchy()
        Try
            Dim mainForm As frmMain = GetMainFormInstance()
            If mainForm Is Nothing Then Return

            ' Get all clickable controls
            Dim allControls As List(Of Object) = GetAllClickableControlsFromForm(mainForm)
            Dim existingForms As List(Of String) = GetExistingFormsFromDatabase()

            ' Detect hierarchy and unmaintained controls
            Dim hierarchyData As HierarchyDetectionResult = DetectButtonHierarchy(allControls, existingForms)

            ' Process new buttons
            If hierarchyData.NewButtons.Count > 0 Then
                ProcessNewButtons(hierarchyData.NewButtons)
            End If

            ' Process new parent containers
            If hierarchyData.NewParents.Count > 0 Then
                ProcessNewParentContainers(hierarchyData.NewParents)
            End If

            ' Establish hierarchy relationships
            EstablishHierarchyRelationships()

            MessageBox.Show("تم إكمال فحص الأزرار وإنشاء التسلسل الهرمي", "انتهى الفحص", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"خطأ في فحص الأزرار: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Detect button hierarchy structure
    Private Function DetectButtonHierarchy(allControls As List(Of Object), existingForms As List(Of String)) As HierarchyDetectionResult
        Dim result As New HierarchyDetectionResult()

        ' Updated main parent prefixes based on your specification
        Dim parentPrefixes() As String = {
            "btnPurchase", "btnStock", "btnSales", "btnSettings",
            "btnCashTrx", "btnSearch", "btnMaster", "btnReports"
        }

        ' Collect all control names (excluding maintenance-excluded buttons)
        Dim allControlNames As New List(Of String)
        For Each ctrl As Object In allControls
            Dim ctrlName As String = GetControlNameFromObject(ctrl)
            If Not String.IsNullOrWhiteSpace(ctrlName) AndAlso Not IsSystemControl(ctrlName) AndAlso Not IsMaintenanceExcludedButton(ctrlName) Then
                allControlNames.Add(ctrlName)
            End If
        Next

        ' Detect parent containers (including sub-parents)
        For Each prefix As String In parentPrefixes
            ' Check if this parent has children
            Dim hasChildren As Boolean = allControlNames.Any(Function(name) name.StartsWith(prefix, StringComparison.OrdinalIgnoreCase) AndAlso name.Length > prefix.Length)

            If hasChildren Then
                                                                     ' Check if parent exists in database
                                                                     If Not existingForms.Contains(prefix) Then
                    result.NewParents.Add(New ParentContainerInfo With {
                        .ButtonName = prefix,
                        .DisplayText = GetParentDisplayText(prefix),
                        .Children = allControlNames.Where(Function(name) name.StartsWith(prefix, StringComparison.OrdinalIgnoreCase) AndAlso name.Length > prefix.Length).ToList()
})
                End If
                                                                 End If
        Next

        ' Detect sub-parent containers (buttons that have children but are not main parents)
        For Each ctrlName As String In allControlNames
            ' Skip if it's already a main parent
            If parentPrefixes.Contains(ctrlName) Then Continue For

            ' Check if this control has children (making it a sub-parent)
            Dim hasChildren As Boolean = allControlNames.Any(Function(name) name.StartsWith(ctrlName, StringComparison.OrdinalIgnoreCase) AndAlso name.Length > ctrlName.Length)

            If hasChildren AndAlso Not existingForms.Contains(ctrlName) Then
                result.NewParents.Add(New ParentContainerInfo With {
                    .ButtonName = ctrlName,
                    .DisplayText = GetSubParentDisplayText(ctrlName),
                    .Children = allControlNames.Where(Function(name) name.StartsWith(ctrlName, StringComparison.OrdinalIgnoreCase) AndAlso name.Length > ctrlName.Length).ToList()
})
            End If
        Next

        ' Detect new child buttons
        For Each ctrlName As String In allControlNames
            If Not existingForms.Contains(ctrlName) Then
                result.NewButtons.Add(New ButtonInfo With {
                    .ButtonName = ctrlName,
                    .ParentPrefix = GetParentPrefix(ctrlName, allControlNames)
                })
            End If
        Next

        Return result
    End Function

    ' Process new buttons found
    Private Sub ProcessNewButtons(newButtons As List(Of ButtonInfo))
        For Each buttonInfo As ButtonInfo In newButtons
            Dim result As DialogResult = MessageBox.Show(
                $"الزر '{buttonInfo.ButtonName}' غير موجود في قاعدة البيانات.{vbNewLine}هل تريد إضافته؟",
                "زر جديد",
                MessageBoxButtons.YesNoCancel,
                MessageBoxIcon.Question)

            Select Case result
                Case DialogResult.Yes
                    Dim description As String = InputBox($"أدخل وصف الزر '{buttonInfo.ButtonName}':", "وصف الزر", buttonInfo.ButtonName)
                    If Not String.IsNullOrWhiteSpace(description) Then
                        AddButtonToDatabase(buttonInfo.ButtonName, description, buttonInfo.ParentPrefix)
                    End If

                Case DialogResult.No
                    Continue For

                Case DialogResult.Cancel
                    Return
            End Select
        Next
    End Sub

    ' Process new parent containers
    Private Sub ProcessNewParentContainers(newParents As List(Of ParentContainerInfo))
        For Each parentInfo As ParentContainerInfo In newParents
            Dim childrenList As String = String.Join(", ", parentInfo.Children)
            Dim result As DialogResult = MessageBox.Show(
                $"تم العثور على حاوي رئيسي جديد: '{parentInfo.ButtonName}'{vbNewLine}" &
                $"الأزرار التابعة: {childrenList}{vbNewLine}{vbNewLine}هل تريد إضافته؟",
                "حاوي رئيسي جديد",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question)

            If result = DialogResult.Yes Then
                AddParentContainerToDatabase(parentInfo.ButtonName, parentInfo.DisplayText)
            End If
        Next
    End Sub

    ' Add button to database with hierarchy support
    Private Sub AddButtonToDatabase(buttonName As String, displayText As String, parentPrefix As String)
        Try
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            ' Get parent FormID if exists
            Dim parentFormID As Object = DBNull.Value
            If Not String.IsNullOrWhiteSpace(parentPrefix) Then
                Dim query As String = "SELECT FormID FROM tblForms WHERE FormName = @ParentName"
                Using cmd As New SqlCommand(query, Con)
                    cmd.Parameters.AddWithValue("@ParentName", parentPrefix)
                    Dim result = cmd.ExecuteScalar()
                    If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                        parentFormID = result
                    End If
                End Using
            End If

            ' Insert the button
            Dim insertQuery As String = "INSERT INTO tblForms (FormName, DisplayText, ParentFormID, IsContainer, SortOrder) " &
                                      "VALUES (@FormName, @DisplayText, @ParentFormID, 0, 10)"

            Using cmd As New SqlCommand(insertQuery, Con)
                cmd.Parameters.AddWithValue("@FormName", buttonName)
                cmd.Parameters.AddWithValue("@DisplayText", displayText)
                cmd.Parameters.AddWithValue("@ParentFormID", parentFormID)
                cmd.ExecuteNonQuery()
            End Using

        Catch ex As Exception
            MessageBox.Show($"خطأ في إضافة الزر: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Add parent container to database
    Private Sub AddParentContainerToDatabase(buttonName As String, displayText As String)
        Try
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            Dim insertQuery As String = "INSERT INTO tblForms (FormName, DisplayText, ParentFormID, IsContainer, SortOrder) " &
                                      "VALUES (@FormName, @DisplayText, NULL, 1, 10)"

            Using cmd As New SqlCommand(insertQuery, Con)
                cmd.Parameters.AddWithValue("@FormName", buttonName)
                cmd.Parameters.AddWithValue("@DisplayText", displayText)
                cmd.ExecuteNonQuery()
            End Using

        Catch ex As Exception
            MessageBox.Show($"خطأ في إضافة الحاوي الرئيسي: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Establish hierarchy relationships for existing buttons
    Private Sub EstablishHierarchyRelationships()
        Try
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            ' Update existing buttons to link to their parents
            Dim mainParentPrefixes() As String = {
                "btnPurchase", "btnStock", "btnSales", "btnSettings",
                "btnCashTrx", "btnSearch", "btnMaster", "btnReports"
            }

            ' First pass: Link direct children to main parents
            For Each prefix As String In mainParentPrefixes
                ' Update direct children of this main parent
                Dim updateQuery As String = "UPDATE tblForms SET ParentFormID = " &
                                          "(SELECT FormID FROM tblForms WHERE FormName = @ParentName), " &
                                          "IsContainer = 0 " &
                                          "WHERE FormName LIKE @ChildPattern AND FormName != @ParentName " &
                                          "AND ParentFormID IS NULL"

                Using cmd As New SqlCommand(updateQuery, Con)
                    cmd.Parameters.AddWithValue("@ParentName", prefix)
                    cmd.Parameters.AddWithValue("@ChildPattern", prefix + "%")
                    cmd.ExecuteNonQuery()
                End Using
            Next

            ' Second pass: Handle sub-parent relationships
            ' Find buttons that have children but are not main parents
            Dim subParentQuery As String = "SELECT DISTINCT f1.FormName, f1.FormID " &
                                         "FROM tblForms f1 " &
                                         "WHERE EXISTS (SELECT 1 FROM tblForms f2 WHERE f2.FormName LIKE f1.FormName + '%' AND f2.FormName != f1.FormName) " &
                                         "AND f1.FormName NOT IN ('" & String.Join("','", mainParentPrefixes) & "')"

            Dim subParents As New List(Of String)
            Using cmd As New SqlCommand(subParentQuery, Con)
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    While reader.Read()
                        subParents.Add(reader("FormName").ToString())
                    End While
                End Using
            End Using

            ' Update sub-parent relationships
            For Each subParent As String In subParents
                ' Mark sub-parent as container
                Dim markContainerQuery As String = "UPDATE tblForms SET IsContainer = 1 WHERE FormName = @SubParent"
                Using cmd As New SqlCommand(markContainerQuery, Con)
                    cmd.Parameters.AddWithValue("@SubParent", subParent)
                    cmd.ExecuteNonQuery()
                End Using

                ' Link children to sub-parent
                Dim linkChildrenQuery As String = "UPDATE tblForms SET ParentFormID = " &
                                                "(SELECT FormID FROM tblForms WHERE FormName = @SubParent) " &
                                                "WHERE FormName LIKE @ChildPattern AND FormName != @SubParent " &
                                                "AND ParentFormID IS NULL"

                Using cmd As New SqlCommand(linkChildrenQuery, Con)
                    cmd.Parameters.AddWithValue("@SubParent", subParent)
                    cmd.Parameters.AddWithValue("@ChildPattern", subParent + "%")
                    cmd.ExecuteNonQuery()
                End Using
            Next

        Catch ex As Exception
            MessageBox.Show($"خطأ في إنشاء العلاقات الهرمية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Get parent prefix for a button name - ENHANCED for sub-parents
    Private Function GetParentPrefix(buttonName As String, allControlNames As List(Of String)) As String
        ' Main parent prefixes
        Dim mainParentPrefixes() As String = {
            "btnPurchase", "btnStock", "btnSales", "btnSettings",
            "btnCashTrx", "btnSearch", "btnMaster", "btnReports"
        }

        ' First check for main parents
        For Each prefix As String In mainParentPrefixes
            If buttonName.StartsWith(prefix, StringComparison.OrdinalIgnoreCase) AndAlso buttonName.Length > prefix.Length Then
                Return prefix
            End If
        Next

        ' Then check for sub-parents (buttons that have children)
        For Each potentialParent As String In allControlNames
            ' Skip if it's the same button or a main parent
            If potentialParent = buttonName OrElse mainParentPrefixes.Contains(potentialParent) Then Continue For

            ' Check if buttonName starts with this potential parent
            If buttonName.StartsWith(potentialParent, StringComparison.OrdinalIgnoreCase) AndAlso buttonName.Length > potentialParent.Length Then
                ' Verify this potential parent actually has children
                Dim hasChildren As Boolean = allControlNames.Any(Function(name) name.StartsWith(potentialParent, StringComparison.OrdinalIgnoreCase) AndAlso name.Length > potentialParent.Length)

                If hasChildren Then
                                                                         Return potentialParent
                                                                     End If
            End If
        Next

        Return ""
    End Function

    ' Get appropriate display text for main parent buttons
    Private Function GetParentDisplayText(parentName As String) As String
        Select Case parentName.ToLower()
            Case "btnpurchase" : Return "المشتريات"
            Case "btnstock" : Return "المخزون"
            Case "btnsales" : Return "المبيعات"
            Case "btnsettings" : Return "الإعدادات"
            Case "btncashtrx" : Return "المعاملات النقدية"
            Case "btnsearch" : Return "البحث"
            Case "btnmaster" : Return "البيانات الأساسية"
            Case "btnreports" : Return "التقارير"
            Case Else : Return parentName
        End Select
    End Function

    ' Get appropriate display text for sub-parent buttons
    Private Function GetSubParentDisplayText(subParentName As String) As String
        ' Try to create meaningful display text for sub-parents
        ' You can enhance this based on your specific sub-parent naming patterns

        ' Remove common prefixes and make it more readable
        Dim displayText As String = subParentName

        If displayText.StartsWith("btn", StringComparison.OrdinalIgnoreCase) Then
            displayText = displayText.Substring(3)
        End If

        ' Add spaces before capital letters (camelCase to readable)
        displayText = System.Text.RegularExpressions.Regex.Replace(displayText, "(?<!^)([A-Z])", " $1")

        ' You can add specific mappings here if needed
        Select Case subParentName.ToLower()
            ' Add specific sub-parent mappings as needed
            ' Case "btnpurchaseinvoices" : Return "فواتير المشتريات"
            ' Case "btnsalesreports" : Return "تقارير المبيعات"
            Case Else : Return displayText
        End Select
    End Function

    ' Clean up orphaned forms that don't exist in frmMain
    Private Sub CleanupOrphanedForms()
        Try
            Dim mainForm As frmMain = GetMainFormInstance()
            If mainForm Is Nothing Then Return

            ' Get all actual clickable controls
            Dim actualControls As List(Of Object) = GetAllClickableControlsFromForm(mainForm)
            Dim actualControlNames As New HashSet(Of String)(StringComparer.OrdinalIgnoreCase)

            For Each ctrl As Object In actualControls
                Dim ctrlName As String = GetControlNameFromObject(ctrl)
                If Not String.IsNullOrWhiteSpace(ctrlName) Then
                    actualControlNames.Add(ctrlName)
                End If
            Next

            ' Get all forms from database
            Dim orphanedForms As New List(Of OrphanedFormInfo)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            Using cmd As New SqlCommand("SELECT FormID, FormName, DisplayText FROM tblForms", Con)
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    While reader.Read()
                        Dim formName As String = reader("FormName").ToString()

                        ' Check if this form exists in actual controls
                        ' Also check if it's a valid parent button that should exist
                        ' Skip maintenance-excluded buttons from cleanup
                        If Not actualControlNames.Contains(formName) AndAlso
                           Not IsValidParentThatShouldExist(formName, actualControlNames) AndAlso
                           Not IsMaintenanceExcludedButton(formName) Then
                            orphanedForms.Add(New OrphanedFormInfo With {
                                .FormID = Convert.ToInt32(reader("FormID")),
                                .FormName = formName,
                                .DisplayText = reader("DisplayText").ToString()
                            })
                        End If
                    End While
                End Using
            End Using

            If orphanedForms.Count = 0 Then
                MessageBox.Show("لا توجد أزرار خاطئة للحذف", "تنظيف قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' Show orphaned forms to user
            Dim orphanedList As String = ""
            For Each orphan In orphanedForms
                orphanedList += $"- {orphan.FormName} ({orphan.DisplayText})" & vbNewLine
            Next

            Dim result As DialogResult = MessageBox.Show($"تم العثور على {orphanedForms.Count} زر خاطئ في قاعدة البيانات لا يوجد في النموذج الرئيسي:{vbNewLine}{vbNewLine}{orphanedList}{vbNewLine}هل تريد حذفها من قاعدة البيانات؟",
                                                       "تنظيف الأزرار الخاطئة",
                                                       MessageBoxButtons.YesNo,
                                                       MessageBoxIcon.Question)

            If result = DialogResult.Yes Then
                DeleteOrphanedForms(orphanedForms)

                ' Refresh the current group display
                If dgvGroups.SelectedRows.Count > 0 Then
                    Dim groupId As Integer = Convert.ToInt32(dgvGroups.SelectedRows(0).Cells(0).Value)
                    LoadPermissionTreeView(groupId)
                End If

                MessageBox.Show($"تم حذف {orphanedForms.Count} زر خاطئ بنجاح", "تم التنظيف", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            MessageBox.Show($"خطأ أثناء تنظيف الأزرار الخاطئة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Check if a form name is a valid parent that should exist based on its children
    Private Function IsValidParentThatShouldExist(formName As String, actualControlNames As HashSet(Of String)) As Boolean
        ' Check if any actual control starts with this form name (making it a valid parent)
        For Each actualName As String In actualControlNames
            If actualName.StartsWith(formName, StringComparison.OrdinalIgnoreCase) AndAlso actualName.Length > formName.Length Then
                Return True
            End If
        Next
        Return False
    End Function

    ' Delete orphaned forms from both tables
    Private Sub DeleteOrphanedForms(orphanedForms As List(Of OrphanedFormInfo))
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim transaction As SqlTransaction = Con.BeginTransaction()

        Try
            For Each orphan In orphanedForms
                ' Delete from permissions table first (foreign key constraint)
                Dim deletePermissionsQuery As String = "DELETE FROM tblGroupFormPermissions WHERE FormID = @FormID"
                Using deletePermCmd As New SqlCommand(deletePermissionsQuery, Con, transaction)
                    deletePermCmd.Parameters.AddWithValue("@FormID", orphan.FormID)
                    deletePermCmd.ExecuteNonQuery()
                End Using

                ' Delete from forms table
                Dim deleteFormQuery As String = "DELETE FROM tblForms WHERE FormID = @FormID"
                Using deleteFormCmd As New SqlCommand(deleteFormQuery, Con, transaction)
                    deleteFormCmd.Parameters.AddWithValue("@FormID", orphan.FormID)
                    deleteFormCmd.ExecuteNonQuery()
                End Using
            Next

            ' Commit the transaction
            transaction.Commit()

        Catch ex As Exception
            ' Rollback on error
            transaction.Rollback()
            Throw ex
        End Try
    End Sub

    ' ========== HELPER FUNCTIONS ==========

    Private Function GetMainFormInstance() As frmMain
        ' Find the main form instance
        For Each form As Form In Application.OpenForms
            If TypeOf form Is frmMain Then
                Return DirectCast(form, frmMain)
            End If
        Next

        ' If main form is not open, try to create a temporary instance
        Try
            Dim mainForm As New frmMain()
            mainForm.WindowState = FormWindowState.Minimized
            mainForm.ShowInTaskbar = False
            mainForm.Visible = False
            Return mainForm
        Catch ex As Exception
            MessageBox.Show("تعذر الوصول إلى النموذج الرئيسي للتحقق من الأزرار", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return Nothing
        End Try
    End Function

    ' Get all clickable controls from a form (IMPROVED VERSION)
    Private Function GetAllClickableControlsFromForm(targetForm As Form) As List(Of Object)
        Dim controls As New List(Of Object)

        Try
            ' Recursively get all controls
            GetControlsRecursively(targetForm, controls)
        Catch ex As Exception
            MessageBox.Show($"خطأ في الحصول على العناصر: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return controls
    End Function

    ' Recursive function to get all controls
    Private Sub GetControlsRecursively(container As Control, ByRef controls As List(Of Object))
        For Each ctrl As Control In container.Controls
            ' Add regular buttons
            If TypeOf ctrl Is Button Then
                controls.Add(ctrl)
            End If

            ' Add ToolStrip items
            If TypeOf ctrl Is ToolStrip Then
                Dim toolstrip As ToolStrip = DirectCast(ctrl, ToolStrip)
                For Each item As ToolStripItem In toolstrip.Items
                    If TypeOf item Is ToolStripButton OrElse
                       TypeOf item Is ToolStripMenuItem OrElse
                       TypeOf item Is ToolStripDropDownButton OrElse
                       TypeOf item Is ToolStripSplitButton Then
                        controls.Add(item)
                    End If
                Next
            End If

            ' Add MenuStrip items
            If TypeOf ctrl Is MenuStrip Then
                Dim menustrip As MenuStrip = DirectCast(ctrl, MenuStrip)
                For Each item As ToolStripItem In menustrip.Items
                    controls.Add(item)
                    If TypeOf item Is ToolStripMenuItem Then
                        AddSubMenuItemsToList(DirectCast(item, ToolStripMenuItem), controls)
                    End If
                Next
            End If

            ' Add StatusStrip items
            If TypeOf ctrl Is StatusStrip Then
                Dim statusstrip As StatusStrip = DirectCast(ctrl, StatusStrip)
                For Each item As ToolStripItem In statusstrip.Items
                    If TypeOf item Is ToolStripButton OrElse TypeOf item Is ToolStripDropDownButton Then
                        controls.Add(item)
                    End If
                Next
            End If

            ' Add ContextMenuStrip items
            If TypeOf ctrl Is ContextMenuStrip Then
                Dim contextMenu As ContextMenuStrip = DirectCast(ctrl, ContextMenuStrip)
                For Each item As ToolStripItem In contextMenu.Items
                    If TypeOf item Is ToolStripMenuItem Then
                        controls.Add(item)
                        AddSubMenuItemsToList(DirectCast(item, ToolStripMenuItem), controls)
                    End If
                Next
            End If

            ' Recursively search in child containers
            If ctrl.HasChildren Then
                GetControlsRecursively(ctrl, controls)
            End If
        Next
    End Sub

    ' Helper function for recursive menu items
    Private Sub AddSubMenuItemsToList(menuItem As ToolStripMenuItem, controls As List(Of Object))
        For Each subItem As ToolStripItem In menuItem.DropDownItems
            controls.Add(subItem)
            If TypeOf subItem Is ToolStripMenuItem Then
                AddSubMenuItemsToList(DirectCast(subItem, ToolStripMenuItem), controls)
            End If
        Next
    End Sub

    ' Get control name from object
    Private Function GetControlNameFromObject(ctrl As Object) As String
        If TypeOf ctrl Is Control Then
            Return DirectCast(ctrl, Control).Name
        ElseIf TypeOf ctrl Is ToolStripItem Then
            Return DirectCast(ctrl, ToolStripItem).Name
        End If
        Return ""
    End Function

    ' Check if control is a system control (to skip) - ENHANCED
    Private Function IsSystemControl(controlName As String) As Boolean
        ' Skip empty or very short names
        If String.IsNullOrWhiteSpace(controlName) OrElse controlName.Length < 3 Then
            Return True
        End If

        ' Exclude frmGroupPermissions buttons (buttons from this form)
        If IsGroupPermissionButton(controlName) Then
            Return True
        End If

        ' System container controls to skip
        Dim systemContainers() As String = {"MenuStrip", "ToolStrip", "StatusStrip", "Timer", "ImageList", "ContextMenuStrip"}

        For Each sysCtrl As String In systemContainers
            If controlName.ToLower().Contains(sysCtrl.ToLower()) Then
                Return True
            End If
        Next

        ' Skip controls with certain prefixes (but be more selective)
        If controlName.StartsWith("lbl", StringComparison.OrdinalIgnoreCase) OrElse
           controlName.StartsWith("txt", StringComparison.OrdinalIgnoreCase) OrElse
           controlName.StartsWith("cmb", StringComparison.OrdinalIgnoreCase) OrElse
           controlName.StartsWith("dgv", StringComparison.OrdinalIgnoreCase) OrElse
           controlName.StartsWith("pnl", StringComparison.OrdinalIgnoreCase) OrElse
           controlName.StartsWith("grp", StringComparison.OrdinalIgnoreCase) OrElse
           controlName.StartsWith("tab", StringComparison.OrdinalIgnoreCase) Then
            Return True
        End If

        ' Skip separators and labels
        If controlName.ToLower().Contains("separator") OrElse
           controlName.ToLower().Contains("label") Then
            Return True
        End If

        Return False
    End Function

    ' Check if button belongs to frmGroupPermissions
    Private Function IsGroupPermissionButton(controlName As String) As Boolean
        ' Common button names in frmGroupPermissions
        Dim groupPermissionButtons() As String = {
            "btnSave", "btnDelete", "btnCreate", "btnCheckAll", "btnUncheckAll",
            "btnCleanup", "btnMaintenance", "btnCheckButtons", "btnCleanupOrphans",
            "btnFullMaintenance", "btnRefresh", "btnClose", "btnCancel", "btnEnhancedMaintenance",
            "btnTestAuthorization", "btnShowUserPermissions", "btnExpandAll", "btnCollapseAll"
        }

        For Each btnName As String In groupPermissionButtons
            If String.Equals(controlName, btnName, StringComparison.OrdinalIgnoreCase) Then
                Return True
            End If
        Next

        Return False
    End Function

    ' Check if button should be excluded from TreeView display
    Private Function IsExcludedButton(buttonName As String) As Boolean
        ' No buttons are excluded from TreeView display now
        Return False
    End Function

    ' Check if button should be excluded from maintenance check - NEW FUNCTION
    Private Function IsMaintenanceExcludedButton(buttonName As String) As Boolean
        ' btnExit and btnTest are excluded from maintenance checks but available to all users
        Dim maintenanceExcludedButtons() As String = {"btnExit", "btnTest"}
        For Each excluded As String In maintenanceExcludedButtons
            If String.Equals(buttonName, excluded, StringComparison.OrdinalIgnoreCase) Then
                Return True
            End If
        Next
        Return False
    End Function

    ' Get existing forms from database
    Private Function GetExistingFormsFromDatabase() As List(Of String)
        Dim existingForms As New List(Of String)

        Try
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            Using cmd As New SqlCommand("SELECT FormName FROM tblForms", Con)
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    While reader.Read()
                        existingForms.Add(reader("FormName").ToString())
                    End While
                End Using
            End Using

        Catch ex As Exception
            MessageBox.Show($"خطأ في قراءة النماذج من قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return existingForms
    End Function

    ' ========== BUTTON EVENT HANDLERS ==========

    ' Button to trigger enhanced maintenance
    Private Sub btnEnhancedMaintenance_Click(sender As Object, e As EventArgs) ' Handles btnEnhancedMaintenance.Click
        Dim result As DialogResult = MessageBox.Show(
            "هل تريد تنفيذ صيانة شاملة مع دعم التسلسل الهرمي؟" & vbNewLine & vbNewLine &
            "1. اكتشاف الأزرار الجديدة وإضافتها" & vbNewLine &
            "2. اكتشاف الحاويات الرئيسية وإنشاؤها" & vbNewLine &
            "3. إنشاء العلاقات الهرمية" & vbNewLine &
            "4. تنظيف الأزرار اليتيمة",
            "صيانة شاملة محسنة",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question)

        If result = DialogResult.Yes Then
            CheckForUnmaintainedButtonsWithHierarchy()
            CleanupOrphanedForms()

            ' Refresh the TreeView
            If dgvGroups.SelectedRows.Count > 0 Then
                Dim groupId As Integer = Convert.ToInt32(dgvGroups.SelectedRows(0).Cells(0).Value)
                LoadPermissionTreeView(groupId)
            End If
        End If
    End Sub

    ' Test authorization button
    Private Sub btnTestAuthorization_Click(sender As Object, e As EventArgs) ' Handles btnTestAuthorization.Click
        Try
            ' Find frmMain
            Dim mainForm As frmMain = GetMainFormInstance()
            If mainForm Is Nothing Then
                MessageBox.Show("frmMain is not open. Please open the main form first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            ' Test with a specific user (change "22" to your test username)
            Dim testUsername As String = InputBox("Enter username to test authorization:", "Test Authorization", "22")
            If String.IsNullOrWhiteSpace(testUsername) Then Return

            ' Ask user if they want debug messages
            Dim result As DialogResult = MessageBox.Show("Do you want to see debug messages during authorization test?",
                                                       "Authorization Test",
                                                       MessageBoxButtons.YesNo,
                                                       MessageBoxIcon.Question)

            Dim showDebug As Boolean = (result = DialogResult.Yes)

            ' Apply authorization
            AuthorizationModule.ApplyUserAuthorization(mainForm, testUsername, Constr, showDebug)

            If Not showDebug Then
                MessageBox.Show("Authorization applied successfully!", "Test Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            MessageBox.Show($"Error testing authorization: {ex.Message}", "Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Show user permissions button
    Private Sub btnShowUserPermissions_Click(sender As Object, e As EventArgs) ' Handles btnShowUserPermissions.Click
        Try
            Dim testUsername As String = InputBox("Enter username to check permissions:", "Check Permissions", "22")

            If String.IsNullOrWhiteSpace(testUsername) Then
                Return
            End If

            ' Get user's authorized forms
            Dim authorizedForms As List(Of String) = AuthorizationModule.GetUserAuthorizedForms(testUsername, Constr)

            If authorizedForms.Count > 0 Then
                Dim formsList As String = String.Join(vbNewLine, authorizedForms)
                MessageBox.Show($"User '{testUsername}' has access to:{vbNewLine}{vbNewLine}{formsList}",
                              "User Permissions", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                MessageBox.Show($"User '{testUsername}' has no permissions or doesn't exist.",
                              "User Permissions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If

        Catch ex As Exception
            MessageBox.Show($"Error checking permissions: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' ========== DATA CLASSES ==========

    ' Helper classes for hierarchy detection
    Public Class HierarchyDetectionResult
        Public Property NewButtons As New List(Of ButtonInfo)
        Public Property NewParents As New List(Of ParentContainerInfo)
    End Class

    Public Class ButtonInfo
        Public Property ButtonName As String
        Public Property ParentPrefix As String
    End Class

    Public Class ParentContainerInfo
        Public Property ButtonName As String
        Public Property DisplayText As String
        Public Property Children As New List(Of String)
    End Class

    Public Class FormNodeInfo
        Public Property FormID As Integer
        Public Property FormName As String
        Public Property IsContainer As Boolean
        Public Property SortOrder As Integer
    End Class

    Public Class OrphanedFormInfo
        Public Property FormID As Integer
        Public Property FormName As String
        Public Property DisplayText As String
    End Class

End Class