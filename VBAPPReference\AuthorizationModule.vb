﻿Imports System.Data.SqlClient

Public Module AuthorizationModule

    ' Main authorization function
    Public Sub ApplyUserAuthorization(targetForm As Form, username As String, connectionString As String, Optional showDebugMessages As Boolean = False)
        Try
            Dim userGroupID As Integer = GetUserGroupID(username, connectionString)

            ' Group 1 has full access
            If userGroupID = 1 Then
                If showDebugMessages Then
                    MessageBox.Show($"User '{username}' has full access (Group 1)")
                End If
                Return
            End If

            ' Get authorized form names for this user
            Dim authorizedForms As HashSet(Of String) = GetUserAuthorizedFormNames(userGroupID, connectionString)

            If showDebugMessages Then
                MessageBox.Show($"User: {username}, Group: {userGroupID}{vbNewLine}Authorized forms: {authorizedForms.Count}{vbNewLine}{String.Join(", ", authorizedForms)}")
            End If

            ' Apply authorization to all controls
            ApplyAuthorizationToControls(targetForm, authorizedForms, showDebugMessages)

        Catch ex As Exception
            If showDebugMessages Then
                MessageBox.Show($"Authorization error: {ex.Message}")
            Else
                System.Diagnostics.Debug.WriteLine($"Authorization error: {ex.Message}")
            End If
        End Try
    End Sub

    ' Get user's authorized form names
    Private Function GetUserAuthorizedFormNames(groupID As Integer, connectionString As String) As HashSet(Of String)
        Dim authorizedForms As New HashSet(Of String)(StringComparer.OrdinalIgnoreCase)

        Try
            Using conn As New SqlConnection(connectionString)
                conn.Open()

                ' Get all authorized forms for the group (including inherited permissions)
                Dim query As String = "SELECT DISTINCT f.FormName " &
                                    "FROM tblGroupFormPermissions gfp " &
                                    "INNER JOIN tblForms f ON gfp.FormID = f.FormID " &
                                    "WHERE gfp.GroupID = @GroupID"

                Using cmd As New SqlCommand(query, conn)
                    cmd.Parameters.AddWithValue("@GroupID", groupID)

                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        While reader.Read()
                            authorizedForms.Add(reader("FormName").ToString())
                        End While
                    End Using
                End Using
            End Using
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error getting authorized forms: {ex.Message}")
        End Try

        Return authorizedForms
    End Function

    ' Apply authorization to all controls in the form
    Private Sub ApplyAuthorizationToControls(targetForm As Form, authorizedForms As HashSet(Of String), showDebugMessages As Boolean)
        Dim allControls As List(Of Object) = GetAllClickableControls(targetForm)
        Dim processedCount As Integer = 0
        Dim hiddenCount As Integer = 0

        For Each ctrl As Object In allControls
            Dim controlName As String = GetControlName(ctrl)

            If Not String.IsNullOrWhiteSpace(controlName) Then
                ' Check if this control is authorized
                If controlName.Equals("btnExit", StringComparison.OrdinalIgnoreCase) Then
                    'OrElse
                    'controlName.Equals("btnTest", StringComparison.OrdinalIgnoreCase) Then
                    SetControlVisibility(ctrl, True)
                    Continue For
                End If

                ' Check if this control is authorized
                Dim isAuthorized As Boolean = authorizedForms.Contains(controlName)

                ' Set visibility
                SetControlVisibility(ctrl, isAuthorized)

                processedCount += 1
                If Not isAuthorized Then
                    hiddenCount += 1

                    If showDebugMessages Then
                        System.Diagnostics.Debug.WriteLine($"Hidden: {controlName}")
                    End If
                Else
                    If showDebugMessages Then
                        System.Diagnostics.Debug.WriteLine($"Visible: {controlName}")
                    End If
                End If
            End If
        Next

        If showDebugMessages Then
            MessageBox.Show($"Authorization complete.{vbNewLine}Processed: {processedCount}{vbNewLine}Hidden: {hiddenCount}")
        End If
    End Sub

    ' Get user's group ID
    Private Function GetUserGroupID(username As String, connectionString As String) As Integer
        Dim groupID As Integer = 0

        Try
            Using conn As New SqlConnection(connectionString)
                conn.Open()
                Dim query As String = "SELECT GroupID FROM tblUsers WHERE Username = @Username"

                Using cmd As New SqlCommand(query, conn)
                    cmd.Parameters.AddWithValue("@Username", username)
                    Dim result = cmd.ExecuteScalar()

                    If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                        groupID = Convert.ToInt32(result)
                    End If
                End Using
            End Using
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error getting user GroupID: {ex.Message}")
        End Try

        Return groupID
    End Function

    ' Get all clickable controls from form
    Private Function GetAllClickableControls(container As Control) As List(Of Object)
        Dim controls As New List(Of Object)

        Try
            For Each ctrl As Control In container.Controls
                ' Add buttons
                If TypeOf ctrl Is Button Then
                    controls.Add(ctrl)
                End If

                ' Add ToolStrip items
                If TypeOf ctrl Is ToolStrip Then
                    Dim toolstrip As ToolStrip = DirectCast(ctrl, ToolStrip)
                    For Each item As ToolStripItem In toolstrip.Items
                        If TypeOf item Is ToolStripButton OrElse
                           TypeOf item Is ToolStripMenuItem OrElse
                           TypeOf item Is ToolStripDropDownButton Then
                            controls.Add(item)
                        End If
                    Next
                End If

                ' Add MenuStrip items
                If TypeOf ctrl Is MenuStrip Then
                    Dim menustrip As MenuStrip = DirectCast(ctrl, MenuStrip)
                    For Each item As ToolStripItem In menustrip.Items
                        controls.Add(item)
                        If TypeOf item Is ToolStripMenuItem Then
                            AddSubMenuItems(DirectCast(item, ToolStripMenuItem), controls)
                        End If
                    Next
                End If

                ' Add StatusStrip items
                If TypeOf ctrl Is StatusStrip Then
                    Dim statusstrip As StatusStrip = DirectCast(ctrl, StatusStrip)
                    For Each item As ToolStripItem In statusstrip.Items
                        If TypeOf item Is ToolStripButton Then
                            controls.Add(item)
                        End If
                    Next
                End If

                ' Recursively process child controls
                If ctrl.HasChildren Then
                    controls.AddRange(GetAllClickableControls(ctrl))
                End If
            Next
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error getting controls: {ex.Message}")
        End Try

        Return controls
    End Function

    ' Add submenu items recursively
    Private Sub AddSubMenuItems(menuItem As ToolStripMenuItem, controls As List(Of Object))
        Try
            For Each subItem As ToolStripItem In menuItem.DropDownItems
                controls.Add(subItem)
                If TypeOf subItem Is ToolStripMenuItem Then
                    AddSubMenuItems(DirectCast(subItem, ToolStripMenuItem), controls)
                End If
            Next
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error adding submenu items: {ex.Message}")
        End Try
    End Sub

    ' Get control name
    Private Function GetControlName(ctrl As Object) As String
        Try
            If TypeOf ctrl Is Control Then
                Return DirectCast(ctrl, Control).Name
            ElseIf TypeOf ctrl Is ToolStripItem Then
                Return DirectCast(ctrl, ToolStripItem).Name
            End If
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error getting control name: {ex.Message}")
        End Try
        Return ""
    End Function

    ' Set control visibility and enabled state
    Private Sub SetControlVisibility(ctrl As Object, visible As Boolean)
        Try
            If TypeOf ctrl Is Control Then
                Dim control As Control = DirectCast(ctrl, Control)
                control.Visible = visible
                control.Enabled = visible
            ElseIf TypeOf ctrl Is ToolStripItem Then
                Dim toolStripItem As ToolStripItem = DirectCast(ctrl, ToolStripItem)
                toolStripItem.Visible = visible
                toolStripItem.Enabled = visible
            End If
        Catch ex As Exception
            ' Fallback: try setting enabled only
            Try
                If TypeOf ctrl Is Control Then
                    DirectCast(ctrl, Control).Enabled = visible
                ElseIf TypeOf ctrl Is ToolStripItem Then
                    DirectCast(ctrl, ToolStripItem).Enabled = visible
                End If
            Catch
                System.Diagnostics.Debug.WriteLine($"Error setting control visibility: {ex.Message}")
            End Try
        End Try
    End Sub

    ' Public function to check if user has access to specific form
    Public Function UserHasAccessToForm(username As String, formName As String, connectionString As String) As Boolean
        Try
            Dim userGroupID As Integer = GetUserGroupID(username, connectionString)

            ' Group 1 has full access
            If userGroupID = 1 Then Return True

            Dim authorizedForms As HashSet(Of String) = GetUserAuthorizedFormNames(userGroupID, connectionString)
            Return authorizedForms.Contains(formName)

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error checking form access: {ex.Message}")
            Return False
        End Try
    End Function

    ' Public function to get user's authorized forms
    Public Function GetUserAuthorizedForms(username As String, connectionString As String) As List(Of String)
        Try
            Dim userGroupID As Integer = GetUserGroupID(username, connectionString)

            ' Group 1 has full access
            If userGroupID = 1 Then
                Return GetAllFormNames(connectionString)
            End If

            Dim authorizedForms As HashSet(Of String) = GetUserAuthorizedFormNames(userGroupID, connectionString)
            Return authorizedForms.ToList()

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error getting user authorized forms: {ex.Message}")
            Return New List(Of String)
        End Try
    End Function

    ' Get all form names
    Private Function GetAllFormNames(connectionString As String) As List(Of String)
        Dim formNames As New List(Of String)

        Try
            Using conn As New SqlConnection(connectionString)
                conn.Open()
                Dim query As String = "SELECT FormName FROM tblForms"

                Using cmd As New SqlCommand(query, conn)
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        While reader.Read()
                            formNames.Add(reader("FormName").ToString())
                        End While
                    End Using
                End Using
            End Using
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error getting all form names: {ex.Message}")
        End Try

        Return formNames
    End Function

End Module