using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AccountingSystem.Services
{
    public class ExpensesService : IExpensesService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<ExpensesService> _logger;

        public ExpensesService(AccountingDbContext context, ILogger<ExpensesService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<SimpleAccountDto>> GetCashierAccountsAsync()
        {
            try
            {
                // Parent account code for cash: from GLConfig where EntryReferenceModule = 'نقدية'
                var parent = await _context.GLConfigs
                    .Where(g => g.EntryReferenceModule == "نقدية")
                    .Select(g => g.AccountNo.ToString())
                    .FirstOrDefaultAsync();

                if (string.IsNullOrEmpty(parent)) return new List<SimpleAccountDto>();

                var list = await _context.ChartOfAccounts
                    .Where(a => a.ParentAccountCode == parent && a.IsPosting)
                    .OrderBy(a => a.AccountCode)
                    .Select(a => new SimpleAccountDto { AccountCode = a.AccountCode!, AccountName = a.AccountName! })
                    .ToListAsync();
                return list;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading cashier accounts");
                return new List<SimpleAccountDto>();
            }
        }

        public async Task<List<SimpleAccountDto>> GetExpenseAccountsAsync()
        {
            try
            {
                // Parent account code for expenses: GLConfig EntryReferenceModule = 'المصروفات'
                var parent = await _context.GLConfigs
                    .Where(g => g.EntryReferenceModule == "المصروفات")
                    .Select(g => g.AccountNo.ToString())
                    .FirstOrDefaultAsync();

                if (string.IsNullOrEmpty(parent)) return new List<SimpleAccountDto>();

                var list = await _context.ChartOfAccounts
                    .Where(a => a.ParentAccountCode == parent && a.IsPosting)
                    .OrderBy(a => a.AccountCode)
                    .Select(a => new SimpleAccountDto { AccountCode = a.AccountCode!, AccountName = a.AccountName! })
                    .ToListAsync();
                return list;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading expense accounts");
                return new List<SimpleAccountDto>();
            }
        }

        public async Task<ExpenseInsertResult> InsertExpenseAsync(
            DateTime invoiceDate,
            string? creditCashier,
            string debitExpense,
            decimal invoiceAmount,
            decimal taxAmount,
            long? vendorNo,
            string? vendorInvoiceNo,
            string? vendorName,
            string? vendorVATRN,
            int? employeeBuyer,
            string? notes,
            string? store,
            string createdBy,
            bool ignoreDuplicate,
            IFormFile? photoFile)
        {
            var result = new ExpenseInsertResult();
            try
            {
                await using var connection = new SqlConnection(_context.Database.GetConnectionString());
                await connection.OpenAsync();

                await using var command = new SqlCommand("sp_InsertExpenseInvoice", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // Inputs
                command.Parameters.AddWithValue("@Invoice_Date", invoiceDate);
                command.Parameters.AddWithValue("@CreditCashier", string.IsNullOrWhiteSpace(creditCashier) ? "حساب الموردين" : creditCashier);
                command.Parameters.AddWithValue("@DebitExpense", debitExpense);
                command.Parameters.AddWithValue("@Invoice_amount", invoiceAmount);
                command.Parameters.AddWithValue("@Tax_amount", taxAmount);
                command.Parameters.AddWithValue("@VendorNo", (object?)vendorNo ?? DBNull.Value);
                command.Parameters.AddWithValue("@VendorInvoiceNo", (object?)vendorInvoiceNo ?? DBNull.Value);
                command.Parameters.AddWithValue("@VendorName", (object?)vendorName ?? DBNull.Value);
                command.Parameters.AddWithValue("@VendorVATRN", (object?)vendorVATRN ?? DBNull.Value);
                command.Parameters.AddWithValue("@EmployeeBuyer", (object?)employeeBuyer ?? DBNull.Value);
                command.Parameters.AddWithValue("@Notes", (object?)notes ?? DBNull.Value);
                command.Parameters.AddWithValue("@CreatedBy", createdBy);
                command.Parameters.AddWithValue("@IgnoreDuplicate", ignoreDuplicate);
                command.Parameters.AddWithValue("@CreateJournalEntry", true);
                command.Parameters.AddWithValue("@Store", (object?)store ?? DBNull.Value);

                // Photo
                if (photoFile != null && photoFile.Length > 0)
                {
                    await using var ms = new System.IO.MemoryStream();
                    await photoFile.CopyToAsync(ms);
                    var bytes = ms.ToArray();
                    _logger.LogInformation("ExpensesService: uploading attachment bytes={Length}, contentType={ContentType}", bytes.Length, photoFile.ContentType);
                    command.Parameters.AddWithValue("@Attached_photo", bytes);
                }
                else
                {
                    _logger.LogInformation("ExpensesService: no attachment provided or empty file.");
                    command.Parameters.Add("@Attached_photo", SqlDbType.VarBinary).Value = DBNull.Value;
                }

                // Outputs
                var newId = new SqlParameter("@NewInvoiceID", SqlDbType.Int) { Direction = ParameterDirection.Output };
                var resMsg = new SqlParameter("@ResultMessage", SqlDbType.NVarChar, 500) { Direction = ParameterDirection.Output };
                var dupWarn = new SqlParameter("@DuplicateWarning", SqlDbType.NVarChar, 500) { Direction = ParameterDirection.Output };
                command.Parameters.Add(newId);
                command.Parameters.Add(resMsg);
                command.Parameters.Add(dupWarn);

                await command.ExecuteNonQueryAsync();

                result.InvoiceID = (newId.Value == DBNull.Value) ? 0 : Convert.ToInt32(newId.Value);
                result.Message = resMsg.Value?.ToString() ?? string.Empty;
                result.DuplicateWarning = dupWarn.Value?.ToString() ?? string.Empty;
                result.Success = result.InvoiceID > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting expense");
                result.Success = false;
                result.InvoiceID = 0;
                result.Message = $"خطأ في الاتصال بقاعدة البيانات: {ex.Message}";
                result.DuplicateWarning = string.Empty;
            }

            return result;
        }
    }
}

