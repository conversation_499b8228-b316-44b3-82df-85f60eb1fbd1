@model IEnumerable<AccountingSystem.Models.Store>
@{
    ViewData["Title"] = "إدارة المتاجر";
    Layout = "_Layout";
}

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-store"></i>
                    إدارة المتاجر
                </h1>
                <div class="page-options">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createStoreModal">
                        <i class="fas fa-plus"></i>
                        إضافة متجر جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i>
                @TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                @TempData["ErrorMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }
    </div>

    <!-- Stores Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">قائمة المتاجر</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="storesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>الكود</th>
                                    <th>الوصف</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>أنشئ بواسطة</th>
                                    <th>تاريخ التعديل</th>
                                    <th>عدل بواسطة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var store in Model)
                                {
                                    <tr>
                                        <td><strong>@store.SN</strong></td>
                                        <td>@store.StoreName</td>
                                        <td>@(store.CreatedOn.HasValue ? store.CreatedOn.Value.ToString("yyyy/MM/dd HH:mm") : "-")</td>
                                        <td>@if (!string.IsNullOrEmpty(store.CreatedBy)) {<span class="badge bg-info">@store.CreatedBy</span>} else {<span>-</span>}</td>
                                        <td>@(store.ModifiedOn.HasValue ? store.ModifiedOn.Value.ToString("yyyy/MM/dd HH:mm") : "-")</td>
                                        <td>@if (!string.IsNullOrEmpty(store.ModifiedBy)) {<span class="badge bg-warning">@store.ModifiedBy</span>} else {<span>-</span>}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="openEditModal(@store.SN)" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmDelete(@store.SN)" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Store Modal -->
<div class="modal fade" id="createStoreModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form asp-controller="StoresV2" asp-action="CreateStore" method="post">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة متجر جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" dir="rtl">
                    <div class="mb-3">
                        <label for="StoreName" class="form-label">وصف المتجر <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="StoreName" required maxlength="100" />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Store Modal -->
<div class="modal fade" id="editStoreModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form asp-controller="StoresV2" asp-action="EditStore" method="post" id="editStoreForm">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المتجر</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" dir="rtl">
                    <input type="hidden" id="editStoreSn" name="SN" />
                    <div class="mb-3">
                        <label for="editStoreName" class="form-label">وصف المتجر <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editStoreName" name="StoreName" required maxlength="100" />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Form -->
<form id="deleteForm" asp-controller="StoresV2" asp-action="DeleteStore" method="post" class="d-none">
    <input type="hidden" id="deleteStoreId" name="id" />
</form>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#storesTable').DataTable({
                language: { url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json' },
                responsive: true,
                order: [[0, 'asc']],
                columnDefs: [{ orderable: false, targets: [6] }]
            });

            setTimeout(function() { $('.alert').fadeOut('slow'); }, 5000);
        });

        function openEditModal(id) {
            $.get(`/StoresV2/Details/${id}`, function (data) {
                $('#editStoreSn').val(data.sn);
                $('#editStoreName').val(data.storeName);
                var editModal = new bootstrap.Modal(document.getElementById('editStoreModal'));
                editModal.show();
            });
        }

        function confirmDelete(id) {
            if (confirm('هل أنت متأكد من أنك تريد حذف هذا المتجر؟')) {
                $('#deleteStoreId').val(id);
                $('#deleteForm').submit();
            }
        }
    </script>
} 