using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Models;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class WarehousesController : Controller
    {
        private readonly IWarehouseService _warehouseService;
        private readonly ILogger<WarehousesController> _logger;

        public WarehousesController(IWarehouseService warehouseService, ILogger<WarehousesController> logger)
        {
            _warehouseService = warehouseService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var warehouses = await _warehouseService.GetWarehousesAsync();
            return View(warehouses);
        }

        [HttpPost]
        public async Task<IActionResult> CreateWarehouse(Warehouse warehouse)
        {
            if (!ModelState.IsValid)
            {
                TempData["ErrorMessage"] = "البيانات المدخلة غير صالحة.";
                return RedirectToAction(nameof(Index));
            }

            var currentUser = User.Identity?.Name ?? "System";
            var (success, createdWarehouse) = await _warehouseService.CreateWarehouseAsync(warehouse, currentUser);

            if (success)
            {
                TempData["SuccessMessage"] = $"تم إنشاء المستودع بنجاح برقم: {createdWarehouse?.SN}";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في إنشاء المستودع.";
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        public async Task<IActionResult> EditWarehouse(Warehouse warehouse)
        {
            if (!ModelState.IsValid)
            {
                TempData["ErrorMessage"] = "البيانات المدخلة غير صالحة.";
                return RedirectToAction(nameof(Index));
            }
            
            var currentUser = User.Identity?.Name ?? "System";
            var success = await _warehouseService.UpdateWarehouseAsync(warehouse, currentUser);

            if (success)
            {
                TempData["SuccessMessage"] = "تم تحديث المستودع بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في تحديث المستودع.";
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        public async Task<IActionResult> DeleteWarehouse(int id)
        {
            var (success, errorMessage) = await _warehouseService.DeleteWarehouseAsync(id);

            if (success)
            {
                TempData["SuccessMessage"] = "تم حذف المستودع بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = errorMessage;
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpGet]
        public async Task<IActionResult> GetWarehouseDetails(int id)
        {
            var warehouse = await _warehouseService.GetWarehouseByIdAsync(id);
            if (warehouse == null)
            {
                return NotFound();
            }
            return Json(warehouse);
        }
    }
}
