using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AccountingSystem.Data;
using AccountingSystem.Models;
using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public class InvoiceToolSettingService : IInvoiceToolSettingService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<InvoiceToolSettingService> _logger;

        public InvoiceToolSettingService(AccountingDbContext context, ILogger<InvoiceToolSettingService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<InvoiceToolSetting> GetSettingsByInvoiceTypeAsync(string invoiceType)
        {
            var settings = await _context.InvoiceToolSettings.AsNoTracking().FirstOrDefaultAsync(s => s.InvoiceType == invoiceType);
            if (settings == null)
            {
                return new InvoiceToolSetting { InvoiceType = invoiceType };
            }
            return settings;
        }

        public async Task<bool> UpdateSettingsAsync(InvoiceToolSetting settings, string currentUser)
        {
            try
            {
                var existingSettings = await _context.InvoiceToolSettings.FirstOrDefaultAsync(s => s.InvoiceType == settings.InvoiceType);
                if (existingSettings == null)
                {
                    settings.CreatedBy = currentUser;
                    settings.CreatedOn = DateTime.Now;
                    _context.InvoiceToolSettings.Add(settings);
                }
                else
                {
                    // Update only the fields that are managed by this screen
                    existingSettings.InvoicePrinter = settings.InvoicePrinter;
                    existingSettings.PrintOption = settings.PrintOption;
                    existingSettings.DefPaymentType = settings.DefPaymentType;
                    existingSettings.DefPriceIncludeVAT = settings.DefPriceIncludeVAT;
                    existingSettings.DefNonVATInvoice = settings.DefNonVATInvoice;
                    existingSettings.ReferenceMandatory = settings.ReferenceMandatory;
                    existingSettings.MandatoryVendorVATReg = settings.MandatoryVendorVATReg;
                    existingSettings.ModifiedBy = currentUser;
                    existingSettings.ModifiedOn = DateTime.Now;
                }
                
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice tool settings for {InvoiceType}", settings.InvoiceType);
                return false;
            }
        }

        public Task<List<string>> GetInvoiceTypesAsync()
        {
            var types = new List<string> { "مبيعات", "مشتريات", "مرتجع مبيعات", "مرتجع مشتريات", "رصيد افتتاحي" };
            return Task.FromResult(types);
        }

        public Task<List<string>> GetPrinterNamesAsync()
        {
            var printerList = new List<string>();
            try
            {
                foreach (string printer in PrinterSettings.InstalledPrinters)
                {
                    printerList.Add(printer);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Could not retrieve installed printers.");
            }
            return Task.FromResult(printerList);
        }
    }
} 