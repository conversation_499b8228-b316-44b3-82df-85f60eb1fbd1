-- =====================================================
-- Database Schema Documentation Query
-- This query generates comprehensive documentation for all database objects
-- Run this query and save the results for future reference
-- =====================================================

-- Set the database context
USE [SULTDB]
GO

-- =====================================================
-- 1. TABLES INFORMATION
-- =====================================================
SELECT 
    'TABLE' AS ObjectType,
    t.TABLE_SCHEMA AS SchemaName,
    t.TABLE_NAME AS ObjectName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    c.CHARACTER_MAXIMUM_LENGTH AS MaxLength,
    c.NUMERIC_PRECISION AS NumericPrecision,
    c.NUMERIC_SCALE AS NumericScale,
    c.IS_NULLABLE AS IsNullable,
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        WHEN fk.COLUMN_NAME IS NOT NULL THEN 'FK'
        ELSE ''
    END AS KeyType,
    c.COLUMN_DEFAULT AS DefaultValue,
    ISNULL(ep.value, '') AS Description
FROM INFORMATION_SCHEMA.TABLES t
INNER JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME AND t.TABLE_SCHEMA = c.TABLE_SCHEMA
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
) pk ON c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
) fk ON c.TABLE_NAME = fk.TABLE_NAME AND c.COLUMN_NAME = fk.COLUMN_NAME
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID(t.TABLE_SCHEMA + '.' + t.TABLE_NAME) 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE t.TABLE_TYPE = 'BASE TABLE'
    AND t.TABLE_SCHEMA = 'dbo'  -- Adjust schema as needed

UNION ALL

-- =====================================================
-- 2. VIEWS INFORMATION
-- =====================================================
SELECT 
    'VIEW' AS ObjectType,
    v.TABLE_SCHEMA AS SchemaName,
    v.TABLE_NAME AS ObjectName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    c.CHARACTER_MAXIMUM_LENGTH AS MaxLength,
    c.NUMERIC_PRECISION AS NumericPrecision,
    c.NUMERIC_SCALE AS NumericScale,
    c.IS_NULLABLE AS IsNullable,
    '' AS KeyType,
    NULL AS DefaultValue,
    ep.value AS Description
FROM INFORMATION_SCHEMA.VIEWS v
INNER JOIN INFORMATION_SCHEMA.COLUMNS c ON v.TABLE_NAME = c.TABLE_NAME AND v.TABLE_SCHEMA = c.TABLE_SCHEMA
LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID(v.TABLE_SCHEMA + '.' + v.TABLE_NAME) 
    AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
WHERE v.TABLE_SCHEMA = 'dbo'  -- Adjust schema as needed

UNION ALL

-- =====================================================
-- 3. STORED PROCEDURES INFORMATION
-- =====================================================
SELECT 
    'STORED PROCEDURE' AS ObjectType,
    SCHEMA_NAME(p.schema_id) AS SchemaName,
    p.name AS ObjectName,
    pr.name AS ColumnName,
    t.name AS DataType,
    pr.max_length AS MaxLength,
    pr.precision AS NumericPrecision,
    pr.scale AS NumericScale,
    CASE WHEN pr.is_nullable = 1 THEN 'YES' ELSE 'NO' END AS IsNullable,
    CASE 
        WHEN pr.is_output = 1 THEN 'OUTPUT'
        ELSE 'INPUT'
    END AS KeyType,
    pr.default_value AS DefaultValue,
    ep.value AS Description
FROM sys.procedures p
INNER JOIN sys.parameters pr ON p.object_id = pr.object_id
INNER JOIN sys.types t ON pr.user_type_id = t.user_type_id
LEFT JOIN sys.extended_properties ep ON ep.major_id = p.object_id 
    AND ep.minor_id = pr.parameter_id AND ep.name = 'MS_Description'
WHERE SCHEMA_NAME(p.schema_id) = 'dbo'  -- Adjust schema as needed

ORDER BY ObjectType, SchemaName, ObjectName, ColumnName;

-- =====================================================
-- 4. INDEXES INFORMATION
-- =====================================================
SELECT 
    'INDEX' AS ObjectType,
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS TableName,
    i.name AS IndexName,
    i.type_desc AS IndexType,
    STUFF((
        SELECT ', ' + c.name
        FROM sys.index_columns ic
        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id
        ORDER BY ic.key_ordinal
        FOR XML PATH('')
    ), 1, 2, '') AS IndexColumns,
    i.is_unique AS IsUnique,
    i.is_primary_key AS IsPrimaryKey,
    i.is_unique_constraint AS IsUniqueConstraint
FROM sys.indexes i
INNER JOIN sys.tables t ON i.object_id = t.object_id
WHERE i.type > 0  -- Exclude heaps
    AND SCHEMA_NAME(t.schema_id) = 'dbo'  -- Adjust schema as needed
ORDER BY SchemaName, TableName, IndexName;

-- =====================================================
-- 5. FOREIGN KEY RELATIONSHIPS
-- =====================================================
SELECT 
    'FOREIGN KEY' AS ObjectType,
    SCHEMA_NAME(fk.schema_id) AS SchemaName,
    OBJECT_NAME(fk.parent_object_id) AS TableName,
    fk.name AS ForeignKeyName,
    COL_NAME(fkc.parent_object_id, fkc.parent_column_id) AS ColumnName,
    SCHEMA_NAME(ref_schema.schema_id) AS ReferencedSchemaName,
    OBJECT_NAME(fk.referenced_object_id) AS ReferencedTableName,
    COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) AS ReferencedColumnName,
    fk.delete_referential_action_desc AS DeleteAction,
    fk.update_referential_action_desc AS UpdateAction
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.objects ref_table ON fk.referenced_object_id = ref_table.object_id
INNER JOIN sys.schemas ref_schema ON ref_table.schema_id = ref_schema.schema_id
WHERE SCHEMA_NAME(fk.schema_id) = 'dbo'  -- Adjust schema as needed
ORDER BY SchemaName, TableName, ForeignKeyName;

-- =====================================================
-- 6. TRIGGERS INFORMATION
-- =====================================================
SELECT 
    'TRIGGER' AS ObjectType,
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS TableName,
    tr.name AS TriggerName,
    tr.type_desc AS TriggerType,
    tr.is_disabled AS IsDisabled,
    tr.is_instead_of_trigger AS IsInsteadOfTrigger,
    ep.value AS Description
FROM sys.triggers tr
INNER JOIN sys.tables t ON tr.parent_id = t.object_id
LEFT JOIN sys.extended_properties ep ON ep.major_id = tr.object_id 
    AND ep.name = 'MS_Description'
WHERE SCHEMA_NAME(t.schema_id) = 'dbo'  -- Adjust schema as needed
ORDER BY SchemaName, TableName, TriggerName;

-- =====================================================
-- 7. FUNCTIONS INFORMATION
-- =====================================================
SELECT 
    'FUNCTION' AS ObjectType,
    SCHEMA_NAME(f.schema_id) AS SchemaName,
    f.name AS FunctionName,
    f.type_desc AS FunctionType,
    pr.name AS ParameterName,
    t.name AS ParameterType,
    pr.max_length AS MaxLength,
    pr.precision AS NumericPrecision,
    pr.scale AS NumericScale,
    CASE WHEN pr.is_nullable = 1 THEN 'YES' ELSE 'NO' END AS IsNullable,
    CASE 
        WHEN pr.is_output = 1 THEN 'OUTPUT'
        ELSE 'INPUT'
    END AS ParameterDirection,
    pr.default_value AS DefaultValue,
    ep.value AS Description
FROM sys.objects f
INNER JOIN sys.parameters pr ON f.object_id = pr.object_id
INNER JOIN sys.types t ON pr.user_type_id = t.user_type_id
LEFT JOIN sys.extended_properties ep ON ep.major_id = f.object_id 
    AND ep.minor_id = pr.parameter_id AND ep.name = 'MS_Description'
WHERE f.type IN ('FN', 'IF', 'TF')  -- Scalar, Inline Table-Valued, Table-Valued Functions
    AND SCHEMA_NAME(f.schema_id) = 'dbo'  -- Adjust schema as needed
ORDER BY SchemaName, FunctionName, pr.parameter_id;

-- =====================================================
-- 8. SUMMARY STATISTICS
-- =====================================================
SELECT 
    'SUMMARY' AS ObjectType,
    'DATABASE STATISTICS' AS SchemaName,
    'TOTAL COUNTS' AS ObjectName,
    'Count' AS ColumnName,
    'INT' AS DataType,
    NULL AS MaxLength,
    NULL AS NumericPrecision,
    NULL AS NumericScale,
    'NO' AS IsNullable,
    '' AS KeyType,
    NULL AS DefaultValue,
    NULL AS Description
FROM (SELECT 1 AS dummy) AS d

UNION ALL

SELECT 
    'SUMMARY',
    'TABLES',
    'Total Tables',
    CAST((SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA = 'dbo') AS VARCHAR(10)),
    'INT',
    NULL,
    NULL,
    NULL,
    'NO',
    '',
    NULL,
    NULL

UNION ALL

SELECT 
    'SUMMARY',
    'VIEWS',
    'Total Views',
    CAST((SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_SCHEMA = 'dbo') AS VARCHAR(10)),
    'INT',
    NULL,
    NULL,
    NULL,
    'NO',
    '',
    NULL,
    NULL

UNION ALL

SELECT 
    'SUMMARY',
    'STORED PROCEDURES',
    'Total Stored Procedures',
    CAST((SELECT COUNT(*) FROM sys.procedures WHERE SCHEMA_NAME(schema_id) = 'dbo') AS VARCHAR(10)),
    'INT',
    NULL,
    NULL,
    NULL,
    'NO',
    '',
    NULL,
    NULL

UNION ALL

SELECT 
    'SUMMARY',
    'FUNCTIONS',
    'Total Functions',
    CAST((SELECT COUNT(*) FROM sys.objects WHERE type IN ('FN', 'IF', 'TF') AND SCHEMA_NAME(schema_id) = 'dbo') AS VARCHAR(10)),
    'INT',
    NULL,
    NULL,
    NULL,
    'NO',
    '',
    NULL,
    NULL

UNION ALL

SELECT 
    'SUMMARY',
    'TRIGGERS',
    'Total Triggers',
    CAST((SELECT COUNT(*) FROM sys.triggers tr INNER JOIN sys.tables t ON tr.parent_id = t.object_id WHERE SCHEMA_NAME(t.schema_id) = 'dbo') AS VARCHAR(10)),
    'INT',
    NULL,
    NULL,
    NULL,
    'NO',
    '',
    NULL,
    NULL

UNION ALL

SELECT 
    'SUMMARY',
    'INDEXES',
    'Total Indexes',
    CAST((SELECT COUNT(*) FROM sys.indexes i INNER JOIN sys.tables t ON i.object_id = t.object_id WHERE i.type > 0 AND SCHEMA_NAME(t.schema_id) = 'dbo') AS VARCHAR(10)),
    'INT',
    NULL,
    NULL,
    NULL,
    'NO',
    '',
    NULL,
    NULL

UNION ALL

SELECT 
    'SUMMARY',
    'FOREIGN KEYS',
    'Total Foreign Keys',
    CAST((SELECT COUNT(*) FROM sys.foreign_keys fk INNER JOIN sys.tables t ON fk.parent_object_id = t.object_id WHERE SCHEMA_NAME(fk.schema_id) = 'dbo') AS VARCHAR(10)),
    'INT',
    NULL,
    NULL,
    NULL,
    'NO',
    '',
    NULL,
    NULL;

-- =====================================================
-- 9. TABLE SIZE INFORMATION
-- =====================================================
SELECT 
    'TABLE SIZE' AS ObjectType,
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS TableName,
    'Size Info' AS ColumnName,
    'INFO' AS DataType,
    NULL AS MaxLength,
    NULL AS NumericPrecision,
    NULL AS NumericScale,
    'NO' AS IsNullable,
    '' AS KeyType,
    NULL AS DefaultValue,
    CONCAT(
        'Rows: ', CAST(p.rows AS VARCHAR(20)), 
        ', Data: ', CAST(CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS DECIMAL(18,2)) AS VARCHAR(20)), ' MB',
        ', Index: ', CAST(CAST(ROUND(((SUM(CASE WHEN a.type <> 1 THEN a.used_pages WHEN p.index_id < 2 THEN a.data_pages ELSE 0 END) * 8) / 1024.00), 2) AS DECIMAL(18,2)) AS VARCHAR(20)), ' MB',
        ', Total: ', CAST(CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS DECIMAL(18,2)) AS VARCHAR(20)), ' MB'
    ) AS Description
FROM sys.tables t
INNER JOIN sys.indexes i ON t.object_id = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
WHERE SCHEMA_NAME(t.schema_id) = 'dbo'  -- Adjust schema as needed
GROUP BY t.schema_id, t.name, p.rows
ORDER BY SchemaName, TableName;

-- =====================================================
-- 10. COLUMN STATISTICS
-- =====================================================
SELECT 
    'COLUMN STATS' AS ObjectType,
    c.TABLE_SCHEMA AS SchemaName,
    c.TABLE_NAME AS TableName,
    c.COLUMN_NAME AS ColumnName,
    c.DATA_TYPE AS DataType,
    c.CHARACTER_MAXIMUM_LENGTH AS MaxLength,
    c.NUMERIC_PRECISION AS NumericPrecision,
    c.NUMERIC_SCALE AS NumericScale,
    c.IS_NULLABLE AS IsNullable,
    '' AS KeyType,
    c.COLUMN_DEFAULT AS DefaultValue,
    CONCAT(
        'Table: ', c.TABLE_NAME,
        ', Column: ', c.COLUMN_NAME,
        ', Type: ', c.DATA_TYPE,
        CASE 
            WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CONCAT('(', c.CHARACTER_MAXIMUM_LENGTH, ')')
            WHEN c.NUMERIC_PRECISION IS NOT NULL THEN CONCAT('(', c.NUMERIC_PRECISION, ',', c.NUMERIC_SCALE, ')')
            ELSE ''
        END,
        ', Nullable: ', c.IS_NULLABLE
    ) AS Description
FROM INFORMATION_SCHEMA.COLUMNS c
INNER JOIN INFORMATION_SCHEMA.TABLES t ON c.TABLE_NAME = t.TABLE_NAME AND c.TABLE_SCHEMA = t.TABLE_SCHEMA
WHERE t.TABLE_TYPE = 'BASE TABLE'
    AND c.TABLE_SCHEMA = 'dbo'  -- Adjust schema as needed
ORDER BY c.TABLE_NAME, c.ORDINAL_POSITION; 