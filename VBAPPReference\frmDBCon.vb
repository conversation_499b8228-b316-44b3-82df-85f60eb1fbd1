﻿Imports Microsoft.Win32
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports Microsoft.SqlServer.Management.Smo
Imports Microsoft.SqlServer.Server
Imports Microsoft.SqlServer.Management.Smo.ServerProxyAccount
Imports Microsoft.SqlServer.Management.Common
Imports System
Public Class frmDBCon

    Public serverconnection As ServerConnection = Nothing
    Public Property connectionstring() As String
    Public Property serverName() As String
    Public Property DatabaseName() As String
    Public dt As New DataTable
    Public cmd As New SqlCommand
    Public da As New SqlDataAdapter

    Private Sub frmDBCon_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        comboserver.Text = My.Settings.server
        combodatabase.Text = My.Settings.database
        If My.Settings.model = "SQL" Then
            chkmode.Checked = True
        ElseIf My.Settings.model = "Windows" Then
            chkmode.Checked = False
        End If

        txtuser.Text = My.Settings.id
        txtpass.Text = My.Settings.passowrd
    End Sub

    Public Sub loadserver(ByVal combo As ComboBox)

        combo.Items.Clear()

        Try
            Dim localmachine = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64)
            Dim rk = localmachine.OpenSubKey("SOFTWARE\microsoft\microsoft sql server")
            Dim instances = CType(rk.GetValue("installedinstances"), String())
            If instances.Length > 0 Then
                For Each element As String In instances

                    If element = "MSSQLSERVER" Then
                        combo.Items.Add(System.Environment.MachineName)
                    Else
                        combo.Items.Add(System.Environment.MachineName + "\" + element)
                    End If
                Next element
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Alert", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End Try
    End Sub

    Public Sub selectdata(comboserver As ComboBox, combodatabase As ComboBox)

        combodatabase.Items.Clear()

        Dim con As New SqlConnection("server=" & comboserver.Text & "; database = master ; integrated security=true ")
        Dim da As New SqlDataAdapter("select name from sys.databases ", con)
        Dim dt As New DataTable
        da.Fill(dt)

        For i As Integer = 0 To dt.Rows.Count - 1
            combodatabase.Items.Add(dt.Rows(i)("name").ToString)
        Next
    End Sub

    Public Sub DatabaseDropDown(checkmode As CheckBox, cmbserver As ComboBox, txtuser As TextBox, txtpass As TextBox, combdata As ComboBox)

        If checkmode.Checked Then
            serverconnection = New ServerConnection(cmbserver.Text, txtuser.Text, txtpass.Text)
        Else
            serverconnection = New ServerConnection(cmbserver.Text)

        End If
        Dim server As New Server(serverconnection)
        Dim databases As DatabaseCollection = server.Databases
        combdata.Items.Clear()

        For Each Database As Database In databases

            combdata.Items.Add(Database.Name)
        Next Database

    End Sub

    Public Sub Databaseselectedindexchanged(checkmode As CheckBox, cmbserver As ComboBox, txtuser As TextBox, txtpass As TextBox, combodata As ComboBox)

        serverconnection.Disconnect()
        If checkmode.Checked Then

            serverconnection = New ServerConnection(cmbserver.Text, txtuser.Text, txtpass.Text)
        Else

            serverconnection = New ServerConnection(cmbserver.Text)

        End If

        serverconnection.DatabaseName = combodata.Text.ToString()
        connectionstring = serverconnection.ConnectionString
        serverName = serverconnection.ServerInstance
        DatabaseName = combodata.Text.ToString()

    End Sub



    Private Sub ConfForm_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        loadserver(comboserver)
    End Sub

    Private Sub combodatabase_DropDown(sender As System.Object, e As System.EventArgs) Handles combodatabase.DropDown
        DatabaseDropDown(chkmode, comboserver, txtuser, txtpass, combodatabase)
    End Sub

    Private Sub combodatabase_SelectedIndexChanged(sender As System.Object, e As System.EventArgs) Handles combodatabase.SelectedIndexChanged
        Databaseselectedindexchanged(chkmode, comboserver, txtuser, txtpass, combodatabase)
    End Sub

    Private Sub chkmode_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles chkmode.CheckedChanged
        txtuser.Enabled = chkmode.Checked
        txtpass.Enabled = chkmode.Checked
    End Sub

    Private Sub btnconnection_Click(sender As System.Object, e As System.EventArgs) Handles btnconnection.Click

        My.Settings.server = comboserver.Text
        My.Settings.database = combodatabase.Text
        'My.Settings.model = If(chkmode.Checked = True, "SQL", "Windows")
        My.Settings.model = "SQL"
        My.Settings.id = txtuser.Text
        My.Settings.passowrd = txtpass.Text
        My.Settings.Save()

        ConServerName = My.Settings.server
        ConDatabaseName = My.Settings.database
        ConUserName = My.Settings.id
        ConPassword = My.Settings.passowrd
        ConModel = "SQL"

        MessageBox.Show("تم الاتصال حفظ قاعدة البيانات بنجاح", "تحديث قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Me.Close()
        frmLogin.Show()
    End Sub

    Private Sub btnClose_Click(sender As System.Object, e As System.EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub


End Class