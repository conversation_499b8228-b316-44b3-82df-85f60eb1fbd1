﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="ACCOUNTING.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <connectionStrings />
    <system.diagnostics>
        <sources>
            <!-- This section defines the logging configuration for My.Application.Log -->
            <source name="DefaultSource" switchName="DefaultSwitch">
                <listeners>
                    <add name="FileLog" />
                    <!-- Uncomment the below section to write to the Application Event Log -->
                    <!--<add name="EventLog"/>-->
                </listeners>
            </source>
        </sources>
        <switches>
            <add name="DefaultSwitch" value="Information" />
        </switches>
        <sharedListeners>
            <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter" />
            <!-- Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log -->
            <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
        </sharedListeners>
    </system.diagnostics>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="EnvDTE" publicKeyToken="B03F5F7F11D50A3A" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Abstractions" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Identity.Client" publicKeyToken="0a613f4dd989e8ae" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.69.1.0" newVersion="4.69.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.1.1.3" newVersion="4.1.1.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.InteropServices.RuntimeInformation" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="CrystalDecisions.CrystalReports.Engine" publicKeyToken="692fbea5521e1304" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.2000.0" newVersion="13.0.2000.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="CrystalDecisions.Shared" publicKeyToken="692fbea5521e1304" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.4000.0" newVersion="13.0.4000.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="CrystalDecisions.ReportSource" publicKeyToken="692fbea5521e1304" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.2000.0" newVersion="13.0.2000.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.ClientModel" publicKeyToken="92742159e12e44c8" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.3.0.0" newVersion="1.3.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory.Data" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Azure.Core" publicKeyToken="92742159e12e44c8" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.45.0.0" newVersion="1.45.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Identity.Client.Extensions.Msal" publicKeyToken="0a613f4dd989e8ae" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.69.1.0" newVersion="4.69.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.0.4.0" newVersion="3.0.4.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Caching.Memory" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Azure.Identity" publicKeyToken="92742159e12e44c8" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.13.2.0" newVersion="1.13.2.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Caching.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Protocols.OpenIdConnect" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Protocols" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.JsonWebTokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Security.Cryptography.ProtectedData" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Security.AccessControl" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.1" newVersion="6.0.0.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.TimeProvider" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.3" newVersion="9.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Data.SqlClient" publicKeyToken="23ec7fc2d6eaa4a5" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.1.5.0" newVersion="4.1.5.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" /></startup><userSettings>
  <ACCOUNTING.My.MySettings>
   <setting name="server" serializeAs="String">
    <value />
   </setting>
   <setting name="database" serializeAs="String">
    <value />
   </setting>
   <setting name="model" serializeAs="String">
    <value>SQL</value>
   </setting>
   <setting name="id" serializeAs="String">
    <value />
   </setting>
   <setting name="passowrd" serializeAs="String">
    <value />
   </setting>
   <setting name="nameuser" serializeAs="String">
    <value />
   </setting>
   <setting name="LastUsername" serializeAs="String">
    <value />
   </setting>
   <setting name="LastPassword" serializeAs="String">
    <value />
   </setting>
   <setting name="LastMachineId" serializeAs="String">
    <value />
   </setting>
   <setting name="LastWindowsUser" serializeAs="String">
    <value />
   </setting>
   <setting name="RememberPassword" serializeAs="String">
    <value />
   </setting>
   <setting name="MachineActivated" serializeAs="String">
    <value />
   </setting>
   <setting name="ActivationCode" serializeAs="String">
    <value />
   </setting>
   <setting name="LicenseExpiryDate" serializeAs="String">
    <value />
   </setting>
   <setting name="MachineFingerprint" serializeAs="String">
    <value />
   </setting>
   <setting name="LicenseActivationDate" serializeAs="String">
    <value />
   </setting>
   <setting name="RememberMe" serializeAs="String">
    <value>False</value>
   </setting>
  </ACCOUNTING.My.MySettings>
 </userSettings>
</configuration>
