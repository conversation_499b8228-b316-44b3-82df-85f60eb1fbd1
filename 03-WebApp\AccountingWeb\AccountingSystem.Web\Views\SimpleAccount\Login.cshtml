@model AccountingSystem.Web.Models.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = "_LoginLayout";
}

<div class="login-container">
    <div class="login-card">
        <!-- Logo Section -->
        <div class="logo-section">
            <div class="logo-container">
                <img src="~/images/Alsultan-logo.jpg" alt="Alsultan Solutions" class="company-logo" />
                <div class="company-name">
                    <h2>حلول السلطان</h2>
                    <p>Alsultan Solutions</p>
                </div>
            </div>
        </div>

        <!-- Login Form -->
        <div class="login-form-section">
            <h3 class="login-title">تسجيل الدخول</h3>
            
            <form asp-action="Login" asp-controller="SimpleAccount" method="post" class="login-form">
                <div asp-validation-summary="All" class="alert alert-danger" role="alert"></div>
                
                <div class="form-group">
                    <label asp-for="Username" class="form-label">اسم المستخدم</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input asp-for="Username" class="form-control" placeholder="أدخل اسم المستخدم" autocomplete="username" />
                    </div>
                    <span asp-validation-for="Username" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <label asp-for="Password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input asp-for="Password" class="form-control" placeholder="أدخل كلمة المرور" autocomplete="current-password" />
                        <button type="button" class="btn btn-outline-secondary toggle-password" tabindex="-1">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <span asp-validation-for="Password" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input asp-for="RememberMe" class="form-check-input" />
                        <label asp-for="RememberMe" class="form-check-label">تذكر كلمة المرور</label>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt"></i>
                        دخول
                    </button>
                    <button type="button" class="btn btn-secondary btn-cancel" onclick="window.close();">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>

            <!-- Connection Settings -->
            <div class="connection-settings">
                <button type="button" class="btn btn-link btn-sm" data-bs-toggle="modal" data-bs-target="#connectionModal">
                    <i class="fas fa-cog"></i>
                    تغيير الاتصال
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Connection Settings Modal -->
<div class="modal fade" id="connectionModal" tabindex="-1" aria-labelledby="connectionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="connectionModalLabel">إعدادات الاتصال</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>لتغيير إعدادات الاتصال بقاعدة البيانات، يرجى الاتصال بمدير النظام.</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    يتم تكوين إعدادات الاتصال من خلال ملف التكوين الخاص بالخادم.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('.toggle-password').click(function() {
                const passwordInput = $(this).siblings('input[type="password"], input[type="text"]');
                const icon = $(this).find('i');
                
                if (passwordInput.attr('type') === 'password') {
                    passwordInput.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordInput.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Focus on username field
            $('#Username').focus();

            // Handle Enter key
            $('.login-form input').keypress(function(e) {
                if (e.which === 13) { // Enter key
                    $('.btn-login').click();
                }
            });

            // Validate username input (letters only, like in VB.NET version)
            $('#Username').on('input', function() {
                let value = $(this).val();
                let filteredValue = value.replace(/[^a-zA-Z]/g, '');
                if (value !== filteredValue) {
                    $(this).val(filteredValue);
                }
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });
    </script>
}

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }

    .login-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        width: 100%;
        max-width: 450px;
        animation: slideUp 0.5s ease-out;
    }

    @@keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .logo-section {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        padding: 30px 20px;
        text-align: center;
        color: white;
    }

    .logo-container {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .company-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 15px;
        border-radius: 50%;
        border: 3px solid rgba(255, 255, 255, 0.2);
    }

    .company-name h2 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 600;
    }

    .company-name p {
        margin: 5px 0 0 0;
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .login-form-section {
        padding: 40px 30px 30px;
    }

    .login-title {
        text-align: center;
        margin-bottom: 30px;
        color: #2c3e50;
        font-weight: 600;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 500;
        color: #2c3e50;
        margin-bottom: 8px;
        display: block;
    }

    .input-group {
        position: relative;
    }

    .input-group-text {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-left: none;
        color: #6c757d;
    }

    .form-control {
        border-right: none;
        padding: 12px 15px;
        font-size: 14px;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .toggle-password {
        border-left: none;
        background: #f8f9fa;
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .form-actions {
        display: flex;
        gap: 10px;
        margin-top: 30px;
    }

    .btn-login {
        flex: 2;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px 20px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-cancel {
        flex: 1;
        padding: 12px 20px;
        border-radius: 8px;
    }

    .connection-settings {
        text-align: center;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #dee2e6;
    }

    .text-danger {
        font-size: 0.875rem;
        margin-top: 5px;
    }

    .alert {
        border-radius: 8px;
        margin-bottom: 20px;
    }

    /* RTL Support */
    [dir="rtl"] .input-group-text {
        border-left: 1px solid #dee2e6;
        border-right: none;
    }

    [dir="rtl"] .form-control {
        border-left: none;
        border-right: 1px solid #dee2e6;
    }
</style>
