-- Debug script to check invoice 89 items and their descriptions
-- This will help identify why item descriptions are missing in thermal receipt

-- Check invoice 89 details
SELECT 
    'Invoice Header' as Section,
    TrxNo,
    TrxDate,
    Store,
    Cashier,
    PartnerName,
    TrxTotal,
    TrxNetAmount,
    ReadyForUse
FROM tblStockMovHeader 
WHERE TrxNo = 89 AND TrxType = 'مبيعات';

-- Check invoice 89 items
SELECT 
    'Invoice Items' as Section,
    DocNo,
    LineSN,
    ItemNo,
    TrxQTY,
    UnitPrice,
    LineAmount,
    UofM
FROM tblStockMovement 
WHERE DocNo = 89 AND TrxType = 'مبيعات'
ORDER BY LineSN;

-- Check item descriptions for items in invoice 89
SELECT 
    'Item Descriptions' as Section,
    i.ItemNo,
    i.ItemDescription,
    i.ItemDescription2,
    CASE 
        WHEN i.ItemDescription2 IS NOT NULL AND LTRIM(RTRIM(i.ItemDescription2)) != '' 
        THEN i.ItemDescription2 
        ELSE i.ItemDescription 
    END as FinalDescription,
    i.Status,
    i.Barcode
FROM tblItems i
WHERE i.ItemNo IN (
    SELECT DISTINCT ItemNo 
    FROM tblStockMovement 
    WHERE DocNo = 89 AND TrxType = 'مبيعات'
)
ORDER BY i.ItemNo;

-- Check if there are any items with missing descriptions
SELECT 
    'Missing Descriptions' as Section,
    i.ItemNo,
    i.ItemDescription,
    i.ItemDescription2,
    CASE 
        WHEN (i.ItemDescription IS NULL OR LTRIM(RTRIM(i.ItemDescription)) = '') 
         AND (i.ItemDescription2 IS NULL OR LTRIM(RTRIM(i.ItemDescription2)) = '')
        THEN 'BOTH DESCRIPTIONS MISSING'
        WHEN i.ItemDescription IS NULL OR LTRIM(RTRIM(i.ItemDescription)) = ''
        THEN 'PRIMARY DESCRIPTION MISSING'
        WHEN i.ItemDescription2 IS NULL OR LTRIM(RTRIM(i.ItemDescription2)) = ''
        THEN 'SECONDARY DESCRIPTION MISSING'
        ELSE 'DESCRIPTIONS OK'
    END as DescriptionStatus
FROM tblItems i
WHERE i.ItemNo IN (
    SELECT DISTINCT ItemNo 
    FROM tblStockMovement 
    WHERE DocNo = 89 AND TrxType = 'مبيعات'
)
ORDER BY i.ItemNo;

-- Compare with a working invoice (let's check invoice 88)
SELECT 
    'Working Invoice 88 Items' as Section,
    DocNo,
    LineSN,
    ItemNo,
    TrxQTY,
    UnitPrice,
    LineAmount
FROM tblStockMovement 
WHERE DocNo = 88 AND TrxType = 'مبيعات'
ORDER BY LineSN;

-- Check item descriptions for working invoice 88
SELECT 
    'Working Invoice 88 Item Descriptions' as Section,
    i.ItemNo,
    i.ItemDescription,
    i.ItemDescription2,
    CASE 
        WHEN i.ItemDescription2 IS NOT NULL AND LTRIM(RTRIM(i.ItemDescription2)) != '' 
        THEN i.ItemDescription2 
        ELSE i.ItemDescription 
    END as FinalDescription
FROM tblItems i
WHERE i.ItemNo IN (
    SELECT DISTINCT ItemNo 
    FROM tblStockMovement 
    WHERE DocNo = 88 AND TrxType = 'مبيعات'
)
ORDER BY i.ItemNo;
