﻿Imports System.Data.SqlClient
Imports System.Drawing.Printing
Imports CrystalDecisions.CrystalReports.Engine
Imports QRCoder

Public Class frmPOS

    ' === Session/User ===
    Public Property SessionID As Long
    'Public Property StoreName As String
    Public Property DefaultCustomerID As Long
    Dim ForItemChange As Integer = 0

    ' === POS Fields ===
    '
    Dim ForClose As Integer = 0
    Dim LineSN As Integer = 1
    Dim QRImage As Byte()
    Dim PrintCart As DataGridView
    Dim WithEvents POSPrinter As New PrintDocument()
    Dim discountAmount As Decimal
    Dim discountpercent As Decimal


    Dim PrintOption, ReferenceMandatory, DefaultPrinter, MandatoryCustomerVATReg, PriceIncludeVATDef, NonVATInvoiceDef, PaymentType As String

    'Sub btnsize_Click(sender As Object, e As EventArgs) Handles btnSize.Click
    '    Dim width As Integer = Me.Width
    '    Dim height As Integer = Me.Height
    '    MessageBox.Show($"Current form size: Width={width}, Height={height}", "Form Size")
    'End Sub
    ' === Load ===
    Private Sub frmPOS_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InvNo = 0
        CustomerLoad()
        LoadSessionInfo()
        LoadFavoriteItems()
        LoadSettings()
        txtBarcode.Focus()
        GetSerial()
        StoresLoad()
        CashierLoad()
        ForceGregorianForAllPickers(Me)
        Me.Size = New Size(frmWidth, frmHeight)

    End Sub
    Sub CashierLoad()
        Dim CMD As New SqlCommand("Select CashierAccount from tblPOSCashier where Store = '" & cmbxStore.Text & "'", Con)
        OpenConnection()
        Dim rdr = CMD.ExecuteReader
        If rdr.Read() Then
            Cashier = rdr("CashierAccount").ToString()
        Else
            Cashier = ""
        End If
        If rdr.IsClosed Then
        Else
            rdr.Close()
        End If
        CloseConnection()

    End Sub



    Sub LoadFavoriteItems()
        Dim dt As New DataTable()
        Dim cmd As New SqlClient.SqlCommand("
        SELECT T.ItemNo, T.ItemDescription 
        FROM tblUserPOSItems U 
        INNER JOIN tblItems T ON U.ItemNo = T.ItemNo 
        WHERE U.Username = @User", New SqlClient.SqlConnection(ConStr))
        cmd.Parameters.AddWithValue("@User", UserName)
        Dim da As New SqlClient.SqlDataAdapter(cmd)
        da.Fill(dt)

        LoadItemButtons(pnlFavorites, dt)
    End Sub
    Sub StoresLoad()
        Dim CMD As New SqlCommand("Select SN,Store from tblStores Order by Store", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxStore.Items.Clear()
        Do While reader.Read
            cmbxStore.Items.Add(reader.Item(1).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Private Sub DGVCart_CellBeginEdit(sender As Object, e As DataGridViewCellCancelEventArgs) Handles DGVCart.CellBeginEdit
        If e.ColumnIndex = 5 Then ' سعر الوحدة
            If ChangeInvoicePrice <> 1 Then
                e.Cancel = True
            End If
        End If
    End Sub



    Private Sub DGVCart_CellValidating(sender As Object, e As DataGridViewCellValidatingEventArgs) Handles DGVCart.CellValidating
        If e.ColumnIndex = 3 Then ' الكمية
            If Not IsNumeric(e.FormattedValue) OrElse Val(e.FormattedValue) <= 0 Then
                MessageBox.Show("الكمية يجب أن تكون أكبر من صفر", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                e.Cancel = True
            End If
        End If
    End Sub

    Private Sub btnCustomerSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCustomerSearch.Click
        CustomerSearchForm = "frmPOS"
        frmCustomerSearch.ShowDialog()
    End Sub

    Sub CustomerLoad()
        Dim CMD As New SqlCommand("Select AccountCode,AccountName from tbl_Acc_Accounts where ParentAccountCode = (Select AccountNo from tblGLConfig where EntryReferenceModule = 'عملاء') order by AccountCode", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountName + ' - ' + CONVERT(AccountCode, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxPartnerNo.DataSource = dt
        cmbxPartnerNo.DisplayMember = "DisplayText" ' Show Name
        cmbxPartnerNo.ValueMember = "AccountCode" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Sub LoadSessionInfo()
        Dim cmd As New SqlCommand("SELECT DefaultStore,DefaultCustomer,MaxDiscountPercent,ChangeInvoicePrice FROM tblusers where Username = @Username", Con)
        cmd.Parameters.AddWithValue("@Username", UserName)
        OpenConnection()
        Dim rdr = cmd.ExecuteReader
        If rdr.Read() Then
            Store = rdr("DefaultStore").ToString()
            If Not IsDBNull(rdr("DefaultCustomer")) Then
                DefaultCustomerID = Val(rdr("DefaultCustomer"))
                btnCustomerSearch.Visible = False
                cmbxPartnerNo.Enabled = False
            Else
                DefaultCustomerID = 0
                btnCustomerSearch.Visible = True
                cmbxPartnerNo.Enabled = True
            End If

            If Not IsDBNull(rdr("MaxDiscountPercent")) Then
                MaxDiscountPercent = Convert.ToDecimal(rdr("MaxDiscountPercent"))
            Else
                MaxDiscountPercent = 0
            End If
            cmbxStore.Text = Store
            cmbxPartnerNo.SelectedValue = DefaultCustomerID
            If Not IsDBNull("ChangeInvoicePrice") Then
                ChangeInvoicePrice = Val(rdr("ChangeInvoicePrice").ToString)
            Else
                ChangeInvoicePrice = 0
            End If

            If UserName = "admin" Then MaxDiscountPercent = 99.99

            If Store <> "" Then
                cmbxStore.Enabled = False
            Else
                cmbxStore.Enabled = True
            End If

        End If
        rdr.Close()
        CloseConnection()
    End Sub

    Sub LoadSettings()
        Dim CMD As New SqlCommand("Select * from tblToolsInvoice where InvoiceType = 'مبيعات'", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        If reader.Read Then

            PriceIncludeVATDef = Trim(reader.Item(13).ToString)
            DefaultPrinter = Trim(reader.Item(6).ToString)


        End If
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If


    End Sub
    Private Sub LoadItemButtons(panel As Control, itemSource As DataTable)
        panel.Controls.Clear()

        For Each row As DataRow In itemSource.Rows
            Dim btn As New Button()
            btn.Text = row("ItemNo").ToString() & " " & row("ItemDescription").ToString()
            btn.Tag = row("ItemNo")
            btn.Width = 120
            btn.Height = 40
            btn.BackColor = Color.LightSkyBlue
            btn.TextAlign = ContentAlignment.MiddleCenter

            ' Ensure each button is clickable
            AddHandler btn.Click, AddressOf ItemButton_Click

            panel.Controls.Add(btn)
        Next
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Dim keyword As String = txtSearch.Text.Trim()
        If keyword = "" Then
            LoadFavoriteItems() ' Reload favorites
            Return
        End If

        Dim dt As New DataTable()
        Dim cmd As New SqlClient.SqlCommand("
        SELECT ItemNo, ItemDescription 
        FROM tblItems 
        WHERE ItemDescription LIKE @kw OR CAST(ItemNo AS NVARCHAR) LIKE @kw", New SqlClient.SqlConnection(ConStr))
        cmd.Parameters.AddWithValue("@kw", "%" & keyword & "%")
        Dim da As New SqlClient.SqlDataAdapter(cmd)
        da.Fill(dt)

        LoadItemButtons(pnlFavorites, dt)
    End Sub

    Private Sub ItemButton_Click(sender As Object, e As EventArgs)
        Dim btn As Button = CType(sender, Button)
        Dim itemNo As String = btn.Tag.ToString()
        AddItemToCart(itemNo, 1, 0, 0)
    End Sub



    Private Sub txtDiscount_TextChanged(sender As Object, e As EventArgs) Handles txtDiscount.TextChanged
        UpdateTotals()
    End Sub


    ' === Barcode ===
    Private Sub txtBarcode_KeyDown(sender As Object, e As KeyEventArgs) Handles txtBarcode.KeyDown
        If e.KeyCode = Keys.Enter And txtBarcode.Text.Trim <> "" Then
            ProcessScannedBarcode(txtBarcode.Text.Trim)
            txtBarcode.Clear()
        End If
    End Sub


    Private Sub ProcessScannedBarcode(barcode As String)

        ' 1. محاولة التعرف على باركود الوزن المدمج من الإعدادات
        Dim format As String = "", divisor As Integer = 1000
        Dim cmd As New SqlCommand("SELECT EmbeddedFormat, WeightDivisor FROM tblBarcodeSettings WHERE Shop = @Shop", Con)
        cmd.Parameters.AddWithValue("@Shop", Store)

        OpenConnection()
        Dim rdr = cmd.ExecuteReader()
        If rdr.Read() Then
            format = rdr("EmbeddedFormat").ToString()
            divisor = Val(rdr("WeightDivisor"))
        End If
        rdr.Close()
        CloseConnection()

        ' إذا تم العثور على تنسيق مدمج نحاول استخراجه
        If Not String.IsNullOrEmpty(format) Then
            Dim itemLen = format.Count(Function(c) c = "x"c)
            Dim weightLen = format.Count(Function(c) c = "w"c)

            If barcode.Length >= itemLen + weightLen AndAlso barcode.StartsWith("27") Then
                Dim itemCode = barcode.Substring(0, itemLen)
                Dim weightVal As Decimal = Val(barcode.Substring(itemLen, weightLen)) / divisor
                AddItemToCart(itemCode, weightVal, 0, 0)
                Exit Sub
            End If
        End If

        ''''''If Not String.IsNullOrEmpty(format) Then
        ''''''    Dim itemLen = format.Count(Function(c) c = "x"c)
        ''''''    Dim weightLen = format.Count(Function(c) c = "w"c)
        ''''''    Dim expectedLen = itemLen + weightLen

        ''''''    ' Only treat as embedded barcode if it starts with "27" and has exact length
        ''''''    If barcode.StartsWith("27") AndAlso barcode.Length = expectedLen Then
        ''''''        Dim itemCode = barcode.Substring(0, itemLen)
        ''''''        Dim weightVal As Decimal = Val(barcode.Substring(itemLen, weightLen)) / divisor
        ''''''        AddItemToCart(itemCode, weightVal, 0, 0)
        ''''''        Exit Sub
        ''''''    End If
        ''''''End If


        ' 2. محاولة البحث بالباركود أو رقم الصنف - مع معالجة النوع
        cmd = New SqlCommand("
        SELECT ItemNo 
        FROM tblItems 
        WHERE Barcode = @BC 
           OR (ISNUMERIC(@BC) = 1 AND ItemNo = TRY_CAST(@BC AS BIGINT))
    ", Con)
        cmd.Parameters.AddWithValue("@BC", barcode)

        OpenConnection()
        Dim result = cmd.ExecuteScalar()
        CloseConnection()

        If result IsNot Nothing Then
            AddItemToCart(result.ToString(), 1, 0, 0)
            Exit Sub
        End If

        ' 3. البحث الجزئي في وصف الصنف
        Dim dt As New DataTable
        cmd = New SqlCommand("SELECT ItemNo, ItemDescription FROM tblItems WHERE ItemDescription LIKE @Desc", Con)
        cmd.Parameters.AddWithValue("@Desc", "%" & barcode & "%")
        Dim da As New SqlClient.SqlDataAdapter(cmd)
        da.Fill(dt)

        If dt.Rows.Count = 1 Then
            AddItemToCart(dt.Rows(0)("ItemNo").ToString(), 1, 0, 0)
        ElseIf dt.Rows.Count > 1 Then
            Dim frm As New frmSelectItem()
            frm.LoadItems(barcode)
            If frm.ShowDialog() = DialogResult.OK Then
                AddItemToCart(frm.SelectedItemNo, 1, 0, 0)
            End If
        Else
            MsgBox("لم يتم العثور على صنف مطابق للباركود أو الاسم.", MsgBoxStyle.Exclamation)
        End If


    End Sub

    Private Sub AddItemToCart(itemCode As String, qty As Decimal, unitPrice As Decimal, LSN As Integer)
        If Val(itemCode) = 0 OrElse Val(qty) = 0 Then Exit Sub

        Dim itemQuery As String = "SELECT ItemNo, UnitSalesPrice, SalesUofM,ItemDescription FROM tblItems WHERE ItemNo = @ItemNo OR Barcode = @ItemNo"
        Using conn As New SqlConnection(ConStr)
            Using cmd As New SqlCommand(itemQuery, conn)
                cmd.Parameters.AddWithValue("@ItemNo", itemCode)
                conn.Open()
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    If reader.Read() Then


                        ItemNo = reader("ItemNo").ToString()

                    Else
                        MessageBox.Show("لم يتم العثور على الصنف المرتبط بالباركود.")
                    End If
                End Using
            End Using
        End Using

        Dim isInsert As Boolean = (unitPrice = 0 AndAlso LSN = 0)

        Dim itemDetails = GetItemDetails(itemCode)
        'Dim itemNo As String = itemCode
        Dim itemDescription As String = itemDetails.Description
        Dim inputUofM As String = itemDetails.UoM

        ' Generate unit price and lineSN if new insert
        If isInsert Then
            unitPrice = itemDetails.Price
            GetLineSerial()
        Else
            LineSN = LSN
        End If

        Dim vatPercentage As Decimal = 0
        Dim vatAmount As Decimal = 0
        Dim lineTotal As Decimal = 0
        Dim isVatIncluded As Boolean = PriceIncludeVATDef

        ' === Get VAT percentage ===
        Dim vatCmd As New SqlCommand("SELECT ISNULL(Tax_Percent, 0) FROM tblItems WHERE ItemNo = @ItemNo", Con)
        vatCmd.Parameters.AddWithValue("@ItemNo", ItemNo)
        OpenConnection()
        vatPercentage = Convert.ToDecimal(vatCmd.ExecuteScalar())
        CloseConnection()

        ' === VAT & Line Total calculation ===
        If isVatIncluded Then
            vatAmount = Math.Round((unitPrice * qty * vatPercentage) / (100 + vatPercentage), 2)
            lineTotal = unitPrice * qty
        Else
            vatAmount = Math.Round((unitPrice * qty * vatPercentage) / 100, 2)
            lineTotal = (unitPrice * qty) + vatAmount
        End If

        ' === Ensure invoice number ===
        If InvNo = 0 Then GetSerial()

        ' === Check Stock ===
        If CheckStockAvailabilityByUnit(ItemNo, Store, qty, inputUofM) = 0 Then
            MessageBox.Show("لايوجد رصيد متاح", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        ' === Check if this line already exists ===
        Dim CheckCMD As New SqlCommand("SELECT 1 FROM tblStockMovement WHERE DocNo = @DocNo AND TrxType = 'مبيعات' AND LineSN = @LineSN", Con)
        CheckCMD.Parameters.AddWithValue("@DocNo", Val(InvNo))
        CheckCMD.Parameters.AddWithValue("@LineSN", Val(LineSN))
        OpenConnection()
        Dim exists As Boolean = CheckCMD.ExecuteScalar() IsNot Nothing
        CloseConnection()

        If exists Then
            ' === Update existing line ===
            Dim UpdateCMD As New SqlCommand("
            UPDATE tblStockMovement 
            SET UofMConversion = @Conv, UofM = @UofM, TrxQTY = @Qty, UnitPrice = @Price,
                ItemNo = @ItemNo, ModifiedOn = GetDate(), ModifiedBy = @User, 
                Store = @Store, VATAmount = @VAT, LineAmount = @LineAmount
            WHERE DocNo = @DocNo AND TrxType = 'مبيعات' AND LineSN = @LineSN", Con)

            With UpdateCMD.Parameters
                .AddWithValue("@Conv", GetConversionFactorToBaseUofM(ItemNo, inputUofM))
                .AddWithValue("@UofM", inputUofM)
                .AddWithValue("@Qty", qty)
                .AddWithValue("@Price", unitPrice)
                .AddWithValue("@ItemNo", ItemNo)
                .AddWithValue("@User", UserName)
                .AddWithValue("@Store", Trim(cmbxStore.Text))
                .AddWithValue("@VAT", vatAmount)
                .AddWithValue("@LineAmount", lineTotal)
                .AddWithValue("@DocNo", Val(InvNo))
                .AddWithValue("@LineSN", Val(LineSN))
            End With

            OpenConnection()
            UpdateCMD.ExecuteNonQuery()
            CloseConnection()
        Else
            ' === Insert new line ===
            Dim InsertCMD As New SqlCommand("
            INSERT INTO tblStockMovement 
            (DocNo, ItemNo, LineSN, TrxType, TrxDate, TrxQTY, UnitPrice, CreatedBy, CreatedOn, Store, UofM, UofMConversion, VATAmount, LineAmount) 
            VALUES 
            (@DocNo, @ItemNo, @LineSN, 'مبيعات', GetDate(), @Qty, @Price, @User, GetDate(), @Store, @UofM, @Conv, @VAT, @LineAmount)", Con)

            With InsertCMD.Parameters
                .AddWithValue("@DocNo", Val(InvNo))
                .AddWithValue("@ItemNo", ItemNo)
                .AddWithValue("@LineSN", Val(LineSN))
                .AddWithValue("@Qty", qty)
                .AddWithValue("@Price", unitPrice)
                .AddWithValue("@User", UserName)
                .AddWithValue("@Store", Trim(cmbxStore.Text))
                .AddWithValue("@UofM", inputUofM)
                .AddWithValue("@Conv", GetConversionFactorToBaseUofM(ItemNo, inputUofM))
                .AddWithValue("@VAT", vatAmount)
                .AddWithValue("@LineAmount", lineTotal)
            End With

            OpenConnection()
            InsertCMD.ExecuteNonQuery()
            CloseConnection()
        End If

        RefreshGrid()
        ForItemChange = 1
        SaveTrx()
        UpdateTotals()
        ForItemChange = 0
        txtBarcode.Clear()
        GetLineSerial()
    End Sub

    Sub GetLineSerial()
        Try
            Dim SelectCMD As New SqlCommand("Select Max(LineSN) + 1 as SN from tblStockMovement where TrxType = 'مبيعات' and DocNo = " & Val(InvNo) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                If reader.Item("SN").ToString <> "" Then
                    LineSN = Val(reader.Item("SN").ToString)
                Else
                    LineSN = 1
                End If
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Sub ClearFields()
        lblInvoiceNo.Text = ""
        lblItemCount.Text = ""
        lblQty.Text = ""
        lblInvoiceAmount.Text = ""
        lblVATAmount.Text = ""
        DGVCart.DataSource = ""
        InvNo = 0
        LineSN = 0
        GetSerial()
        txtBarcode.Clear()
        txtBarcode.Focus()
        txtDiscount.Text = "0"
        lblDiscountAmount.Text = "0.00"
        txtSearch.Clear()
        LoadFavoriteItems()
        txtCash.Text = "0.00"
        txtCard.Text = "0.00"
        txtCustomerName.Clear()
        txtCustomerPhone.Clear()
        txtCustomerName.Text = cmbxPartnerNo.SelectedText
    End Sub

    Private Sub cmbxstore_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbxStore.SelectedIndexChanged
        Store = cmbxStore.Text
        CashierLoad()
    End Sub
    ' === Add Item ===




    Sub RefreshGrid()
        Dim ds As DataSet = New DataSet
        Dim FillCMD As New SqlCommand("SELECT tblStockMovement.LineSN AS م, tblStockMovement.ItemNo AS [رقم الصنف], tblItems.ItemDescription AS [وصف الصنف], tblStockMovement.TrxQTY AS الكمية,tblItems.UofM AS [الوحدة], tblStockMovement.UnitPrice AS [سعر الوحدة],  FORMAT(tblStockMovement.TrxQTY * tblStockMovement.UnitPrice, 'N2') AS [السعر الإجمالي] FROM tblStockMovement INNER JOIN tblItems ON tblStockMovement.ItemNo = tblItems.ItemNo WHERE (tblStockMovement.DocNo = " & Val(InvNo) & ") AND (tblStockMovement.TrxType = 'مبيعات') ORDER BY م", Con)
        Dim da As New SqlDataAdapter(FillCMD)
        ds.Clear()
        da.Fill(ds)
        DGVCart.DataSource = ds.Tables(0)

        For Each row As DataGridViewRow In DGVCart.Rows
            row.Cells(3).Tag = row.Cells(3).Value ' الكمية
            row.Cells(5).Tag = row.Cells(5).Value ' سعر الوحدة
        Next
    End Sub

    Sub SaveTrx()
        If Val(InvNo) <> 0 And Val(lblInvoiceAmount.Text) <> 0 And Val(lblItemCount.Text) <> 0 Then
            If Trim(cmbxStore.Text) = "" Then
                MsgBox("يجب إدخال المخزن للاستمرار", MsgBoxStyle.Critical, "نظام السلطان")
                Exit Sub
            End If
            InvNoForPrint = InvNo


            sellername = StoreName
            vatregistration = VATReg
            timestamp = Format(DateAndTime.Now, "yyyy-MM-dd HH:mm:ss")
            invoiceamount = Val(lblInvoiceAmount.Text)
            vatAmount = Val(lblVATAmount.Text)


            Dim saudiConvertion = New SaudiConvertion()
            Dim QRString = saudiConvertion.GetData()
            Dim gen As New QRCodeGenerator
            Dim qrCodeData = gen.CreateQrCode(QRString, QRCodeGenerator.ECCLevel.Q)
            Dim code As New QRCode(qrCodeData)
            Dim qrCodeImage As Bitmap = New Bitmap(code.GetGraphic(20))
            Dim imageData As Byte() = ImageToByteArray(qrCodeImage)
            '=========================================================================

            QRImage = GenerateQRCode(StoreName, VATReg, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), lblInvoiceAmount.Text, lblVATAmount.Text)




            'Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set ReferenceInvoice='',TrxDate=GetDate(),PartnerNo=" & Val(DefaultCustomerID) & ",PaymentMethod='" & Trim("نقدي") & "',PartnerReference='',TrxNote = '',TrxVAT = " & Val(lblVATAmount.Text) & ",TrxTotal=" & Val(lblInvoiceAmount.Text - lblVATAmount.Text) & ",TrxDiscount =" & Convert.ToDecimal(txtDiscount.Text) & " ,TrxDiscountValue = " & Convert.ToDecimal(lblDiscountAmount.Text) & ",TrxNetAmount = " & Val(lblInvoiceAmount.Text) & ",ModifiedBy='" & UserName & "',ModifiedOn='" & Format(DateAndTime.Now, "yyyy/MM/dd hh:mm:ss tt") & "',VOIDSTTS=0,ReadyForUse='False',QRCodeImage = @QRCodeImage,Store= '" & Trim(cmbxStore.Text) & "',Cashier = " & Cashier & ",PartnerPhoneNo='" & txtCustomerPhone.Text.Trim & "',PartnerName='" & txtCustomerName.Text.Trim & "' where TrxNo = " & Val(InvNo) & " and TrxType='مبيعات'", Con)
            'UpdateCMD.Parameters.AddWithValue("@QRCodeImage", imageData)
            Dim UpdateCMD As New SqlCommand("
            UPDATE tblStockMovHeader 
            SET ReferenceInvoice = '',
                TrxDate = GETDATE(),
                PartnerNo = @PartnerNo,
                PaymentMethod = @PaymentMethod,
                PartnerReference = '',
                TrxNote = '',
                TrxVAT = @TrxVAT,
                TrxTotal = @TrxTotal,
                TrxDiscount = @TrxDiscount,
                TrxDiscountValue = @TrxDiscountValue,
                TrxNetAmount = @TrxNetAmount,
                ModifiedBy = @ModifiedBy,
                ModifiedOn = GETDATE(),
                VOIDSTTS = 0,
                ReadyForUse = 'False',
                QRCodeImage = @QRCodeImage,
                Store = @Store,
                Cashier = @Cashier,
                PartnerPhoneNo = @PartnerPhoneNo,
                PartnerName = @PartnerName
            WHERE TrxNo = @TrxNo AND TrxType = 'مبيعات'", Con)

            ' Add parameters properly
            With UpdateCMD.Parameters
                .AddWithValue("@PartnerNo", Val(DefaultCustomerID))
                .AddWithValue("@PaymentMethod", "نقدي")
                .AddWithValue("@TrxVAT", Val(lblVATAmount.Text))
                .AddWithValue("@TrxTotal", Val(lblInvoiceAmount.Text) - Val(lblVATAmount.Text))
                .AddWithValue("@TrxDiscount", Convert.ToDecimal(txtDiscount.Text))
                .AddWithValue("@TrxDiscountValue", Convert.ToDecimal(lblDiscountAmount.Text))
                .AddWithValue("@TrxNetAmount", Val(lblInvoiceAmount.Text))
                .AddWithValue("@ModifiedBy", UserName)
                .AddWithValue("@QRCodeImage", imageData)
                .AddWithValue("@Store", Trim(cmbxStore.Text))
                .AddWithValue("@Cashier", Cashier)
                .AddWithValue("@PartnerPhoneNo", txtCustomerPhone.Text.Trim)
                .AddWithValue("@PartnerName", txtCustomerName.Text.Trim)
                .AddWithValue("@TrxNo", Val(InvNo))
            End With

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If

            If ForClose = 1 Then
                Me.Close()
            ElseIf ForItemChange <> 1 Then
                MsgBox("تم حفظ الفاتورة بنجاح", MsgBoxStyle.Information, "نظام السلطان")
                PrintType = "POSInvoice"
                LoadSettings()
                'ClearFields()
                If PrintOption = 0 Then
                    'frmPrintPreview.MdiParent = frmMain
                    'frmPrintPreview.Show()
                    frmPrintPreview.ShowDialog()
                ElseIf PrintOption = 1 Then
                    If MsgBox("هل ترغب في طباعة الفاتورة؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
                        'frmPrintPreview.MdiParent = frmMain
                        'frmPrintPreview.Show()
                        frmPrintPreview.ShowDialog()
                    End If
                End If
            End If

        End If
    End Sub

    Private Sub DGVCart_CellEndEdit(sender As Object, e As DataGridViewCellEventArgs) Handles DGVCart.CellEndEdit
        Try
            Dim row = DGVCart.Rows(e.RowIndex)

            Dim itemNo As String = row.Cells(1).Value.ToString()       ' ItemNo
            Dim qty As Decimal = Val(row.Cells(3).Value)               ' Quantity (الكمية)
            Dim unitPrice As Decimal = Val(row.Cells(5).Value)         ' Unit Price (سعر الوحدة)
            Dim lineSN As Integer = Val(row.Cells(0).Value)            ' LineSN (م)

            Dim oldQty As Decimal = If(row.Cells(3).Tag IsNot Nothing, Val(row.Cells(3).Tag), qty)
            Dim oldPrice As Decimal = If(row.Cells(5).Tag IsNot Nothing, Val(row.Cells(5).Tag), unitPrice)

            If e.ColumnIndex = 3 AndAlso qty <> oldQty Then
                ' Quantity changed
                AddItemToCart(itemNo, qty, unitPrice, lineSN)
                UpdateTotals()
                row.Cells(3).Tag = qty

            ElseIf e.ColumnIndex = 5 AndAlso unitPrice <> oldPrice Then
                ' Unit price changed
                AddItemToCart(itemNo, qty, unitPrice, lineSN)
                UpdateTotals()
                row.Cells(5).Tag = unitPrice
            End If

        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء حفظ التعديل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub


    Sub UpdateTotals()
        Dim grossTotal As Decimal = 0
        Dim qty As Decimal = 0

        ' حساب إجمالي السعر الإجمالي (قبل الخصم)
        For Each r As DataGridViewRow In DGVCart.Rows
            If Not r.IsNewRow Then
                grossTotal += r.Cells("السعر الإجمالي").Value
                qty += Val(r.Cells("الكمية").Value)
            End If
        Next

        ' التحقق من الخصم
        Dim discountPercent As Decimal = 0
        If Decimal.TryParse(txtDiscount.Text, discountPercent) Then
            If discountPercent > MaxDiscountPercent Then
                MessageBox.Show("الخصم يتجاوز الحد المسموح به (" & MaxDiscountPercent & "%)", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                discountPercent = MaxDiscountPercent
                txtDiscount.Text = MaxDiscountPercent.ToString("0.##")
            End If
        Else
            discountPercent = 0
            txtDiscount.Text = "0"
        End If

        ' حساب مبلغ الخصم
        Dim discountAmount As Decimal = Math.Round((discountPercent / 100) * grossTotal, 2)
        Dim totalAfterDiscount As Decimal = grossTotal - discountAmount

        ' حساب ضريبة القيمة المضافة
        Dim vatAmount As Decimal
        If PriceIncludeVATDef = "True" Then
            vatAmount = Math.Round((15 / 115) * totalAfterDiscount, 2, MidpointRounding.AwayFromZero)
            lblInvoiceAmount.Text = totalAfterDiscount.ToString
        Else
            'vatAmount = Math.Round((totalAfterDiscount * 15) / 100, 2, MidpointRounding.AwayFromZero)
            vatAmount = Math.Round(totalAfterDiscount * 0.15D, 2, MidpointRounding.AwayFromZero)
            lblInvoiceAmount.Text = totalAfterDiscount + vatAmount
        End If

        ' عرض النتائج
        lblDiscountAmount.Text = discountAmount.ToString("0.00")

        If (txtCash.Text + txtCard.Text) <> lblInvoiceAmount.Text Then
            txtCash.Text = lblInvoiceAmount.Text
        End If
        lblVATAmount.Text = vatAmount.ToString
        lblQty.Text = qty
        lblItemCount.Text = DGVCart.Rows.Count

        ' لحساب إجمالي المبيعات قبل الخصم لاستخدامه في القيد المحاسبي
        grossAmountBeforeDiscount = GetGrossAmountBeforeDiscount(DGVCart, PriceIncludeVATDef)
    End Sub

    Function GetGrossAmountBeforeDiscount(dgv As DataGridView, isPriceIncludesVAT As Boolean) As Decimal
        Dim total As Decimal = 0

        For Each row As DataGridViewRow In dgv.Rows
            If Not row.IsNewRow Then
                Dim unitPrice As Decimal = Val(row.Cells("سعر الوحدة").Value)
                Dim qty As Decimal = Val(row.Cells("الكمية").Value)
                Dim lineAmount As Decimal = unitPrice * qty

                total += lineAmount
            End If
        Next

        ' إذا كانت الأسعار تشمل الضريبة، نستخرج الأساس قبل الضريبة
        'If isPriceIncludesVAT Then
        '    total = Math.Round(total / 1.15D, 2, MidpointRounding.AwayFromZero)
        'End If

        Return total
    End Function

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click

        If DGVCart.SelectedRows.Count = 1 Then
            If Val(DGVCart.SelectedRows(0).Cells(0).Value.ToString()) <> 0 And Val(lblInvoiceNo.Text) <> 0 Then
                InvNo = Val(lblInvoiceNo.Text)
                If MsgBox("هل ترغب بحذف السطر بالتأكيد ؟", MsgBoxStyle.YesNoCancel, "نظام السلطان") = MsgBoxResult.Yes Then
                    Dim DeleteCMD As New SqlCommand("Delete tblStockMovement where TrxType = 'مبيعات' and DocNo = " & Val(InvNo) & " and LineSN = " & Val(DGVCart.SelectedRows(0).Cells(0).Value.ToString()) & " ", Con)

                    Dim Delete3CMD As New SqlCommand("Update tblStockMovement set LineSN = LineSN - 1 where LineSN > " & Val(DGVCart.SelectedRows(0).Cells(0).Value.ToString()) & " and TrxType = 'مبيعات' and DocNo = " & Val(InvNo) & "", Con)

                    If Con.State <> ConnectionState.Open Then
                        Con.Open()
                    End If
                    DeleteCMD.ExecuteNonQuery()
                    Delete3CMD.ExecuteNonQuery()
                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                    RefreshGrid()
                    UpdateTotals()
                    ForItemChange = 1
                    SaveTrx()
                End If
            End If
        End If


    End Sub

    ' === Save Invoice ===
    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If DGVCart.Rows.Count = 0 Then Exit Sub
        If cmbxPartnerNo.SelectedValue = 0 Then
            MessageBox.Show("يجب اختيار عميل قبل حفظ الفاتورة.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        'Dim payForm As New frmPaySplit With {
        '    .InvoiceTotal = Val(lblInvoiceAmount.Text),
        '    .CallerForm = "POS"
        '}
        'If payForm.ShowDialog() <> DialogResult.OK Then Exit Sub
        UpdateTotals()
        ForItemChange = 0
        InvNoForPrint = InvNo
        SaveTrx()
        If Val(txtCash.Text) > 0 Then SavePaymentLine(InvNo, 1, Val(txtCash.Text))
        If Val(txtCard.Text) > 0 Then SavePaymentLine(InvNo, 2, Val(txtCard.Text))

        ' GL Entry

        Dim netAmount As Decimal = Convert.ToDecimal(lblInvoiceAmount.Text)
        Dim discountAmount As Decimal = Convert.ToDecimal(lblDiscountAmount.Text)
        'Dim grossAmountBeforeDiscount As Decimal = netAmount + discountAmount

        Call UpdateTotals()
        Try
            Using Con As New SqlConnection(ConStr)
                Using cmd As New SqlCommand("sp_CreateSalesInvoiceEntryFromView", Con)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@InvoiceNo", InvNo)

                    If Con.State <> ConnectionState.Open Then
                        Con.Open()
                    End If
                    cmd.ExecuteNonQuery()
                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ أثناء ترحيل الفاتورة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End Try
        Try
            Using Con As New SqlConnection(ConStr)
                Using cmd As New SqlCommand("sp_CreatePaymentEntryFromView", Con)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@InvoiceNo", InvNo)

                    If Con.State <> ConnectionState.Open Then
                        Con.Open()
                    End If
                    cmd.ExecuteNonQuery()
                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ أثناء ترحيل دفعة الفاتورة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End Try

        Try
            Using Con As New SqlConnection(ConStr)
                Using cmd As New SqlCommand("sp_CreateCOGSEntryFromView", Con)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@InvoiceNo", InvNo)

                    If Con.State <> ConnectionState.Open Then
                        Con.Open()
                    End If
                    cmd.ExecuteNonQuery()
                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ أثناء ترحيل تكلفة البيع: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End Try



        ' Print
        'PrintReceipt()

        ' Clear
        ClearFields()

    End Sub
    Private Function GetItemDetails(itemCode As String) As (Price As Decimal, UoM As String, Description As String)
        Dim price As Decimal = 0
        Dim uom As String = ""
        Dim desc As String = ""

        Dim cmd As New SqlCommand("SELECT UnitSalesPrice, SalesUofM, ItemDescription FROM tblItems WHERE ItemNo = @ItemNo OR Barcode = @ItemNo", Con)
        cmd.Parameters.AddWithValue("@ItemNo", itemCode)

        OpenConnection()
        Using rdr As SqlDataReader = cmd.ExecuteReader()
            If rdr.Read() Then
                price = If(IsDBNull(rdr("UnitSalesPrice")), 0, Convert.ToDecimal(rdr("UnitSalesPrice")))
                uom = If(IsDBNull(rdr("SalesUofM")), "", rdr("SalesUofM").ToString())
                desc = If(IsDBNull(rdr("ItemDescription")), "", rdr("ItemDescription").ToString())
            End If
        End Using
        CloseConnection()

        Return (price, uom, desc)
    End Function



    Private Sub ExecuteCommand(cmd As SqlCommand)
        Try
            If Con.State <> ConnectionState.Open Then Con.Open()
            cmd.Connection = Con
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            MessageBox.Show("Database error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            If Con.State = ConnectionState.Open Then Con.Close()
        End Try
    End Sub

    Private Function ExecuteScalar(cmd As SqlCommand) As Object
        Try
            If Con.State <> ConnectionState.Open Then Con.Open()
            cmd.Connection = Con
            Return cmd.ExecuteScalar()
        Catch ex As Exception
            MessageBox.Show("Scalar execution error: " & ex.Message)
            Return Nothing
        Finally
            If Con.State = ConnectionState.Open Then Con.Close()
        End Try
    End Function



    Sub SavePaymentLine(trxNo As Long, methodID As Long, amount As Decimal)
        Dim cmd As New SqlCommand("INSERT INTO tblPayMethodTrx (TrxNo, Pay_mthd, Pay_amnt, CreatedBy, CreatedOn) VALUES (@TrxNo, @Mthd, @Amt, @User, GETDATE())", Con)
        cmd.Parameters.AddWithValue("@TrxNo", InvNo)
        cmd.Parameters.AddWithValue("@Mthd", methodID)
        cmd.Parameters.AddWithValue("@Amt", amount)
        cmd.Parameters.AddWithValue("@User", UserName)
        ExecuteCommand(cmd)
    End Sub

    Sub GetSerial()
        Try
            Dim PreUsedCMD As New SqlCommand("SELECT MIN(TrxNo) as SN FROM tblStockMovHeader WHERE ReadyForUse = 'True' AND TrxType = 'مبيعات'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = PreUsedCMD.ExecuteReader()
            If reader.Read Then
                InvNo = Val(reader.Item(0).ToString)
            Else
                InvNo = 0
            End If
            If Not reader.IsClosed Then
                reader.Close()
            End If

            If InvNo = 0 Then
                Dim SelectCMD As New SqlCommand("SELECT (MAX(TrxNo)) + 1 as SN FROM tblStockMovHeader WHERE TrxType = 'مبيعات'", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                Dim reader2 As SqlDataReader = SelectCMD.ExecuteReader()
                If reader2.Read Then
                    If Not IsDBNull(reader2.Item("SN")) AndAlso reader2.Item("SN").ToString <> "" Then
                        InvNo = Val(reader2.Item("SN").ToString)
                    Else
                        InvNo = 1
                    End If
                End If
                If Not reader2.IsClosed Then
                    reader2.Close()
                End If
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If

                ' Use parameterized insert
                Dim InsertCMD As New SqlCommand("INSERT INTO tblStockMovHeader (TrxType, TrxNo, ReadyForUse, CreatedBy, CreatedOn) VALUES (@TrxType, @TrxNo, @ReadyForUse, @CreatedBy, GETDATE())", Con)
                With InsertCMD.Parameters
                    .AddWithValue("@TrxType", "مبيعات")
                    .AddWithValue("@TrxNo", Val(InvNo))
                    .AddWithValue("@ReadyForUse", "False")
                    .AddWithValue("@CreatedBy", UserName)
                End With

                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                InsertCMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            Else
                Dim UpdateCMD As New SqlCommand("UPDATE tblStockMovHeader SET ReadyForUse = 'False' WHERE TrxNo = @TrxNo AND TrxType = 'مبيعات'", Con)
                UpdateCMD.Parameters.AddWithValue("@TrxNo", Val(InvNo))

                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                UpdateCMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If
            lblInvoiceNo.Text = InvNo
        Catch ex As Exception
            MessageBox.Show("Error in GetSerial: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End Try
    End Sub

    'Sub GetSerial()
    '    'Dim cmd As New SqlCommand("Select (Min(TrxNo)) as SN from tblStockMovHeader where ReadyForUse = 'True' and TrxType = 'مبيعات'", Con)
    '    'Return ExecuteScalar(cmd)

    '    Try
    '        Dim PreUsedCMD As New SqlCommand("Select (Min(TrxNo)) as SN from tblStockMovHeader where ReadyForUse = 'True' and TrxType = 'مبيعات'", Con)
    '        If Con.State <> ConnectionState.Open Then
    '            Con.Open()
    '        End If
    '        Dim reader As SqlDataReader = PreUsedCMD.ExecuteReader()
    '        If reader.Read Then
    '            InvNo = Val(reader.Item(0).ToString)
    '        Else
    '            InvNo = 0
    '        End If
    '        If reader.IsClosed Then
    '        Else
    '            reader.Close()
    '        End If
    '        If InvNo = 0 Then
    '            Dim SelectCMD As New SqlCommand("Select (Max(TrxNo)) + 1 as SN from tblStockMovHeader where TrxType = 'مبيعات'", Con)
    '            If Con.State <> ConnectionState.Open Then
    '                Con.Open()
    '            End If
    '            Dim reader2 As SqlDataReader = SelectCMD.ExecuteReader()
    '            If reader2.Read Then
    '                If reader2.Item("SN").ToString <> "" Then
    '                    InvNo = Val(reader2.Item("SN").ToString)
    '                Else
    '                    InvNo = 1
    '                End If
    '            End If
    '            If Con.State <> ConnectionState.Closed Then
    '                Con.Close()
    '            End If
    '            Dim InsertCMD As New SqlCommand("Insert into tblStockMovHeader (TrxType,TrxNo,ReadyForUse,CreatedBy,CreatedOn) values ('مبيعات'," & Val(InvNo) & ",'False','" & UserName & "','" & Format(DateAndTime.Now, "yyyy/MM/dd hh:mm:ss tt") & "')", Con)
    '            If Con.State <> ConnectionState.Open Then
    '                Con.Open()
    '            End If
    '            InsertCMD.ExecuteNonQuery()
    '            If Con.State <> ConnectionState.Closed Then
    '                Con.Close()
    '            End If
    '        Else
    '            Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set ReadyForUse = 'False' where TrxNo = " & Val(InvNo) & " and TrxType = 'مبيعات' ", Con)
    '            If Con.State <> ConnectionState.Open Then
    '                Con.Open()
    '            End If
    '            UpdateCMD.ExecuteNonQuery()
    '            If Con.State <> ConnectionState.Closed Then
    '                Con.Close()
    '            End If
    '        End If
    '        lblInvoiceNo.Text = InvNo
    '    Catch ex As Exception

    '    End Try
    'End Sub


    ' === QR ===
    Function GenerateQRCode(name As String, vat As String, dt As String, total As String, vatVal As String) As Byte()
        sellername = name
        vatregistration = vat
        timestamp = dt
        invoiceamount = total
        vatAmount = vatVal


        Dim saudiConvertion = New SaudiConvertion()
        Dim qrText = saudiConvertion.GetData()
        Dim gen = New QRCodeGenerator()
        Dim data = gen.CreateQrCode(qrText, QRCodeGenerator.ECCLevel.Q)
        Dim bmp = New QRCode(data).GetGraphic(4)
        Dim ms As New IO.MemoryStream()
        bmp.Save(ms, Imaging.ImageFormat.Png)
        Return ms.ToArray()
    End Function

    ' === Printing ===
    Sub PrintReceipt()
        'PrintCart = DGVCart
        'POSPrinter.DefaultPageSettings.PaperSize = New PaperSize("POS", 280, 600)
        'POSPrinter.Print()

        PrintType = "POSInvoice"

        If PrintOption = 0 Then
            frmPrintPreview.MdiParent = frmMain
            frmPrintPreview.Show()
        ElseIf PrintOption = 1 Then
            If MsgBox("هل ترغب في طباعة الفاتورة؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
                frmPrintPreview.MdiParent = frmMain
                frmPrintPreview.Show()
            End If
        End If
    End Sub

    Private Sub POSPrinter_PrintPage(sender As Object, e As PrintPageEventArgs) Handles POSPrinter.PrintPage
        Dim g = e.Graphics
        Dim y = 10
        Dim font = New Font("Tahoma", 8)
        Dim bold = New Font("Tahoma", 9, FontStyle.Bold)

        g.DrawString("فاتورة مبيعات", bold, Brushes.Black, 5, y) : y += 20
        g.DrawString("رقم: " & InvNo, font, Brushes.Black, 5, y) : y += 15
        g.DrawString("الفرع: " & Store, font, Brushes.Black, 5, y) : y += 15
        g.DrawString("الوقت: " & Now.ToString("yyyy/MM/dd HH:mm"), font, Brushes.Black, 5, y) : y += 20

        For Each row As DataGridViewRow In PrintCart.Rows
            g.DrawString(row.Cells("Description").Value, font, Brushes.Black, 5, y)
            g.DrawString("×" & row.Cells("QTY").Value.ToString(), font, Brushes.Black, 130, y)
            g.DrawString(row.Cells("Total").Value.ToString(), font, Brushes.Black, 200, y)
            y += 15
        Next
        g.DrawString("الإجمالي: " & lblInvoiceAmount.Text & " ريال", bold, Brushes.Black, 5, y)
    End Sub

    Private Sub frmPOS_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        InvNo = Val(lblInvoiceNo.Text)
        If InvNo <> 0 Then
            Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set ReadyForUse = 'True',TrxVAT=0, TrxTotal=0, TrxDiscount=0, TrxDiscountValue=0 where TrxNo = " & Val(InvNo) & " and  TrxType ='مبيعات'", Con)
            Con.Close()
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            Dim DeleteCMD As New SqlCommand("Delete tblStockMovement where TrxType = 'مبيعات' and DocNo = " & Val(InvNo) & "", Con)
            Dim Delete2CMD As New SqlCommand("Update tblStockTrx set OutInvoiceType= NULL, OutInvoiceNo= NULL,OutInvoiceItemSN = NULL, OutCreatedBy = NULL,OutCreatedOn = NULL,UnitPrice=0,InStock = 1 where OutInvoiceType= 'مبيعات' and OutInvoiceNo = " & Val(InvNo) & " and InStock = 0", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            DeleteCMD.ExecuteNonQuery()
            Delete2CMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End If


    End Sub
    Private Sub btnSplit_Click(sender As Object, e As EventArgs) Handles btnSplit.Click
        If Val(lblInvoiceAmount.Text) <> 0 Then
            txtCash.Text = Val(lblInvoiceAmount.Text) / 2
            txtCard.Text = Val(lblInvoiceAmount.Text) / 2
        End If
    End Sub



    Private Sub btnCash_Click(sender As Object, e As EventArgs) Handles btnCash.Click
        If Val(lblInvoiceAmount.Text) <> 0 Then
            txtCash.Text = Val(lblInvoiceAmount.Text)
            txtCard.Text = 0
        End If
    End Sub
    Private Sub btnCard_Click(sender As Object, e As EventArgs) Handles btnCard.Click
        If Val(lblInvoiceAmount.Text) <> 0 Then
            txtCash.Text = 0
            txtCard.Text = Val(lblInvoiceAmount.Text)
        End If
    End Sub


    Private Sub txtCash_textChange(sender As Object, e As EventArgs) Handles txtCash.TextChanged
        Try
            Dim total As Decimal = Convert.ToDecimal(lblInvoiceAmount.Text)
            Dim amt1 As Decimal = 0
            Decimal.TryParse(txtCash.Text, amt1)

            If amt1 < total Then

                txtCard.Text = (total - amt1).ToString()
            Else

                txtCard.Text = 0
            End If
        Catch ex As Exception

        End Try

    End Sub

    Private Sub txtCard_textChange(sender As Object, e As EventArgs) Handles txtCard.TextChanged
        Try
            Dim total As Decimal = Convert.ToDecimal(lblInvoiceAmount.Text)
            Dim amt1 As Decimal = 0
            Decimal.TryParse(txtCard.Text, amt1)

            If amt1 < total Then

                txtCash.Text = (total - amt1).ToString()
            Else

                txtCash.Text = 0
            End If
        Catch ex As Exception

        End Try

    End Sub

    'Private Sub txtCash_GotFocus(sender As Object, e As EventArgs) Handles txtCash.GotFocus
    '    txtCash.SelectAll()
    'End Sub
    Private Sub txtCard_GotFocus(sender As Object, e As EventArgs) Handles txtCard.GotFocus
        txtCard.SelectAll()
    End Sub

    Private Sub txtCash_Enter(sender As Object, e As EventArgs) Handles txtCash.Enter
        txtCash.SelectAll()
    End Sub

    Private Sub cmbxPartnerNo_SelectedValueChanged(sender As Object, e As EventArgs) Handles cmbxPartnerNo.SelectedValueChanged
        Try
            If cmbxPartnerNo.SelectedValue <> 0 Then
                DefaultCustomerID = cmbxPartnerNo.SelectedValue.ToString()
                txtCustomerName.Text = cmbxPartnerNo.Text.Trim
                txtCustomerPhone.Clear()
            Else
                DefaultCustomerID = 0
            End If

        Catch ex As Exception
            Exit Sub
        End Try

    End Sub

    Private Sub txtCustomerPhone_KeyDown(sender As Object, e As KeyEventArgs) Handles txtCustomerPhone.KeyDown
        If e.KeyCode = Keys.Enter Then
            If txtCustomerPhone.TextLength = 10 Then
                Try
                    Dim cmd As New SqlCommand("Select TOP 1 PartnerName from tblStockMovHeader where PartnerPhoneNo = @PartnerPhoneNo and TrxType = 'مبيعات' order by TrxNo desc", Con)
                    cmd.Parameters.AddWithValue("@PartnerPhoneNo", txtCustomerPhone.Text)
                    OpenConnection()
                    Dim rdr = cmd.ExecuteReader()
                    If rdr.Read() Then
                        txtCustomerName.Text = rdr("PartnerName").ToString()
                    Else
                        txtCustomerName.Clear()
                    End If
                    rdr.Close()
                    CloseConnection()
                Catch ex As Exception

                End Try

            Else
                MessageBox.Show("تحقق من رقم الجوال", "خطأ في رقم الجوال", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        End If
    End Sub
End Class
