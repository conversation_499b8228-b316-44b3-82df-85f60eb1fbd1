-- =================================================================
-- Web Authorization Data Seeding Script
-- Created: October 2025
-- Purpose: Populate WebFormMenus and WebQuickActions tables with initial data
--          based on existing sidebar menu structure
-- =================================================================

USE [SULTDB] -- Using the actual database name
GO

-- =================================================================
-- 1. Seed WebFormMenus - Main Menu Items and Sub-Items
-- =================================================================

PRINT 'Seeding WebFormMenus...'

-- Main Dashboard
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES (N'الصفحة الرئيسية', N'Dashboard', '/SimpleDashboard/Index', 'fas fa-home', NULL, 1, N'Dashboard', 0, 1)

-- Sales Section (Container)
DECLARE @SalesContainerId INT
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES (N'المبيعات', N'Sales', '#', 'fas fa-shopping-cart', NULL, 2, N'Sales', 1, 1)
SET @SalesContainerId = SCOPE_IDENTITY()

-- Sales Sub-Items
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES 
    (N'فواتير المبيعات', N'Sales Invoices', '/Sales/Invoice', 'fas fa-file-invoice', @SalesContainerId, 1, N'Sales', 0, 1),
    (N'نقاط البيع', N'POS', '/POS/Index', 'fas fa-cash-register', @SalesContainerId, 2, N'Sales', 0, 1),
    (N'جلسات نقاط البيع', N'POS Sessions', '/POSSessions/Index', 'fas fa-clock', @SalesContainerId, 3, N'Sales', 0, 1),
    (N'مرتجع المبيعات', N'Sales Return', '/Sales/Return', 'fas fa-undo', @SalesContainerId, 4, N'Sales', 0, 1),
    (N'بحث المبيعات', N'Sales Search', '/Sales/Search', 'fas fa-search', @SalesContainerId, 5, N'Sales', 0, 1)

-- Purchase Section (Container)
DECLARE @PurchaseContainerId INT
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES (N'المشتريات', N'Purchases', '#', 'fas fa-shopping-cart', NULL, 3, N'Purchase', 1, 1)
SET @PurchaseContainerId = SCOPE_IDENTITY()

-- Purchase Sub-Items
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES 
    (N'إنشاء فاتورة مشتريات', N'Create Purchase Invoice', '/Purchase/Create', 'fas fa-plus', @PurchaseContainerId, 1, N'Purchase', 0, 1),
    (N'عرض فواتير المشتريات', N'View Purchase Invoices', '/Purchase/Index', 'fas fa-list', @PurchaseContainerId, 2, N'Purchase', 0, 1),
    (N'مرتجع المشتريات', N'Purchase Return', '/Purchase/Return', 'fas fa-undo', @PurchaseContainerId, 3, N'Purchase', 0, 1)

-- Cash Movements Section (Container)
DECLARE @CashContainerId INT
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES (N'الحركات النقدية', N'Cash Movements', '#', 'fas fa-money-bill-wave', NULL, 4, N'Cash', 1, 1)
SET @CashContainerId = SCOPE_IDENTITY()

-- Cash Sub-Items
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES 
    (N'سندات القبض', N'Receipt Vouchers', '/Cash/Receipt', 'fas fa-hand-holding-usd', @CashContainerId, 1, N'Cash', 0, 1),
    (N'سندات الصرف', N'Payment Vouchers', '/Cash/Payment', 'fas fa-money-bill-wave', @CashContainerId, 2, N'Cash', 0, 1),
    (N'القيود اليومية', N'Journal Entries', '/Journal/Entry', 'fas fa-book', @CashContainerId, 3, N'Cash', 0, 1),
    (N'المصروفات', N'Expenses', '/Expenses/Create', 'fas fa-file-invoice-dollar', @CashContainerId, 4, N'Cash', 0, 1)

-- Master Data Section (Container)
DECLARE @MasterDataContainerId INT
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES (N'البيانات الأساسية', N'Master Data', '#', 'fas fa-database', NULL, 5, N'MasterData', 1, 1)
SET @MasterDataContainerId = SCOPE_IDENTITY()

-- Master Data Sub-Items
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES 
    (N'العملاء', N'Customers', '/Customers/Index', 'fas fa-users', @MasterDataContainerId, 1, N'MasterData', 0, 1),
    (N'الموردين', N'Vendors', '/Vendors/Index', 'fas fa-truck', @MasterDataContainerId, 2, N'MasterData', 0, 1),
    (N'الأصناف', N'Items', '/Items/Index', 'fas fa-boxes', @MasterDataContainerId, 3, N'MasterData', 0, 1),
    (N'تصنيفات الأصناف', N'Item Categories', '/ItemsCategories', 'fas fa-sitemap', @MasterDataContainerId, 4, N'MasterData', 0, 1),
    (N'الموظفين', N'Employees', '/Employees/Index', 'fas fa-users', @MasterDataContainerId, 5, N'MasterData', 0, 1),
    (N'دليل الحسابات', N'Chart of Accounts', '/ChartOfAccounts/Index', 'fas fa-sitemap', @MasterDataContainerId, 6, N'MasterData', 0, 1),
    (N'إدارة المتاجر', N'Stores Management', '/StoresV2/Index', 'fas fa-store', @MasterDataContainerId, 7, N'MasterData', 0, 1),
    (N'إدارة المستودعات', N'Warehouses Management', '/Warehouses/Index', 'fas fa-warehouse', @MasterDataContainerId, 8, N'MasterData', 0, 1)

-- Reports Section (Container)
DECLARE @ReportsContainerId INT
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES (N'التقارير', N'Reports', '#', 'fas fa-chart-line', NULL, 6, N'Reports', 1, 1)
SET @ReportsContainerId = SCOPE_IDENTITY()

-- Reports Sub-Items
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES 
    (N'التقارير المالية', N'Financial Reports', '/Reports/Financial', 'fas fa-chart-line', @ReportsContainerId, 1, N'Reports', 0, 1),
    (N'تقارير المخزون', N'Inventory Reports', '/Reports/Inventory', 'fas fa-warehouse', @ReportsContainerId, 2, N'Reports', 0, 1)

-- Settings Section (Container)
DECLARE @SettingsContainerId INT
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES (N'الإعدادات', N'Settings', '#', 'fas fa-cogs', NULL, 7, N'Settings', 1, 1)
SET @SettingsContainerId = SCOPE_IDENTITY()

-- Settings Sub-Items
INSERT INTO [dbo].[WebFormMenus] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ParentMenuId], [DisplayOrder], [ModuleName], [IsContainer], [IsActive])
VALUES 
    (N'إدارة المستخدمين', N'User Management', '/UserManagement/Index', 'fas fa-user-cog', @SettingsContainerId, 1, N'Settings', 0, 1),
    (N'صلاحيات المجموعات (VB.NET)', N'Group Permissions (VB.NET)', '/GroupPermissions/Index', 'fas fa-shield-alt', @SettingsContainerId, 2, N'Settings', 0, 1),
    (N'صلاحيات النظام الإلكتروني', N'Web Authorization', '/WebAuthorization/Index', 'fas fa-shield-alt', @SettingsContainerId, 3, N'Settings', 0, 1),
    (N'ربط الحسابات', N'GL Configuration', '/GLConfig/Index', 'fas fa-link', @SettingsContainerId, 4, N'Settings', 0, 1),
    (N'إعدادات الفواتير', N'Invoice Settings', '/InvoiceToolSettings', 'fas fa-tools', @SettingsContainerId, 5, N'Settings', 0, 1),
    (N'إعدادات النظام', N'System Settings', '/SystemSetup/Index', 'fas fa-cogs', @SettingsContainerId, 6, N'Settings', 0, 1),
    (N'إعدادات الباركود', N'Barcode Settings', '/BarcodeSettings/Index', 'fas fa-barcode', @SettingsContainerId, 7, N'Settings', 0, 1)

PRINT 'WebFormMenus seeded successfully'
GO

-- =================================================================
-- 2. Seed WebQuickActions - Dashboard Quick Action Buttons
-- =================================================================

PRINT 'Seeding WebQuickActions...'

INSERT INTO [dbo].[WebQuickActions] ([DisplayName], [DisplayNameEn], [Route], [Icon], [ColorClass], [DisplayOrder], [Category], [IsActive])
VALUES 
    (N'نقطة بيع جديدة', N'New POS Session', '/POS/Index', 'fas fa-cash-register', 'primary', 1, N'Sales', 1),
    (N'فاتورة مبيعات', N'Sales Invoice', '/Sales/Invoice', 'fas fa-file-invoice', 'success', 2, N'Sales', 1),
    (N'فاتورة مشتريات', N'Purchase Invoice', '/Purchase/Create', 'fas fa-shopping-cart', 'info', 3, N'Purchase', 1),
    (N'سند قبض', N'Receipt Voucher', '/Cash/Receipt', 'fas fa-hand-holding-usd', 'warning', 4, N'Cash', 1),
    (N'سند صرف', N'Payment Voucher', '/Cash/Payment', 'fas fa-money-bill-wave', 'danger', 5, N'Cash', 1),
    (N'إضافة عميل', N'Add Customer', '/Customers/Create', 'fas fa-user-plus', 'secondary', 6, N'MasterData', 1),
    (N'إضافة صنف', N'Add Item', '/Items/Create', 'fas fa-box', 'dark', 7, N'MasterData', 1),
    (N'التقارير المالية', N'Financial Reports', '/Reports/Financial', 'fas fa-chart-line', 'primary', 8, N'Reports', 1)

PRINT 'WebQuickActions seeded successfully'
GO

-- =================================================================
-- 3. Grant Full Permissions to Admin Group (GroupID = 1)
-- =================================================================

PRINT 'Granting full permissions to Admin group...'

-- Grant all menu permissions to Admin group
INSERT INTO [dbo].[WebGroupMenuPermissions] ([GroupID], [MenuId], [CanView], [CanAdd], [CanEdit], [CanDelete], [CanPrint], [CreatedBy])
SELECT 1, [MenuID], 1, 1, 1, 1, 1, 'System'
FROM [dbo].[WebFormMenus]
WHERE NOT EXISTS (
    SELECT 1 FROM [dbo].[WebGroupMenuPermissions] 
    WHERE [GroupID] = 1 AND [MenuId] = [WebFormMenus].[MenuID]
)

-- Grant all quick actions to Admin group
INSERT INTO [dbo].[WebGroupQuickActionPermissions] ([GroupID], [QuickActionId], [IsVisible], [CreatedBy])
SELECT 1, [QuickActionID], 1, 'System'
FROM [dbo].[WebQuickActions]
WHERE NOT EXISTS (
    SELECT 1 FROM [dbo].[WebGroupQuickActionPermissions] 
    WHERE [GroupID] = 1 AND [QuickActionId] = [WebQuickActions].[QuickActionID]
)

PRINT 'Admin group permissions granted successfully'
GO

PRINT ''
PRINT '========================================='
PRINT 'Web Authorization Data Seeded Successfully!'
PRINT 'Total Menu Items: ' + CAST((SELECT COUNT(*) FROM [dbo].[WebFormMenus]) AS VARCHAR)
PRINT 'Total Quick Actions: ' + CAST((SELECT COUNT(*) FROM [dbo].[WebQuickActions]) AS VARCHAR)
PRINT '========================================='
GO

-- =================================================================
-- 4. View Current Data (Optional)
-- =================================================================

-- View all menu items with hierarchy
SELECT 
    m.[MenuID],
    m.[DisplayName],
    m.[Route],
    m.[ModuleName],
    m.[IsContainer],
    m.[ParentMenuId],
    p.[DisplayName] AS ParentMenuName
FROM [dbo].[WebFormMenus] m
LEFT JOIN [dbo].[WebFormMenus] p ON m.[ParentMenuId] = p.[MenuID]
ORDER BY m.[DisplayOrder], m.[MenuID]

-- View all quick actions
SELECT 
    [QuickActionID],
    [DisplayName],
    [Route],
    [Icon],
    [ColorClass],
    [Category]
FROM [dbo].[WebQuickActions]
ORDER BY [DisplayOrder]

-- View admin permissions
SELECT 
    'Menu Permissions' AS PermissionType,
    COUNT(*) AS Count
FROM [dbo].[WebGroupMenuPermissions]
WHERE [GroupID] = 1
UNION ALL
SELECT 
    'Quick Action Permissions' AS PermissionType,
    COUNT(*) AS Count
FROM [dbo].[WebGroupQuickActionPermissions]
WHERE [GroupID] = 1
GO

