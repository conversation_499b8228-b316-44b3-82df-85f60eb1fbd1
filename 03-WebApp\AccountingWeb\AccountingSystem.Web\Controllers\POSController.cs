using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Web.Models;
using AccountingSystem.Models;
using System.Security.Claims;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class POSController : Controller
    {
        private readonly IPOSService _posService;
        private readonly ILogger<POSController> _logger;
        private readonly IGlobalConfigurationService _configService;

        public POSController(IPOSService posService, ILogger<POSController> logger, IGlobalConfigurationService configService)
        {
            _posService = posService;
            _logger = logger;
            _configService = configService;
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult Test()
        {
            var isAuthenticated = User.Identity?.IsAuthenticated ?? false;
            var username = User.Identity?.Name ?? "Not authenticated";
            return Content($"POS Controller is working! User: {username}, Authenticated: {isAuthenticated}");
        }

        public async Task<IActionResult> Index()
        {
            _logger.LogInformation("POS Index action called for user: {Username}", User.Identity?.Name);

            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                _logger.LogInformation("Processing POS Index for username: {Username}", username);

                // Get user authorization information
                var userAuth = await _posService.GetUserAuthorizationAsync(username);
                var currentInvoice = await _posService.GetCurrentInvoiceAsync();
                var stores = await _posService.GetStoresAsync();
                var customers = await _posService.GetCustomersAsync();
                var favoriteItems = await _posService.GetFavoriteItemsAsync(username);
                var settings = await _posService.GetPOSSettingsAsync();

                // Determine default store and customer
                string defaultStore = userAuth.DefaultStore ?? stores.FirstOrDefault()?.StoreName ?? "";
                long? defaultCustomerId = userAuth.DefaultCustomer;

                // Check session validation for the default store
                bool hasActiveSession = await _posService.ValidateActiveSessionAsync(username, defaultStore);
                POSSession? activeSession = await _posService.GetActiveSessionAsync(username, defaultStore);

                // If no active session for default store, check if user can change stores
                if (!hasActiveSession && userAuth.StoreChange)
                {
                    // Try to find any store with active session
                    foreach (var store in stores)
                    {
                        hasActiveSession = await _posService.ValidateActiveSessionAsync(username, store.StoreName);
                        if (hasActiveSession)
                        {
                            defaultStore = store.StoreName;
                            activeSession = await _posService.GetActiveSessionAsync(username, store.StoreName);
                            break;
                        }
                    }
                }

                // Get invoice items
                var invoiceItems = currentInvoice != null ? await _posService.GetInvoiceItemsAsync(currentInvoice.TrxNo) : new List<POSInvoiceItem>();

                // Get customer information
                string customerName = "";
                string customerPhone = "";
                long? customerId = null;

                if (currentInvoice != null && currentInvoice.PartnerNo.HasValue)
                {
                    var customer = customers.FirstOrDefault(c => c.AccountCode == currentInvoice.PartnerNo.Value.ToString());
                    if (customer != null)
                    {
                        customerName = customer.AccountName ?? "";
                        customerPhone = currentInvoice.PartnerPhoneNo ?? "";
                        customerId = currentInvoice.PartnerNo.Value;
                    }
                }
                else if (defaultCustomerId.HasValue)
                {
                    // Set default customer if no customer is selected
                    var defaultCustomer = customers.FirstOrDefault(c => c.AccountCode == defaultCustomerId.Value.ToString());
                    if (defaultCustomer != null)
                    {
                        customerName = defaultCustomer.AccountName ?? "";
                        customerId = defaultCustomerId.Value;
                    }
                }

                // Fetch cashier account for the selected store
                var cashierAccount = await _posService.GetCashierAccountForStoreAsync(defaultStore);
                bool hasCashier = cashierAccount.HasValue;

                var viewModel = new POSViewModel
                {
                    InvoiceNo = currentInvoice != null ? currentInvoice.TrxNo : 0,
                    Store = currentInvoice?.Store ?? defaultStore,
                    Cashier = currentInvoice?.Cashier ?? username,
                    CustomerName = customerName,
                    CustomerPhone = customerPhone,
                    CustomerId = customerId,
                    TotalAmount = currentInvoice?.TrxTotal ?? 0,
                    VATAmount = currentInvoice?.TrxVAT ?? 0,
                    DiscountAmount = currentInvoice?.TrxDiscountValue ?? 0,
                    NetAmount = currentInvoice?.TrxNetAmount ?? 0,
                    ItemCount = invoiceItems.Count,
                    TotalQuantity = invoiceItems.Sum(i => i.TrxQTY),
                    Items = invoiceItems.Select(i => new POSInvoiceItemViewModel
                    {
                        LineSN = i.LineSN,
                        ItemNo = i.ItemNo,
                        ItemDescription = _posService.GetItemDescriptionAsync(i.ItemNo).GetAwaiter().GetResult(),
                        Quantity = i.TrxQTY,
                        UofM = i.UofM ?? "",
                        UnitPrice = i.UnitPrice,
                        LineAmount = i.LineAmount,
                        VATAmount = i.VATAmount
                    }).ToList(),
                    FavoriteItems = favoriteItems,
                    Stores = stores,
                    Customers = customers,
                    Settings = settings,
                    HasActiveSession = hasActiveSession,
                    ActiveSession = activeSession,
                    // User authorization properties
                    CanChangeCustomer = userAuth.IsAdmin || userAuth.CustomerChange,
                    CanChangeStore = userAuth.IsAdmin || userAuth.StoreChange,
                    CanChangePrice = userAuth.IsAdmin || userAuth.ChangeInvoicePrice,
                    MaxDiscountPercent = userAuth.MaxDiscountPercent ?? 0,
                    DefaultCustomerId = defaultCustomerId,
                    DefaultStore = defaultStore,
                    CashierAccount = cashierAccount,
                    HasCashier = hasCashier
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading POS screen");
                TempData["Error"] = "حدث خطأ أثناء تحميل شاشة نقاط البيع";
                return RedirectToAction("Index", "Home");
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateNewInvoice([FromBody] string store)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                // Validate active session before creating invoice
                if (!await _posService.ValidateActiveSessionAsync(username, store))
                {
                    // Try to find any store that currently has an active session and use it
                    var stores = await _posService.GetStoresAsync();
                    string? activeStoreName = null;
                    foreach (var s in stores)
                    {
                        if (await _posService.ValidateActiveSessionAsync(username, s.StoreName))
                        {
                            activeStoreName = s.StoreName;
                            break;
                        }
                    }

                    if (string.IsNullOrEmpty(activeStoreName))
                    {
                        return Json(new {
                            success = false,
                            message = $"لا يوجد جلسة نشطة للمستخدم {username}. يرجى فتح جلسة جديدة أولاً.",
                            requiresSession = true
                        });
                    }

                    // Use the store that has an active session
                    store = activeStoreName;
                }

                await _posService.ReleaseUserEmptyInvoicesAsync(username);

                var invoice = await _posService.CreateNewInvoiceAsync(username, store);

                return Json(new { success = true, invoiceNo = invoice.TrxNo, store });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating new invoice");
                return Json(new { success = false, message = "حدث خطأ أثناء إنشاء الفاتورة الجديدة" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ReleaseIfEmpty([FromBody] int invoiceNo)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                var released = await _posService.ReleaseInvoiceIfEmptyAsync(invoiceNo, username);
                return Json(new { success = released });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error releasing invoice if empty");
                return Json(new { success = false });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddItem([FromBody] AddItemRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                // Validate active session
                if (!await _posService.ValidateActiveSessionAsync(username, request.Store))
                {
                    return Json(new {
                        success = false,
                        message = "لا يوجد جلسة نشطة لنقاط البيع",
                        requiresSession = true
                    });
                }

                var item = await _posService.AddItemToInvoiceAsync(
                    request.InvoiceNo,
                    request.ItemNo,
                    request.Quantity,
                    request.UnitPrice,
                    username,
                    request.Store
                );

                return Json(new { success = true, item = item });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to invoice");
                return Json(new { success = false, message = "حدث خطأ أثناء إضافة الصنف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> UpdateItem([FromBody] UpdateItemRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                var success = await _posService.UpdateInvoiceItemAsync(
                    request.InvoiceNo,
                    request.LineSN,
                    request.Quantity,
                    request.UnitPrice,
                    username
                );

                return Json(new { success = success, message = success ? "تم تحديث الصنف بنجاح" : "فشل في تحديث الصنف" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating item");
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث الصنف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteItem([FromBody] DeleteItemRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                var success = await _posService.DeleteInvoiceItemAsync(
                    request.InvoiceNo,
                    request.LineSN,
                    username
                );

                return Json(new { success = success, message = success ? "تم حذف الصنف بنجاح" : "فشل في حذف الصنف" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting item");
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الصنف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveAndCompleteInvoice([FromBody] SaveAndCompleteInvoiceRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                // First save customer information if provided
                if (request.CustomerId.HasValue || !string.IsNullOrEmpty(request.CustomerName))
                {
                    await _posService.UpdateInvoiceCustomerAsync(
                        request.InvoiceNo,
                        request.CustomerId,
                        request.CustomerName,
                        request.CustomerPhone,
                        username
                    );
                }

                // Then save the invoice (only takes invoiceNo and username)
                await _posService.SaveInvoiceAsync(request.InvoiceNo, username);

                // Finally complete the transaction with payments
                var invoice = await _posService.CompleteInvoiceAsync(
                    request.InvoiceNo,
                    request.CashAmount,
                    request.CardAmount,
                    username
                );

                // Clear the invoice items after successful completion
                await _posService.ClearInvoiceItemsAsync(request.InvoiceNo);

                return Json(new { success = true, invoice = invoice });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving and completing invoice");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ وإتمام البيع" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ClearInvoiceItems([FromBody] ClearInvoiceRequest request)
        {
            try
            {
                await _posService.ClearInvoiceItemsAsync(request.InvoiceNo);
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing invoice items");
                return Json(new { success = false, message = "حدث خطأ أثناء مسح أصناف الفاتورة" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SearchItems([FromBody] SearchItemsRequest request)
        {
            try
            {
                var items = await _posService.SearchItemsAsync(request.SearchTerm);

                var result = items.Select(item => new
                {
                    itemNo = item.ItemNo,
                    description = item.ItemDescription ?? "",
                    unitPrice = item.UnitSalesPrice,
                    uofM = item.SalesUofM ?? ""
                }).ToList();

                return Json(new { success = true, items = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching items");
                return Json(new { success = false, message = "حدث خطأ أثناء البحث عن الأصناف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ProcessBarcode([FromBody] ProcessBarcodeRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                var result = await _posService.ProcessBarcodeAsync(
                    request.Barcode,
                    request.InvoiceNo,
                    request.Store,
                    username
                );

                if (result.Success)
                {
                    // Add the item to the invoice
                    var item = await _posService.AddItemToInvoiceAsync(
                        request.InvoiceNo,
                        result.Item.ItemNo ?? 0,
                        result.Quantity,
                        result.UnitPrice,
                        username,
                        request.Store
                    );

                    return Json(new { success = true, message = "تم إضافة الصنف بنجاح" });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing barcode");
                return Json(new { success = false, message = "حدث خطأ أثناء معالجة الباركود" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SearchCustomerByPhone([FromBody] SearchCustomerByPhoneRequest request)
        {
            try
            {
                var customers = await _posService.GetCustomersAsync();
                // Since ChartOfAccount doesn't have Phone/Mobile, we'll search by account name for now
                var customer = customers.FirstOrDefault(c =>
                    c.AccountName.Contains(request.Phone, StringComparison.OrdinalIgnoreCase));

                if (customer != null)
                {
                    return Json(new {
                        success = true,
                        customer = new
                        {
                            accountCode = customer.AccountCode,
                            accountName = customer.AccountName,
                            phone = request.Phone // Use the searched phone number
                        }
                    });
                }
                else
                {
                    return Json(new { success = false, message = "لم يتم العثور على العميل" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching customer by phone");
                return Json(new { success = false, message = "حدث خطأ أثناء البحث عن العميل" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SearchCustomers([FromBody] SearchCustomersRequest request)
        {
            try
            {
                var customers = await _posService.GetCustomersAsync();
                var filteredCustomers = customers.Where(c =>
                    c.AccountName.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)
                ).Take(20).ToList();

                var result = filteredCustomers.Select(c => new
                {
                    accountCode = c.AccountCode,
                    accountName = c.AccountName,
                    phone = "" // ChartOfAccount doesn't have phone property
                }).ToList();

                return Json(new { success = true, customers = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching customers");
                return Json(new { success = false, message = "حدث خطأ أثناء البحث عن العملاء" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ChangeStore([FromBody] ChangeStoreRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                // Validate active session for the new store
                if (!await _posService.ValidateActiveSessionAsync(username, request.Store))
                {
                    return Json(new {
                        success = false,
                        message = $"لا يوجد جلسة نشطة للمستخدم {username} في المتجر {request.Store}",
                        requiresSession = true
                    });
                }

                return Json(new { success = true, message = "تم تغيير المتجر بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing store");
                return Json(new { success = false, message = "حدث خطأ أثناء تغيير المتجر" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetStores()
        {
            try
            {
                var stores = await _posService.GetStoresAsync();
                return Json(new { success = true, stores = stores });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stores");
                return Json(new { success = false, message = "حدث خطأ أثناء جلب المتاجر" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetCustomers()
        {
            try
            {
                var customers = await _posService.GetCustomersAsync();
                return Json(new { success = true, customers = customers });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customers");
                return Json(new { success = false, message = "حدث خطأ أثناء جلب العملاء" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetInvoiceItems(int invoiceNo)
        {
            try
            {
                var items = await _posService.GetInvoiceItemsAsync(invoiceNo);
                var itemsWithDescriptions = new List<object>();

                foreach (var item in items)
                {
                    var description = await _posService.GetItemDescriptionAsync(item.ItemNo);
                    itemsWithDescriptions.Add(new
                    {
                        lineSN = item.LineSN,
                        itemNo = item.ItemNo,
                        itemDescription = description,
                        quantity = item.TrxQTY,
                        uofM = item.UofM ?? "",
                        unitPrice = item.UnitPrice,
                        lineAmount = item.LineAmount,
                        vatAmount = item.VATAmount
                    });
                }

                return Json(new { success = true, items = itemsWithDescriptions });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice items");
                return Json(new { success = false, message = "حدث خطأ أثناء جلب أصناف الفاتورة" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveCustomerInfo([FromBody] SaveCustomerInfoRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                var success = await _posService.UpdateInvoiceCustomerAsync(
                    request.InvoiceNo,
                    request.CustomerId,
                    request.CustomerName,
                    request.CustomerPhone,
                    username
                );

                return Json(new { success = success, message = success ? "تم حفظ بيانات العميل بنجاح" : "فشل في حفظ بيانات العميل" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving customer info");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ بيانات العميل" });
            }
        }

                [HttpPost]
        public async Task<IActionResult> ValidateSession([FromBody] ValidateSessionRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                var hasActiveSession = await _posService.ValidateActiveSessionAsync(username, request.Store);

                if (!hasActiveSession)
                {
                    return Json(new {
                        success = false,
                        message = $"لا يوجد جلسة نشطة للمستخدم {username} في المتجر {request.Store}. يرجى فتح جلسة جديدة أولاً.",
                        requiresSession = true
                    });
                }

                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating session");
                return Json(new { success = false, message = "حدث خطأ أثناء التحقق من الجلسة" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveStoreInfo([FromBody] SaveStoreInfoRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                var success = await _posService.UpdateInvoiceStoreAsync(
                    request.InvoiceNo,
                    request.Store,
                    username
                );

                return Json(new { success = success, message = success ? "تم حفظ بيانات المتجر بنجاح" : "فشل في حفظ بيانات المتجر" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving store info");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ بيانات المتجر" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> GetCashierAccount([FromBody] string store)
        {
            var cashierAccount = await _posService.GetCashierAccountForStoreAsync(store);
            return Json(new { cashierAccount, hasCashier = cashierAccount.HasValue });
        }

        [HttpGet]
        public async Task<IActionResult> GetStoresAndCashiersDebug()
        {
            try
            {
                var debugInfo = await _posService.GetStoresAndCashiersDebugAsync();
                return Json(debugInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting debug information");
                return Json(new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> Receipt(int invoiceNo)
        {
            try
            {
                var invoice = await _posService.GetInvoiceByNumberAsync(invoiceNo);
                if (invoice == null)
                {
                    TempData["Error"] = "لم يتم العثور على الفاتورة";
                    return RedirectToAction("Index");
                }

                var invoiceItems = await _posService.GetInvoiceItemsAsync(invoice.TrxNo);
                var payments = await _posService.GetInvoicePaymentsAsync(invoiceNo);

                // Load store header config
                var storeName = await _configService.GetConfigValueAsync("StoreName", "");
                var vatRegNo = await _configService.GetConfigValueAsync("VATRegNo", "");
                var addressFooter = await _configService.GetConfigValueAsync("AddressFooter", "");

                // Prepare item descriptions
                var itemDescriptions = new Dictionary<long, string>();
                foreach (var itemNo in invoiceItems.Select(i => i.ItemNo).Distinct())
                {
                    var desc = await _posService.GetItemDescriptionAsync(itemNo);
                    itemDescriptions[itemNo] = desc;
                }

                var viewModel = new ReceiptViewModel
                {
                    Invoice = invoice,
                    Items = invoiceItems,
                    Payments = payments,
                    StoreName = storeName,
                    VATRegNo = vatRegNo,
                    AddressFooter = addressFooter,
                    ItemDescriptions = itemDescriptions
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating receipt");
                TempData["Error"] = "حدث خطأ أثناء إنشاء الإيصال";
                return RedirectToAction("Index");
            }
        }

        [HttpGet]
        [Route("POS/ThermalReceipt/{id:long}")]
        public async Task<IActionResult> ThermalReceipt(int id)
        {
            try
            {
                _logger.LogInformation("ThermalReceipt requested for invoice: {InvoiceNo}", id);

                var invoice = await _posService.GetInvoiceByNumberAsync(id);
                if (invoice == null)
                {
                    _logger.LogWarning("Invoice {InvoiceNo} not found", id);
                    return Content($@"
                        <html dir='rtl'><body style='text-align:center;padding:20px;font-family:Arial;'>
                        <h3>لم يتم العثور على الفاتورة رقم {id}</h3>
                        </body></html>", "text/html");
                }

                _logger.LogInformation("Invoice found: {InvoiceNo}, getting items...", id);
                var invoiceItems = await _posService.GetInvoiceItemsAsync(invoice.TrxNo);
                _logger.LogInformation("Found {ItemCount} items for invoice {InvoiceNo}", invoiceItems.Count, id);

                var payments = await _posService.GetInvoicePaymentsAsync(id);
                _logger.LogInformation("Found {PaymentCount} payments for invoice {InvoiceNo}", payments.Count, id);

                // Load store header config
                var storeName = await _configService.GetConfigValueAsync("StoreName", "");
                var vatRegNo = await _configService.GetConfigValueAsync("VATRegNo", "");
                var addressFooter = await _configService.GetConfigValueAsync("AddressFooter", "");

                // Prepare item descriptions
                var itemDescriptions = new Dictionary<long, string>();
                foreach (var itemNo in invoiceItems.Select(i => i.ItemNo).Distinct())
                {
                    var desc = await _posService.GetItemDescriptionAsync(itemNo);
                    itemDescriptions[itemNo] = desc;
                }

                var viewModel = new ReceiptViewModel
                {
                    Invoice = invoice,
                    Items = invoiceItems,
                    Payments = payments,
                    StoreName = storeName,
                    VATRegNo = vatRegNo,
                    AddressFooter = addressFooter,
                    ItemDescriptions = itemDescriptions
                };

                _logger.LogInformation("Rendering ThermalReceipt view for invoice: {InvoiceNo}", id);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ThermalReceipt for invoice {InvoiceNo}: {ErrorMessage}", id, ex.Message);
                return Content($@"
                    <html dir='rtl'><body style='text-align:center;padding:20px;font-family:Arial;'>
                    <h3>حدث خطأ أثناء إنشاء الإيصال الحراري</h3>
                    <p>رقم الفاتورة: {id}</p>
                    <p>الخطأ: {ex.Message}</p>
                    </body></html>", "text/html");
            }
        }

        [HttpGet]
        [Route("POS/TestReceipt/{id:long}")]
        public IActionResult TestReceipt(long id)
        {
            _logger.LogInformation("TestReceipt called with invoiceNo: {InvoiceNo}", id);
            return Content($@"
                <html dir='rtl'>
                <body style='text-align:center;padding:20px;font-family:Arial;'>
                <h3>Test Receipt for Invoice: {id}</h3>
                <p>This is a test to verify routing is working</p>
                </body>
                </html>", "text/html");
        }

        [HttpGet]
        [Route("POS/QRCode/{invoiceNo:int}")]
        public async Task<IActionResult> GetQRCode(int invoiceNo)
        {
            try
            {
                var invoice = await _posService.GetInvoiceByNumberAsync(invoiceNo);
                if (invoice?.QRCodeImage != null)
                {
                    return File(invoice.QRCodeImage, "image/png");
                }

                // Generate QR code if not exists
                var timestamp = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ");
                var totalAmount = (invoice?.TrxNetAmount ?? 0).ToString("F2");
                var vatAmount = (invoice?.TrxVAT ?? 0).ToString("F2");

                var qrCodeData = await _posService.GenerateQRCodeAsync(
                    "نظام السلطان للمبيعات والمحاسبة",
                    "***************",
                    timestamp,
                    totalAmount,
                    vatAmount
                );

                if (qrCodeData != null)
                {
                    return File(qrCodeData, "image/png");
                }

                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting QR code for invoice {InvoiceNo}", invoiceNo);
                return NotFound();
            }
        }

        /// <summary>
        /// Manually create GL entries for an invoice (for cases where automatic creation failed)
        /// </summary>
        [HttpPost]
        [Route("POS/CreateGLEntries/{invoiceNo:int}")]
        public async Task<IActionResult> CreateGLEntries(int invoiceNo)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";

                // Verify invoice exists and is completed
                var invoice = await _posService.GetInvoiceByNumberAsync(invoiceNo);
                if (invoice == null)
                {
                    return Json(new { success = false, message = "لم يتم العثور على الفاتورة" });
                }

                if (invoice.ReadyForUse != "True")
                {
                    return Json(new { success = false, message = "يجب إكمال الفاتورة أولاً قبل إنشاء القيود المحاسبية" });
                }

                // Create all GL entries
                var result = await _posService.CreateAllGLEntriesAsync(invoiceNo);

                if (result)
                {
                    _logger.LogInformation("GL entries created successfully for invoice {InvoiceNo} by user {Username}", invoiceNo, username);
                    return Json(new { success = true, message = "تم إنشاء القيود المحاسبية بنجاح" });
                }
                else
                {
                    _logger.LogWarning("Failed to create GL entries for invoice {InvoiceNo} by user {Username}", invoiceNo, username);
                    return Json(new { success = false, message = "فشل في إنشاء القيود المحاسبية" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating GL entries for invoice {InvoiceNo}", invoiceNo);
                return Json(new { success = false, message = "حدث خطأ أثناء إنشاء القيود المحاسبية" });
            }
        }

        /// <summary>
        /// Create individual GL entry types for an invoice
        /// </summary>
        [HttpPost]
        [Route("POS/CreateSalesGLEntry/{invoiceNo:int}")]
        public async Task<IActionResult> CreateSalesGLEntry(int invoiceNo)
        {
            try
            {
                var result = await _posService.CreateSalesInvoiceGLEntryAsync(invoiceNo);
                return Json(new { success = result, message = result ? "تم إنشاء قيد المبيعات بنجاح" : "فشل في إنشاء قيد المبيعات" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating sales GL entry for invoice {InvoiceNo}", invoiceNo);
                return Json(new { success = false, message = "حدث خطأ أثناء إنشاء قيد المبيعات" });
            }
        }

        [HttpPost]
        [Route("POS/CreatePaymentGLEntry/{invoiceNo:int}")]
        public async Task<IActionResult> CreatePaymentGLEntry(int invoiceNo)
        {
            try
            {
                var result = await _posService.CreatePaymentGLEntryAsync(invoiceNo);
                return Json(new { success = result, message = result ? "تم إنشاء قيد الدفع بنجاح" : "فشل في إنشاء قيد الدفع" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment GL entry for invoice {InvoiceNo}", invoiceNo);
                return Json(new { success = false, message = "حدث خطأ أثناء إنشاء قيد الدفع" });
            }
        }

        [HttpPost]
        [Route("POS/CreateCOGSGLEntry/{invoiceNo:int}")]
        public async Task<IActionResult> CreateCOGSGLEntry(int invoiceNo)
        {
            try
            {
                var result = await _posService.CreateCOGSGLEntryAsync(invoiceNo);
                return Json(new { success = result, message = result ? "تم إنشاء قيد تكلفة البيع بنجاح" : "فشل في إنشاء قيد تكلفة البيع" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating COGS GL entry for invoice {InvoiceNo}", invoiceNo);
                return Json(new { success = false, message = "حدث خطأ أثناء إنشاء قيد تكلفة البيع" });
            }
        }
    }

    // Request models
    public class AddItemRequest
    {
        public int InvoiceNo { get; set; }
        public long ItemNo { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public string Store { get; set; } = "";
    }

    public class UpdateItemRequest
    {
        public int InvoiceNo { get; set; }
        public int LineSN { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
    }

    public class DeleteItemRequest
    {
        public int InvoiceNo { get; set; }
        public int LineSN { get; set; }
    }

    public class SaveInvoiceRequest
    {
        public int InvoiceNo { get; set; }
        public long? CustomerId { get; set; }
        public string CustomerName { get; set; } = "";
        public string CustomerPhone { get; set; } = "";
        public decimal DiscountPercent { get; set; }
    }

    public class CompleteTransactionRequest
    {
        public int InvoiceNo { get; set; }
        public decimal CashAmount { get; set; }
        public decimal CardAmount { get; set; }
    }

    public class ClearInvoiceRequest
    {
        public int InvoiceNo { get; set; }
    }

    public class SearchItemsRequest
    {
        public string SearchTerm { get; set; } = "";
    }

    public class ProcessBarcodeRequest
    {
        public string Barcode { get; set; } = "";
        public int InvoiceNo { get; set; }
        public string Store { get; set; } = "";
    }

    public class SearchCustomerByPhoneRequest
    {
        public string Phone { get; set; } = "";
    }

    public class SearchCustomersRequest
    {
        public string SearchTerm { get; set; } = "";
    }

    public class ChangeStoreRequest
    {
        public string Store { get; set; } = "";
    }

    public class SaveCustomerInfoRequest
    {
        public int InvoiceNo { get; set; }
        public long? CustomerId { get; set; }
        public string CustomerName { get; set; } = "";
        public string CustomerPhone { get; set; } = "";
    }

    public class ValidateSessionRequest
    {
        public string Store { get; set; } = "";
    }

    public class SaveStoreInfoRequest
    {
        public int InvoiceNo { get; set; }
        public string Store { get; set; } = "";
    }

    public class SaveAndCompleteInvoiceRequest
    {
        public int InvoiceNo { get; set; }
        public long? CustomerId { get; set; }
        public string CustomerName { get; set; } = "";
        public string CustomerPhone { get; set; } = "";
        public decimal DiscountPercent { get; set; }
        public decimal CashAmount { get; set; }
        public decimal CardAmount { get; set; }
    }
}
