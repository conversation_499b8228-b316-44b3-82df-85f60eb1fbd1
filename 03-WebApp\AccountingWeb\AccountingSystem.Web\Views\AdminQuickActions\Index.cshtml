@model (List<AccountingSystem.Web.Utilities.SidebarItem> allItems, HashSet<string> enabledRoutes)
@{
    ViewData["Title"] = "إدارة الإجراءات السريعة";
    var role = ViewBag.Role as string ?? "admin";
    var sections = Model.allItems.GroupBy(i => i.Section).OrderBy(g => g.Key);
}

<div class="container mt-4">
    <h2 class="mb-4">إدارة الإجراءات السريعة للمستخدمين</h2>
    <form asp-action="Index" method="get" class="mb-3">
        <label for="role">نوع المستخدم:</label>
        <select id="role" name="role" class="form-select d-inline w-auto" onchange="this.form.submit()">
            @foreach (var group in (IEnumerable<AccountingSystem.Models.UserGroup>)ViewBag.Groups)
            {
                <option value="@group.GroupName" selected="@(role == group.GroupName)">@group.GroupName</option>
            }
        </select>
    </form>
    <form asp-action="FixTodayTrxTypes" asp-controller="AdminQuickActions" method="post" class="mb-3">
        @Html.AntiForgeryToken()
        <button type="submit" class="btn btn-warning" onclick="return confirm('سيتم تصحيح أنواع الحركات لليوم فقط. هل أنت متأكد؟');">
            تصحيح أنواع الحركات لليوم (مبيعات/مشتريات)
        </button>
    </form>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success">@TempData["SuccessMessage"]</div>
    }
    <form asp-action="Save" method="post">
        <input type="hidden" name="role" value="@role" />
        <div class="accordion" id="sidebarSections">
            @foreach (var sec in sections)
            {
                <div class="accordion-item mb-2">
                    <h2 class="accordion-header" id="<EMAIL>">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#<EMAIL>" aria-expanded="false" aria-controls="<EMAIL>">
                            @sec.Key
                        </button>
                    </h2>
                    <div id="<EMAIL>" class="accordion-collapse collapse" aria-labelledby="<EMAIL>" data-bs-parent="#sidebarSections">
                        <div class="accordion-body">
                            <div class="row">
                                @foreach (var item in sec)
                                {
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="enabledRoutes" value="@item.Route" id="<EMAIL>("/", "-")" @(Model.enabledRoutes.Contains(item.Route) ? "checked" : null) />
                                            <label class="form-check-label" for="<EMAIL>("/", "-")">
                                                <i class="@item.Icon"></i> @item.Title
                                            </label>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
        <button type="submit" class="btn btn-primary mt-3">حفظ الإعدادات</button>
    </form>
</div>