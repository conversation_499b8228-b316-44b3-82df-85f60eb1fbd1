﻿Public Class frmSelectItem
    Public SelectedItemNo As String = ""

    Private Sub frmSelectItem_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        DGVItems.SelectionMode = DataGridViewSelectionMode.FullRowSelect
        DGVItems.ReadOnly = True
    End Sub

    Public Sub LoadItems(searchText As String)
        Dim dt As New DataTable
        Using cmd As New SqlClient.SqlCommand("SELECT ItemNo, ItemDescription FROM tblItems WHERE ItemDescription LIKE @desc", New SqlClient.SqlConnection(ConStr))
            cmd.Parameters.AddWithValue("@desc", "%" & searchText & "%")
            Dim da As New SqlClient.SqlDataAdapter(cmd)
            da.Fill(dt)
        End Using
        DGVItems.DataSource = dt
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        If DGVItems.SelectedRows.Count > 0 Then
            SelectedItemNo = DGVItems.SelectedRows(0).Cells("ItemNo").Value.ToString()
            Me.DialogResult = DialogResult.OK
            Me.Close()
        End If
    End Sub
End Class
