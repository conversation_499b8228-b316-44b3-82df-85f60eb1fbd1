@model WebAuthorizationViewModel
@{
    ViewData["Title"] = "إدارة صلاحيات النظام الإلكتروني";
    Layout = "_Layout";
}

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-shield-alt"></i>
                    إدارة صلاحيات النظام الإلكتروني
                </h1>
                <div class="page-options">
                    <button type="button" class="btn btn-primary" onclick="saveAllPermissions()" id="saveAllBtn">
                        <i class="fas fa-save"></i>
                        حفظ جميع التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alertContainer"></div>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <!-- Groups Panel -->
        <div class="col-lg-3">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users"></i>
                        مجموعات المستخدمين
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="groupsList">
                        @foreach (var group in Model.Groups)
                        {
                            <div class="list-group-item list-group-item-action group-item" 
                                 data-group-id="@group.GroupID" 
                                 onclick="selectGroup(@group.GroupID, '@group.GroupName')">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1 fw-bold">@group.GroupName</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-user"></i>
                                            @group.UserCount مستخدم
                                        </small>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions Panel -->
        <div class="col-lg-9">
            <!-- Tabs for Menu and Quick Actions -->
            <ul class="nav nav-tabs mb-3" id="permissionTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="menu-tab" data-bs-toggle="tab" data-bs-target="#menuPermissions" 
                            type="button" role="tab">
                        <i class="fas fa-list"></i>
                        صلاحيات القوائم
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="quickaction-tab" data-bs-toggle="tab" data-bs-target="#quickActionPermissions" 
                            type="button" role="tab">
                        <i class="fas fa-bolt"></i>
                        صلاحيات الإجراءات السريعة
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="permissionTabContent">
                <!-- Menu Permissions Tab -->
                <div class="tab-pane fade show active" id="menuPermissions" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-list"></i>
                                    صلاحيات القوائم: <span id="selectedGroupNameMenu" class="text-primary fw-bold">اختر مجموعة</span>
                                </h5>
                                <div class="btn-group" id="menuActions" style="display: none;">
                                    <button type="button" class="btn btn-sm btn-success" onclick="checkAllMenus()">
                                        <i class="fas fa-check-double"></i>
                                        تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-sm btn-warning" onclick="uncheckAllMenus()">
                                        <i class="fas fa-times"></i>
                                        إلغاء الكل
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="menuPermissionsContainer">
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-hand-pointer fa-3x mb-3"></i>
                                    <p>اختر مجموعة من القائمة لعرض وتعديل صلاحياتها</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Action Permissions Tab -->
                <div class="tab-pane fade" id="quickActionPermissions" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bolt"></i>
                                    صلاحيات الإجراءات السريعة: <span id="selectedGroupNameQuickAction" class="text-primary fw-bold">اختر مجموعة</span>
                                </h5>
                                <div class="btn-group" id="quickActionActions" style="display: none;">
                                    <button type="button" class="btn btn-sm btn-success" onclick="checkAllQuickActions()">
                                        <i class="fas fa-check-double"></i>
                                        تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-sm btn-warning" onclick="uncheckAllQuickActions()">
                                        <i class="fas fa-times"></i>
                                        إلغاء الكل
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="quickActionPermissionsContainer">
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-hand-pointer fa-3x mb-3"></i>
                                    <p>اختر مجموعة من القائمة لعرض وتعديل صلاحياتها</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let currentGroupId = null;
        let currentGroupName = '';
        let allMenuItems = @Html.Raw(Json.Serialize(Model.MenuItems));
        let allQuickActions = @Html.Raw(Json.Serialize(Model.QuickActions));
        let originalMenuPermissions = [];
        let originalQuickActionPermissions = [];

        // Select group and load permissions
        function selectGroup(groupId, groupName) {
            currentGroupId = groupId;
            currentGroupName = groupName;
            
            // Update UI
            $('.group-item').removeClass('active');
            $(`.group-item[data-group-id="${groupId}"]`).addClass('active');
            $('#selectedGroupNameMenu').text(groupName);
            $('#selectedGroupNameQuickAction').text(groupName);
            $('#menuActions').show();
            $('#quickActionActions').show();

            // Load both menu and quick action permissions
            loadMenuPermissions(groupId);
            loadQuickActionPermissions(groupId);
        }

        // Load menu permissions
        function loadMenuPermissions(groupId) {
            $('#menuPermissionsContainer').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>');

            $.get(`/WebAuthorization/GetGroupMenuPermissions?groupId=${groupId}`)
                .done(function(data) {
                    if (data.success) {
                        originalMenuPermissions = data.permissions;
                        buildMenuPermissionsTree(data.permissions);
                    } else {
                        showAlert('danger', data.error || 'حدث خطأ أثناء جلب صلاحيات القوائم');
                    }
                })
                .fail(function() {
                    showAlert('danger', 'حدث خطأ أثناء جلب صلاحيات القوائم');
                });
        }

        // Build menu permissions tree
        function buildMenuPermissionsTree(selectedPermissions) {
            let html = '<div class="permissions-tree" id="menuPermissionsTree">';
            
            function buildNode(menu, level = 0) {
                const permission = selectedPermissions.find(p => p.menuId === menu.id);
                const isChecked = permission?.canView ?? false;
                const indent = level * 25;
                const isContainer = menu.isContainer;
                
                html += `
                    <div class="permission-node" style="margin-right: ${indent}px;">
                        <div class="row align-items-center mb-2">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input menu-checkbox" 
                                           type="checkbox" 
                                           id="menu_${menu.id}" 
                                           data-menu-id="${menu.id}"
                                           data-parent-id="${menu.parentMenuId || ''}"
                                           data-is-container="${isContainer}"
                                           ${isChecked ? 'checked' : ''}
                                           onchange="handleMenuChange(this)">
                                    <label class="form-check-label ${isContainer ? 'fw-bold text-primary' : ''}" for="menu_${menu.id}">
                                        ${menu.icon ? '<i class="' + menu.icon + '"></i>' : '<i class="fas fa-circle"></i>'}
                                        ${menu.displayName}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-8" id="menuPermDetails_${menu.id}" style="${isChecked && !isContainer ? '' : 'display:none;'}">
                                <div class="btn-group btn-group-sm" role="group">
                                    <input type="checkbox" class="btn-check" id="canAdd_${menu.id}" ${permission?.canAdd ? 'checked' : ''}>
                                    <label class="btn btn-outline-success" for="canAdd_${menu.id}">
                                        <i class="fas fa-plus"></i> إضافة
                                    </label>
                                    
                                    <input type="checkbox" class="btn-check" id="canEdit_${menu.id}" ${permission?.canEdit ? 'checked' : ''}>
                                    <label class="btn btn-outline-primary" for="canEdit_${menu.id}">
                                        <i class="fas fa-edit"></i> تعديل
                                    </label>
                                    
                                    <input type="checkbox" class="btn-check" id="canDelete_${menu.id}" ${permission?.canDelete ? 'checked' : ''}>
                                    <label class="btn btn-outline-danger" for="canDelete_${menu.id}">
                                        <i class="fas fa-trash"></i> حذف
                                    </label>
                                    
                                    <input type="checkbox" class="btn-check" id="canPrint_${menu.id}" ${permission?.canPrint ? 'checked' : ''}>
                                    <label class="btn btn-outline-info" for="canPrint_${menu.id}">
                                        <i class="fas fa-print"></i> طباعة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                if (menu.children && menu.children.length > 0) {
                    menu.children.forEach(child => buildNode(child, level + 1));
                }
            }
            
            allMenuItems.forEach(menu => buildNode(menu));
            html += '</div>';
            
            $('#menuPermissionsContainer').html(html);
        }

        // Handle menu checkbox change
        function handleMenuChange(checkbox) {
            const menuId = $(checkbox).data('menu-id');
            const isChecked = checkbox.checked;
            const isContainer = $(checkbox).data('is-container') === 'true';
            
            // Show/hide permission details
            if (!isContainer) {
                $(`#menuPermDetails_${menuId}`).toggle(isChecked);
            }
            
            // Auto-check/uncheck children
            if (isContainer) {
                checkUncheckMenuChildren(menuId, isChecked);
            }
            
            // Auto-check parents if child is checked
            if (isChecked) {
                checkMenuParents(menuId);
            }
        }

        // Check/uncheck menu children recursively
        function checkUncheckMenuChildren(parentMenuId, isChecked) {
            $(`.menu-checkbox[data-parent-id="${parentMenuId}"]`).each(function() {
                this.checked = isChecked;
                const childMenuId = $(this).data('menu-id');
                const isChildContainer = $(this).data('is-container') === 'true';
                
                if (!isChildContainer) {
                    $(`#menuPermDetails_${childMenuId}`).toggle(isChecked);
                }
                
                if (isChildContainer) {
                    checkUncheckMenuChildren(childMenuId, isChecked);
                }
            });
        }

        // Check menu parents
        function checkMenuParents(menuId) {
            const parentId = $(`.menu-checkbox[data-menu-id="${menuId}"]`).data('parent-id');
            if (parentId) {
                const parentCheckbox = $(`.menu-checkbox[data-menu-id="${parentId}"]`)[0];
                if (parentCheckbox && !parentCheckbox.checked) {
                    parentCheckbox.checked = true;
                    checkMenuParents(parentId);
                }
            }
        }

        // Check all menus
        function checkAllMenus() {
            $('.menu-checkbox').prop('checked', true);
            $('.menu-checkbox').each(function() {
                const menuId = $(this).data('menu-id');
                const isContainer = $(this).data('is-container') === 'true';
                if (!isContainer) {
                    $(`#menuPermDetails_${menuId}`).show();
                }
            });
        }

        // Uncheck all menus
        function uncheckAllMenus() {
            $('.menu-checkbox').prop('checked', false);
            $('[id^="menuPermDetails_"]').hide();
        }

        // Load quick action permissions
        function loadQuickActionPermissions(groupId) {
            $('#quickActionPermissionsContainer').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>');

            $.get(`/WebAuthorization/GetGroupQuickActionPermissions?groupId=${groupId}`)
                .done(function(data) {
                    if (data.success) {
                        originalQuickActionPermissions = data.quickActionIds;
                        buildQuickActionsPermissions(data.quickActionIds);
                    } else {
                        showAlert('danger', data.error || 'حدث خطأ أثناء جلب صلاحيات الإجراءات السريعة');
                    }
                })
                .fail(function() {
                    showAlert('danger', 'حدث خطأ أثناء جلب صلاحيات الإجراءات السريعة');
                });
        }

        // Build quick actions permissions
        function buildQuickActionsPermissions(selectedQuickActionIds) {
            let html = '<div class="row">';
            
            allQuickActions.forEach(qa => {
                const isChecked = selectedQuickActionIds.includes(qa.id);
                html += `
                    <div class="col-md-4 mb-3">
                        <div class="card quick-action-card ${isChecked ? 'border-primary' : ''}">
                            <div class="card-body">
                                <div class="form-check">
                                    <input class="form-check-input quickaction-checkbox" 
                                           type="checkbox" 
                                           id="qa_${qa.id}" 
                                           data-qa-id="${qa.id}"
                                           ${isChecked ? 'checked' : ''}>
                                    <label class="form-check-label" for="qa_${qa.id}">
                                        <div class="d-flex align-items-center">
                                            <div class="quick-action-icon bg-${qa.colorClass || 'primary'} text-white p-2 rounded me-2">
                                                <i class="${qa.icon || 'fas fa-bolt'}"></i>
                                            </div>
                                            <div>
                                                <strong>${qa.displayName}</strong>
                                                ${qa.category ? '<br><small class="text-muted">' + qa.category + '</small>' : ''}
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            $('#quickActionPermissionsContainer').html(html);
            
            // Add change event to update card style
            $('.quickaction-checkbox').on('change', function() {
                const card = $(this).closest('.quick-action-card');
                if (this.checked) {
                    card.addClass('border-primary');
                } else {
                    card.removeClass('border-primary');
                }
            });
        }

        // Check all quick actions
        function checkAllQuickActions() {
            $('.quickaction-checkbox').prop('checked', true).trigger('change');
        }

        // Uncheck all quick actions
        function uncheckAllQuickActions() {
            $('.quickaction-checkbox').prop('checked', false).trigger('change');
        }

        // Save all permissions
        function saveAllPermissions() {
            if (!currentGroupId) {
                showAlert('warning', 'يرجى اختيار مجموعة أولاً');
                return;
            }

            // Save menu permissions
            const menuPermissions = [];
            $('.menu-checkbox:checked').each(function() {
                const menuId = $(this).data('menu-id');
                const isContainer = $(this).data('is-container') === 'true';
                
                menuPermissions.push({
                    menuId: menuId,
                    canView: true,
                    canAdd: isContainer ? false : $(`#canAdd_${menuId}`).is(':checked'),
                    canEdit: isContainer ? false : $(`#canEdit_${menuId}`).is(':checked'),
                    canDelete: isContainer ? false : $(`#canDelete_${menuId}`).is(':checked'),
                    canPrint: isContainer ? false : $(`#canPrint_${menuId}`).is(':checked')
                });
            });

            // Save quick action permissions
            const quickActionIds = [];
            $('.quickaction-checkbox:checked').each(function() {
                quickActionIds.push($(this).data('qa-id'));
            });

            // Show loading
            $('#saveAllBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');

            // Save menu permissions
            $.ajax({
                url: '/WebAuthorization/SaveMenuPermissions',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    groupId: currentGroupId,
                    permissions: menuPermissions
                }),
                headers: {
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        // Save quick action permissions
                        $.ajax({
                            url: '/WebAuthorization/SaveQuickActionPermissions',
                            method: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                groupId: currentGroupId,
                                quickActionIds: quickActionIds
                            }),
                            headers: {
                                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                            },
                            success: function(response2) {
                                if (response2.success) {
                                    showAlert('success', 'تم حفظ جميع الصلاحيات بنجاح');
                                    originalMenuPermissions = menuPermissions;
                                    originalQuickActionPermissions = quickActionIds;
                                } else {
                                    showAlert('danger', 'تم حفظ صلاحيات القوائم ولكن حدث خطأ في حفظ صلاحيات الإجراءات السريعة');
                                }
                            },
                            error: function() {
                                showAlert('danger', 'تم حفظ صلاحيات القوائم ولكن حدث خطأ في حفظ صلاحيات الإجراءات السريعة');
                            },
                            complete: function() {
                                $('#saveAllBtn').prop('disabled', false).html('<i class="fas fa-save"></i> حفظ جميع التغييرات');
                            }
                        });
                    } else {
                        showAlert('danger', response.message);
                        $('#saveAllBtn').prop('disabled', false).html('<i class="fas fa-save"></i> حفظ جميع التغييرات');
                    }
                },
                error: function() {
                    showAlert('danger', 'حدث خطأ أثناء حفظ الصلاحيات');
                    $('#saveAllBtn').prop('disabled', false).html('<i class="fas fa-save"></i> حفظ جميع التغييرات');
                }
            });
        }

        // Show alert message
        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'info' ? 'info-circle' : 'exclamation-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('#alertContainer').html(alertHtml);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                $('#alertContainer .alert').fadeOut('slow', function() {
                    $(this).remove();
                });
            }, 5000);
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Add CSRF token to all AJAX requests
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (settings.type !== 'GET' && settings.type !== 'HEAD') {
                    xhr.setRequestHeader('RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());
                }
            }
        });
    </script>

    <style>
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #dee2e6;
        }

        .page-title {
            margin: 0;
            color: #2c3e50;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 1.5rem;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
        }

        .card-header.bg-primary {
            background-color: #0d6efd !important;
        }

        .group-item {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .group-item:hover {
            background-color: #f8f9fa;
        }

        .group-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }

        .permissions-tree {
            max-height: 600px;
            overflow-y: auto;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .permission-node {
            margin-bottom: 0.75rem;
        }

        .permission-node .form-check-label {
            cursor: pointer;
            user-select: none;
        }

        .permission-node .form-check-label i {
            margin-left: 0.5rem;
            width: 16px;
        }

        .quick-action-card {
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .quick-action-card.border-primary {
            border-color: #0d6efd !important;
            background-color: #f0f8ff;
        }

        .quick-action-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .nav-tabs .nav-link {
            color: #6c757d;
            border: none;
            border-bottom: 3px solid transparent;
        }

        .nav-tabs .nav-link.active {
            color: #0d6efd;
            border-bottom-color: #0d6efd;
            background-color: transparent;
        }

        .nav-tabs .nav-link:hover {
            border-bottom-color: #0d6efd;
        }

        /* Custom scrollbar */
        .permissions-tree::-webkit-scrollbar {
            width: 8px;
        }

        .permissions-tree::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .permissions-tree::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .permissions-tree::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
}

@Html.AntiForgeryToken()

