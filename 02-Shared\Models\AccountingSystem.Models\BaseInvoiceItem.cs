using System;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem.Models
{
    /// <summary>
    /// Base class for invoice items that share the tblStockMovement table
    /// </summary>
    public abstract class BaseInvoiceItem
    {
        public int DocNo { get; set; } // Invoice number

        public int LineSN { get; set; } // Line sequence number

        public DateTime? TrxDate { get; set; } = DateTime.Now;

        public long ItemNo { get; set; }

        [StringLength(50)]
        public string? Store { get; set; }

        public decimal TrxQTY { get; set; } = 0;

        [Required]
        [StringLength(50)]
        public string TrxType { get; set; } = string.Empty;

        public decimal UnitPrice { get; set; } = 0;

        [StringLength(50)]
        public string? UofM { get; set; }

        public decimal UofMConversion { get; set; } = 1;

        public decimal VATAmount { get; set; } = 0;

        public decimal LineAmount { get; set; } = 0;

        [StringLength(50)]
        public string? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        [StringLength(50)]
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
    }
} 