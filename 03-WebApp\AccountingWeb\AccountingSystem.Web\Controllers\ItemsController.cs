using AccountingSystem.Services;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace AccountingSystem.Web.Controllers
{
    public class ItemsController : Controller
    {
        private readonly IItemsService _itemsService;

        public ItemsController(IItemsService itemsService)
        {
            _itemsService = itemsService;
        }

        public async Task<IActionResult> Index(string searchTerm)
        {
            var items = await _itemsService.GetItemsAsync(searchTerm);
            ViewBag.SearchTerm = searchTerm;
            return View(items);
        }

        public async Task<IActionResult> Create()
        {
            await PopulateDropdowns();
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(AccountingSystem.Models.Item item)
        {
            if (ModelState.IsValid)
            {
                await _itemsService.CreateItemAsync(item);
                TempData["SuccessMessage"] = "تم إنشاء الصنف بنجاح.";
                return RedirectToAction(nameof(Index));
            }
            await PopulateDropdowns();
            return View(item);
        }

        public async Task<IActionResult> Edit(long id)
        {
            var item = await _itemsService.GetItemByIdAsync(id);
            if (item == null)
            {
                return NotFound();
            }
            await PopulateDropdowns();
            return View(item);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(long id, AccountingSystem.Models.Item item)
        {
            if (id != item.SN)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                await _itemsService.UpdateItemAsync(item);
                TempData["SuccessMessage"] = "تم تحديث الصنف بنجاح.";
                return RedirectToAction(nameof(Index));
            }
            await PopulateDropdowns();
            return View(item);
        }

        public async Task<IActionResult> Details(long id)
        {
            var item = await _itemsService.GetItemByIdAsync(id);
            if (item == null)
            {
                return NotFound();
            }
            return View(item);
        }

        public async Task<IActionResult> Delete(long id)
        {
            var item = await _itemsService.GetItemByIdAsync(id);
            if (item == null)
            {
                return NotFound();
            }
            return View(item);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(long id)
        {
            await _itemsService.DeleteItemAsync(id);
            TempData["SuccessMessage"] = "تم حذف الصنف بنجاح.";
            return RedirectToAction(nameof(Index));
        }

        private async Task PopulateDropdowns()
        {
            ViewBag.UofMs = await _itemsService.GetUofMsAsync();
            ViewBag.Categories = await _itemsService.GetCategoriesAsync();
            ViewBag.ItemTypes = await _itemsService.GetItemTypesAsync();
            ViewBag.Taxes = await _itemsService.GetTaxesAsync();
            ViewBag.Brands = await _itemsService.GetBrandsAsync();
            ViewBag.Shops = await _itemsService.GetShopsAsync();
        }
    }
} 