using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    public class ChartOfAccount
    {
        [Key]
        [StringLength(50)]
        public string AccountCode { get; set; } = string.Empty;

        [StringLength(20)]
        public string? SegmentCode { get; set; }

        [Required]
        [StringLength(200)]
        public string AccountName { get; set; } = string.Empty;

        [StringLength(50)]
        public string? ParentAccountCode { get; set; }

        public int AccountLevel { get; set; }

        public bool IsPosting { get; set; } = true;

        [StringLength(50)]
        public string? AccountType { get; set; }

        [StringLength(20)]
        public string? AccountNature { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(50)]
        public string? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        [StringLength(50)]
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
    }
} 