﻿Imports System.Data.SqlClient
Imports System.Runtime.CompilerServices.RuntimeHelpers
Public Class frmEmployees
    Dim RegionSN As Int64 = 0
    Dim RegionDes As String = ""
    Dim Salesman As String = ""

    Private Sub frmCustomers_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        ' frmCards.Enabled = True
    End Sub

    Private Sub frmStores_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        EmployeesLoad()
        CheckParentID()
    End Sub
    Sub EmployeesLoad()
        Dim ds As DataSet = New DataSet
        Dim usercmd As New SqlCommand("SELECT EmployeeNo AS [رقم الموظف], Emp_name AS [اسم الموظف], Emp_Salary AS [الراتب الشهري] FROM tblEmployees  order by EmployeeNo", Con)
        Dim da As New SqlDataAdapter(usercmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Sub GetSerial()
        Try
            Dim SelectCMD As New SqlCommand("Select (Max(RootID)) + 1 as SN from tblRoots where ParentID = " & ParentID & "", Con)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                If reader.Item("SN").ToString <> "" Then
                    VendNo = Val(reader.Item("SN").ToString)
                Else
                    VendNo = 1
                End If
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub


    Sub CheckEmpByNo()
        Try
            Dim SelectCMD As New SqlCommand("Select Emp_Name from tblEmployees where EmpNo = " & Val(EmpNo) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                Salesman = Trim(reader.Item(0).ToString)
            Else
                Salesman = ""
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Sub CheckParentID()
        Try
            Dim SelectCMD As New SqlCommand("Select AccountNo from tblGLConfig where EntryReferenceModule = 'موظفين'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                ParentID = Val(reader.Item(0).ToString)
            Else
                ParentID = 0
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Sub CheckRootLevel()
        Try
            Dim SelectCMD As New SqlCommand("Select RootLevel from tblRoots where RootID = " & Val(ParentID) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                RootLevel = Val(reader.Item(0).ToString)
            Else
                RootLevel = 0
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If Val(txtEmpNo.Text) <> 0 Then
            Dim SearchCMD As New SqlCommand("Select * from tblEmployees where EmployeeNo = " & Val(txtEmpNo.Text) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                reader.Close()
                Dim UpdateCMD As New SqlCommand("Update tblEmployees set Emp_name = '" & txtEmpName.Text.Trim & "', Emp_Salary = '" & Val(txtMonthlySalary.Text) & "',ModifiedBy = '" & UserName & "',ModifiedOn = GetDate() where EmployeeNo =  " & Val(txtEmpNo.Text) & "", Con)
                Dim UpdateRootCMD As New SqlCommand("Update tblRoots set RootName = '" & txtEmpName.Text.Trim & "',ModifiedBy = '" & UserName & "',ModifiedOn = GetDate() where RootID =  " & Val(txtEmpNo.Text) & "", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                UpdateCMD.ExecuteNonQuery()
                UpdateRootCMD.ExecuteNonQuery()
                MsgBox("تم تعديل بيانات الموظف بنجاح", MsgBoxStyle.Information, "نظام السلطان")
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                ClearFields()
                EmployeesLoad()
                If reader.IsClosed Then
                Else
                    reader.Close()
                End If
            Else
                MsgBox("الرجاء المحاولة مرة أخرى", MsgBoxStyle.Critical, "نظام السلطان")
            End If
        Else
            GetSerial()
            CheckParentID()
            CheckRootLevel()
            Dim InsertCMD As New SqlCommand("Insert Into tblEmployees (EmployeeNo,Emp_name,Emp_Salary,CreatedBy,CreatedOn) Values (" & Val(VendNo) & ",'" & txtEmpName.Text.Trim & "','" & txtMonthlySalary.Text.Trim & "','" & UserName & "',GetDate())", Con)
            Dim InsertRootCMD As New SqlCommand("Insert Into tblRoots (RootID,RootLevel,ParentID,RootName,CreatedBy,CreatedOn) Values (" & Val(VendNo) & "," & RootLevel & "," & ParentID & ",'" & txtEmpName.Text.Trim & "','" & UserName & "',GetDate())", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            InsertCMD.ExecuteNonQuery()
            InsertRootCMD.ExecuteNonQuery()
            MsgBox("تم إضافة الموظف بنجاح", MsgBoxStyle.Information, "نظام السلطان")
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            EmployeesLoad()
            ClearFields()
        End If
    End Sub
    Dim EntryCheck As Int64 = 0

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If txtEmpNo.Text.Trim <> "" And Val(txtEmpNo.Text) <> 1 Then
            Dim CheckCMD As New SqlCommand("Select * from tblCustomers where EmployeeNo = " & Val(txtEmpNo.Text) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CheckCMD.ExecuteReader
            If reader.Read Then
                MsgBox("لايمكن حذف الموظف لوجود ادخالات على حسابه", MsgBoxStyle.Critical, "نظام السلطان")
                reader.Close()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                Exit Sub
            End If
            If MsgBox("هل تريد بالتأكيد حذف العميل ؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
                Dim DeleteCMD As New SqlCommand("Delete from tblEmployees where EmployeeNo = " & Val(txtEmpNo.Text) & "", Con)
                Dim DeleteRootCMD As New SqlCommand("Delete from tblRoots where RootID = " & Val(txtEmpNo.Text) & "", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                reader.Close()
                DeleteCMD.ExecuteNonQuery()
                DeleteRootCMD.ExecuteNonQuery()
                MsgBox("تم حذف الموظف بنجاح", MsgBoxStyle.Information, "نظام السلطان")
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                EmployeesLoad()
                ClearFields()
                GetSerial()
            End If
        End If
    End Sub

    'Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
    '    ClearFields()
    'End Sub
    'Private Sub DataGridView1_CellClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellClick
    '    If DataGridView1.SelectedRows.Count - 1 >= 0 Then
    '        txtEmpNo.Text = DataGridView1.SelectedRows(0).Cells(0).Value
    '    End If
    'End Sub

    Private Sub DataGridView1_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If e.RowIndex >= 0 Then
            Dim row As DataGridViewRow = DataGridView1.Rows(e.RowIndex)
            txtEmpNo.Text = row.Cells(0).Value.ToString()
        End If
    End Sub
    Sub ClearFields()
        txtEmpNo.Clear()
        txtEmpName.Clear()
        txtMonthlySalary.Clear()
        txtEmpName.Focus()
    End Sub

    Private Sub txtCustomerNo_TextChanged(ByVal sender As Object, ByVal e As EventArgs) Handles txtEmpNo.TextChanged
        Try
            If Val(txtEmpNo.Text) <> 0 Then
                Dim SearchCMD As New SqlCommand("Select * from tblEmployees where EmployeeNo = " & Val(txtEmpNo.Text) & " ", Con)

                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                Dim reader As SqlDataReader = SearchCMD.ExecuteReader
                If reader.Read Then
                    txtEmpName.Text = Trim(reader.Item(1).ToString)
                    txtMonthlySalary.Text = Trim(reader.Item(2).ToString)
                Else
                    txtEmpName.Text = ""
                    txtMonthlySalary.Text = ""
                End If
                If reader.IsClosed Then
                Else
                    reader.Close()
                End If
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If
        Catch ex As Exception
            Exit Sub
        End Try
    End Sub
End Class