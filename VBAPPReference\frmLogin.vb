﻿Imports System.Data.SqlClient
Imports SAPBusinessObjects.WPF.ViewerShared.EMFRender
Imports System.Globalization
Imports System.Threading
Imports System.Security.Cryptography
Imports System.Text
Imports System.IO
Imports System.Net

Public Class frmLogin
    ' Session management variables
    Private CurrentSessionToken As String = ""
    Private CurrentSessionID As Integer = 0

    Private Sub btnLogin_Click(sender As Object, e As EventArgs) Handles btnLogin.Click
        If txtUsername.Text.Trim <> "" And txtPassword.Text.Trim <> "" Then

            Dim SelCMD As SqlCommand = New SqlCommand("Select * from tblUsers where Username = '" & txtUsername.Text.Trim & "' and Password = '" & txtPassword.Text.Trim.GetHashCode & "'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            Dim reader As SqlDataReader = SelCMD.ExecuteReader
            If reader.HasRows Then
                ' Login successful
                reader.Close()

                ' Generate session token and log successful login
                CurrentSessionToken = Guid.NewGuid().ToString()
                LogSessionAttempt(txtUsername.Text.Trim, "SUCCESS", CurrentSessionToken, "")

                ' Save credentials (existing functionality)
                SaveLoginCredentials(txtUsername.Text.Trim, txtPassword.Text.Trim)

                Me.Hide()
                UserName = txtUsername.Text.Trim
                CheckUserType()

                If My.Settings.nameuser = "" Then
                    frmActivation.Show()
                Else
                    frmMain.Show()
                End If
            Else
                ' Login failed
                reader.Close()
                LogSessionAttempt(txtUsername.Text.Trim, "FAILED", "", "Invalid username or password")
                MsgBox("اسم المستخدم او كلمة المرور خطأ", MsgBoxStyle.Critical, "نظام السلطان")
            End If

            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Else
            ' Empty fields - log this as well
            LogSessionAttempt(txtUsername.Text.Trim, "FAILED", "", "Empty username or password")
        End If
    End Sub
    Sub CheckUserType()
        Try
            Dim CMD As New SqlCommand("Select tblUsers.Username, tblGroupsAuth.GroupID, tblGroupsAuth.GroupName from tblUsers INNER JOIN tblGroupsAuth On tblUsers.GroupID = tblGroupsAuth.GroupID where tblUsers.Username = '" & UserName & "'", Con)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CMD.ExecuteReader
            If reader.Read Then
                If Not IsDBNull(reader.Item(2)) Then
                    UserType = Trim(reader.Item(2).ToString)
                Else
                    UserType = ""
                End If
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception
            MsgBox("خطأ في تحميل بيانات المستخدم: " & ex.Message, MsgBoxStyle.Critical, "نظام السلطان")
        End Try
    End Sub
    ' Simple method to log session attempts (No security controls)
    Private Sub LogSessionAttempt(username As String, loginStatus As String, sessionToken As String, failureReason As String)
        Try
            Dim machineInfo As MachineInfo = GetMachineInfo()

            Using cmd As New SqlCommand("sp_LogSessionAttempt", Con)
                cmd.CommandType = CommandType.StoredProcedure

                ' Input parameters
                cmd.Parameters.AddWithValue("@Username", If(String.IsNullOrEmpty(username), "UNKNOWN", username))
                cmd.Parameters.AddWithValue("@LoginStatus", loginStatus)
                cmd.Parameters.AddWithValue("@MachineName", machineInfo.MachineName)
                cmd.Parameters.AddWithValue("@MachineUser", machineInfo.WindowsUser)
                cmd.Parameters.AddWithValue("@SystemUsername", username)
                cmd.Parameters.AddWithValue("@IPAddress", machineInfo.IPAddress)
                cmd.Parameters.AddWithValue("@UserAgent", machineInfo.UserAgent)
                cmd.Parameters.AddWithValue("@FailureReason", If(String.IsNullOrEmpty(failureReason), DBNull.Value, failureReason))

                ' Output parameter for session token
                Dim sessionTokenParam As New SqlParameter("@SessionToken", SqlDbType.NVarChar, 255)
                If Not String.IsNullOrEmpty(sessionToken) Then
                    sessionTokenParam.Value = sessionToken
                Else
                    sessionTokenParam.Value = DBNull.Value
                End If
                sessionTokenParam.Direction = ParameterDirection.InputOutput
                cmd.Parameters.Add(sessionTokenParam)

                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If

                Dim reader As SqlDataReader = cmd.ExecuteReader()
                If reader.Read() Then
                    CurrentSessionID = Convert.ToInt32(reader("SessionID"))
                    If Not IsDBNull(reader("SessionToken")) Then
                        CurrentSessionToken = reader("SessionToken").ToString()
                    End If
                End If
                reader.Close()

                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End Using
        Catch ex As Exception
            ' Log error but don't interrupt login process
            Console.WriteLine($"Error logging session: {ex.Message}")
        End Try
    End Sub

    ' Method to end current session (call this on logout/form close)
    Public Sub EndCurrentSession()
        If Not String.IsNullOrEmpty(CurrentSessionToken) Then
            Try
                Using cmd As New SqlCommand("sp_EndSession", Con)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@SessionToken", CurrentSessionToken)

                    If Con.State <> ConnectionState.Open Then
                        Con.Open()
                    End If

                    cmd.ExecuteNonQuery()

                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                End Using
            Catch ex As Exception
                Console.WriteLine($"Error ending session: {ex.Message}")
            End Try
        End If
    End Sub

    ' Structure to hold machine information
    Private Structure MachineInfo
        Public MachineName As String
        Public WindowsUser As String
        Public IPAddress As String
        Public UserAgent As String
    End Structure

    ' Method to get comprehensive machine information
    Private Function GetMachineInfo() As MachineInfo
        Dim info As New MachineInfo()

        Try
            ' Basic machine info
            info.MachineName = Environment.MachineName
            info.WindowsUser = Environment.UserDomainName & "\" & Environment.UserName

            ' Get IP Address
            info.IPAddress = GetLocalIPAddress()

            ' Create user agent string with system info
            info.UserAgent = $"DesktopApp/1.0 (Windows {Environment.OSVersion}; {Environment.ProcessorCount} cores; {Environment.MachineName})"

        Catch ex As Exception
            ' Fallback values
            info.MachineName = "UNKNOWN"
            info.WindowsUser = "UNKNOWN"
            info.IPAddress = "127.0.0.1"
            info.UserAgent = "DesktopApp/1.0"
        End Try

        Return info
    End Function

    ' Method to get local IP address
    Private Function GetLocalIPAddress() As String
        Try
            Dim host As IPHostEntry = Dns.GetHostEntry(Dns.GetHostName())
            For Each ip As IPAddress In host.AddressList
                If ip.AddressFamily = Net.Sockets.AddressFamily.InterNetwork Then
                    Return ip.ToString()
                End If
            Next
        Catch ex As Exception
            ' Fallback
        End Try
        Return "127.0.0.1"
    End Function

    Private Sub txtUsername_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs)
        If Asc(e.KeyChar) < 65 Or Asc(e.KeyChar) > 90 And Asc(e.KeyChar) < 97 Or Asc(e.KeyChar) > 122 Then
            e.Handled = True
        End If
    End Sub

    Private Sub frmLogin_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ConServerName = My.Settings.server
        ConDatabaseName = My.Settings.database
        ConUserName = My.Settings.id
        ConPassword = My.Settings.passowrd
        ConModel = "SQL"

        ConStr = "Server=" + ConServerName + ";Database=" + ConDatabaseName + ";User Id=" + ConUserName + ";Password=" + ConPassword + ";TrustServerCertificate=True"
        If ConServerName = "" Then
            frmDBCon.ShowDialog()
        End If
        Con.ConnectionString = ConStr
        Dim SelectCMD As New SqlCommand("select * from tblConfig", Con)
        Try
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
        Catch ex As Exception
            frmDBCon.ShowDialog()
        End Try

        Dim reader As SqlDataReader = SelectCMD.ExecuteReader
        If reader.Read Then
            frmRPTHeight = Val(reader.Item("RPTHeight").ToString)
            frmRPTWidth = Val(reader.Item("RPTWidth").ToString)
            StoreName = Trim(reader.Item("StoreName").ToString)
            frmHeight = Val(reader.Item("frmHeight").ToString)
            frmWidth = Val(reader.Item("frmWidth").ToString)
            DBBackupLocation = Trim(reader.Item("BackupLocation").ToString)
            LinkStoreCash = Val(reader.Item("CashWithStore").ToString)
            ItemClassL1 = Trim(reader.Item("ItemLevel1").ToString)
            ItemClassL2 = Trim(reader.Item("ItemLevel2").ToString)
            ItemClassL3 = Trim(reader.Item("ItemLevel3").ToString)
            ItemClassL4 = Trim(reader.Item("ItemLevel4").ToString)
            ItemClassL5 = Trim(reader.Item("ItemLevel5").ToString)
            CashPayPrint = Trim(reader.Item("CashPayPrint").ToString)
            CashRecPrint = Trim(reader.Item("CashRecPrint").ToString)
            VATReg = Trim(reader.Item("VATRegNo").ToString)
            isTestDeploy = Val(reader.Item("isTestDeploy").ToString)

            '==================
            If ItemClassL1 <> "" Then
                ItemClassLevels += 1
            End If
            If ItemClassL2 <> "" Then
                ItemClassLevels += 1
            End If
            If ItemClassL3 <> "" Then
                ItemClassLevels += 1
            End If
            If ItemClassL4 <> "" Then
                ItemClassLevels += 1
            End If
            If ItemClassL5 <> "" Then
                ItemClassLevels += 1
            End If
            '==================
            reader.Close()
        Else
            frmRPTHeight = 600
            frmRPTWidth = 800
            StoreName = ""
            frmHeight = 600
            frmWidth = 800
            DBBackupLocation = ""
            LinkStoreCash = 0
        End If

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If

        ' Load saved credentials
        LoadSavedCredentials()
    End Sub

    Private Sub btnCancel_Click_1(sender As Object, e As EventArgs) Handles btnCancel.Click
        ' End session before closing
        EndCurrentSession()
        Me.Close()
    End Sub

    Private Sub btn_ConnectionChange_Click(sender As Object, e As EventArgs) Handles btn_ConnectionChange.Click
        frmDBCon.ShowDialog()
    End Sub

    ' Handle form closing to end session
    Private Sub frmLogin_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        EndCurrentSession()
    End Sub

    ' Method to save login credentials (existing functionality preserved)
    Private Sub SaveLoginCredentials(username As String, password As String)
        Try
            Dim machineId As String = GetMachineIdentifier()
            Dim windowsUser As String = Environment.UserName

            ' Always save username and machine/user info
            My.Settings.LastUsername = username
            My.Settings.LastMachineId = machineId
            My.Settings.LastWindowsUser = windowsUser

            ' Only save password if remember is checked
            If ckbxPassRemember.Checked Then
                Dim encryptedData As String = EncryptPassword(password, machineId & windowsUser)
                My.Settings.LastPassword = encryptedData
                My.Settings.RememberPassword = True
            Else
                ' Clear password but keep username
                My.Settings.LastPassword = ""
                My.Settings.RememberPassword = False
            End If

            My.Settings.Save()

        Catch ex As Exception
            ' Handle error silently or show message
            Console.WriteLine("Error saving credentials: " & ex.Message)
        End Try
    End Sub

    ' Method to load saved credentials (existing functionality preserved)
    Private Sub LoadSavedCredentials()
        Try
            ' Always try to load username if it exists
            If My.Settings.LastUsername <> "" Then
                Dim currentMachineId As String = GetMachineIdentifier()
                Dim currentWindowsUser As String = Environment.UserName

                ' Validate machine and Windows user
                If My.Settings.LastMachineId = currentMachineId AndAlso My.Settings.LastWindowsUser = currentWindowsUser Then
                    ' Always load username
                    txtUsername.Text = My.Settings.LastUsername

                    ' Only load password if remember password was checked and password exists
                    If My.Settings.RememberPassword AndAlso My.Settings.LastPassword <> "" Then
                        Try
                            Dim decryptedPassword As String = DecryptPassword(My.Settings.LastPassword, currentMachineId & currentWindowsUser)
                            txtPassword.Text = decryptedPassword
                            ckbxPassRemember.Checked = True
                        Catch
                            ' If decryption fails, clear saved password but keep username
                            My.Settings.LastPassword = ""
                            My.Settings.RememberPassword = False
                            My.Settings.Save()
                        End Try
                    Else
                        ' Username exists but no password saved
                        txtPassword.Text = ""
                        ckbxPassRemember.Checked = False
                    End If
                Else
                    ' Different machine or user, clear all saved data
                    ClearSavedCredentials()
                End If
            End If
        Catch ex As Exception
            ' Handle error silently
            Console.WriteLine("Error loading credentials: " & ex.Message)
        End Try
    End Sub

    ' Method to clear saved credentials (existing functionality preserved)
    Private Sub ClearSavedCredentials()
        My.Settings.LastUsername = ""
        My.Settings.LastPassword = ""
        My.Settings.LastMachineId = ""
        My.Settings.LastWindowsUser = ""
        My.Settings.RememberPassword = False
        My.Settings.Save()
    End Sub

    ' Method to get unique machine identifier (existing functionality preserved)
    Private Function GetMachineIdentifier() As String
        Try
            Dim machineId As String = Environment.MachineName & "|" & Environment.ProcessorCount.ToString()
            Return machineId
        Catch
            Return Environment.MachineName
        End Try
    End Function

    ' Method to encrypt password (existing functionality preserved)
    Private Function EncryptPassword(password As String, key As String) As String
        Try
            Dim keyBytes As Byte() = Encoding.UTF8.GetBytes(key.PadRight(32).Substring(0, 32))
            Dim passwordBytes As Byte() = Encoding.UTF8.GetBytes(password)

            Using aes As Aes = Aes.Create()
                aes.Key = keyBytes
                aes.GenerateIV()

                Using encryptor As ICryptoTransform = aes.CreateEncryptor()
                    Dim encryptedBytes As Byte() = encryptor.TransformFinalBlock(passwordBytes, 0, passwordBytes.Length)
                    Dim result As Byte() = New Byte(aes.IV.Length + encryptedBytes.Length - 1) {}
                    Array.Copy(aes.IV, 0, result, 0, aes.IV.Length)
                    Array.Copy(encryptedBytes, 0, result, aes.IV.Length, encryptedBytes.Length)
                    Return Convert.ToBase64String(result)
                End Using
            End Using
        Catch
            Return ""
        End Try
    End Function

    ' Method to decrypt password (existing functionality preserved)
    Private Function DecryptPassword(encryptedPassword As String, key As String) As String
        Try
            Dim keyBytes As Byte() = Encoding.UTF8.GetBytes(key.PadRight(32).Substring(0, 32))
            Dim fullCipher As Byte() = Convert.FromBase64String(encryptedPassword)

            Using aes As Aes = Aes.Create()
                aes.Key = keyBytes

                Dim iv As Byte() = New Byte(aes.IV.Length - 1) {}
                Dim cipher As Byte() = New Byte(fullCipher.Length - aes.IV.Length - 1) {}

                Array.Copy(fullCipher, 0, iv, 0, iv.Length)
                Array.Copy(fullCipher, iv.Length, cipher, 0, cipher.Length)

                aes.IV = iv

                Using decryptor As ICryptoTransform = aes.CreateDecryptor()
                    Dim decryptedBytes As Byte() = decryptor.TransformFinalBlock(cipher, 0, cipher.Length)
                    Return Encoding.UTF8.GetString(decryptedBytes)
                End Using
            End Using
        Catch
            Return ""
        End Try
    End Function

    ' Event handler for remember password checkbox (existing functionality preserved)
    Private Sub ckbxPassRemember_CheckedChanged(sender As Object, e As EventArgs) Handles ckbxPassRemember.CheckedChanged
        If Not ckbxPassRemember.Checked Then
            ' Clear saved credentials when checkbox is unchecked
            ClearSavedCredentials()
        End If
    End Sub

End Class