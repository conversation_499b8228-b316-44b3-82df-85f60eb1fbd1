# نظام الصلاحيات الإلكتروني (Web Authorization System)

## نظرة عامة (Overview)

تم تطوير نظام صلاحيات مستقل للنظام الإلكتروني، منفصل تماماً عن نظام صلاحيات VB.NET GUI. يتيح هذا النظام التحكم الكامل في:
- عناصر القائمة الجانبية (Sidebar Menu Items)
- الإجراءات السريعة في لوحة التحكم (Dashboard Quick Actions)

---

## المكونات (Components)

### 1. قاعدة البيانات (Database Tables)

#### **WebFormMenus** - جدول عناصر القائمة
يحتوي على جميع عناصر القائمة الجانبية بشكل هرمي:

```sql
- MenuID: معرف فريد
- DisplayName: الاسم بالعربية
- DisplayNameEn: الاسم بالإنجليزية  
- Route: المسار (URL)
- Icon: أيقونة Font Awesome
- ParentMenuId: معرف العنصر الأب (للهرمية)
- DisplayOrder: ترتيب العرض
- ModuleName: اسم الوحدة (Sales, Purchase, إلخ)
- IsContainer: هل هو قسم رئيسي أم رابط
- IsActive: هل العنصر نشط
```

#### **WebQuickActions** - جدول الإجراءات السريعة
يحتوي على أزرار الإجراءات السريعة في لوحة التحكم:

```sql
- QuickActionID: معرف فريد
- DisplayName: الاسم بالعربية
- Route: المسار (URL)
- Icon: أيقونة Font Awesome
- ColorClass: لون الزر (primary, success, info, إلخ)
- DisplayOrder: ترتيب العرض
- Category: التصنيف
- IsActive: هل الإجراء نشط
```

#### **WebGroupMenuPermissions** - صلاحيات القوائم للمجموعات
يربط المجموعات بعناصر القائمة مع تحديد الصلاحيات:

```sql
- PermissionID: معرف فريد
- GroupID: معرف المجموعة (من tblGroupsAuth)
- MenuId: معرف عنصر القائمة
- CanView: إمكانية المشاهدة
- CanAdd: إمكانية الإضافة
- CanEdit: إمكانية التعديل
- CanDelete: إمكانية الحذف
- CanPrint: إمكانية الطباعة
```

#### **WebGroupQuickActionPermissions** - صلاحيات الإجراءات السريعة
يربط المجموعات بالإجراءات السريعة:

```sql
- PermissionID: معرف فريد
- GroupID: معرف المجموعة
- QuickActionId: معرف الإجراء السريع
- IsVisible: هل الإجراء مرئي للمجموعة
```

---

### 2. الخدمات (Services)

#### **WebAuthorizationService**
خدمة شاملة لإدارة الصلاحيات مع التخزين المؤقت (Caching):

**الوظائف الرئيسية:**
- `GetAllMenuItemsAsync()` - جلب جميع عناصر القائمة
- `GetMenuItemsByGroupAsync(groupId)` - جلب القوائم المتاحة لمجموعة معينة
- `CanAccessMenuAsync(groupId, route)` - فحص إمكانية الوصول لرابط معين
- `SaveMenuPermissionsAsync()` - حفظ صلاحيات القوائم
- `GetAllQuickActionsAsync()` - جلب جميع الإجراءات السريعة
- `GetQuickActionsByGroupAsync(groupId)` - جلب الإجراءات المتاحة لمجموعة
- `SaveQuickActionPermissionsAsync()` - حفظ صلاحيات الإجراءات السريعة

**التخزين المؤقت (Caching):**
- مدة التخزين: 30 دقيقة
- يتم مسح التخزين المؤقت تلقائياً عند تحديث الصلاحيات

---

### 3. الواجهة (User Interface)

#### **صفحة إدارة الصلاحيات**
المسار: `/WebAuthorization/Index`

**المميزات:**
1. **قائمة المجموعات:**
   - عرض جميع مجموعات المستخدمين
   - عدد المستخدمين في كل مجموعة

2. **تبويبات:**
   - صلاحيات القوائم (Menu Permissions)
   - صلاحيات الإجراءات السريعة (Quick Action Permissions)

3. **صلاحيات القوائم:**
   - عرض هرمي للقوائم
   - تحديد الصلاحيات بالتفصيل (عرض، إضافة، تعديل، حذف، طباعة)
   - تحديد/إلغاء تحديد الكل
   - التحديد التلقائي للعناصر الأب والأبناء

4. **صلاحيات الإجراءات السريعة:**
   - عرض بطاقات ملونة لكل إجراء
   - تحديد/إلغاء تحديد الكل
   - تصنيف حسب الوحدة

---

## التثبيت والإعداد (Installation & Setup)

### 1. تنفيذ سكريبتات قاعدة البيانات

قم بتنفيذ السكريبتات التالية بالترتيب:

```bash
# 1. إنشاء الجداول
05-Scripts/create-web-authorization-tables.sql

# 2. ملء البيانات الأولية
05-Scripts/seed-web-authorization-data.sql
```

**ملاحظة مهمة:** تأكد من تغيير `USE [YourDatabaseName]` إلى اسم قاعدة بياناتك الفعلية.

---

### 2. التحقق من التثبيت

بعد تنفيذ السكريبتات، تحقق من:

```sql
-- عدد عناصر القائمة
SELECT COUNT(*) FROM WebFormMenus

-- عدد الإجراءات السريعة  
SELECT COUNT(*) FROM WebQuickActions

-- صلاحيات مجموعة المدير (GroupID = 1)
SELECT COUNT(*) FROM WebGroupMenuPermissions WHERE GroupID = 1
SELECT COUNT(*) FROM WebGroupQuickActionPermissions WHERE GroupID = 1
```

---

## الاستخدام (Usage)

### 1. الوصول إلى صفحة إدارة الصلاحيات

```
القائمة الجانبية > الإعدادات > صلاحيات النظام الإلكتروني
```

أو مباشرة:
```
/WebAuthorization/Index
```

---

### 2. تعيين صلاحيات لمجموعة

**الخطوات:**
1. اختر المجموعة من القائمة اليمنى
2. انتقل إلى تبويب "صلاحيات القوائم"
3. حدد العناصر المراد منح الوصول إليها
4. للعناصر غير الرئيسية، حدد الصلاحيات التفصيلية (إضافة، تعديل، حذف، طباعة)
5. انتقل إلى تبويب "صلاحيات الإجراءات السريعة"
6. حدد الإجراءات المراد إظهارها في لوحة التحكم
7. اضغط "حفظ جميع التغييرات"

---

### 3. إضافة عناصر قائمة جديدة

لإضافة عنصر قائمة جديد في قاعدة البيانات:

```sql
INSERT INTO WebFormMenus 
    (DisplayName, DisplayNameEn, Route, Icon, ParentMenuId, DisplayOrder, ModuleName, IsContainer, IsActive)
VALUES 
    (N'اسم العنصر', 'Item Name', '/Controller/Action', 'fas fa-icon', NULL, 10, N'Module', 0, 1)
```

---

### 4. إضافة إجراء سريع جديد

```sql
INSERT INTO WebQuickActions 
    (DisplayName, DisplayNameEn, Route, Icon, ColorClass, DisplayOrder, Category, IsActive)
VALUES 
    (N'إجراء جديد', 'New Action', '/Controller/Action', 'fas fa-icon', 'primary', 10, N'Category', 1)
```

---

## البنية الهرمية للقوائم (Menu Hierarchy)

```
├── الصفحة الرئيسية (Dashboard)
├── المبيعات (Sales)
│   ├── فواتير المبيعات
│   ├── نقاط البيع
│   ├── جلسات نقاط البيع
│   ├── مرتجع المبيعات
│   └── بحث المبيعات
├── المشتريات (Purchase)
│   ├── إنشاء فاتورة مشتريات
│   ├── عرض فواتير المشتريات
│   └── مرتجع المشتريات
├── الحركات النقدية (Cash)
│   ├── سندات القبض
│   ├── سندات الصرف
│   ├── القيود اليومية
│   └── المصروفات
├── البيانات الأساسية (Master Data)
│   ├── العملاء
│   ├── الموردين
│   ├── الأصناف
│   ├── تصنيفات الأصناف
│   ├── الموظفين
│   ├── دليل الحسابات
│   ├── إدارة المتاجر
│   └── إدارة المستودعات
├── التقارير (Reports)
│   ├── التقارير المالية
│   └── تقارير المخزون
└── الإعدادات (Settings)
    ├── إدارة المستخدمين
    ├── صلاحيات المجموعات (VB.NET)
    ├── صلاحيات النظام الإلكتروني
    ├── ربط الحسابات
    ├── إعدادات الفواتير
    ├── إعدادات النظام
    └── إعدادات الباركود
```

---

## الإجراءات السريعة الافتراضية (Default Quick Actions)

| الإجراء | اللون | الوحدة |
|---------|------|--------|
| نقطة بيع جديدة | أزرق | Sales |
| فاتورة مبيعات | أخضر | Sales |
| فاتورة مشتريات | فيروزي | Purchase |
| سند قبض | برتقالي | Cash |
| سند صرف | أحمر | Cash |
| إضافة عميل | رمادي | MasterData |
| إضافة صنف | أسود | MasterData |
| التقارير المالية | أزرق | Reports |

---

## الأسئلة الشائعة (FAQ)

### هل يؤثر هذا النظام على نظام VB.NET؟
لا، النظام الجديد مستقل تماماً ويستخدم جداول منفصلة.

### كيف أمنع مجموعة من الوصول لقسم معين؟
ببساطة لا تحدد أي عناصر من ذلك القسم عند تعيين الصلاحيات للمجموعة.

### هل يمكن تخصيص صلاحيات على مستوى المستخدم الفردي؟
حالياً النظام يعمل على مستوى المجموعات فقط. لتخصيص صلاحيات فردية، قم بإنشاء مجموعة خاصة للمستخدم.

### كيف أحدث التخزين المؤقت (Cache) بعد تغيير الصلاحيات يدوياً؟
التخزين المؤقت يُحدث تلقائياً عند استخدام الواجهة. للتحديث اليدوي، أعد تشغيل التطبيق أو انتظر 30 دقيقة.

---

## الأمان (Security)

### الحماية المطبقة:
1. **CSRF Protection:** جميع عمليات POST محمية بتوكنات مضادة للتزوير
2. **Authorization:** صفحة إدارة الصلاحيات محمية بـ `[Authorize]`
3. **Foreign Key Constraints:** حماية سلامة البيانات
4. **Cascade Delete:** حذف الصلاحيات تلقائياً عند حذف مجموعة أو عنصر قائمة

---

## الأداء (Performance)

### تحسينات الأداء:
1. **Memory Caching:** تخزين مؤقت للصلاحيات لمدة 30 دقيقة
2. **Database Indexes:** فهارس على الأعمدة المستخدمة بكثرة
3. **Eager Loading:** تحميل البيانات المرتبطة مرة واحدة
4. **Unique Constraints:** منع التكرارات وتحسين الأداء

---

## التطوير المستقبلي (Future Enhancements)

### مقترحات للتطوير:
1. ✅ تطبيق الصلاحيات على القائمة الجانبية (إخفاء العناصر غير المصرح بها)
2. ✅ تطبيق الصلاحيات على الإجراءات السريعة
3. 🔄 واجهة لإدارة عناصر القائمة والإجراءات السريعة
4. 🔄 تتبع السجل (Audit Log) للتغييرات في الصلاحيات
5. 🔄 تصدير/استيراد الصلاحيات
6. 🔄 نسخ صلاحيات من مجموعة إلى أخرى

---

## الدعم الفني (Support)

للمساعدة أو الإبلاغ عن مشاكل:
- راجع سجلات التطبيق (Application Logs)
- تحقق من اتصال قاعدة البيانات
- تأكد من تنفيذ جميع السكريبتات بنجاح

---

## الملفات المرتبطة (Related Files)

```
Models:
- 02-Shared/Models/AccountingSystem.Models/WebAuthorizationModels.cs

Services:
- 02-Shared/Services/AccountingSystem.Services/WebAuthorizationService.cs

Controllers:
- 03-WebApp/AccountingWeb/AccountingSystem.Web/Controllers/WebAuthorizationController.cs

Views:
- 03-WebApp/AccountingWeb/AccountingSystem.Web/Views/WebAuthorization/Index.cshtml

Scripts:
- 05-Scripts/create-web-authorization-tables.sql
- 05-Scripts/seed-web-authorization-data.sql

Database:
- AccountingDbContext.cs (ConfigureWebAuthorizationEntities)

Configuration:
- Program.cs (WebAuthorizationService registration)
```

---

## الخلاصة (Summary)

نظام الصلاحيات الإلكتروني الجديد يوفر:
- ✅ تحكم كامل في القوائم والإجراءات السريعة
- ✅ واجهة سهلة الاستخدام
- ✅ أداء عالي مع التخزين المؤقت
- ✅ أمان محسّن
- ✅ مستقل عن نظام VB.NET
- ✅ قابل للتوسع والتطوير

---

**تاريخ الإنشاء:** أكتوبر 2025  
**الإصدار:** 1.0  
**الحالة:** جاهز للاستخدام

