﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnVendorSearch.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAAQdJREFUOE9jYBgewHHxx3uOi9/eBbFlIzd1ykRu+S8TufW/TNS2/9JgvP2/dNSO
        /1JROyE4etd/yehdYPVQA97edVz8BiwgHbmlk7Dm3f8lo/cgDBh4MHTCYNaP/2pTv/936/3wXwhdjiCY
        +fN/8+Sv///1ff7/v/3d/3e1r/7bwiXxh8H2/9rZm/9P/PLvf8+n//9b3/7/X/vy//+sK99fIBmAKwxA
        /t/+37Jq2//uj///N7/5/7/q+f//RY///0+/+ecH3ABCAOTn+lf/31U8+/+/4OH//+l3/v+Pu/JzE7o6
        vKD06X/bvAf/z6Xc+v8BpNl838e94nMe6aCrIxqANEvMerQJAPNJ92ZONDuKAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnPhotoDelete.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAAnlJREFUWEftj81OE2EUhrkKtmpAxQgWbEuxPyilUotQbJUWGhITiIkbr0ZXXYgL
        kpn0FhSxGq9BF9ApGtvO9CdlGgyp4GvOmKkzp4Uv2lm44EmeZHJ+3vPNwMA5f0krsTaoL2aSrXhm2apR
        S6wN8nnH0edTn/X5FHp5EEt94vP/DCTpIiQpCVletnr45BnOks8bGVtbF3j+mUCWZyBJR5BlCuxfysrl
        bvM7pwJZ3uwK6d9NfudUkMvdaiyuwEkpk985k2+XJuCkPF9IaegmnJTnCykNu+GkPF9I+bIX3P1hD/aG
        3Cj16FGNejTDeyTPF1K+OgmrxSteVF5kcfTlK3bvJlCy9OibatQrP88as3yf5wspj/hgdW9lHSZtVcPu
        XBKlEZ8hfVPNpJBet+2SPF9I5doUrCqjfjTfvLM9Yi/60NB6nGZolu/zfCGV635wCzeCaG7nbY+wHd/O
        GzN8j+T5QtTRAHqpuEJovt7pHDXR8x+hjIe65k15vhB1LIhelseC2F3K4OfxSec4fVONenzelOcL0Vwh
        cFVXCMr9NNpq1fb3xI9aHYX4Kio99kieL0Qbn4ZVdXwaysIK2tqf4wdv3xua0MMKC2lU2C7J84VoE3dg
        VYk8QFur2Y4X3WFD/gglHLftkjxfSNU9A6vKUgYnh99/H9/5gH3vbKdH31QjjvUWirFHtl2S5wupesKw
        qnpmUHz8FNrLLexPRmw9kmpa9hWKqxvGLO/zfCE17yy4Ve8sVE+4q25KPZrhdZLnC6n5InBSni+kPjUH
        J+X5Qur+KJyU5wupB6J6PXAPzhjVeb6QRmh+qRGISY1gLNeXlOGPxnn+Of8NvwCOl0+29O0whQAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="btnPhotoSelect.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAAlRJREFUWEftl71PE3Ecxjv0f7DVYOLsCwwOKBoGEYiBXt1hMjoYa+JLDCnBJrDo
        QIzxJZoDyiSxCTTVP0DTxcFNLG8Vaq/eUWzDXWlLFSyP+eEi32v5XrlCHHySz9JLns9zv/461OH4n381
        HhWXJQ0TkoqQFdo/5ieb32aP0J49R1KR92qAVS5EDTQOzxdaQupR2rWnUAFHW9TA8f5pMaJ4PrR8jPbV
        HCrgaIvmcKJ/un4jqIBDDDjp/1y/EVTAcTGawyl/rH4jqICj40Mejf6YaUTTo4VCy5vV2i8mFbB8A1rD
        33F2LLXNmTEFzcE/nIukE7SfjUlgE9rPhhbYhfazoQV2of1saIFdaD8bWmAX2s+GFtiF9rOhBXah/Wxo
        QS1c+rKO7hkdPXEdPQs6umOrBzNAUrfgmdMxmC0iWAaCv4DRTeDFD+BpCeEnRRymnqqh5VaQ5gw8L21i
        /C/5y5/AsxLwuAAM56A8zFgcQcs5xLGLN68qXwMe6MBgBlPUVTFUwCHNGjuOvZJ8KAsMpFG+uwIX9ZlC
        BRy9cZ2V318B+jTgtoIO6jOFCjjEjbciv5MCfEtopz5TqICja1bfvu2c/MYSytcT+/AViEvYt1zYVe5L
        AFfjmKSuivGqWKMSjq4ZHUOZjary3k/FjabIYqT1HZzUZ4pXhVTLHxOPhrCUKn/1zOe0m8nS+kB6x7Fv
        XYtjSshPvzeSDeOpEUfAwgg7ET+1Wwo6fQl0XlnEIfGZeHMhd8sK3LLyet9HVEwATiEXI1xycoI+PpgE
        4HTJyVduWbn3G2OVt/qGJ4s3AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnSave.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAA2xJREFUWEftlttuE1cUhnkLEBIxJE4IpRDSuz5IWw6i6gvwBkioQqhqe8Mth6IS
        kibkYCcO5yYFJITaclGI44wntufkmfHsOfkQN01QpL/a256Q2RZiK1busqRf9t33rbXX3vaBA/u1X1x9
        sRjeHl4It4YXquifcpAYtdHzq456o4nGWhONRhP1+hqqtQb8sAbXC+EQH1aFwDArUHUTRcVAvqDi23kZ
        J2aJOZhxvuQ5H60IPvx7GBOgwChhtY4grMHzQziuD9txcf7OC5y78wLX5t+gUNIhFZSWwJyLE2lSH0wJ
        SkTwM5yAH1ThBzX26QXVVueuj4rjwbQJDl6eYfnm9h+QixpW5BIusgm4GKRJOWISEfzMs7gAcQMQrx03
        YGOnnVM4Hf1OgXxBQS5fxMVMvgVPExynSTllntdREXzoaVyAwmzHa8dlZ27aDoNrhrUt8PWtRdZ9VlrF
        BSqwDScYmCHgeR0VwYeeBuif/CBQthyY7dDvFKyXbaiGhZJW3hb46tYilqUC3uVkXJhdicEHZpxPC0Tw
        oScBkpHAXR0l3YJWtlsxLLbtilZGUTXY0tHOKfxK6jWWcjL+yUo4m16JwQemhQRa8NNUYJowgcQ9E9df
        qVhVy6zbkmqwq0bBdOHomdOx084j+NyfKxgeL8bg/UICbfjpxwFOPvSQnCJI3nfQN26id1RH7z3tQ0Y0
        HOuIytI3pjPoTjjdKZ7XURH81GMfpx75+PxhOw88nKSZ9/AZTcZr3XGa6KpxC8fDk1MVAYE9hCcnBQT2
        Et53X0RgD+FCAjz80t81ZIx/8UBvYl5bQ0ZtYE6pI1WsYmY1xLQcYCrvY1LyMJFzMJ6tYGzJxshbC98t
        2DF434SowI7O6/+9x+bmJjY2NrC+vo5mk/4iNlCr1RCGIYIggOd5IISgUqnAsiyYpgnDMCApegzeO2EL
        CHBj3y1c0zQoihKD944LCPBn3g28VCrF4Md+ExHgFq4beKFQiMGFBPht7wYuy3IMfnTMEhDgrlo3cEmS
        YnAhAf6edwPP5XIx+NFRQYEjP79Bz4+v2SPTDXx5eZmBD137C4evvkJCSGCWbFF44oeX7IVz/d3D3y5l
        mQCFH/7+ORIj5S2e11HH0+TmYNrZip7XX7L0r9ju4D89l9nYaecU3nPXvMHz9mu//gcEcQiOOZW3RQAA
        AABJRU5ErkJggg==
</value>
  </data>
  <data name="btnCancel.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAA8pJREFUWEftlstyGlcQhlnE+zh2kl1Swi5IiLiY63BHGpBmuCOMBLbL1ipvESXy
        E7iSyqNYzks4sbxIlb1MopnhMjfGlUgoVqcaZaihGUmAvfS3FP85H+Kc090OxweWYFju3DQqbZ/ObRd1
        vskbfMurs/UbNPde0SoPbhuV9v6wvPPCKG3DsNiCIX8XhlwT9M0t0AtboLGN31S29oO2Vr9F1y+NUWl/
        /qba/nlY3hkZ5R24UJ5vgMbWQVuvgbJWGam58k9GvPEZ3W8h/q534ka1IxqVNswrV9eqoOYqoGbLIKeL
        fSVdzNB958Kot7eNaufYTq5zWyN9o3GoFRpPVbb+VGPrh+p6dWSVK5kiKGke5CR/LMe5Ft3/UvTaTsJW
        zjVf6VzzkZqtfUzXaKnSdSVXeaRky6+UdAmUFMo5kBObMGAKx3KEjdM1tpyfeUeyyvVi62zItfYgm/2I
        5ilKhr8nJ/m3plyOb8AgVoBBNC8KAe5Tmp8BLxyVa5vNDs3ZIadKLTnJn9rIoR9hoR/M/UjXTIFPbea2
        c609mrPDTt6L5s8m8tAa9ILZEymcctK1E4xy+zE9cwh9e43mKHbyfjT/7yDKPuyF139HeT+Yg96dLPR8
        me/p+gm0yOCFoxnKRfJ+hL2Pn3eD2d2J3J8ByZv8le4xZlxeyVOzu+1WrpIjmjd1vRvIjlDe9aXwC5z9
        4WE+md4JL19p20+KzAuasTKP3KTnzxyivOtNgvRNAqTVmI9mHNhUrBVOy28d0IzJInJE8iafTeSeOHRd
        DEczDq3Y4qzlVc3Xn9EMsqgckVaTv5hy6SsGjtyRTZpxvNlo+Kdre23mCJaRI6In8dKUi+4oSC67I2Dr
        N6yNBWs7llfz82XleOHEr5lTUy64IvaXEMF+bu1qWNvx78vKEdET2zXloisCwu3wc5qZMB4mLC0VG4uc
        KrWXlUModE10M68tchCcoe9obgJOMkqucjJpqekSttOpxjKvHBHdsX0iPxG/8K3Q3BQ4yZhya0tdVC65
        mfuCO3o2kd8KwV8rd57Q3Aw4Rinpkng+TFjlhbdKJH+P5innP3tsn8qPnAHhyBW6SfO2yJlyfDzJ0JYa
        Xn+NtR3LK10zvu2e2O7MmZ/L//nzyyBD11wKjlE4yUz18/+7WjeQOe350y8lX+pAWk0c4Du3PrUZ+Urg
        Lt1/LgbRPIOTjFVudrWp2m4pMvRnX/g/p+AYhZMMDhNzy52hE7xwc5/5POAkg8ME9vNL5M8FZ3Dvyqf2
        ruCFk9yMV3DFeMEV5iV3yHthef3AFfwHOPPloOxzEnYAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="OpenFileDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>