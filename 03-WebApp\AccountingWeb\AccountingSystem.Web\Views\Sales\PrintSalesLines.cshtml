@model System.Data.DataTable
@{
    Layout = null;
    var filters = ViewBag.Filters;
}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>تقرير بنود الفواتير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet" />
    <style>
        body { font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji"; }
        .table th, .table td { font-size: 12px; }
        .header { margin: 10px 0; }
        @@media print {
            .no-print { display:none; }
            .table th, .table td { font-size: 11px; }
        }
    </style>
</head>
<body class="container-fluid py-3">
    <div class="no-print mb-2">
        <button class="btn btn-primary" onclick="window.print()"><i class="fas fa-print"></i> طباعة</button>
        <button class="btn btn-secondary" onclick="window.close()">إغلاق</button>
    </div>

    <div class="header">
        <h4 class="mb-1">تقرير بنود الفواتير</h4>
        <div class="text-muted small">
            @* Optionally render selected filters *@
            @if (filters != null)
            {
                <div>فترة: @((filters.dateFrom != null ? ((DateTime)filters.dateFrom).ToString("yyyy-MM-dd") : "-") ) إلى @((filters.dateTo != null ? ((DateTime)filters.dateTo).ToString("yyyy-MM-dd") : "-"))</div>
                <div>رقم الفاتورة: @(filters.invoiceNo ?? "-") | رقم الحساب: @(filters.accountNo?.ToString() ?? "-") | ملاحظات: @(filters.notes ?? "-")</div>
                <div>المستخدم: @(filters.user ?? "الكل") | المخزن: @(filters.store ?? "الكل") | نوع الفاتورة: @(filters.invoiceType ?? "الكل")</div>
                <div>طريقة الدفع: @(filters.paymentMethod ?? "الكل") | رقم مرجع: @(filters.reference ?? "-") | رقم الصنف: @(filters.itemCode ?? "-")</div>
            }
        </div>
    </div>

    @if (Model != null && Model.Rows.Count > 0)
    {
        <table class="table table-bordered table-striped">
            <thead class="table-dark">
                <tr>
                    @foreach (System.Data.DataColumn col in Model.Columns)
                    {
                        <th>@col.ColumnName</th>
                    }
                </tr>
            </thead>
            <tbody>
                @foreach (System.Data.DataRow row in Model.Rows)
                {
                    <tr>
                        @foreach (System.Data.DataColumn col in Model.Columns)
                        {
                            <td>@(row[col] ?? "")</td>
                        }
                    </tr>
                }
            </tbody>
        </table>
    }
    else
    {
        <div class="alert alert-info">لا توجد نتائج للعرض.</div>
    }

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        window.addEventListener('load', function () {
            // Auto print in modal iframe context
            setTimeout(function () { window.print(); }, 300);
        });
    </script>
</body>
</html>

