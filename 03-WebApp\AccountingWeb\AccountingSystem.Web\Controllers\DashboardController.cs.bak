using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Models;
using AccountingSystem.Data;
using AccountingSystem.Web.Models;
using Microsoft.EntityFrameworkCore;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly IApplicationStateService _appState;
        private readonly IAuthorizationService _authorizationService;
        private readonly AccountingDbContext _context;
        private readonly ILogger<DashboardController> _logger;

        public DashboardController(
            IApplicationStateService appState,
            IAuthorizationService authorizationService,
            AccountingDbContext context,
            ILogger<DashboardController> logger)
        {
            _appState = appState;
            _authorizationService = authorizationService;
            _context = context;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            return Content("Dashboard - temporarily disabled for testing. Please go to /Test for basic testing.");
        }

        private async Task<List<UserPermissionViewModel>> GetUserPermissionsAsync()
        {
            var permissions = new List<UserPermissionViewModel>();

            try
            {
                var username = User.Identity?.Name;
                if (string.IsNullOrEmpty(username)) return permissions;

                // Get user's group ID
                var user = await _context.Users
                    .Include(u => u.Group)
                    .FirstOrDefaultAsync(u => u.Username == username);

                if (user?.GroupID == null) return permissions;

                // Group 1 has full access (Admin)
                if (user.GroupID == 1)
                {
                    permissions = await _context.FormPermissions
                        .Select(f => new UserPermissionViewModel
                        {
                            FormName = f.FormName,
                            FormTitle = f.FormTitle ?? f.FormName,
                            ModuleName = f.ModuleName ?? "General",
                            CanView = true,
                            CanAdd = true,
                            CanEdit = true,
                            CanDelete = true,
                            CanPrint = true
                        }).ToListAsync();
                }
                else
                {
                    // Get specific permissions for the user's group
                    permissions = await _context.GroupFormPermissions
                        .Include(gfp => gfp.Form)
                        .Where(gfp => gfp.GroupID == user.GroupID && gfp.IsActive)
                        .Select(gfp => new UserPermissionViewModel
                        {
                            FormName = gfp.Form.FormName,
                            FormTitle = gfp.Form.FormTitle ?? gfp.Form.FormName,
                            ModuleName = gfp.Form.ModuleName ?? "General",
                            CanView = gfp.CanView,
                            CanAdd = gfp.CanAdd,
                            CanEdit = gfp.CanEdit,
                            CanDelete = gfp.CanDelete,
                            CanPrint = gfp.CanPrint
                        }).ToListAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions for {Username}", User.Identity?.Name);
            }

            return permissions;
        }

        private async Task<DashboardStatisticsViewModel> GetDashboardStatisticsAsync()
        {
            var stats = new DashboardStatisticsViewModel();

            try
            {
                // Get today's date range
                var today = DateTime.Today;
                var tomorrow = today.AddDays(1);

                // Sample statistics - replace with actual queries based on your tables
                stats.TodaySales = await GetTodaySalesAsync(today, tomorrow);
                stats.TodayPurchases = await GetTodayPurchasesAsync(today, tomorrow);
                stats.TotalCustomers = await GetTotalCustomersAsync();
                stats.TotalVendors = await GetTotalVendorsAsync();
                stats.LowStockItems = await GetLowStockItemsCountAsync();
                stats.PendingInvoices = await GetPendingInvoicesCountAsync();
                stats.CashBalance = await GetCashBalanceAsync();
                stats.TotalInventoryValue = await GetTotalInventoryValueAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard statistics");
            }

            return stats;
        }

        private async Task<decimal> GetTodaySalesAsync(DateTime today, DateTime tomorrow)
        {
            try
            {
                // Replace with actual sales table query
                // Example: return await _context.SalesInvoices
                //     .Where(s => s.InvoiceDate >= today && s.InvoiceDate < tomorrow)
                //     .SumAsync(s => s.TotalAmount);
                return 0; // Placeholder
            }
            catch
            {
                return 0;
            }
        }

        private async Task<decimal> GetTodayPurchasesAsync(DateTime today, DateTime tomorrow)
        {
            try
            {
                // Replace with actual purchase table query
                return 0; // Placeholder
            }
            catch
            {
                return 0;
            }
        }

        private async Task<int> GetTotalCustomersAsync()
        {
            try
            {
                // Replace with actual customers table query
                // Example: return await _context.Customers.CountAsync(c => c.IsActive);
                return 0; // Placeholder
            }
            catch
            {
                return 0;
            }
        }

        private async Task<int> GetTotalVendorsAsync()
        {
            try
            {
                // Replace with actual vendors table query
                return 0; // Placeholder
            }
            catch
            {
                return 0;
            }
        }

        private async Task<int> GetLowStockItemsCountAsync()
        {
            try
            {
                // Replace with actual inventory query
                return 0; // Placeholder
            }
            catch
            {
                return 0;
            }
        }

        private async Task<int> GetPendingInvoicesCountAsync()
        {
            try
            {
                // Replace with actual pending invoices query
                return 0; // Placeholder
            }
            catch
            {
                return 0;
            }
        }

        private async Task<decimal> GetCashBalanceAsync()
        {
            try
            {
                // Replace with actual cash account balance query
                return 0; // Placeholder
            }
            catch
            {
                return 0;
            }
        }

        private async Task<decimal> GetTotalInventoryValueAsync()
        {
            try
            {
                // Replace with actual inventory value query
                return 0; // Placeholder
            }
            catch
            {
                return 0;
            }
        }

        private List<QuickActionViewModel> GetQuickActions(List<UserPermissionViewModel> permissions)
        {
            var actions = new List<QuickActionViewModel>();

            // Define quick actions based on permissions
            var quickActionMappings = new Dictionary<string, QuickActionViewModel>
            {
                ["btnSalesInvoice"] = new QuickActionViewModel
                {
                    Title = "فاتورة مبيعات",
                    Description = "إنشاء فاتورة مبيعات جديدة",
                    Icon = "fas fa-file-invoice",
                    Color = "primary",
                    Action = "/Sales/Invoice/Create"
                },
                ["btnPurchaseInvoice"] = new QuickActionViewModel
                {
                    Title = "فاتورة مشتريات",
                    Description = "إنشاء فاتورة مشتريات جديدة",
                    Icon = "fas fa-shopping-cart",
                    Color = "success",
                    Action = "/Purchase/Invoice/Create"
                },
                ["btnMasterCustomers"] = new QuickActionViewModel
                {
                    Title = "إدارة العملاء",
                    Description = "إضافة وتعديل بيانات العملاء",
                    Icon = "fas fa-users",
                    Color = "info",
                    Action = "/Master/Customers"
                },
                ["btnMasterItems"] = new QuickActionViewModel
                {
                    Title = "إدارة الأصناف",
                    Description = "إضافة وتعديل بيانات الأصناف",
                    Icon = "fas fa-boxes",
                    Color = "warning",
                    Action = "/Master/Items"
                },
                ["btnCashTrxReceipt"] = new QuickActionViewModel
                {
                    Title = "سند قبض",
                    Description = "إنشاء سند قبض نقدي",
                    Icon = "fas fa-hand-holding-usd",
                    Color = "success",
                    Action = "/Cash/Receipt/Create"
                },
                ["btnCashTrxPay"] = new QuickActionViewModel
                {
                    Title = "سند صرف",
                    Description = "إنشاء سند صرف نقدي",
                    Icon = "fas fa-money-bill-wave",
                    Color = "danger",
                    Action = "/Cash/Payment/Create"
                }
            };

            // Add actions based on user permissions
            foreach (var permission in permissions.Where(p => p.CanView))
            {
                if (quickActionMappings.ContainsKey(permission.FormName))
                {
                    actions.Add(quickActionMappings[permission.FormName]);
                }
            }

            return actions.Take(6).ToList(); // Limit to 6 quick actions
        }
    }
}
