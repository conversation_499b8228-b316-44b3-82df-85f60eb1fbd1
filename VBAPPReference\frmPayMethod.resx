﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnSave.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAACxIAAAsSAdLdfvwAAANsSURBVFhH7ZbbbhNXFIZ5CxASMSROCKUQ0rs+SFsOouoL
        8AZIqEKoanvDLYeiEpIm5GAnDucmBSSE2nJRiOOMJ7bn5Jnx7Dn5EDdNUKS/2tuekNkWYitW7rKkX/bd
        96211972gQP7tV9cfbEY3h5eCLeGF6ron3KQGLXR86uOeqOJxloTjUYT9foaqrUG/LAG1wvhEB9WhcAw
        K1B1E0XFQL6g4tt5GSdmiTmYcb7kOR+tCD78exgToMAoYbWOIKzB80M4rg/bcXH+zgucu/MC1+bfoFDS
        IRWUlsCcixNpUh9MCUpE8DOcgB9U4Qc19ukF1Vbnro+K48G0CQ5enmH55vYfkIsaVuQSLrIJuBikSTli
        EhH8zLO4AHEDEK8dN2Bjp51TOB39ToF8QUEuX8TFTL4FTxMcp0k5ZZ7XURF86GlcgMJsx2vHZWdu2g6D
        a4a1LfD1rUXWfVZaxQUqsA0nGJgh4HkdFcGHngbon/wgULYcmO3Q7xSsl22ohoWSVt4W+OrWIpalAt7l
        ZFyYXYnBB2acTwtE8KEnAZKRwF0dJd2CVrZbMSy27YpWRlE12NLRzin8Suo1lnIy/slKOJteicEHpoUE
        WvDTVGCaMIHEPRPXX6lYVcus25JqsKtGwXTh6JnTsdPOI/jcnysYHi/G4P1CAm346ccBTj70kJwiSN53
        0DduondUR+897UNGNBzriMrSN6Yz6E443Sme11ER/NRjH6ce+fj8YTsPPJykmffwGU3Ga91xmuiqcQvH
        w5NTFQGBPYQnJwUE9hLed19EYA/hQgI8/NLfNWSMf/FAb2JeW0NGbWBOqSNVrGJmNcS0HGAq72NS8jCR
        czCerWBsycbIWwvfLdgxeN+EqMCOzuv/vcfm5iY2Njawvr6OZpP+IjZQq9UQhiGCIIDneSCEoFKpwLIs
        mKYJwzAgKXoM3jthCwhwY98tXNM0KIoSg/eOCwjwZ94NvFQqxeDHfhMR4BauG3ihUIjBhQT4be8GLsty
        DH50zBIQ4K5aN3BJkmJwIQH+nncDz+VyMfjRUUGBIz+/Qc+Pr9kj0w18eXmZgQ9d+wuHr75CQkhglmxR
        eOKHl+yFc/3dw98uZZkAhR/+/jkSI+UtntdRx9Pk5mDa2Yqe11+y9K/Y7uA/PZfZ2GnnFN5z17zB8/Zr
        v/4HBHEIjjmVt0UAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnDelete.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAACxIAAAsSAdLdfvwAAAOTSURBVFhH7dZrUxJRGAdwPkPXdzVYYTeIUBAUFAQSFhDM
        UJjpkzRTppU0WXabvk4zNVPTi64foBeVe+EOu06JM/hvni0IDru4Ki/7vxN1f3vOeXjOYzL9zz4ix7NH
        lETGVo8scPXofFSJpq31YOow+3cDTS1x/bSSyCzL8cXPSmwBMpeGHL0GOTKP+sxV1MNXUQvOfaoGk7dr
        gdQp9v/3HSWROb45m3khxxe3lfgidPHQHGrBFGrTSVQCie2qP/5c8cwdY5+3p/xMZT3KbFZUEhkYxauB
        WVT9CVSn4ij7uGLFx02yzzUUJZVZUGazW/vFK5MxVHwcyuPRrbInkmaf3zf15OL4IPDKRBTl8RmUPOFG
        wRk2thN/zjwrDQove66gNBZGyRUSBbv3KOv1hApOC69H5lEIJ1EhWAcv+jiI3gjKvTiKzmkUHf5nrNcV
        +qppVTvhfDIL5c07iLl1FKeTPXjex0FYeQD59Vt8DyVRYvGRAAqOqYZk9Q6xbjtKPLPC4rTttHLC1TR3
        IN5/jALBHbh475H6O4r86g14V7ALLzr8KNinULB5l1i3HTm28IXF6cxp22nlLUB9idw6CpOxHhzNJvil
        HPLOYC9+yQfJOvGRddWo7VUDbxUcbbvU+RI7O5DWnkK4u9b1mbD6CKIOnrd5IV2c2Pl+3n2I9U2bXNqu
        h7cKjrZdXH34D/yLGsXzFycgXRiHZBmzsb6JLpV+eKvgaNvVVbfgveLn3Mhb3BHWN9W4dGQ3vF3t7AsA
        EFbXIYwGdsWls2Pgh50zrG/avDJ3yTDeceZdR3DvIYTLU31xcdgFaXjEyvomus/3iou06tv31cpvfcbf
        WQNPsA4unHFqFyGF7nMtnDocNZnOrxr9TGdO284vd7xEs4mNGysQCGZw8YwTwunR96zbjjpMaPR2aq/U
        4Vi8VXC07Ru3cu2XqL98jW/nPVo4hKGRm6zbDk0yFX+iwV4s1NupvaodbimnWe207bRywr+6QhA1cUdD
        PGEzs25XaJLRutWot1N71etwdOa07bRyTfyUAxtm+2PW6wmNURVfTNS5UnVxrYLrxHmzXeAtI0dYTzPl
        ybhHnWQGh//6cdLhZp2+oTGq5A5vDQQ326+xzzeUkivkpknmALiw55WzoTGKJhkaJgzjQ47GhvnyE8Nn
        biQ0ydAwQfd5H/yDMOS4tetX7aChVioNu62CZSwqWEaj1Nt12+v/7JLfbzfy/DB7M/YAAAAASUVORK5C
        YII=
</value>
  </data>
</root>