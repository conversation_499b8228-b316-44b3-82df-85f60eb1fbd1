using Microsoft.AspNetCore.Mvc;

namespace AccountingSystem.Web.Controllers
{
    public class SimpleTestController : Controller
    {
        public IActionResult Index()
        {
            ViewBag.Message = "SimpleTest Controller is working!";
            return View();
        }

        public IActionResult SystemSetupTest()
        {
            return Json(new
            {
                status = "success",
                message = "SimpleTest controller can access SystemSetupTest action",
                timestamp = DateTime.Now
            });
        }
    }
}
