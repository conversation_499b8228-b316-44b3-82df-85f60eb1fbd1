﻿Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports CrystalDecisions.Windows.Forms
Imports System.Data.SqlClient

Public Class frmCashStatementAllPrint

    Private Sub frmCashStatementAllPrint_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        '  frmCashStatement.Enabled = True
    End Sub


    Private Sub frmCashStatementAllPrint_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Me.Size = New Size(frmRPTWidth, frmRPTHeight)
        'If frmCashStatement.cmbxTrxType.Text.Trim = "الكل" Then
        '    Dim RPT As New rptCashStatementAll
        '    UpdateCRDataSource(RPT)
        '    RPT.Refresh()
        '    RPT.SetParameterValue(0, frmCashStatement.dtpFrom.Value.Date)
        '    RPT.SetParameterValue(1, frmCashStatement.dtpTo.Value.Date)
        '    'RPT.SetParameterValue(2, frmCashStatement.cmbxCashier.Text.Trim)
        '    Dim OpPay As Decimal = 0
        '    Dim OpRec As Decimal = 0
        '    Dim CMDRec As New SQLCommand("Select sum(Amount) as Amount from tblCashTrxView where DocType = 'قبض' and DocDate < '" & Format(frmCashStatement.dtpFrom.Value.Date, "yyyy/MM/dd") & "'", Con)
        '    Dim CMDPay As New SQLCommand("Select sum(Amount) as Amount from tblCashTrxView where DocType = 'صرف' and DocDate < '" & Format(frmCashStatement.dtpFrom.Value.Date, "yyyy/MM/dd") & "'", Con)
        '    If Con.State <> ConnectionState.Open Then
        '        Con.Open()
        '    End If
        '    dim reader As SqlDataReader cmdRec.ExecuteReader
        '    If reader.Read Then
        '        OpRec = Val(reader.Item("Amount").ToString)
        '    Else
        '        OpRec = 0
        '    End If
        '    If reader.IsClosed Then
        '    Else
        '        reader.Close()
        '    End If
        '    dim reader As SqlDataReader cmdPay.ExecuteReader
        '    If reader.Read Then
        '        OpPay = Val(reader.Item("Amount").ToString)
        '    Else
        '        OpPay = 0
        '    End If
        '    If reader.IsClosed Then
        '    Else
        '        reader.Close()
        '    End If


        '    If Con.State <> ConnectionState.Closed Then
        '        Con.Close()
        '    End If
        '    Dim Opening As Int64 = OpRec - OpPay

        '    RPT.DataDefinition.FormulaFields(0).Text = "" & Val(Opening) & ""
        '    CrystalReportViewer1.ReportSource = RPT
        'ElseIf frmCashStatement.cmbxTrxType.Text.Trim = "صرف" Then
        '    Dim RPT As New rptCashStatementAllOut
        '    UpdateCRDataSource(RPT)
        '    RPT.Refresh()
        '    RPT.SetParameterValue(0, frmCashStatement.dtpFrom.Value.Date)
        '    RPT.SetParameterValue(1, frmCashStatement.dtpTo.Value.Date)
        '    RPT.SetParameterValue(2, frmCashStatement.cmbxCashier.Text)
        '    CrystalReportViewer1.ReportSource = RPT
        'ElseIf frmCashStatement.cmbxTrxType.Text.Trim = "قبض" Then
        '    Dim RPT As New rptCashStatementAllIn
        '    UpdateCRDataSource(RPT)
        '    RPT.Refresh()
        '    RPT.SetParameterValue(0, frmCashStatement.dtpFrom.Value.Date)
        '    RPT.SetParameterValue(1, frmCashStatement.dtpTo.Value.Date)
        '    RPT.SetParameterValue(2, frmCashStatement.cmbxCashier.Text)
        '    CrystalReportViewer1.ReportSource = RPT
        'End If

    End Sub


End Class