@{
    ViewData["Title"] = "الوصول مرفوض";
    Layout = "_Layout";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white text-center">
                    <h3><i class="fas fa-ban"></i> الوصول مرفوض</h3>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                    </div>
                    <h4 class="text-danger mb-3">ليس لديك صلاحية للوصول إلى هذه الصفحة</h4>
                    <p class="text-muted mb-4">
                        يرجى التواصل مع مدير النظام للحصول على الصلاحيات المطلوبة.
                    </p>
                    <div class="d-grid gap-2">
                        <a href="@Url.Action("Index", "Dashboard")" class="btn btn-primary">
                            <i class="fas fa-home"></i> العودة إلى الصفحة الرئيسية
                        </a>
                        <a href="@Url.Action("Logout", "Account")" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
    }
    
    .card-header {
        border-radius: 10px 10px 0 0;
    }
    
    .btn {
        border-radius: 25px;
        padding: 10px 20px;
    }
</style>
