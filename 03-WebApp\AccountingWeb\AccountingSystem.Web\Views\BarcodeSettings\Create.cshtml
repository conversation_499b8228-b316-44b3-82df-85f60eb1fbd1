@model AccountingSystem.Models.BarcodeSettings

@{
    ViewData["Title"] = "Create Barcode Settings";
}

<h1>@ViewData["Title"]</h1>

<h4>Barcode Settings</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Shop" class="control-label"></label>
                <input asp-for="Shop" class="form-control" />
                <span asp-validation-for="Shop" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="BarcodeType" class="control-label"></label>
                <input asp-for="BarcodeType" class="form-control" />
                <span asp-validation-for="BarcodeType" class="text-danger"></span>
            </div>
            <div class="form-group form-check">
                <label class="form-check-label">
                    <input class="form-check-input" asp-for="EnableEmbeddedWeight" /> @Html.DisplayNameFor(model => model.EnableEmbeddedWeight)
                </label>
            </div>
            <div class="form-group">
                <label asp-for="EmbeddedFormat" class="control-label"></label>
                <input asp-for="EmbeddedFormat" class="form-control" />
                <span asp-validation-for="EmbeddedFormat" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="WeightDivisor" class="control-label"></label>
                <input asp-for="WeightDivisor" class="form-control" />
                <span asp-validation-for="WeightDivisor" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CurrencyDivisor" class="control-label"></label>
                <input asp-for="CurrencyDivisor" class="form-control" />
                <span asp-validation-for="CurrencyDivisor" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Notes" class="control-label"></label>
                <input asp-for="Notes" class="form-control" />
                <span asp-validation-for="Notes" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Barcode" class="control-label"></label>
                <input asp-for="Barcode" class="form-control" />
                <span asp-validation-for="Barcode" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Weight" class="control-label"></label>
                <input asp-for="Weight" class="form-control" />
                <span asp-validation-for="Weight" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="FixedCode" class="control-label"></label>
                <input asp-for="FixedCode" class="form-control" />
                <span asp-validation-for="FixedCode" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 