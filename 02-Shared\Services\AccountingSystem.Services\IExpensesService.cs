using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public class ExpenseInsertResult
    {
        public bool Success { get; set; }
        public int InvoiceID { get; set; }
        public string Message { get; set; } = string.Empty;
        public string DuplicateWarning { get; set; } = string.Empty;
    }

    public class SimpleAccountDto
    {
        public string AccountCode { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
    }

    public interface IExpensesService
    {
        Task<List<SimpleAccountDto>> GetCashierAccountsAsync(); // Parent: GLConfig.EntryReferenceModule = "نقدية"
        Task<List<SimpleAccountDto>> GetExpenseAccountsAsync(); // Parent: GLConfig.EntryReferenceModule = "المصروفات"
        Task<ExpenseInsertResult> InsertExpenseAsync(
            System.DateTime invoiceDate,
            string? creditCashier,
            string debitExpense,
            decimal invoiceAmount,
            decimal taxAmount,
            long? vendorNo,
            string? vendorInvoiceNo,
            string? vendorName,
            string? vendorVATRN,
            int? employeeBuyer,
            string? notes,
            string? store,
            string createdBy,
            bool ignoreDuplicate,
            IFormFile? photoFile
        );
    }
}

