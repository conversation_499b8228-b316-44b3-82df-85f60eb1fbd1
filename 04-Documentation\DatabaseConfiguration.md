# Database Configuration Guide

## Connection String Setup

The web application is configured to use the same database as your VB.NET Windows application. This ensures both applications can work simultaneously without any database schema changes.

### Configuration Files

#### appsettings.json (Production)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=YOUR_SERVER_NAME;Database=YOUR_ACCOUNTING_DATABASE;Trusted_Connection=true;TrustServerCertificate=true;",
    "AccountingDatabase": "Server=YOUR_SERVER_NAME;Database=YOUR_ACCOUNTING_DATABASE;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

#### appsettings.Development.json (Development)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=AccountingDB_Dev;Trusted_Connection=true;TrustServerCertificate=true;",
    "AccountingDatabase": "Server=localhost;Database=AccountingDB_Dev;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

## Connection String Formats

### Windows Authentication (Recommended)
```
Server=YOUR_SERVER_NAME;Database=YOUR_DATABASE_NAME;Trusted_Connection=true;TrustServerCertificate=true;
```

### SQL Server Authentication
```
Server=YOUR_SERVER_NAME;Database=YOUR_DATABASE_NAME;User Id=YOUR_USERNAME;Password=YOUR_PASSWORD;TrustServerCertificate=true;
```

### Local SQL Server Express
```
Server=.\\SQLEXPRESS;Database=YOUR_DATABASE_NAME;Trusted_Connection=true;TrustServerCertificate=true;
```

### Azure SQL Database
```
Server=tcp:YOUR_SERVER.database.windows.net,1433;Initial Catalog=YOUR_DATABASE;Persist Security Info=False;User ID=YOUR_USERNAME;Password=YOUR_PASSWORD;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;
```

## Setup Steps

### 1. Update Connection Strings
Replace the placeholder values in both appsettings files:
- `YOUR_SERVER_NAME`: Your SQL Server instance name
- `YOUR_ACCOUNTING_DATABASE`: Your existing accounting database name

### 2. Test Connection
Use SQL Server Management Studio or similar tool to verify the connection string works.

### 3. Generate Entity Framework Models
Run this command to scaffold your existing database:

```bash
cd 03-WebApp/AccountingWeb/AccountingSystem.Web
dotnet ef dbcontext scaffold "YOUR_CONNECTION_STRING" Microsoft.EntityFrameworkCore.SqlServer -o Models/Generated -c AccountingDbContext --force
```

### 4. Configure DbContext in Program.cs
```csharp
builder.Services.AddDbContext<AccountingDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("AccountingDatabase")));
```

## Security Considerations

### Connection String Security
- Never commit actual connection strings to source control
- Use environment variables or Azure Key Vault for production
- Use different databases for development and production

### Environment Variables (Production)
Set these environment variables instead of hardcoding connection strings:
```
ConnectionStrings__DefaultConnection=YOUR_PRODUCTION_CONNECTION_STRING
ConnectionStrings__AccountingDatabase=YOUR_PRODUCTION_CONNECTION_STRING
```

### User Secrets (Development)
For development, use user secrets:
```bash
dotnet user-secrets init
dotnet user-secrets set "ConnectionStrings:DefaultConnection" "YOUR_DEV_CONNECTION_STRING"
```

## Database Compatibility

### Key Principles
1. **No Schema Changes**: The web app must work with existing database structure
2. **Preserve Data Types**: Match existing column types exactly
3. **Maintain Relationships**: Keep all foreign key relationships intact
4. **Respect Constraints**: Honor existing check constraints and triggers

### Entity Framework Configuration
- Use Database-First approach
- Configure entities to match existing table names
- Map properties to existing column names
- Handle legacy naming conventions

### Example Entity Configuration
```csharp
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    // Map to existing table names
    modelBuilder.Entity<Customer>().ToTable("Customers");
    
    // Map to existing column names
    modelBuilder.Entity<Customer>()
        .Property(e => e.CustomerId)
        .HasColumnName("CustomerID");
        
    // Preserve existing relationships
    modelBuilder.Entity<Invoice>()
        .HasOne(i => i.Customer)
        .WithMany(c => c.Invoices)
        .HasForeignKey(i => i.CustomerId);
}
```

## Troubleshooting

### Common Issues
1. **Connection Timeout**: Increase connection timeout in connection string
2. **Authentication Failed**: Verify SQL Server authentication mode
3. **Database Not Found**: Check database name spelling and case sensitivity
4. **Permission Denied**: Ensure user has appropriate database permissions

### Testing Connection
Create a simple test in your web application:
```csharp
public async Task<IActionResult> TestConnection()
{
    try
    {
        using var connection = new SqlConnection(_configuration.GetConnectionString("AccountingDatabase"));
        await connection.OpenAsync();
        return Ok("Database connection successful");
    }
    catch (Exception ex)
    {
        return BadRequest($"Database connection failed: {ex.Message}");
    }
}
```
