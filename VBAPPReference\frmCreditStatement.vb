﻿Imports System.Data.SqlClient

Public Class frmCreditStatement
    Private Sub frmCreditStatement_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        LoadPayments()
        LoadTrxTypes()
        LoadVendors()
    End Sub
    Sub LoadTrxTypes()
        cmbxTrxType.Items.Clear()
        cmbxTrxType.Items.Add("فاتورة مشتريات")
        cmbxTrxType.Items.Add("دفعة")
        cmbxTrxType.Enabled = True
    End Sub
    Sub LoadPayments()
        cmbxPaymentType.Items.Clear()
        cmbxPaymentType.Items.Add("آجل")
        cmbxPaymentType.Items.Add("نقدي")
        cmbxPaymentType.Enabled = True
    End Sub
    Sub LoadVendors()
        Dim CMD As New SQLCommand("Select VendorNo from tblVendor Order by VendorNo", Con)
        
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxVendorID.Items.Clear()
        Do While reader.Read
            cmbxVendorID.Items.Add(reader.Item("VendorNo").ToString)
        Loop
        cmbxVendorID.Enabled = True
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Me.Close()
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        frmCreditStatementPrint.MdiParent = frmMain
        frmCreditStatementPrint.Show()
    End Sub





    Private Sub chbxTrxType_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbxTrxType.CheckedChanged
        If chbxTrxType.Checked = True Then
            LoadTrxTypes()
            cmbxTrxType.Enabled = True
        Else
            cmbxTrxType.Items.Clear()
            cmbxTrxType.Enabled = False
        End If
    End Sub

    Private Sub chbxPaymentType_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbxPaymentType.CheckedChanged
        If chbxPaymentType.Checked = True Then
            LoadPayments()
            cmbxPaymentType.Enabled = True
        Else
            cmbxPaymentType.Items.Clear()
            cmbxPaymentType.Enabled = False
        End If
    End Sub

    Private Sub chbxVendorNo_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chbxVendorNo.CheckedChanged
        If chbxVendorNo.Checked = True Then
            LoadVendors()
            cmbxVendorID.Enabled = True
        Else
            cmbxVendorID.Items.Clear()
            cmbxVendorID.Enabled = False
        End If
    End Sub
End Class