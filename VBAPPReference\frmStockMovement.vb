﻿Imports System.Data.SqlClient
Imports QRCoder

Public Class frmStockMovement
    Dim EnteredBefore As Int64 = 0
    Dim Cr As Int64
    Dim LableCode As Int64
    Dim txtPrice As Int64
    Dim QTY As Int64
    'Dim TrxNo As Int64
    Dim ForClose As Integer = 0
    Dim ForItemChange As Integer = 0
    Dim flag_cell_edited As Boolean
    Dim currentRow As Integer
    Dim currentColumn As Integer
    Dim ItemFailed As String = ""
    Dim x As Int64 = 0
    Dim PrintCopies, MaterialAccount, DiscountAccount As Int64
    Dim PrintOption, ReferenceMandatory, DefaultPrinter, MandatoryCustomerVATReg, PriceIncludeVATDef, NonVATInvoiceDef, PaymentType As String


    Private Sub frmTrxInvIn_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed

    End Sub

    Private Sub frmStockMovement_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        InvNo = Val(txtInvoiceNo.Text)
        If InvNo <> 0 And Val(lblQTY.Text) = 0 Then
            Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set ReadyForUse = 'True' where TrxNo = " & Val(InvNo) & " and  TrxType ='تحويل مخزون'", Con)
            Con.Close()
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            Dim DeleteCMD As New SqlCommand("Delete tblStockMovement where TrxType like 'تحويل مخزون%' and DocNo = " & Val(InvNo) & "", Con)
            Dim Delete2CMD As New SqlCommand("Update tblStockTrx set OutInvoiceType= NULL, OutInvoiceNo= NULL,OutInvoiceItemSN = NULL, OutCreatedBy = NULL,OutCreatedOn = NULL,UnitPrice=0,InStock = 1 where OutInvoiceType= 'مبيعات' and OutInvoiceNo = " & Val(InvNo) & " and InStock = 0", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            DeleteCMD.ExecuteNonQuery()
            Delete2CMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        ElseIf InvNo <> 0 And Val(lblQTY.Text) <> 0 Then
            'If MsgBox("هل ترغب بحفظ التغييرات ؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
            ForClose = 1
            SaveTrx()
        End If

        'End If
    End Sub
    Private Sub frmTrxInvIn_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        ForceGregorianForAllPickers(Me)
        'CustomerLoad()
        StoresLoad()
        'CashierLoad()
        'LoadSettings()
        GetSerial()
        GetLineSerial()
    End Sub
    Sub GetSerial()
        Try
            Dim PreUsedCMD As New SqlCommand("Select (Min(TrxNo)) as SN from tblStockMovHeader where ReadyForUse = 'True' and TrxType = 'تحويل مخزون' ", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = PreUsedCMD.ExecuteReader()
            If reader.Read Then
                InvNo = Val(reader.Item(0).ToString)
            Else
                InvNo = 0
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If InvNo = 0 Then
                Dim SelectCMD As New SqlCommand("Select (Max(TrxNo)) + 1 as SN from tblStockMovHeader where TrxType = 'تحويل مخزون' ", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                Dim reader2 As SqlDataReader = SelectCMD.ExecuteReader()
                If reader2.Read Then
                    If reader2.Item("SN").ToString <> "" Then
                        InvNo = Val(reader2.Item("SN").ToString)
                    Else
                        InvNo = 1
                    End If
                End If
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                Dim InsertCMD As New SqlCommand("Insert into tblStockMovHeader (TrxType,TrxNo,ReadyForUse,CreatedBy,CreatedOn) values ('تحويل مخزون'," & Val(InvNo) & ",'False','" & UserName & "','" & Format(DateAndTime.Now, "yyyy/MM/dd hh:mm:ss tt") & "')", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                InsertCMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            Else
                Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set ReadyForUse = 'False' where TrxNo = " & Val(InvNo) & " and TrxType = 'تحويل مخزون'  ", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                UpdateCMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If
            txtInvoiceNo.Text = InvNo
        Catch ex As Exception

        End Try
    End Sub
    Sub GetLineSerial()
        Try
            Dim SelectCMD As New SqlCommand("Select Max(LineSN) + 1 as SN from tblStockMovement where TrxType like 'تحويل مخزون%'  and DocNo = " & Val(txtInvoiceNo.Text) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                If reader.Item("SN").ToString <> "" Then
                    LineSN = Val(reader.Item("SN").ToString)
                Else
                    LineSN = 1
                End If
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            txtItemSN.Text = LineSN
        Catch ex As Exception

        End Try
    End Sub

    Sub StoresLoad()
        Dim CMD As New SqlCommand("Select Store from tblStores Order by Store", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxStoreFrom.Items.Clear()
        cmbxStoreTo.Items.Clear()
        Do While reader.Read
            cmbxStoreFrom.Items.Add(reader.Item(0).ToString)
            cmbxStoreTo.Items.Add(reader.Item(0).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub


    Sub SaveTrx()
        If Val(txtInvoiceNo.Text) <> 0 And Val(lblQTY.Text) <> 0 Then
            InvNoForPrint = InvNo

            Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set TrxDate='" & Format(dtp1.Value.Date, "yyyy/MM/dd hh:mm:ss tt") & "',TrxNote = '" & Trim(txtNotes.Text) & "',StoreTo= '" & cmbxStoreTo.Text.Trim & "',ModifiedBy='" & UserName & "',ModifiedOn='" & Format(DateAndTime.Now, "yyyy/MM/dd hh:mm:ss tt") & "',VOIDSTTS=0,ReadyForUse='False',Store= '" & cmbxStoreFrom.Text.Trim & "' where TrxNo = " & Val(txtInvoiceNo.Text) & " and TrxType='تحويل مخزون'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If ForItemChange = 0 Then
                GLEntry()
            End If
            'InvNo = Val(txtInvoiceNo.Text)
            If ForClose = 1 Then
                Me.Close()
            ElseIf ForItemChange <> 1 Then
                MsgBox("تم حفظ التحويل بنجاح", MsgBoxStyle.Information, "نظام السلطان")
                PrintType = "StockTransfer"
                'LoadSettings()
                ClearFields()
                If PrintOption = 0 Then
                    frmPrintPreview.MdiParent = frmMain
                    frmPrintPreview.Show()
                ElseIf PrintOption = 1 Then
                    If MsgBox("هل ترغب في طباعة التحويل؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
                        frmPrintPreview.MdiParent = frmMain
                        frmPrintPreview.Show()
                    End If
                End If
            End If

        End If
    End Sub
    Private Sub btnInvInSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub
    Sub GLEntry()
        '' Check Credit Account الحساب الدائن حساب الصندوق اذا كان نقدي او حساب الموردين اذا كان آجل
        'Dim CreditString As String = ""
        'If cmbxPaymentType.Text.Trim = "نقدي" Then
        '    Dim CMDCheckCredit As New SqlCommand("Select RootID from tblRoots where RootName = 'صندوق رقم 1'", Con)
        '    If Con.State <> ConnectionState.Open Then
        '        Con.Open()
        '    End If
        '    Dim reader As SqlDataReader = CMDCheckCredit.ExecuteReader
        '    If reader.Read Then
        '        TrxCredit = Val(reader.Item(0).ToString)
        '    Else
        '        TrxCredit = 0
        '    End If
        '    If reader.IsClosed Then
        '    Else
        '        reader.Close()
        '    End If
        '    If Con.State <> ConnectionState.Closed Then
        '        Con.Close()
        '    End If
        'Else

        '    TrxCredit = Val(cmbxAccountNo.Text)

        'End If
        ''select VATAccount from tblConfig
        ''===========================================
        '' Check Debit Account الحساب المدين هو حساب المشتريات في جميع الأحوال 
        'Dim CMDCheckVAT As New SqlCommand("select VATAccount from tblConfig", Con)
        'If Con.State <> ConnectionState.Open Then
        '    Con.Open()
        'End If
        'Dim reader2 As SqlDataReader = CMDCheckVAT.ExecuteReader
        'If reader2.Read Then
        '    TrxVAT = Val(reader2.Item(0).ToString)
        'Else
        '    TrxVAT = 0
        'End If
        ''If reader.IsClosed Then
        ''Else
        ''    reader.Close()
        ''End If
        'If Con.State <> ConnectionState.Closed Then
        '    Con.Close()
        'End If
        ''===========================================
        ''===========================================
        '' Check Debit Account الحساب المدين هو حساب المشتريات في جميع الأحوال 
        'Dim CMDCheckDebit As New SqlCommand("select MaterialAccountNo from tblToolsInvoice where InvoiceType = 'مبيعات'", Con)
        'If Con.State <> ConnectionState.Open Then
        '    Con.Open()
        'End If
        'Dim reader3 As SqlDataReader = CMDCheckDebit.ExecuteReader
        'If reader3.Read Then
        '    TrxDebit = Val(reader3.Item(0).ToString)
        'Else
        '    TrxDebit = 0
        'End If
        ''If reader.IsClosed Then
        ''Else
        ''    reader.Close()
        ''End If
        'If Con.State <> ConnectionState.Closed Then
        '    Con.Close()
        'End If
        ''===========================================
        '' Get Serial
        'JEHeader()
        ''===========================================
        '' Insert Trx
        'If TrxCredit And TrxDebit <> 0 Then
        '    Dim DeleteCMD As New SqlCommand("Delete tblGLTrx where JESN = " & Val(JESN) & " ", Con)
        '    If Con.State <> ConnectionState.Open Then
        '        Con.Open()
        '    End If
        '    DeleteCMD.ExecuteNonQuery()
        '    If Con.State <> ConnectionState.Closed Then
        '        Con.Close()
        '    End If
        '    Dim InsertDebitCMD As New SqlCommand("Insert Into tblGLTrx (JESN,RootID,Debit,Credit) Values (" & Val(JESN) & "," & Val(TrxDebit) & ",0," & Val(lblTotal.Text) & ")", Con)
        '    Dim InsertCreditCMD As New SqlCommand("Insert Into tblGLTrx (JESN,RootID,Debit,Credit) Values (" & Val(JESN) & "," & Val(TrxCredit) & "," & Val(lblInvoiceAmount.Text) & ",0)", Con)
        '    If Val(txtVATValue.Text) <> 0 Then
        '        Dim InsertVATDebitCMD As New SqlCommand("Insert Into tblGLTrx (JESN,RootID,Debit,Credit) Values (" & Val(JESN) & "," & Val(TrxVAT) & ",0," & Val(txtVATValue.Text) & ")", Con)
        '        If Con.State <> ConnectionState.Open Then
        '            Con.Open()
        '        End If
        '        InsertVATDebitCMD.ExecuteNonQuery()
        '    End If
        '    If Con.State <> ConnectionState.Open Then
        '        Con.Open()
        '    End If
        '    InsertDebitCMD.ExecuteNonQuery()
        '    InsertCreditCMD.ExecuteNonQuery()

        '    If Con.State <> ConnectionState.Closed Then
        '        Con.Close()
        '    End If
        'Else
        '    MsgBox("خطأ في اعدادات الدليل المحاسبي ... لن يتم انشاء القيد", MsgBoxStyle.Critical, "نظام السلطان")
        'End If
    End Sub
    Sub JEHeader()
        'Dim CheckCMD As New SqlCommand("Select JESN from tblJEHeader where ReferenceTrx = " & Val(txtInvoiceNo.Text) & " and ReferenceType = 'مبيعات'", Con)
        'If Con.State <> ConnectionState.Open Then
        '    Con.Open()
        'End If
        'Dim reader As SqlDataReader = CheckCMD.ExecuteReader
        'If reader.Read Then
        '    JESN = Val(reader.Item(0).ToString)
        '    reader.Close()
        '    Dim UpdateCMD As New SqlCommand("Update tblJEHeader set Amount=" & Val(lblInvoiceAmount.Text) & ",TrxDate = '" & Format(dtp1.Value.Date, "yyyy/MM/dd") & "' where JESN = " & JESN & " ", Con)
        '    If Con.State <> ConnectionState.Open Then
        '        Con.Open()
        '    End If
        '    UpdateCMD.ExecuteReader()
        '    If Con.State <> ConnectionState.Closed Then
        '        Con.Close()
        '    End If
        'Else
        '    Dim InsertHeaderCMD As New SqlCommand("Insert Into tblJEHeader (TrxDate,Amount,Reference,ReferenceTrx,ReferenceType,PostStatus,CreatedBy,CreatedOn) values ('" & Format(dtp1.Value.Date, "yyyy/MM/dd") & "'," & Val(lblInvoiceAmount.Text) & ",'فاتورة مبيعات - " & Val(InvNo) & "'," & Val(InvNo) & ",'مبيعات','Open','" & UserName & "',getdate()) SELECT SCOPE_IDENTITY()", Con)
        '    If Con.State <> ConnectionState.Open Then
        '        Con.Open()
        '    End If
        '    reader.Close()
        '    JESN = InsertHeaderCMD.ExecuteScalar()
        '    If Con.State <> ConnectionState.Closed Then
        '        Con.Close()
        '    End If
        'End If
    End Sub
    Sub ClearFieldsWOGetSN()
        DGV1.DataSource = ""
        txtNotes.Clear()
        'lblAccountName.Text = ""
        'cmbxPaymentType.Text = ""
        'txtVendorInvNo.Clear()
        lblCount.Text = ""
        lblQTY.Text = ""
        'LoadSettings()
        txtItemSN.Text = 1

    End Sub
    Sub ClearFields()
        DGV1.DataSource = ""
        txtNotes.Clear()
        txtInvoiceNo.Clear()
        lblCount.Text = ""
        lblQTY.Text = ""
        GetSerial()
        txtItemSN.Text = 1

    End Sub

    Private Sub DataGridView2_CellContentClick(sender As Object, e As DataGridViewCellEventArgs) Handles DGV1.CellContentClick

    End Sub

    Private Sub Totalcount()
        Dim Total1 As Double
        Dim QTY As Integer = 0
        Dim Lines As Integer = 0
        Dim CountCMD As New SqlCommand("Select sum(TrxQTY) as QTY,Max(LineSN) as Lines from tblStockMovement where TrxType = 'تحويل مخزون إلى'  and DocNo = " & Val(txtInvoiceNo.Text) & "", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CountCMD.ExecuteReader
        If reader.Read Then
            If reader.Item(0).ToString <> "" Then
                'Total1 = Val(reader.Item("Total").ToString)
                QTY = Val(reader.Item("QTY").ToString)
                Lines = Val(reader.Item("Lines").ToString)
            Else
                Total1 = 0
                QTY = 0
                Lines = 0
            End If
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If

        'If ckbxNonVatInvoice.Checked = False Then

        lblQTY.Text = QTY
        lblCount.Text = Lines
        If Lines > 0 Then
            cmbxStoreFrom.Enabled = False
            cmbxStoreTo.Enabled = False
        ElseIf Lines = 0 Then
            cmbxStoreFrom.Enabled = True
            cmbxStoreTo.Enabled = True
        End If
    End Sub

    'Private Sub ckbxVATIncluded_CheckedChanged(sender As Object, e As EventArgs) Handles ckbxVATIncluded.CheckedChanged
    '    Totalcount()
    'End Sub

    Private Sub btn_New_Click(sender As Object, e As EventArgs) Handles btn_New.Click
        If Val(lblCount.Text) = 0 And Val(lblQTY.Text) = 0 Then
            ClearFieldsWOGetSN()
        End If
    End Sub

    Private Sub DGV1_KeyDown(sender As Object, e As KeyEventArgs) Handles DGV1.KeyDown
        Try
            Dim iCol = DGV1.CurrentCell.ColumnIndex
            Dim iRow = DGV1.CurrentCell.RowIndex

            If e.KeyCode = Keys.Enter Then
                e.SuppressKeyPress = True




                If e.KeyCode = Keys.Enter Then
                    e.SuppressKeyPress = True
                    If iCol = DGV1.Columns.Count - 1 Then
                        If iRow < DGV1.Rows.Count - 1 Then
                            DGV1.CurrentCell = DGV1(0, iRow + 1)
                        End If
                    Else

                        If iCol = 1 Then
                            DGV1.CurrentCell = DGV1(iCol + 3, iRow)
                        Else
                            DGV1.CurrentCell = DGV1(iCol + 1, iRow)
                        End If
                    End If
                End If




            ElseIf e.KeyCode = Keys.Delete Then
                If DGV1.CurrentRow.Cells(0).Value <> 0 Then
                    If DGV1.Rows.Count <> 0 Then

                        x = DGV1.CurrentRow.Index
                        DGV1.Rows.Remove(DGV1.Rows(x))
                        x += 1
                        UpdateLineSN()

                    End If
                End If

            End If
        Catch ex As Exception

        End Try







    End Sub
    Sub UpdateLineSN()
        Dim DeleteCMD As New SqlCommand("Delete from tblStockMovement where DocNo =  " & Val(txtInvoiceNo.Text) & " and LineSN = " & Val(x) & " and TrxType = 'تحويل مخزون' ", Con)
        Dim Delete2CMD As New SqlCommand("Delete from tblStockTrx where OutInvoiceNo =  " & Val(txtInvoiceNo.Text) & " and OutInvoiceItemSN = " & Val(x) & " and OutInvoiceType = 'مبيعات'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        DeleteCMD.ExecuteNonQuery()
        Delete2CMD.ExecuteNonQuery()
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        '====================================================
        Dim UpdateCMD As New SqlCommand("Update tblStockMovement set LineSN = LineSN - 1 where DocNo = " & Val(txtInvoiceNo.Text) & " and LineSN > " & Val(x) & " and TrxType = 'تحويل مخزون' ", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        UpdateCMD.ExecuteNonQuery()

        'If reader.isclosed Then
        'Else
        '    reader.close()
        'End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        '====================================================
        Dim Update2CMD As New SqlCommand("Update tblStockTrx set OutInvoiceItemSN = OutInvoiceItemSN -1 where OutInvoiceNo = " & Val(txtInvoiceNo.Text) & " and OutInvoiceItemSN > " & Val(x) & " and OutInvoiceType = 'مبيعات'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Update2CMD.ExecuteNonQuery()
        'If reader.IsClosed Then
        'Else
        '    reader.Close()
        'End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Private Sub DGV1_DoubleClick(sender As Object, e As EventArgs) Handles DGV1.DoubleClick
        If DGV1.SelectedRows.Count = 1 And Val(DGV1.CurrentRow.Cells(0).Value) <> 0 Then
            txtItemSN.Text = Val(DGV1.CurrentRow.Cells(0).Value)
            txtItemNo.Text = DGV1.CurrentRow.Cells(1).Value
            txtQTY.Text = Val(DGV1.CurrentRow.Cells(3).Value)

            txtItemDescription.Text = Trim(DGV1.CurrentRow.Cells(2).Value)

            EnteredItemBySN = 0
        End If
    End Sub

    Private Sub txtItemNo_KeyDown(sender As Object, e As KeyEventArgs) Handles txtItemNo.KeyDown
        'If e.KeyCode = Keys.Enter Then
        '    CheckItem()
        'End If
    End Sub

    Sub CheckItem()
        If txtItemNo.Text <> "" Then
            Dim SearchCMD As New SqlCommand("Select ItemDescription,UofM from tblItems where ItemNo = " & Val(txtItemNo.Text) & "", Con)
            OpenConnection()
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                ItemDescription = Trim(reader.Item(0).ToString)
                ItemUnit = Trim(reader.Item(1).ToString)
                UnitPrice = Val(reader.Item(2).ToString)
                txtItemDescription.Text = ItemDescription
                txtUnit.Text = ItemUnit
                If Val(txtQTY.Text) = 0 Then
                    txtQTY.Text = 1
                End If
                reader.Close()
                CloseConnection()
                txtQTY.Focus()
            Else
                txtQTY.Clear()
                SearchItemFor = "StockTransfer"
                frmItemSearch.ShowDialog()
            End If
        Else
            txtItemDescription.Clear()
            txtQTY.Clear()
            SearchItemFor = "StockTransfer"
            frmItemSearch.ShowDialog()
        End If

    End Sub

    Private Sub txtItemNo_Leave(sender As Object, e As EventArgs) Handles txtItemNo.Leave

    End Sub

    Private Sub txtItemNo_LostFocus(sender As Object, e As EventArgs) Handles txtItemNo.LostFocus
        If txtItemNo.Text <> "" Then
            Dim SearchCMD As New SqlCommand("Select ItemDescription,UofM from tblItems where ItemNo = " & Val(txtItemNo.Text) & "", Con)
            OpenConnection()
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                ItemDescription = Trim(reader.Item(0).ToString)
                ItemUnit = Trim(reader.Item(1).ToString)
                txtItemDescription.Text = ItemDescription
                txtUnit.Text = ItemUnit
                If Val(txtQTY.Text) = 0 Then
                    txtQTY.Text = 1
                End If
                reader.Close()
                CloseConnection()
                txtQTY.Focus()
            Else
                If reader.IsClosed = False Then
                    reader.Close()
                End If
                txtQTY.Clear()
                txtItemDescription.Clear()
            End If
        End If
    End Sub

    Private Sub txtItemNo_MouseDown(sender As Object, e As MouseEventArgs) Handles txtItemNo.MouseDown

    End Sub

    Private Sub txtItemNo_TextChanged(sender As Object, e As EventArgs) Handles txtItemNo.TextChanged

    End Sub


    Sub RefreshGrid()
        Dim ds As DataSet = New DataSet
        Dim FillCMD As New SqlCommand("SELECT tblStockMovement.LineSN AS م, tblStockMovement.ItemNo AS [رقم الصنف], tblItems.ItemDescription AS [وصف الصنف], tblStockMovement.TrxQTY AS الكمية,tblItems.UofM AS [الوحدة] FROM tblStockMovement INNER JOIN tblItems ON tblStockMovement.ItemNo = tblItems.ItemNo WHERE        (tblStockMovement.DocNo = " & Val(txtInvoiceNo.Text) & ") AND (tblStockMovement.TrxType Like 'تحويل مخزون%' ) ORDER BY م", Con)
        Dim da As New SqlDataAdapter(FillCMD)
        ds.Clear()
        da.Fill(ds)
        DGV1.DataSource = ds.Tables(0)
    End Sub
    Sub AddItems()
        If cmbxStoreFrom.Text.Trim = "" Or cmbxStoreTo.Text.Trim = "" Then
            MsgBox("يجب تحديد المستودعات أولًا", MsgBoxStyle.Critical, "نظام السلطان")
            Exit Sub
        End If

        If Val(txtItemSN.Text) <> 0 And Val(txtItemNo.Text) <> 0 And Trim(txtItemDescription.Text) <> "" And Val(txtQTY.Text) <> 0 Then
            Dim CheckCMD As New SqlCommand("Select * from tblStockMovement where DocNo = " & Val(txtInvoiceNo.Text) & " and TrxType like 'تحويل مخزون%'  and LineSN = " & Val(txtItemSN.Text) & "", Con)
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CheckCMD.ExecuteReader
            If reader.Read Then
                reader.Close()
                Dim UpdateFromCMD As New SqlCommand("Update tblStockMovement set TrxQTY=" & Val(txtQTY.Text) & ",ItemNo=" & Val(txtItemNo.Text) & ", ModifiedOn = GetDate(),ModifiedBy = '" & UserName & "',Store = '" & cmbxStoreFrom.Text.Trim & "' where DocNo = " & Val(txtInvoiceNo.Text) & " and TrxType = 'تحويل مخزون من'  and LineSN = " & Val(txtItemSN.Text) & "", Con)
                Dim UpdateToCMD As New SqlCommand("Update tblStockMovement set TrxQTY=" & Val(txtQTY.Text) & ",ItemNo=" & Val(txtItemNo.Text) & ", ModifiedOn = GetDate(),ModifiedBy = '" & UserName & "',Store = '" & cmbxStoreTo.Text.Trim & "' where DocNo = " & Val(txtInvoiceNo.Text) & " and TrxType = 'تحويل مخزون إلى'  and LineSN = " & Val(txtItemSN.Text) & "", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                UpdateFromCMD.ExecuteReader()
                UpdateToCMD.ExecuteReader()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            Else
                Dim InsertFromCMD As New SqlCommand("Insert Into tblStockMovement (DocNo,ItemNo,LineSN,TrxType,TrxDate,TrxQTY,CreatedBy,CreatedOn,Store) Values (" & Val(txtInvoiceNo.Text) & "," & Trim(txtItemNo.Text) & "," & Trim(txtItemSN.Text) & ",'تحويل مخزون من','" & Format(dtp1.Value.Date, "yyyy/MM/dd") & "'," & Val(txtQTY.Text) & ",'" & UserName & "',GetDate(),'" & cmbxStoreFrom.Text.Trim & "')", Con)
                Dim InsertToCMD As New SqlCommand("Insert Into tblStockMovement (DocNo,ItemNo,LineSN,TrxType,TrxDate,TrxQTY,CreatedBy,CreatedOn,Store) Values (" & Val(txtInvoiceNo.Text) & "," & Trim(txtItemNo.Text) & "," & Trim(txtItemSN.Text) & ",'تحويل مخزون إلى','" & Format(dtp1.Value.Date, "yyyy/MM/dd") & "'," & Val(txtQTY.Text) & ",'" & UserName & "',GetDate(),'" & cmbxStoreTo.Text.Trim & "')", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                reader.Close()
                InsertFromCMD.ExecuteNonQuery()
                InsertToCMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If
            RefreshGrid()
            'If txtSN.Text.Trim <> "" And Val(txtQTY.Text) = 1 Then
            '    DeleteSNEntries()
            '    EnterSingleSN()
            'ElseIf txtSN.Text.Trim = "" And Val(txtQTY.Text) = 1 Then
            '    DeleteSNEntries()
            'End If
            ForItemChange = 1
            SaveTrx()
            ForItemChange = 0
            Totalcount()
            txtItemNo.Clear()
            txtItemSN.Clear()
            txtItemDescription.Clear()
            txtQTY.Clear()

            'txtSN.Clear()
            'txtSN.ReadOnly = true
            GetLineSerial()
        End If
    End Sub
    Private Sub Button6_Click(sender As Object, e As EventArgs) Handles Button6.Click
        AddItems()
    End Sub



    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If MsgBox("هل ترغب بالتأكيد في حفظ الفاتورة", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
            EnteredItemBySN = 0

            SaveTrx()
        End If
    End Sub


    Private Sub Button7_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Button7.Click
        If Val(txtItemSN.Text) <> 0 And Val(txtInvoiceNo.Text) <> 0 Then
            InvNo = Val(txtInvoiceNo.Text)
            If MsgBox("هل ترغب بحذف السطر بالتأكيد ؟", MsgBoxStyle.YesNoCancel, "نظام السلطان") = MsgBoxResult.Yes Then
                Dim DeleteCMD As New SqlCommand("Delete tblStockMovement where TrxType like 'تحويل مخزون%'  and DocNo = " & Val(InvNo) & " and LineSN = " & Val(txtItemSN.Text) & " ", Con)

                Dim Delete3CMD As New SqlCommand("Update tblStockMovement set LineSN = LineSN - 1 where LineSN > " & Val(txtItemSN.Text) & " and TrxType like 'تحويل مخزون%'  and DocNo = " & Val(InvNo) & "", Con)

                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                DeleteCMD.ExecuteNonQuery()
                'Delete2CMD.ExecuteNonQuery()
                Delete3CMD.ExecuteNonQuery()
                'Delete4CMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                RefreshGrid()
                ForItemChange = 1
                SaveTrx()
                Totalcount()
                ForItemChange = 0
                txtItemNo.Clear()
                txtItemDescription.Clear()
                txtQTY.Clear()

            End If
        End If
    End Sub



    Private Sub ckbxNonVatInvoice_CheckedChanged(sender As Object, e As EventArgs)
        Totalcount()
    End Sub

    Private Sub btnItemLookup_Click(sender As Object, e As EventArgs) Handles btnItemLookup.Click
        SearchItemFor = "Sales"
        frmItemSearch.ShowDialog()
    End Sub


End Class