﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="PictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAQEAAAAAAAD/2wBDAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8k
        KDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5Ojf/2wBDAQoKCg0MDRoPDxo3JR8lNzc3Nzc3Nzc3Nzc3
        Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzf/wAARCACaAI8DAREAAhEBAxEB/8QA
        HAABAQACAwEBAAAAAAAAAAAAAAQFBwIDBggB/8QAQxAAAQMDAQQECAsGBwAAAAAAAAECAwQFEQYHEiEx
        M0FRcRMiUmGBocHRFDI0NkJDYnORsfEIU1RydMIVFiMkZIKS/8QAGgEBAAIDAQAAAAAAAAAAAAAAAAME
        AQIFBv/EACwRAQACAQIDBgYDAQAAAAAAAAABAgMEEQUxMhIhQUJRYRMjUnGRoRQVIsH/2gAMAwEAAhED
        EQA/AN4gAAAAAAAAADIAAAAAAAAAAAAAAAABBU3OOKTwMDHTz/u4+Kp39gHUjbrPxfJBSt7ETfX2IBPI
        yKNV+E31zV8z2NA4s8E75Pf0cvY5zHflgDufJdqaPfjbDXMTmka7rl7kVcesDstt8pK57oUcsVQz48Mj
        Va5voUDKIAAAAAAAAAAACgYurnmrKlaOjduI3jNL5KdiecDx+r9oFq0hG6htrG1Fcmd7jlrV+0vWoGmd
        Q7SL7d5F8LWyJHzRkTt1qehAPNrcrjULvIs0mezKgfjbpXQO8dZWL58oBn7Hre70DmrT107cdW+uAPUV
        Guqu6JDUSyblbB0czeC93nQDaezrWkOpaLwUqoysh4SMzz84HtQAAAAAAAAACO6VXwWkc9E3nrwY3ylX
        giAeL2gaj/yjp1KankRbjVIuX9aZ5u9iAfOM76u83FIIUdNPO/CJ1qoG7tC7H6Gmp46q+tSoqHeNuOb4
        rfRkDZNNpq0U0aMioYUan2EA6K/SFkr41ZPQQqi/YQDUe0HZClFBJcNPZRGpvOgTii93HgBq2kmc1ysf
        vI9q7rkXmioBmNN32bT2oqWvjcqRq5GSp1K1VA+qrZWMrqGGoiXLZGooFQAAAAAAAADB3GRJtQ0NKq+J
        E19Q/wD68E9agfPu1W/Ou9/qpUeqxMXcjTPJEAzmwTTjK2unvFSxHeDXciyn4qB9AoiImETAH6AA4SMb
        IxWPTKKmMAfNe1nTjbHqvw1Ozdgq8uwnLeA8TXN/0+IH0XsUuzrlo+Bkjt58PiKvdwA2EAAAAAAAAA17
        qm7rQX2vRqqjloUY3zZdx/IMPnm/SrJM9yrnKhl9BbDKZkOjIHtRN6TxlX0gbGAAAAGr9ttA2poqGVE8
        dJURF7wNGXiLwSubjigG2v2dJ3Ooa+H6LZvYBukAAAAAAAABqDaOqpqOpwuM0qfmphhpG8fGd3mWX0ds
        U+ZFH/KB78AAAAeD2qoi0dBn+IT2gaC1P8qkA2T+zl0Nx++/tQDeAAAAAAAAADT20j5yVH9Mn5qBpO8f
        GXvA+jtivzIo/wCUD34AAAA8JtU+SUH9QntA0Bqf5VKBsr9nLobj97/agG8AAAAAAAAOt0i5wxu8qc+P
        Ijm877VjdtEeMvL37RdPeq19XUVMjHvZubrETCIaz8bw2/bP+Pd5Gq2I2yodlbjVY7EVPcRzOq9I/bPy
        vd7XS2nptMWmK20UzJYo0wjpU8b1Ec31fhWPy2+T7ssq3FP4b8F95FbUauvkj8s7YfcR1x/434L7zFdR
        q58kfk2w+7knw/rWn/BfeTRfVz5Y/LHyfdyRK3rdB/5U3idV6R+WPle7Fag0+6/RQx1c6RpE/fRYk5r6
        TeJ1Hjt+2P8AHu8fcNjdsrpHPluNUjneTj3G3zvb9sf4ZPSOiU0LHOtse+sZK7ee2RURycMcCPJmyY43
        tHczWtLd272Vvr4q6PejyipzavNCXHlrkjuYvjtTmrJWgAAAAOMi4YuOfI1vO1e5mHFytiZnqRCHNlrp
        8falmIm0sbUXPcVUaebz8V1F5/zOy3TS7opLw9OSFK+u1M+eViujhO+9yp1Fe2pzz55TV0NU777P1YIp
        yZZ80pY0FBl9nzxwYjJljzSToKKI73KvNCWuozxyvKK2hqpjvD15liut1MeeUNtHChlzcv6livENT9SG
        2liFDK7eLVOJZ/VFODZ3sm305Fn+de8bSjmmzGN/29zV7OCPXiiGdNkmt1qY7eNnWrlMndid3PfpkAAA
        Dqmdu7nneiEeSeX3bVjm6LiuITj8YtO1apcEb2YCY87Z1KI5CKViqWUjT1Sv5mEsDOYZlTGZQ2VxG8IL
        K4SaqC66LqJ6q1lsJZorWR1aYqWL5y3j6oWMfQzUXGNvcehp0w51ubmbMAAABNWfU/fN9pDm5R929PF1
        XLo/QcbjPVVLp+bBS8jgS6dUkhFKxVJKRp6pZOZhLDB3zVdqsUiR1kyulVM+DiTLk7+w6Gk4bn1Udqsb
        R6yq6jWYsM7TzX6d1HbL9G51vnVXs+NG9MORO3HYa6rQZtLPzI7vWOTTFqaZul6GIqwzZZCTQguth6ie
        qtZdCWqK1kVZ07e8s4+cJ8fRLNQ9E3uPRU6YULc3M2agAABNWfU/fN9pDm5R929PF1XLo/QcXjPVVNp+
        bAynBs6dUshFKeqSUjlPVLJzEJYaD1m2dupa9KrO/wCFXGezq9WD32i7E6anY5bPLantfGt2ubMbKm1C
        6sgWBHbiMcsionDdx1+orcX7MaS3a9tkmj3+NGzfEfUeNh2bLIeRNCC62HqJ6q1l0JaorWRVnTt7y1j5
        wnx9DNQ9E3uPQU6YULc5czdqAAAE1Z9T9832kOblH3b08XVcuj9BxeM9VU2n5sDKcGzp1SSkUp6pZTSU
        9Ur+ZqlhrDbBGxs1A9rGo97XbzkTiuFTB6ngFpnHeJnxj/ricViIvX33eg2PxRJp+WZI2pK6dWq9E4qm
        61cZKnHr2+PFd+7Zvw+I+HM+7YcXUcWFyyyEmqr3WxdRPVXsuhLVFayKs6dveWsfOE+PoZqHom9x6CnT
        ChbnLmbtQAAAlrlREhVf3zSDP0x94b08XXcujOPxnqqm0/NgZTgWdOqSUilPVJIRynqmfzMJYee1bpaL
        UsUKOnWGWHO67GUVFxlPUdLh3EZ0czvG8SqazSfyIjv2mGV0pZIdP2tlFC9ZPGV73qmMuX9EIdbq7arL
        8SY28Ia4cEYadmHoIivDNlkJNVXuti6ieqvZdCWqK1kVZxnb3lrHzhPj6Gah6Jvcegp0woW5uZu1AAAD
        HXxJP8Pe+FFV8TkkRE68Lkr6ms2xTslwzEX70Mt2gqqZr2rhVTkvUea1monNMb+C5j01q2Y2Woj8pDnW
        herjlJLOzykI5hPXHKWSdnahp2ZT1xymdOzPNDHZlLGORkzM8x2ZZmllMc7O1B2ZQ2pKuKePykN4hBbH
        KuKoZ5SEsQgtjssiqY+HjIT1V747K2VcbU5lqiCcNpT7/h6huO0t4om1oSTHYp3s+xMMRPMegrG0OZPN
        yNmAAAA/HIioqKmUUxI89X6dR0jpKR25nju5ORquG9ud6Ojg1vZjazFyWGv5IufQcy3Dc3ov11+JO/T1
        xX9COeHZvpSxxHC6X6buS/R9Rr/XZ/pSxxLC6XaYua/QMf1+f6UkcTweo3TFzz8Qf1+efKxPE8Hq7W6a
        uSfR9Rn+uz/S0niWF3s09cU6vUbRw7N9KKeI4XeyxV6c/wAiSOHZvRHOvxKI7NWp+hJXh+X0RW1uOVUV
        oqvprwJ6aDIhtq6MtRW9IPGcuXHT0+kjH3ypZdRN+S9C6rAAAAAAAPzAABgBgBgAAwAwAwAwB+gAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//Z
</value>
  </data>
</root>