﻿' Improved GL Account Link Form - Refactored for Maintainability and Efficiency
Imports System.Data.SqlClient

Public Class frmToolsGL
    Dim type As String = ""
    Private AccountMapping As Dictionary(Of ComboBox, (Label As Label, ModuleName As String))

    Private Sub frmToolsGL_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' Initialize mapping AFTER controls are created (Form_Load)
            AccountMapping = New Dictionary(Of ComboBox, (Label As Label, ModuleName As String)) From {
                {cmbxSales, (lblDescriptionSales, "مبيعات")},
                {cmbxPurchasing, (lblDescriptionPurchsing, "مشتريات")},
                {cmbxCash, (lblDescriptionCash, "نقدية")},
                {cmbxOpeningStock, (lblDescriptionOpeningStock, "مخزون أول المدة")},
                {cmbxCustomer, (lblDescriptionCustomer, "عملاء")},
                {cmbxVendor, (lblDescriptionVendor, "موردون")},
                {cmbxSalesDiscount, (lblDescriptionSalesDiscount, "خصم مبيعات")},
                {cmbxPurchasingDiscount, (lblDescriptionPurchasingDiscount, "خصم مشتريات")},
                {cmbxOutVAT, (lblDescriptionOutVAT, "القيمة المضافة المحصلة")},
                {cmbxInVAT, (lblDescriptionInVAT, "القيمة المضافة المدفوعة")},
                {cmbxPurchasingReturns, (lblDescriptionPurchsingReturns, "مرتجعات المشتريات")},
                {cmbxSalesReturns, (lblDescriptionSalesReturns, "مرتجعات المبيعات")},
                {cmbxOwner, (lblDescriptionOwner, "الشريك")},
                {cmbxCogs, (lblDescriptionCogs, "تكلفة المبيعات")}
            }

            ' Load accounts
            Using cmd As New SqlCommand("SELECT AccountNo FROM AccountChart ORDER BY AccountNo", Con)
                If Con.State <> ConnectionState.Open Then Con.Open()
                Using reader = cmd.ExecuteReader()
                    While reader.Read()
                        Dim acc = reader("AccountNo").ToString().Trim()
                        For Each cmb In AccountMapping.Keys
                            cmb.Items.Add(acc)
                        Next
                    End While
                End Using
            End Using
            If Con.State <> ConnectionState.Closed Then Con.Close()

            CheckCurrentConfig() ' Set combo selections
            UpdateDescriptions() ' Now update labels

        Catch ex As Exception
            MessageBox.Show("Form load error: " & ex.Message)
        End Try
    End Sub

    Private Sub CheckCurrentConfig()
        Try
            Using cmd As New SqlCommand("SELECT * FROM tblGLConfig", Con)
                If Con.State <> ConnectionState.Open Then Con.Open()
                Using reader = cmd.ExecuteReader()
                    While reader.Read()
                        Dim moduleName = reader("EntryReferenceModule").ToString().Trim()
                        Dim accNo = reader("AccountNo").ToString()
                        For Each kvp1 In AccountMapping
                            If kvp1.Value.ModuleName = moduleName Then
                                kvp1.Key.Text = accNo
                            End If
                        Next
                    End While
                End Using
            End Using
            If Con.State <> ConnectionState.Closed Then Con.Close()
        Catch ex As Exception
            MessageBox.Show("Error loading configuration: " & ex.Message)
        Finally
            If Con.State = ConnectionState.Open Then Con.Close()
        End Try
    End Sub

    Private Sub UpdateDescriptions()
        For Each kvp In AccountMapping
            Dim accountNo = Val(kvp.Key.Text)
            If accountNo <> 0 Then
                Try
                    Using con As New SqlConnection(Constr)
                        Using cmd As New SqlCommand("SELECT AccountName FROM tbl_Acc_Accounts WHERE AccountCode = @AccountCode", con)
                            cmd.Parameters.AddWithValue("@AccountCode", accountNo)
                            If con.State <> ConnectionState.Open Then con.Open()
                            Dim result = cmd.ExecuteScalar()
                            kvp.Value.Label.Text = If(result IsNot Nothing, result.ToString(), "")
                        End Using
                    End Using
                Catch ex As Exception
                    kvp.Value.Label.Text = ""
                    MessageBox.Show("Error updating description for account: " & accountNo & vbCrLf & ex.Message)
                End Try
            Else
                kvp.Value.Label.Text = ""
            End If
        Next
    End Sub


    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            Using cmd As New SqlCommand("", Con)
                If Con.State <> ConnectionState.Open Then Con.Open()

                For Each kvp In AccountMapping
                    Dim accNo = Val(kvp.Key.Text)
                    If accNo <> 0 Then
                        cmd.CommandText = "UPDATE tblGLConfig SET AccountNo = @AccNo, ModifiedBy = @User, ModifiedOn = GETDATE() WHERE EntryReferenceModule = @ModuleName"
                        cmd.Parameters.Clear()
                        cmd.Parameters.AddWithValue("@AccNo", accNo)
                        cmd.Parameters.AddWithValue("@User", UserName)
                        cmd.Parameters.AddWithValue("@ModuleName", kvp.Value.ModuleName)
                        cmd.ExecuteNonQuery()
                    End If
                Next
            End Using
            MessageBox.Show("تم حفظ التعديلات بنجاح", "نظام السلطان", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show("Error saving configuration: " & ex.Message)
        Finally
            If Con.State <> ConnectionState.Closed Then Con.Close()
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    ' Unified TextChanged handler for all ComboBoxes
    Private Sub ComboBox_TextChanged(sender As Object, e As EventArgs) Handles _
        cmbxSales.TextChanged, cmbxPurchasing.TextChanged, cmbxCash.TextChanged,
        cmbxOpeningStock.TextChanged, cmbxCustomer.TextChanged, cmbxVendor.TextChanged,
        cmbxSalesDiscount.TextChanged, cmbxPurchasingDiscount.TextChanged,
        cmbxOutVAT.TextChanged, cmbxSalesReturns.TextChanged,
        cmbxPurchasingReturns.TextChanged, cmbxOwner.TextChanged, cmbxCogs.TextChanged, cmbxInVAT.TextChanged

        UpdateDescriptions()
    End Sub
End Class

