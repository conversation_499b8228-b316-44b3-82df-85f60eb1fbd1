﻿Imports System.Data.SqlClient
Public Class frmBarcodeSettings


    Private Sub FillComboBoxWithShops()
        Dim query As String = "select Shop_Text from tblShops order by Shop_Text"
        Dim Shop_Text As New List(Of String)()


        Using Con As New SqlConnection(ConStr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Shop_Text.Add(category)
                        End While
                        If reader.IsClosed <> True Then
                            reader.Close()
                        End If
                    End Using

                    cmbxShop.DataSource = Shop_Text
                    cmbxShop.SelectedIndex = -1

                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub

    Private Sub cmbxShop_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbxShop.SelectedIndexChanged
        If cmbxShop.SelectedIndex = -1 Then Exit Sub

        Dim query As String = "SELECT TOP 1 * FROM tblBarcodeSettings WHERE Shop = @Shop"
        Using conn As New SqlConnection(ConStr)
            Using cmd As New SqlCommand(query, conn)
                cmd.Parameters.AddWithValue("@Shop", cmbxShop.Text)
                Try
                    conn.Open()
                    Dim reader As SqlDataReader = cmd.ExecuteReader()
                    If reader.Read() Then
                        cmbxBarcodeType.Text = reader("BarcodeType").ToString()
                        ckbxEnableEmbeddedWeight.Checked = Convert.ToBoolean(reader("EnableEmbeddedWeight"))
                        txtEmbeddedFormat.Text = reader("EmbeddedFormat").ToString()
                        txtWeightDivisor.Text = reader("WeightDivisor").ToString()
                        txtCurrencyDivisor.Text = reader("CurrencyDivisor").ToString()
                        txtNotes.Text = reader("Notes").ToString()
                    Else
                        ' If no settings exist, clear fields
                        cmbxBarcodeType.SelectedIndex = -1
                        ckbxEnableEmbeddedWeight.Checked = False
                        txtEmbeddedFormat.Clear()
                        txtWeightDivisor.Clear()
                        txtCurrencyDivisor.Clear()
                        txtNotes.Clear()
                    End If
                Catch ex As Exception
                    MessageBox.Show("Error loading settings: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If cmbxShop.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار الفرع أولاً", "")
            Exit Sub
        End If

        Dim existsQuery As String = "SELECT COUNT(*) FROM tblBarcodeSettings WHERE Shop = @Shop"
        Dim saveQuery As String
        Dim isUpdate As Boolean = False

        Using conn As New SqlConnection(ConStr)
            conn.Open()

            ' Check if settings already exist for this shop
            Using checkCmd As New SqlCommand(existsQuery, conn)
                checkCmd.Parameters.AddWithValue("@Shop", cmbxShop.Text)
                Dim count As Integer = Convert.ToInt32(checkCmd.ExecuteScalar())
                isUpdate = (count > 0)
            End Using

            ' Decide query type
            If isUpdate Then
                saveQuery = "UPDATE tblBarcodeSettings SET 
                            BarcodeType = @BarcodeType,
                            EnableEmbeddedWeight = @EnableEmbeddedWeight,
                            EmbeddedFormat = @EmbeddedFormat,
                            WeightDivisor = @WeightDivisor,
                            CurrencyDivisor = @CurrencyDivisor,
                            Notes = @Notes,
                            ModifiedBy = @User,
                            ModifiedOn = GETDATE()
                         WHERE Shop = @Shop"
            Else
                saveQuery = "INSERT INTO tblBarcodeSettings (
                            Shop, BarcodeType, EnableEmbeddedWeight,
                            EmbeddedFormat, WeightDivisor, CurrencyDivisor,
                            Notes, CreatedBy, CreatedOn
                         ) VALUES (
                            @Shop, @BarcodeType, @EnableEmbeddedWeight,
                            @EmbeddedFormat, @WeightDivisor, @CurrencyDivisor,
                            @Notes, @User, GETDATE()
                         )"
            End If

            ' Save or update
            Using cmd As New SqlCommand(saveQuery, conn)
                cmd.Parameters.AddWithValue("@Shop", cmbxShop.Text)
                cmd.Parameters.AddWithValue("@BarcodeType", cmbxBarcodeType.Text)
                cmd.Parameters.AddWithValue("@EnableEmbeddedWeight", ckbxEnableEmbeddedWeight.Checked)
                cmd.Parameters.AddWithValue("@EmbeddedFormat", txtEmbeddedFormat.Text)
                cmd.Parameters.AddWithValue("@WeightDivisor", Val(txtWeightDivisor.Text))
                cmd.Parameters.AddWithValue("@CurrencyDivisor", Val(txtCurrencyDivisor.Text))
                cmd.Parameters.AddWithValue("@Notes", txtNotes.Text)
                cmd.Parameters.AddWithValue("@User", UserName)

                Try
                    cmd.ExecuteNonQuery()
                    MessageBox.Show("تم الحفظ بنجاح")
                Catch ex As Exception
                    MessageBox.Show("حدث خطأ أثناء الحفظ: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub
    Private Sub frmBarcodeSettings_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        FillComboBoxWithShops()
    End Sub


End Class