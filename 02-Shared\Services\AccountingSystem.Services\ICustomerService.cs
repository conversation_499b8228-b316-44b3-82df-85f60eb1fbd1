using AccountingSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public interface ICustomerService
    {
        Task<List<Customer>> GetCustomersAsync(string searchTerm = "");
        Task<Customer?> GetCustomerByNoAsync(long customerNo);
        Task<(bool success, string? accountCode)> CreateCustomerAsync(Customer customer, string currentUser);
        Task<bool> UpdateCustomerAsync(Customer customer, string currentUser);
        Task<bool> DeleteCustomerAsync(long customerNo);
        Task<long> GetNextCustomerNoAsync();
        Task<string> GenerateNextCustomerAccountCodeAsync(string parentCode);
        Task<string?> GetCustomerParentAccountCodeAsync();
    }
}
