﻿Imports System.Data.SqlClient
Imports System.Drawing.Printing
Imports CrystalDecisions.CrystalReports.Engine

Public Class frmItemStock
    Private Sub frmItemStock_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        'LoadItemButtons(pnlFavorites, dt)
        StoresLoad()
        FillComboBoxWithCategories()

    End Sub
    Private Sub btnItemLookup_Click(sender As Object, e As EventArgs) Handles btnItemLookup.Click
        SearchItemFor = "ItemStock"
        frmItemSearch.ShowDialog()
    End Sub

    Private Sub txtItemNo_KeyDown(sender As Object, e As KeyEventArgs) Handles txtItemNo.KeyDown
        If e.KeyCode = Keys.Enter Then
            If txtItemNo.Text <> "" Then
                Dim SearchCMD As New SqlCommand("Select ItemDescription from tblItems where ItemNo = " & Val(txtItemNo.Text) & "", Con)
                OpenConnection()
                Dim reader As SqlDataReader = SearchCMD.ExecuteReader
                If reader.Read Then
                    ItemDescription = Trim(reader.Item(0).ToString)

                    txtItemDescription.Text = ItemDescription

                    If reader.IsClosed = False Then
                        reader.Close()
                    End If
                    CloseConnection()
                Else
                    If reader.IsClosed = False Then
                        reader.Close()
                    End If

                    txtItemDescription.Clear()
                End If
            Else
                txtItemDescription.Clear()
            End If
        End If
    End Sub


    Private Sub FillComboBoxWithCategories()
        Dim dtable As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT RootID,RootName FROM tblItemsCategory where ParentID = 1 ORDER BY RootName", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dtable)
        End Using
        cmbxCategory.DataSource = dtable
        cmbxCategory.DisplayMember = "RootName"
        cmbxCategory.ValueMember = "RootID"
    End Sub

    Sub StoresLoad()
        Dim CMD As New SqlCommand("Select SN,Store from tblStores Order by Store", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxStore.Items.Clear()
        Do While reader.Read
            cmbxStore.Items.Add(reader.Item(1).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Private Sub btnPreview_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        If cmbxStore.Text.Trim <> "" Then

            If txtItemDescription.Text.Trim <> "" And txtItemNo.Text.Trim <> "" Then
                ItemNoForReport = txtItemNo.Text.Trim
            Else
                ItemNoForReport = "0"
            End If

            If cmbxCategory.Text <> "" Then
                ItemsCategory = cmbxCategory.SelectedValue
            Else
                ItemsCategory = 0
            End If

            PrintType = "ItemStock"
            StoreForReport = cmbxStore.Text
            frmPrintPreview.MdiParent = frmMain
            frmPrintPreview.Show()
        Else
            MsgBox("فضلا اختر المخزن", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

    Private Sub txtItemNo_LostFocus(sender As Object, e As EventArgs) Handles txtItemNo.LostFocus
        If txtItemNo.Text <> "" Then
            Dim SearchCMD As New SqlCommand("Select ItemDescription from tblItems where ItemNo = " & Val(txtItemNo.Text) & "", Con)
            OpenConnection()
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                ItemDescription = Trim(reader.Item(0).ToString)

                txtItemDescription.Text = ItemDescription

                If reader.IsClosed = False Then
                    reader.Close()
                End If
                CloseConnection()
            Else
                If reader.IsClosed = False Then
                    reader.Close()
                End If

                txtItemDescription.Clear()
            End If
        Else
            txtItemDescription.Clear()
        End If
    End Sub
End Class