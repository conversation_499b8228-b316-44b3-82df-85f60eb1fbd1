@model AccountingSystem.Services.ViewModels.POSSessionCloseViewModel
@{
    ViewData["Title"] = "إغلاق الجلسة";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-lock me-2"></i>
                        إغلاق الجلسة
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>رقم الجلسة:</strong> @Model.SessionSN
                    </div>

                    <form asp-action="Close" method="post" id="closeSessionForm">
                        <input type="hidden" asp-for="SessionID" />
                        <input type="hidden" asp-for="SessionSN" />
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="ClosingCash" class="form-label"></label>
                                    <input asp-for="ClosingCash" class="form-control" type="number" step="0.01" min="0" required />
                                    <span asp-validation-for="ClosingCash" class="text-danger"></span>
                                    <small class="form-text text-muted">أدخل المبلغ النهائي الموجود في الدرج</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="Note" class="form-label"></label>
                                    <textarea asp-for="Note" class="form-control" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                                    <span asp-validation-for="Note" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تحذير:</strong> لا يمكن التراجع عن إغلاق الجلسة بعد التأكيد.
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-warning" onclick="return confirm('هل أنت متأكد من إغلاق هذه الجلسة؟')">
                                <i class="fas fa-lock me-1"></i>
                                إغلاق الجلسة
                            </button>
                            <a href="@Url.Action("Index")" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i>
                                رجوع
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Form validation
            $('#closeSessionForm').submit(function (e) {
                var closingCash = $('#ClosingCash').val();
                
                if (!closingCash || closingCash <= 0) {
                    if (!confirm('هل أنت متأكد من أن المبلغ النهائي هو صفر؟')) {
                        e.preventDefault();
                        $('#ClosingCash').focus();
                        return false;
                    }
                }
            });

            // Auto-format currency input
            $('#ClosingCash').on('input', function() {
                var value = $(this).val();
                if (value && !isNaN(value)) {
                    $(this).val(parseFloat(value).toFixed(2));
                }
            });

            // Focus on closing cash input
            $('#ClosingCash').focus();
        });
    </script>
} 