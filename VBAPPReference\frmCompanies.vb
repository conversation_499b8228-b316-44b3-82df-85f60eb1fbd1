﻿Imports System.Data.SqlClient
Imports System.Data
Public Class frmCompanies

    Private Sub frmCompanies_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        'frmCards.Enabled = True
    End Sub

    Private Sub frmUofM_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        CompanyLoad()
    End Sub
    Sub CompanyLoad()
        Dim ds As DataSet = New DataSet
        Dim usercmd As New SQLCommand("select Company as [الشركة] from tblCompanies order by Company", Con)
        Dim da As New SQLDataAdapter(usercmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If TextBox1.Text.Trim <> "" Then
            Dim SearchCMD As New SQLCommand("Select * from tblCompanies where Company = '" & TextBox1.Text.Trim & "'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                MessageBox.Show("عفوًا هذه الشركة موجودة مسبقًا")
                reader.Close()
                TextBox1.Focus()
                TextBox1.SelectAll()
            Else
                reader.Close()
                Dim InsertCMD As New SQLCommand("Insert Into tblCompanies (Company,CreatedBy,CreatedOn) Values ('" & TextBox1.Text.Trim & "','" & UserName & "','" & Format(DateAndTime.Now, "yyyy/MM/dd hh:mm:ss tt") & "')", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                InsertCMD.ExecuteNonQuery()
                MessageBox.Show("تم إضافة الشركة بنجاح")
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                CompanyLoad()
                TextBox1.Clear()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
        End If

    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If TextBox1.Text.Trim <> "" Then
            If MsgBox("هل تريد بالتأكيد حذف الشركة؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
                Dim DeleteCMD As New SqlCommand("Delete from tblCompanies where Company = '" & TextBox1.Text.Trim & "'", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                DeleteCMD.ExecuteNonQuery()
                MessageBox.Show("تم حذف الشركة بنجاح", "نظام السلطان")
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                CompanyLoad()
                TextBox1.Clear()
            End If
        End If
    End Sub

    Private Sub DataGridView1_CellClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellClick
        If DataGridView1.SelectedRows.Count - 1 >= 0 Then
            TextBox1.Text = DataGridView1.SelectedRows(0).Cells(0).Value
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub DataGridView1_CellContentClick(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView1.CellContentClick

    End Sub
End Class