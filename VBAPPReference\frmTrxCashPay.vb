﻿Imports System.Data.SqlClient
Public Class frmTrxCashPay
    Public Recipient As String = ""
    Public TrxType As String = ""


    Sub CashierLoad()
        Dim CMD As New SqlCommand("SELECT AccountCode,AccountName from tbl_Acc_Accounts WHERE ParentAccountCode = (SELECT AccountNo FROM tblGLConfig WHERE EntryReferenceModule = 'نقدية') ORDER BY AccountName", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()

        dt.Load(reader)
        reader.Close()

        ' Bind the ComboBox to DataTable
        cmbxCashier.DataSource = dt
        cmbxCashier.DisplayMember = "AccountName" ' Show Name
        cmbxCashier.ValueMember = "AccountCode" ' Store ID

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If

    End Sub
    Private Sub frmTrxCashIn_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        CashierLoad()
        ForceGregorianForAllPickers(Me)

        TrxType = "صرف"
        cmbxCashier.SelectedIndex = 0
        If InvNoForPayment <> 0 Then
            txtInvoiceNo.Text = InvNoForPayment.ToString()
            txtAmount.Text = PriceForPayment
            cmbxAccount.Enabled = False
        Else
            txtInvoiceNo.Enabled = True
            AccountsLoad()
        End If
    End Sub

    Sub AccountsLoad()
        Dim CMD As New SqlCommand("SELECT AccountCode,AccountName from tbl_Acc_Accounts ORDER BY AccountCode", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()

        dt.Load(reader)
        reader.Close()

        ' Bind the ComboBox to DataTable
        cmbxAccount.DataSource = dt
        cmbxAccount.DisplayMember = "AccountName" ' Show Name
        cmbxAccount.ValueMember = "AccountCode" ' Store ID

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If

    End Sub


    'Sub GLEntry()
    '    ' Check Credit Account الحساب الدائن حساب الصندوق اذا كان نقدي او حساب الموردين اذا كان آجل
    '    Dim CreditString As String = ""

    '    Dim CMDCheckCredit As New SQLCommand("Select RootID from tblRoots where RootName = '" & Trim(cmbxCashier.Text) & "'", Con)
    '    If Con.State <> ConnectionState.Open Then
    '        Con.Open()
    '    End If
    '    Dim reader As SqlDataReader = CMDCheckCredit.ExecuteReader
    '    If reader.Read Then
    '        TrxCredit = Val(reader.Item(0).ToString)
    '    Else
    '        TrxCredit = 0
    '    End If
    '    If reader.IsClosed Then
    '    Else
    '        reader.Close()
    '    End If
    '    If Con.State <> ConnectionState.Closed Then
    '        Con.Close()
    '    End If

    '    Dim CMDCheckDebit As New SQLCommand("Select AccountNo from AccountChart where AccountDescription = '" & cmbxAccount.Text.Trim & "'", Con)
    '    If Con.State <> ConnectionState.Open Then
    '        Con.Open()
    '    End If
    '    Dim reader2 As SqlDataReader = CMDCheckDebit.ExecuteReader
    '    If reader2.Read Then
    '        TrxDebit = Val(reader2.Item(0).ToString)
    '    Else
    '        TrxDebit = 0
    '    End If
    '    'If reader.IsClosed Then
    '    'Else
    '    '    reader.Close()
    '    'End If
    '    If Con.State <> ConnectionState.Closed Then
    '        Con.Close()
    '    End If
    '    '===========================================
    '    ' Get Serial
    '    JEHeader()
    '    '===========================================
    '    ' Insert Trx
    '    If TrxCredit And TrxDebit <> 0 Then
    '        Dim DeleteCMD As New SQLCommand("Delete tblGLTrx where JESN = " & Val(JESN) & " ", Con)
    '        If Con.State <> ConnectionState.Open Then
    '            Con.Open()
    '        End If
    '        DeleteCMD.ExecuteNonQuery()
    '        If Con.State <> ConnectionState.Closed Then
    '            Con.Close()
    '        End If
    '        Dim InsertDebitCMD As New SQLCommand("Insert Into tblGLTrx (JESN,RootID,Debit,Credit) Values (" & Val(JESN) & "," & Val(TrxDebit) & "," & Val(txtAmount.Text) & ",0)", Con)
    '        Dim InsertCreditCMD As New SQLCommand("Insert Into tblGLTrx (JESN,RootID,Debit,Credit) Values (" & Val(JESN) & "," & Val(TrxCredit) & ",0," & Val(txtAmount.Text) & ")", Con)
    '        If Con.State <> ConnectionState.Open Then
    '            Con.Open()
    '        End If
    '        InsertDebitCMD.ExecuteNonQuery()
    '        InsertCreditCMD.ExecuteNonQuery()
    '        If Con.State <> ConnectionState.Closed Then
    '            Con.Close()
    '        End If
    '        MsgBox("تم حفظ السند بنجاح", MsgBoxStyle.Information, "نظام السلطان")
    '        If CashPayPrint = "True" Then
    '            PrintType = "CashPay"
    '            frmCashPrintPreview.MdiParent = frmMain
    '            frmCashPrintPreview.Show()
    '            JESN = 0
    '        End If
    '        ClearFields()
    '    Else
    '        MsgBox("خطأ في اعدادات الدليل المحاسبي ... لن يتم انشاء القيد", MsgBoxStyle.Critical, "نظام السلطان")
    '    End If
    'End Sub
    Sub ClearFields()
        txtAmount.Clear()
        txtDescription.Clear()
        AccountsLoad()
        CashierLoad()
        txtAmount.Focus()
    End Sub
    'Sub JEHeader()

    '    Dim InsertHeaderCMD As New SQLCommand("Insert Into tblJEHeader (TrxDate,Amount,Reference,ReferenceTrx,ReferenceType,TrxNote,PostStatus,CreatedBy,CreatedOn) values ('" & Format(dtp1.Value.Date, "yyyy/MM/dd") & "'," & Val(txtAmount.Text) & ",'بدون',0,'سند دفع','" & Trim(txtDescription.Text) & "','Open','" & UserName & "',getdate()) SELECT SCOPE_IDENTITY()", Con)
    '        If Con.State <> ConnectionState.Open Then
    '            Con.Open()
    '        End If
    '        JESN = InsertHeaderCMD.ExecuteScalar()
    '        If Con.State <> ConnectionState.Closed Then
    '            Con.Close()
    '        End If

    'End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Me.Close()
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        If cmbxCashier.Text.Trim <> "" And Val(txtAmount.Text) > 0 Then
            If InvNoForPayment = 0 Then
                If txtDescription.Text.Trim = "" Then
                    MessageBox.Show("الرجاء إدخال وصف للقيد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    txtDescription.Focus()
                    Exit Sub
                End If
                If cmbxAccount.Text.Trim = "" Then
                    MessageBox.Show("الرجاء اختيار حساب المورد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    cmbxAccount.Focus()
                    Exit Sub
                End If

                Try
                    Using Con As New SqlConnection(Constr)
                        Using cmd As New SqlCommand("sp_CreateManualCashPaymentEntry", Con)
                            cmd.CommandType = CommandType.StoredProcedure
                            cmd.Parameters.AddWithValue("@TrxDate", dtp1.Value.Date)
                            cmd.Parameters.AddWithValue("@CashAccountCode", cmbxCashier.SelectedValue)
                            cmd.Parameters.AddWithValue("@ReceiverAccountCode", cmbxAccount.SelectedValue)
                            cmd.Parameters.AddWithValue("@Amount", Val(txtAmount.Text))
                            cmd.Parameters.AddWithValue("@Notes", Trim(txtDescription.Text))
                            cmd.Parameters.AddWithValue("@CreatedBy", UserName)

                            If Con.State <> ConnectionState.Open Then
                                Con.Open()
                            End If
                            cmd.ExecuteNonQuery()
                            If Con.State <> ConnectionState.Closed Then
                                Con.Close()
                            End If
                        End Using
                    End Using
                Catch ex As Exception
                    MessageBox.Show("خطأ أثناء ترحيل القيد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Exit Sub
                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                End Try
                MsgBox("تم حفظ القيد بنجاح", MsgBoxStyle.Information, "نظام السلطان")
                ClearFields()
            Else
                Try
                        Using Con As New SqlConnection(Constr)
                            Using cmd As New SqlCommand("sp_CreateVendorPaymentForInvoice", Con)
                                cmd.CommandType = CommandType.StoredProcedure
                                cmd.Parameters.AddWithValue("@EntryDate", dtp1.Value.Date)
                                cmd.Parameters.AddWithValue("@CashAccountCode", cmbxCashier.SelectedValue)
                                cmd.Parameters.AddWithValue("@Amount", Val(txtAmount.Text))
                                cmd.Parameters.AddWithValue("@Description", Trim(txtDescription.Text))
                                cmd.Parameters.AddWithValue("@InvoiceNo", Val(txtInvoiceNo.Text))
                                If Con.State <> ConnectionState.Open Then
                                    Con.Open()
                                End If
                                cmd.ExecuteNonQuery()
                                If Con.State <> ConnectionState.Closed Then
                                    Con.Close()
                                End If
                            End Using
                        End Using
                    Catch ex As Exception
                        MessageBox.Show("خطأ أثناء ترحيل القيد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                        Exit Sub
                        If Con.State <> ConnectionState.Closed Then
                            Con.Close()
                        End If
                    End Try
                    MsgBox("تم حفظ القيد بنجاح", MsgBoxStyle.Information, "نظام السلطان")
                    ClearFields()
                InvNoForPayment = 0
                PriceForPayment = 0
                Me.Close()
            End If
            'GLEntry()

        Else
            MsgBox("البيانات غير مكتملة، لايمكن حفظ المستند", MsgBoxStyle.Critical, "نظام السلطان")
        End If

    End Sub

    Private Sub btnDebitSearch_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnAccountSearch.Click
        AccountSearchForm = "frmTrxCashPay"
        frmAccountSearch.ShowDialog()
    End Sub
End Class