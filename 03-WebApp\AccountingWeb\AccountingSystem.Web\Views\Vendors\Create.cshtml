@model AccountingSystem.Models.Vendor

@{
    ViewData["Title"] = "إضافة مورد جديد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مورد جديد
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">المعلومات الأساسية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="VendorNo" class="form-label"></label>
                                            <input asp-for="VendorNo" class="form-control" readonly />
                                            <span asp-validation-for="VendorNo" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="VendorName" class="form-label"></label>
                                            <input asp-for="VendorName" class="form-control" />
                                            <span asp-validation-for="VendorName" class="text-danger"></span>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="FirstName" class="form-label"></label>
                                                    <input asp-for="FirstName" class="form-control" />
                                                    <span asp-validation-for="FirstName" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="LastName" class="form-label"></label>
                                                    <input asp-for="LastName" class="form-control" />
                                                    <span asp-validation-for="LastName" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="Mobile" class="form-label"></label>
                                                    <input asp-for="Mobile" class="form-control" />
                                                    <span asp-validation-for="Mobile" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="Phone" class="form-label"></label>
                                                    <input asp-for="Phone" class="form-control" />
                                                    <span asp-validation-for="Phone" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="Email" class="form-label"></label>
                                            <input asp-for="Email" class="form-control" type="email" />
                                            <span asp-validation-for="Email" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information -->
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">معلومات العنوان</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="StreetAddress1" class="form-label"></label>
                                            <input asp-for="StreetAddress1" class="form-control" />
                                            <span asp-validation-for="StreetAddress1" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="StreetAddress2" class="form-label"></label>
                                            <input asp-for="StreetAddress2" class="form-control" />
                                            <span asp-validation-for="StreetAddress2" class="text-danger"></span>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="City" class="form-label"></label>
                                                    <input asp-for="City" class="form-control" />
                                                    <span asp-validation-for="City" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="Region" class="form-label"></label>
                                                    <input asp-for="Region" class="form-control" />
                                                    <span asp-validation-for="Region" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="PostalCode" class="form-label"></label>
                                            <input asp-for="PostalCode" class="form-control" />
                                            <span asp-validation-for="PostalCode" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Business Information -->
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">المعلومات التجارية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="PaymentMethod" class="form-label"></label>
                                            <select asp-for="PaymentMethod" class="form-select">
                                                <option value="">اختر طريقة الدفع</option>
                                                @foreach (var method in ViewBag.PaymentMethods as List<string>)
                                                {
                                                    <option value="@method">@method</option>
                                                }
                                            </select>
                                            <span asp-validation-for="PaymentMethod" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="CreditLimit" class="form-label"></label>
                                            <input asp-for="CreditLimit" class="form-control" type="number" step="0.01" />
                                            <span asp-validation-for="CreditLimit" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="PaymentTerm" class="form-label"></label>
                                            <input asp-for="PaymentTerm" class="form-control" />
                                            <span asp-validation-for="PaymentTerm" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="ContactPerson" class="form-label"></label>
                                            <input asp-for="ContactPerson" class="form-control" />
                                            <span asp-validation-for="ContactPerson" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Information -->
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">معلومات إضافية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="CR" class="form-label"></label>
                                                    <input asp-for="CR" class="form-control" />
                                                    <span asp-validation-for="CR" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="VATRegNo" class="form-label"></label>
                                                    <input asp-for="VATRegNo" class="form-control" />
                                                    <span asp-validation-for="VATRegNo" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="Shop" class="form-label"></label>
                                            <select asp-for="Shop" class="form-select">
                                                <option value="">اختر المتجر</option>
                                                @if (ViewBag.Shops != null)
                                                {
                                                    @foreach (var shop in ViewBag.Shops)
                                                    {
                                                        <option value="@shop.StoreName">@shop.StoreName</option>
                                                    }
                                                }
                                            </select>
                                            <span asp-validation-for="Shop" class="text-danger"></span>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="Status" class="form-label"></label>
                                                    <select asp-for="Status" class="form-select">
                                                        <option value="">اختر الحالة</option>
                                                        @foreach (var status in ViewBag.StatusOptions as List<string>)
                                                        {
                                                            <option value="@status">@status</option>
                                                        }
                                                    </select>
                                                    <span asp-validation-for="Status" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="LocalVendor" class="form-label"></label>
                                                    <select asp-for="LocalVendor" class="form-select">
                                                        <option value="">اختر النوع</option>
                                                        @foreach (var type in ViewBag.LocalVendorOptions as List<string>)
                                                        {
                                                            <option value="@type">@type</option>
                                                        }
                                                    </select>
                                                    <span asp-validation-for="LocalVendor" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>



                                        <div class="mb-3">
                                            <label asp-for="Notes" class="form-label"></label>
                                            <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                                            <span asp-validation-for="Notes" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        العودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المورد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
