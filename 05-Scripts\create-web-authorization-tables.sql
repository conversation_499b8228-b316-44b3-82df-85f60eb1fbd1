-- =================================================================
-- Web Authorization Tables Creation Script
-- Created: October 2025
-- Purpose: Create tables for web application authorization 
--          (separate from VB.NET GUI authorization)
-- =================================================================

USE [YourDatabaseName] -- Replace with your database name
GO

-- =================================================================
-- 1. WebFormMenus Table - Stores web menu items
-- =================================================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[WebFormMenus]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[WebFormMenus](
        [MenuID] [int] IDENTITY(1,1) NOT NULL,
        [DisplayName] [nvarchar](200) NOT NULL,
        [DisplayNameEn] [nvarchar](200) NULL,
        [Route] [nvarchar](500) NOT NULL,
        [Icon] [nvarchar](100) NULL,
        [ParentMenuId] [int] NULL,
        [DisplayOrder] [int] NOT NULL DEFAULT(0),
        [ModuleName] [nvarchar](100) NULL,
        [IsContainer] [bit] NOT NULL DEFAULT(0),
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [CssClass] [nvarchar](200) NULL,
        [Description] [nvarchar](500) NULL,
        [CreatedBy] [nvarchar](50) NULL,
        [ModifiedBy] [nvarchar](50) NULL,
        CONSTRAINT [PK_WebFormMenus] PRIMARY KEY CLUSTERED ([MenuID] ASC),
        CONSTRAINT [FK_WebFormMenus_ParentMenu] FOREIGN KEY([ParentMenuId]) 
            REFERENCES [dbo].[WebFormMenus] ([MenuID])
    )
    
    CREATE NONCLUSTERED INDEX [IX_WebFormMenus_Route] ON [dbo].[WebFormMenus]([Route])
    CREATE NONCLUSTERED INDEX [IX_WebFormMenus_Module] ON [dbo].[WebFormMenus]([ModuleName], [DisplayOrder])
    
    PRINT 'WebFormMenus table created successfully'
END
ELSE
BEGIN
    PRINT 'WebFormMenus table already exists'
END
GO

-- =================================================================
-- 2. WebQuickActions Table - Stores dashboard quick actions
-- =================================================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[WebQuickActions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[WebQuickActions](
        [QuickActionID] [int] IDENTITY(1,1) NOT NULL,
        [DisplayName] [nvarchar](200) NOT NULL,
        [DisplayNameEn] [nvarchar](200) NULL,
        [Route] [nvarchar](500) NOT NULL,
        [Icon] [nvarchar](100) NULL,
        [ColorClass] [nvarchar](50) NULL DEFAULT('primary'),
        [DisplayOrder] [int] NOT NULL DEFAULT(0),
        [Category] [nvarchar](100) NULL,
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [Description] [nvarchar](500) NULL,
        [CreatedBy] [nvarchar](50) NULL,
        [ModifiedBy] [nvarchar](50) NULL,
        CONSTRAINT [PK_WebQuickActions] PRIMARY KEY CLUSTERED ([QuickActionID] ASC)
    )
    
    CREATE NONCLUSTERED INDEX [IX_WebQuickActions_Route] ON [dbo].[WebQuickActions]([Route])
    CREATE NONCLUSTERED INDEX [IX_WebQuickActions_Category] ON [dbo].[WebQuickActions]([Category], [DisplayOrder])
    
    PRINT 'WebQuickActions table created successfully'
END
ELSE
BEGIN
    PRINT 'WebQuickActions table already exists'
END
GO

-- =================================================================
-- 3. WebGroupMenuPermissions Table - Links groups to menu permissions
-- =================================================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[WebGroupMenuPermissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[WebGroupMenuPermissions](
        [PermissionID] [int] IDENTITY(1,1) NOT NULL,
        [GroupID] [int] NOT NULL,
        [MenuId] [int] NOT NULL,
        [CanView] [bit] NOT NULL DEFAULT(1),
        [CanAdd] [bit] NOT NULL DEFAULT(0),
        [CanEdit] [bit] NOT NULL DEFAULT(0),
        [CanDelete] [bit] NOT NULL DEFAULT(0),
        [CanPrint] [bit] NOT NULL DEFAULT(0),
        [CustomPermission1] [bit] NOT NULL DEFAULT(0),
        [CustomPermission2] [bit] NOT NULL DEFAULT(0),
        [CreatedBy] [nvarchar](50) NULL,
        [ModifiedBy] [nvarchar](50) NULL,
        CONSTRAINT [PK_WebGroupMenuPermissions] PRIMARY KEY CLUSTERED ([PermissionID] ASC),
        CONSTRAINT [FK_WebGroupMenuPermissions_Group] FOREIGN KEY([GroupID]) 
            REFERENCES [dbo].[tblGroupsAuth] ([GroupID]) ON DELETE CASCADE,
        CONSTRAINT [FK_WebGroupMenuPermissions_Menu] FOREIGN KEY([MenuId]) 
            REFERENCES [dbo].[WebFormMenus] ([MenuID]) ON DELETE CASCADE,
        CONSTRAINT [UQ_WebGroupMenuPermissions] UNIQUE([GroupID], [MenuId])
    )
    
    PRINT 'WebGroupMenuPermissions table created successfully'
END
ELSE
BEGIN
    PRINT 'WebGroupMenuPermissions table already exists'
END
GO

-- =================================================================
-- 4. WebGroupQuickActionPermissions Table - Links groups to quick actions
-- =================================================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[WebGroupQuickActionPermissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[WebGroupQuickActionPermissions](
        [PermissionID] [int] IDENTITY(1,1) NOT NULL,
        [GroupID] [int] NOT NULL,
        [QuickActionId] [int] NOT NULL,
        [IsVisible] [bit] NOT NULL DEFAULT(1),
        [CreatedBy] [nvarchar](50) NULL,
        [ModifiedBy] [nvarchar](50) NULL,
        CONSTRAINT [PK_WebGroupQuickActionPermissions] PRIMARY KEY CLUSTERED ([PermissionID] ASC),
        CONSTRAINT [FK_WebGroupQuickActionPermissions_Group] FOREIGN KEY([GroupID]) 
            REFERENCES [dbo].[tblGroupsAuth] ([GroupID]) ON DELETE CASCADE,
        CONSTRAINT [FK_WebGroupQuickActionPermissions_QuickAction] FOREIGN KEY([QuickActionId]) 
            REFERENCES [dbo].[WebQuickActions] ([QuickActionID]) ON DELETE CASCADE,
        CONSTRAINT [UQ_WebGroupQuickActionPermissions] UNIQUE([GroupID], [QuickActionId])
    )
    
    PRINT 'WebGroupQuickActionPermissions table created successfully'
END
ELSE
BEGIN
    PRINT 'WebGroupQuickActionPermissions table already exists'
END
GO

PRINT ''
PRINT '========================================='
PRINT 'Web Authorization Tables Created Successfully!'
PRINT 'Next Step: Run seed-web-authorization-data.sql to populate initial data'
PRINT '========================================='
GO

