using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Models;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Linq;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    [Route("ItemsCategories")]
    public class ItemsCategoriesController : Controller
    {
        private readonly IItemsCategoryService _categoryService;
        private readonly ILogger<ItemsCategoriesController> _logger;

        public ItemsCategoriesController(IItemsCategoryService categoryService, ILogger<ItemsCategoriesController> logger)
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        [HttpGet("")]
        public async Task<IActionResult> Index()
        {
            var categories = await _categoryService.GetCategoriesAsTreeAsync();
            var allCategories = categories.SelectMany(Flatten).ToList();
            ViewBag.AllCategories = allCategories;
            return View(categories);
        }

        [HttpPost("Create")]
        public async Task<IActionResult> CreateCategory(ItemsCategory category)
        {
            if (string.IsNullOrWhiteSpace(category.Name))
            {
                TempData["ErrorMessage"] = "اسم التصنيف مطلوب.";
                return RedirectToAction(nameof(Index));
            }

            var currentUser = User.Identity?.Name ?? "System";
            var success = await _categoryService.CreateCategoryAsync(category, currentUser);

            if (success)
            {
                TempData["SuccessMessage"] = "تم إنشاء التصنيف بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في إنشاء التصنيف.";
            }

            return RedirectToAction(nameof(Index));
        }
        
        [HttpPost("Edit")]
        public async Task<IActionResult> EditCategory(ItemsCategory category)
        {
            if (string.IsNullOrWhiteSpace(category.Name))
            {
                TempData["ErrorMessage"] = "اسم التصنيف مطلوب.";
                return RedirectToAction(nameof(Index));
            }

            var currentUser = User.Identity?.Name ?? "System";
            var success = await _categoryService.UpdateCategoryAsync(category, currentUser);

            if (success)
            {
                TempData["SuccessMessage"] = "تم تحديث التصنيف بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في تحديث التصنيف.";
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpPost("Delete")]
        public async Task<IActionResult> DeleteCategory(int id)
        {
            var (success, errorMessage) = await _categoryService.DeleteCategoryAsync(id);

            if (success)
            {
                TempData["SuccessMessage"] = "تم حذف التصنيف بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = errorMessage;
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpGet("Details/{id}")]
        public async Task<IActionResult> GetCategoryDetails(int id)
        {
            var category = await _categoryService.GetCategoryByIdAsync(id);
            if (category == null)
            {
                return NotFound();
            }
            return Json(new { id = category.Id, name = category.Name, parentId = category.ParentId });
        }

        private System.Collections.Generic.IEnumerable<ItemsCategory> Flatten(ItemsCategory category)
        {
            yield return category;
            foreach (var child in category.Children.SelectMany(Flatten))
            {
                yield return child;
            }
        }
    }
} 