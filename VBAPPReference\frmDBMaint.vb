﻿Imports System.Data.SqlClient
Public Class frmDBMaint
    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Dim Filename As String
        Dim db_name As String = My.Settings.database
        Try
            Dim dt As DateTime = Today
            Dim destdir As String = "" & db_name & "" & System.DateTime.Now.ToString("dd-MM-yyyy_h-mm-ss") & ".bak"
            Dim objdlg As New SaveFileDialog
            objdlg.FileName = destdir
            objdlg.ShowDialog()
            Filename = objdlg.FileName
            Cursor = Cursors.WaitCursor
            Timer2.Enabled = True

            Con.Open()
            Dim cb As String = "backup database " & db_name & "  to disk='" & Filename & "'"
            Dim cmd As New SqlCommand(cb)
            cmd.Connection = Con
            cmd.ExecuteReader()
            Con.Close()
            MessageBox.Show("تم حفظ النسخة الاحتياطية بنجاح", "النسخ الاحتياطي", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click

        'Dim ofdRestore As New OpenFileDialog

      
        Dim db_name As String = My.Settings.database
        Try
            With OpenFileDialog1
                .Filter = ("DB Backup File|*.bak;")
                .FilterIndex = 4
            End With

            OpenFileDialog1.FileName = ""

            If OpenFileDialog1.ShowDialog() = DialogResult.OK Then
                Cursor = Cursors.WaitCursor
                Timer2.Enabled = True
                SqlConnection.ClearAllPools()

                Con.Open()

                Dim cb As String = "ALTER DATABASE " & db_name & " SET OFFLINE WITH ROLLBACK IMMEDIATE;Restore Database " & db_name & " From Disk = '" & OpenFileDialog1.FileName & "'"
                Dim cmd As New SqlCommand(cb)
                cmd.Connection = Con
                cmd.ExecuteReader()
                Con.Close()
                Dim st As String = "تمت عملية الاستعادة بنجاح"

                MessageBox.Show("تم استعادة قاعدة البيانات", "الاستعادة", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

    End Sub

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        Me.Close()
    End Sub

    Private Sub frmDBMaint_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub
End Class