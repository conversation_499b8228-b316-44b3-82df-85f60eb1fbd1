namespace AccountingSystem.Web.ViewModels
{
    public class InvoiceToolSettingsViewModel
    {
        public string InvoiceType { get; set; }
        public string? DefaultPrinter { get; set; }
        public int? PrintOption { get; set; }
        public bool PriceIncludeVATDef { get; set; }
        public bool NonVATInvoiceDef { get; set; }
        public bool ReferenceMandatory { get; set; }
        public bool MandatoryCustomerVATReg { get; set; }
        public string? PaymentType { get; set; }
    }
} 