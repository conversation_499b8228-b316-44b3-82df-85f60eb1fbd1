using AccountingSystem.Services;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace AccountingSystem.Web.Controllers
{
    public class BarcodeSettingsController : Controller
    {
        private readonly IBarcodeSettingsService _barcodeSettingsService;

        public BarcodeSettingsController(IBarcodeSettingsService barcodeSettingsService)
        {
            _barcodeSettingsService = barcodeSettingsService;
        }

        public async Task<IActionResult> Index()
        {
            var settings = await _barcodeSettingsService.GetBarcodeSettingsAsync();
            return View(settings);
        }

        public IActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(AccountingSystem.Models.BarcodeSettings barcodeSettings)
        {
            if (ModelState.IsValid)
            {
                await _barcodeSettingsService.CreateBarcodeSettingsAsync(barcodeSettings);
                return RedirectToAction(nameof(Index));
            }
            return View(barcodeSettings);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var barcodeSettings = await _barcodeSettingsService.GetBarcodeSettingsByIdAsync(id);
            if (barcodeSettings == null)
            {
                return NotFound();
            }
            return View(barcodeSettings);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, AccountingSystem.Models.BarcodeSettings barcodeSettings)
        {
            if (id != barcodeSettings.ID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                await _barcodeSettingsService.UpdateBarcodeSettingsAsync(barcodeSettings);
                return RedirectToAction(nameof(Index));
            }
            return View(barcodeSettings);
        }

        public async Task<IActionResult> Details(int id)
        {
            var barcodeSettings = await _barcodeSettingsService.GetBarcodeSettingsByIdAsync(id);
            if (barcodeSettings == null)
            {
                return NotFound();
            }
            return View(barcodeSettings);
        }

        public async Task<IActionResult> Delete(int id)
        {
            var barcodeSettings = await _barcodeSettingsService.GetBarcodeSettingsByIdAsync(id);
            if (barcodeSettings == null)
            {
                return NotFound();
            }
            return View(barcodeSettings);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            await _barcodeSettingsService.DeleteBarcodeSettingsAsync(id);
            return RedirectToAction(nameof(Index));
        }
    }
} 