using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Models;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class StoresV2Controller : Controller
    {
        private readonly IStoreServiceV2 _storeService;
        private readonly ILogger<StoresV2Controller> _logger;

        public StoresV2Controller(IStoreServiceV2 storeService, ILogger<StoresV2Controller> logger)
        {
            _storeService = storeService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var stores = await _storeService.GetStoresAsync();
            return View(stores);
        }

        [HttpPost]
        public async Task<IActionResult> CreateStore(Store store)
        {
            if (!ModelState.IsValid)
            {
                TempData["ErrorMessage"] = "البيانات المدخلة غير صالحة.";
                return RedirectToAction(nameof(Index));
            }

            var currentUser = User.Identity?.Name ?? "System";
            var (success, createdStore) = await _storeService.CreateStoreAsync(store, currentUser);

            if (success)
            {
                TempData["SuccessMessage"] = $"تم إنشاء المتجر بنجاح برقم: {createdStore.SN}";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في إنشاء المتجر.";
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        public async Task<IActionResult> EditStore(Store store)
        {
            if (!ModelState.IsValid)
            {
                TempData["ErrorMessage"] = "البيانات المدخلة غير صالحة.";
                return RedirectToAction(nameof(Index));
            }
            
            var currentUser = User.Identity?.Name ?? "System";
            var success = await _storeService.UpdateStoreAsync(store, currentUser);

            if (success)
            {
                TempData["SuccessMessage"] = "تم تحديث المتجر بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في تحديث المتجر.";
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        public async Task<IActionResult> DeleteStore(int id)
        {
            var (success, errorMessage) = await _storeService.DeleteStoreAsync(id);

            if (success)
            {
                TempData["SuccessMessage"] = "تم حذف المتجر بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = errorMessage;
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpGet]
        public async Task<IActionResult> GetStoreDetails(int id)
        {
            var store = await _storeService.GetStoreByIdAsync(id);
            if (store == null)
            {
                return NotFound();
            }
            return Json(store);
        }
    }
} 