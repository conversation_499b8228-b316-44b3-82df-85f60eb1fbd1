using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    /// <summary>
    /// Web form/menu items for authorization control
    /// Separate from VB.NET GUI forms
    /// </summary>
    [Table("WebFormMenus")]
    public class WebFormMenu : BaseEntity
    {
        /// <summary>
        /// Display name in Arabic
        /// </summary>
        [Required]
        [StringLength(200)]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Display name in English (optional)
        /// </summary>
        [StringLength(200)]
        public string? DisplayNameEn { get; set; }

        /// <summary>
        /// Route/URL for the menu item (e.g., "/POS/Index", "/Sales/Invoice")
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Route { get; set; } = string.Empty;

        /// <summary>
        /// Font Awesome icon class (e.g., "fas fa-shopping-cart")
        /// </summary>
        [StringLength(100)]
        public string? Icon { get; set; }

        /// <summary>
        /// Parent menu item ID (null for root items)
        /// </summary>
        public int? ParentMenuId { get; set; }

        /// <summary>
        /// Display order for sorting
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Module/Section name (e.g., "Sales", "Purchase", "Reports")
        /// </summary>
        [StringLength(100)]
        public string? ModuleName { get; set; }

        /// <summary>
        /// Is this a container/section (not a clickable link)
        /// </summary>
        public bool IsContainer { get; set; } = false;

        /// <summary>
        /// Is this menu item active/enabled
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Additional CSS classes for styling
        /// </summary>
        [StringLength(200)]
        public string? CssClass { get; set; }

        /// <summary>
        /// Description/notes for admin reference
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        // Navigation properties
        public virtual WebFormMenu? ParentMenu { get; set; }
        public virtual ICollection<WebFormMenu> SubMenus { get; set; } = new List<WebFormMenu>();
        public virtual ICollection<WebGroupMenuPermission> GroupPermissions { get; set; } = new List<WebGroupMenuPermission>();
    }

    /// <summary>
    /// Quick action buttons for dashboard
    /// Separate from sidebar menu
    /// </summary>
    [Table("WebQuickActions")]
    public class WebQuickAction : BaseEntity
    {
        /// <summary>
        /// Display name in Arabic
        /// </summary>
        [Required]
        [StringLength(200)]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Display name in English (optional)
        /// </summary>
        [StringLength(200)]
        public string? DisplayNameEn { get; set; }

        /// <summary>
        /// Route/URL for the quick action
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Route { get; set; } = string.Empty;

        /// <summary>
        /// Font Awesome icon class
        /// </summary>
        [StringLength(100)]
        public string? Icon { get; set; }

        /// <summary>
        /// Bootstrap color class (e.g., "primary", "success", "warning")
        /// </summary>
        [StringLength(50)]
        public string? ColorClass { get; set; } = "primary";

        /// <summary>
        /// Display order for sorting
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Category/Group for the quick action
        /// </summary>
        [StringLength(100)]
        public string? Category { get; set; }

        /// <summary>
        /// Is this quick action active/enabled
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Description/notes for admin reference
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        // Navigation properties
        public virtual ICollection<WebGroupQuickActionPermission> GroupPermissions { get; set; } = new List<WebGroupQuickActionPermission>();
    }

    /// <summary>
    /// Group permissions for web menu items
    /// Links UserGroup to WebFormMenu
    /// </summary>
    [Table("WebGroupMenuPermissions")]
    public class WebGroupMenuPermission : BaseEntity
    {
        /// <summary>
        /// User group ID (from tblGroupsAuth)
        /// </summary>
        [Required]
        public int GroupID { get; set; }

        /// <summary>
        /// Web form menu ID
        /// </summary>
        [Required]
        public int MenuId { get; set; }

        /// <summary>
        /// Can view/access this menu item
        /// </summary>
        public bool CanView { get; set; } = true;

        /// <summary>
        /// Can add/create new records (if applicable)
        /// </summary>
        public bool CanAdd { get; set; } = false;

        /// <summary>
        /// Can edit existing records (if applicable)
        /// </summary>
        public bool CanEdit { get; set; } = false;

        /// <summary>
        /// Can delete records (if applicable)
        /// </summary>
        public bool CanDelete { get; set; } = false;

        /// <summary>
        /// Can print/export (if applicable)
        /// </summary>
        public bool CanPrint { get; set; } = false;

        /// <summary>
        /// Additional custom permission 1 (flexible for future use)
        /// </summary>
        public bool CustomPermission1 { get; set; } = false;

        /// <summary>
        /// Additional custom permission 2 (flexible for future use)
        /// </summary>
        public bool CustomPermission2 { get; set; } = false;

        // Navigation properties
        public virtual UserGroup Group { get; set; } = null!;
        public virtual WebFormMenu Menu { get; set; } = null!;
    }

    /// <summary>
    /// Group permissions for quick actions
    /// Links UserGroup to WebQuickAction
    /// </summary>
    [Table("WebGroupQuickActionPermissions")]
    public class WebGroupQuickActionPermission : BaseEntity
    {
        /// <summary>
        /// User group ID (from tblGroupsAuth)
        /// </summary>
        [Required]
        public int GroupID { get; set; }

        /// <summary>
        /// Quick action ID
        /// </summary>
        [Required]
        public int QuickActionId { get; set; }

        /// <summary>
        /// Is this quick action visible for the group
        /// </summary>
        public bool IsVisible { get; set; } = true;

        // Navigation properties
        public virtual UserGroup Group { get; set; } = null!;
        public virtual WebQuickAction QuickAction { get; set; } = null!;
    }

    /// <summary>
    /// ViewModel for managing web authorization
    /// </summary>
    public class WebAuthorizationViewModel
    {
        public List<UserGroupDto> Groups { get; set; } = new List<UserGroupDto>();
        public List<WebMenuItemDto> MenuItems { get; set; } = new List<WebMenuItemDto>();
        public List<WebQuickActionDto> QuickActions { get; set; } = new List<WebQuickActionDto>();
    }

    /// <summary>
    /// DTO for user group with user count
    /// </summary>
    public class UserGroupDto
    {
        public int GroupID { get; set; }
        public string GroupName { get; set; } = string.Empty;
        public int UserCount { get; set; }
    }

    /// <summary>
    /// DTO for web menu item with hierarchical structure
    /// </summary>
    public class WebMenuItemDto
    {
        public int Id { get; set; }
        public string DisplayName { get; set; } = string.Empty;
        public string? DisplayNameEn { get; set; }
        public string Route { get; set; } = string.Empty;
        public string? Icon { get; set; }
        public int? ParentMenuId { get; set; }
        public int DisplayOrder { get; set; }
        public string? ModuleName { get; set; }
        public bool IsContainer { get; set; }
        public bool IsActive { get; set; }
        public List<WebMenuItemDto> Children { get; set; } = new List<WebMenuItemDto>();
    }

    /// <summary>
    /// DTO for quick action
    /// </summary>
    public class WebQuickActionDto
    {
        public int Id { get; set; }
        public string DisplayName { get; set; } = string.Empty;
        public string? DisplayNameEn { get; set; }
        public string Route { get; set; } = string.Empty;
        public string? Icon { get; set; }
        public string? ColorClass { get; set; }
        public int DisplayOrder { get; set; }
        public string? Category { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// DTO for group menu permissions
    /// </summary>
    public class GroupMenuPermissionsDto
    {
        public int GroupID { get; set; }
        public List<MenuPermissionDto> MenuPermissions { get; set; } = new List<MenuPermissionDto>();
    }

    /// <summary>
    /// DTO for individual menu permission
    /// </summary>
    public class MenuPermissionDto
    {
        public int MenuId { get; set; }
        public bool CanView { get; set; }
        public bool CanAdd { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public bool CanPrint { get; set; }
    }

    /// <summary>
    /// DTO for group quick action permissions
    /// </summary>
    public class GroupQuickActionPermissionsDto
    {
        public int GroupID { get; set; }
        public List<int> QuickActionIds { get; set; } = new List<int>();
    }
}

