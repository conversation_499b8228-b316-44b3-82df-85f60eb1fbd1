﻿Imports Guna.UI2.WinForms

Public Class frmMain_new

    Private mainPanel As Guna2Panel
    Private currentForm As Form = Nothing
    Private currentSubButton As Guna2Button = Nothing

    Private Sub FormMain_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "نظام السلطان للمبيعات والمحاسبة"
        Me.WindowState = FormWindowState.Maximized
        Me.BackColor = Color.White

        ' ✅ القائمة الجانبية مع التمرير
        Dim sidebar As New Guna2Panel()
        sidebar.Dock = DockStyle.Left
        sidebar.Width = 220
        sidebar.FillColor = Color.FromArgb(21, 101, 192)
        sidebar.AutoScroll = True
        Me.Controls.Add(sidebar)

        mainPanel = New Guna2Panel()
        mainPanel.Dock = DockStyle.Fill
        mainPanel.FillColor = Color.White
        Me.Controls.Add(mainPanel)
        mainPanel.BringToFront()

        Dim top As Integer = 10

        ' 🧭 القوائم الرئيسية مع العناصر الفرعية
        CreateMenuWithSubmenu(sidebar, "المبيعات", top, {
            ("فاتورة مبيعات", GetType(frmSalesInvoiceTrx)),
            ("مرتجع مبيعات", GetType(frmSalesReturnInvoiceTrx)),
            ("نقاط البيع", GetType(frmPOS))
        })

        CreateMenuWithSubmenu(sidebar, "المشتريات", top, {
            ("فاتورة مشتريات", GetType(frmPurchaseInvoiceTrx)),
            ("مرتجع مشتريات", GetType(frmPurchasingReturnInvoice)),
            ("إدارة فواتير الموردين", GetType(frmVendorInvoices))
        })

        CreateMenuWithSubmenu(sidebar, "المخزون", top, {
            ("رصيد أول المدة", GetType(frmOpenStockInvoice)),
            ("تحويل وتعديل المخزون", GetType(frmStockMovement))
        })

        CreateMenuWithSubmenu(sidebar, "البيانات الأساسية", top, {
            ("العملاء", GetType(frmCustomers)),
            ("الموردين", GetType(frmVendors)),
            ("الموظفين", GetType(frmEmployees)),
            ("المواد", GetType(frmItems)),
            ("فئات", GetType(frmItemsCategory)),
            ("المتاجر", GetType(frmShopsMaster)),
            ("المستودعات", GetType(frmStoresMaster)),
            ("الاصناف المفضلة للمستخدمين", GetType(frmUserPOSItems))
        })

        CreateMenuWithSubmenu(sidebar, "التقارير", top, {
            ("المبيعات (بحث)", GetType(frmSearchInvoices)),
            ("كشف حساب عام", GetType(frmJEStatement)),
            ("كشف حساب عميل", GetType(frmCustomerStatement)),
            ("كشف حساب صندوق", GetType(frmCashStatement)),
            ("رصيد المخزون", GetType(frmItemStock))
        })

        CreateMenuWithSubmenu(sidebar, "الضبط", top, {
            ("كلمة مروري", GetType(frmChangeMyPass)),
            ("المستخدمين", GetType(frmUsers)),
            ("مجموعات الصلاحيات", GetType(frmGroupPermissions)),
            ("نسخ احتياطي لقاعدة البيانات", GetType(frmDBMaint)),
            ("الطابعات", GetType(frmToolsPrint)),
            ("إعدادات النظام", GetType(frmSetup)),
            ("توجيه الحسابات", GetType(frmToolsGL)),
            ("الفواتير", GetType(frmToolsInvoice)),
            ("قاعدة البيانات", GetType(frmDBCon)),
            ("ترخيص النسخة", GetType(frmActivation)),
            ("إعدادات الباركود", GetType(frmBarcodeSettings)),
            ("اعدادات وسائل الدفع", GetType(frmPayMethod)),
            ("نقاط البيع - عامة", GetType(frmPOSSettings)),
            ("نقاط البيع - الأجهزة", GetType(frmPOSDevices)),
            ("نقاط البيع - الجلسات", GetType(frmPOSSessions)),
            ("نقاط البيع - الورديات", GetType(frmPOSShifts)),
            ("إدارة دليل الحسابات", GetType(frmCOAManagement))
        })

        ' زر الخروج
        Dim btnExit As New Guna2Button()
        btnExit.Text = "خروج"
        btnExit.Width = sidebar.Width - 20
        btnExit.Height = 45
        btnExit.Left = 10
        btnExit.Top = top + 20
        btnExit.FillColor = Color.Transparent
        btnExit.ForeColor = Color.White
        btnExit.Font = New Font("Segoe UI", 11, FontStyle.Bold)
        btnExit.TextAlign = HorizontalAlignment.Right
        AddHandler btnExit.Click, Sub() Me.Close()
        sidebar.Controls.Add(btnExit)
    End Sub

    Private Sub CreateMenuWithSubmenu(sidebar As Guna2Panel, title As String, ByRef top As Integer, items As (String, Type)())
        ' الزر الرئيسي
        Dim btn As New Guna2Button()
        btn.Text = title & " ▼"
        btn.Width = sidebar.Width - 20
        btn.Height = 45
        btn.Left = 10
        btn.Top = top
        btn.FillColor = Color.FromArgb(25, 118, 210)
        btn.ForeColor = Color.White
        btn.Font = New Font("Segoe UI", 11, FontStyle.Bold)
        btn.BorderRadius = 5
        btn.TextAlign = HorizontalAlignment.Right
        btn.Cursor = Cursors.Hand

        ' البانل الفرعي
        Dim subPanel As New Guna2Panel()
        subPanel.Width = sidebar.Width
        subPanel.Left = 0
        subPanel.Top = top + btn.Height
        subPanel.Height = 0
        subPanel.FillColor = Color.White
        subPanel.Visible = False
        sidebar.Controls.Add(subPanel)

        Dim thisPanel = subPanel
        AddHandler btn.Click, Sub()
                                  thisPanel.Visible = Not thisPanel.Visible
                                  btn.Text = title & If(thisPanel.Visible, " ▲", " ▼")
                              End Sub

        sidebar.Controls.Add(btn)
        top += btn.Height

        ' الأزرار الفرعية
        For Each item In items
            Dim subBtn As New Guna2Button()
            subBtn.Text = "   " & item.Item1
            subBtn.Width = subPanel.Width - 20
            subBtn.Height = 40
            subBtn.Left = 10
            subBtn.Top = subPanel.Controls.Count * (subBtn.Height + 5)
            subBtn.FillColor = Color.White
            subBtn.ForeColor = Color.FromArgb(25, 118, 210)
            subBtn.Font = New Font("Segoe UI", 10)
            subBtn.TextAlign = HorizontalAlignment.Right
            subBtn.Cursor = Cursors.Hand
            subBtn.BorderRadius = 0
            subBtn.HoverState.FillColor = Color.FromArgb(230, 240, 250)
            subBtn.HoverState.ForeColor = Color.FromArgb(25, 118, 210)
            subBtn.Tag = item.Item2
            AddHandler subBtn.Click, AddressOf SubMenuButton_Click
            subPanel.Controls.Add(subBtn)
        Next

        subPanel.Height = subPanel.Controls.Count * 45
        top += subPanel.Height
    End Sub

    Private Sub SubMenuButton_Click(sender As Object, e As EventArgs)
        Dim btn = CType(sender, Guna2Button)
        Dim formType = TryCast(btn.Tag, Type)

        If formType Is Nothing Then Exit Sub

        ' ✅ لا تفتح نفس النموذج مرتين
        If currentForm IsNot Nothing AndAlso currentForm.GetType() Is formType Then Return

        ' ✅ إغلاق النموذج السابق
        If currentForm IsNot Nothing Then currentForm.Close()

        ' ✅ تمييز الزر الفرعي الحالي
        If currentSubButton IsNot Nothing Then
            currentSubButton.FillColor = Color.White
            currentSubButton.ForeColor = Color.FromArgb(25, 118, 210)
        End If

        currentSubButton = btn
        btn.FillColor = Color.FromArgb(230, 240, 255)
        btn.ForeColor = Color.FromArgb(0, 102, 204)

        ' ✅ فتح النموذج الجديد
        Dim frm = CType(Activator.CreateInstance(formType), Form)
        frm.TopLevel = False
        frm.FormBorderStyle = FormBorderStyle.None
        frm.Dock = DockStyle.Fill
        mainPanel.Controls.Clear()
        mainPanel.Controls.Add(frm)
        frm.Show()
        currentForm = frm
    End Sub

End Class
