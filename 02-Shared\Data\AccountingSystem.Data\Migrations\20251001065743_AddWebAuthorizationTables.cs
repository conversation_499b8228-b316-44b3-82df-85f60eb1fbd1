﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AccountingSystem.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddWebAuthorizationTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_tblGroupFormPermissions_tblGroupsAuth_GroupID",
                table: "tblGroupFormPermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_tblGroupPermissions_tblGroupsAuth_GroupID",
                table: "tblGroupPermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_tblUsers_tblGroupsAuth_GroupID",
                table: "tblUsers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_tblGroupsAuth",
                table: "tblGroupsAuth");

            migrationBuilder.DropColumn(
                name: "ID",
                table: "tblGroupsAuth");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "tblGroupsAuth");

            migrationBuilder.DropColumn(
                name: "ModifiedBy",
                table: "tblGroupsAuth");

            migrationBuilder.AlterColumn<int>(
                name: "VOIDSTTS",
                table: "tblStockMovHeader",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<decimal>(
                name: "TrxVAT",
                table: "tblStockMovHeader",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "TrxTotal",
                table: "tblStockMovHeader",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "TrxNetAmount",
                table: "tblStockMovHeader",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "TrxDiscountValue",
                table: "tblStockMovHeader",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "TrxDiscount",
                table: "tblStockMovHeader",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<DateTime>(
                name: "TrxDate",
                table: "tblStockMovHeader",
                type: "datetime2",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AddColumn<string>(
                name: "PaymentStatus",
                table: "tblStockMovHeader",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReferenceInvoice",
                table: "tblStockMovHeader",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "TrxDate",
                table: "tblStockMovement",
                type: "datetime2",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<DateTime>(
                name: "LoginTime",
                table: "tblSessionLogs",
                type: "datetime2",
                nullable: true,
                defaultValueSql: "GETDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValueSql: "GETDATE()");

            migrationBuilder.AlterColumn<int>(
                name: "GroupID",
                table: "tblGroupsAuth",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "tblConfig",
                type: "datetime2",
                nullable: true,
                defaultValueSql: "GETDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValueSql: "GETDATE()");

            migrationBuilder.AddPrimaryKey(
                name: "PK_tblGroupsAuth",
                table: "tblGroupsAuth",
                column: "GroupID");

            migrationBuilder.CreateTable(
                name: "tblPOSCashier",
                columns: table => new
                {
                    SN = table.Column<int>(type: "int", nullable: false),
                    Store = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CashierAccount = table.Column<long>(type: "bigint", nullable: true),
                    Createdby = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tblPOSCashier", x => x.SN);
                });

            migrationBuilder.CreateTable(
                name: "WebFormMenus",
                columns: table => new
                {
                    MenuID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DisplayName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    DisplayNameEn = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Route = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Icon = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ParentMenuId = table.Column<int>(type: "int", nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    ModuleName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    IsContainer = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CssClass = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WebFormMenus", x => x.MenuID);
                    table.ForeignKey(
                        name: "FK_WebFormMenus_WebFormMenus_ParentMenuId",
                        column: x => x.ParentMenuId,
                        principalTable: "WebFormMenus",
                        principalColumn: "MenuID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "WebQuickActions",
                columns: table => new
                {
                    QuickActionID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DisplayName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    DisplayNameEn = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Route = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Icon = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ColorClass = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true, defaultValue: "primary"),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    Category = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WebQuickActions", x => x.QuickActionID);
                });

            migrationBuilder.CreateTable(
                name: "WebGroupMenuPermissions",
                columns: table => new
                {
                    PermissionID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GroupID = table.Column<int>(type: "int", nullable: false),
                    MenuId = table.Column<int>(type: "int", nullable: false),
                    CanView = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CanAdd = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CanEdit = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CanDelete = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CanPrint = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CustomPermission1 = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CustomPermission2 = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WebGroupMenuPermissions", x => x.PermissionID);
                    table.ForeignKey(
                        name: "FK_WebGroupMenuPermissions_WebFormMenus_MenuId",
                        column: x => x.MenuId,
                        principalTable: "WebFormMenus",
                        principalColumn: "MenuID",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WebGroupMenuPermissions_tblGroupsAuth_GroupID",
                        column: x => x.GroupID,
                        principalTable: "tblGroupsAuth",
                        principalColumn: "GroupID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WebGroupQuickActionPermissions",
                columns: table => new
                {
                    PermissionID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GroupID = table.Column<int>(type: "int", nullable: false),
                    QuickActionId = table.Column<int>(type: "int", nullable: false),
                    IsVisible = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WebGroupQuickActionPermissions", x => x.PermissionID);
                    table.ForeignKey(
                        name: "FK_WebGroupQuickActionPermissions_WebQuickActions_QuickActionId",
                        column: x => x.QuickActionId,
                        principalTable: "WebQuickActions",
                        principalColumn: "QuickActionID",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WebGroupQuickActionPermissions_tblGroupsAuth_GroupID",
                        column: x => x.GroupID,
                        principalTable: "tblGroupsAuth",
                        principalColumn: "GroupID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_WebFormMenus_ModuleName_DisplayOrder",
                table: "WebFormMenus",
                columns: new[] { "ModuleName", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_WebFormMenus_ParentMenuId",
                table: "WebFormMenus",
                column: "ParentMenuId");

            migrationBuilder.CreateIndex(
                name: "IX_WebFormMenus_Route",
                table: "WebFormMenus",
                column: "Route");

            migrationBuilder.CreateIndex(
                name: "IX_WebGroupMenuPermissions_GroupID_MenuId",
                table: "WebGroupMenuPermissions",
                columns: new[] { "GroupID", "MenuId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WebGroupMenuPermissions_MenuId",
                table: "WebGroupMenuPermissions",
                column: "MenuId");

            migrationBuilder.CreateIndex(
                name: "IX_WebGroupQuickActionPermissions_GroupID_QuickActionId",
                table: "WebGroupQuickActionPermissions",
                columns: new[] { "GroupID", "QuickActionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WebGroupQuickActionPermissions_QuickActionId",
                table: "WebGroupQuickActionPermissions",
                column: "QuickActionId");

            migrationBuilder.CreateIndex(
                name: "IX_WebQuickActions_Category_DisplayOrder",
                table: "WebQuickActions",
                columns: new[] { "Category", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_WebQuickActions_Route",
                table: "WebQuickActions",
                column: "Route");

            migrationBuilder.AddForeignKey(
                name: "FK_tblGroupFormPermissions_tblGroupsAuth_GroupID",
                table: "tblGroupFormPermissions",
                column: "GroupID",
                principalTable: "tblGroupsAuth",
                principalColumn: "GroupID",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_tblGroupPermissions_tblGroupsAuth_GroupID",
                table: "tblGroupPermissions",
                column: "GroupID",
                principalTable: "tblGroupsAuth",
                principalColumn: "GroupID",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_tblUsers_tblGroupsAuth_GroupID",
                table: "tblUsers",
                column: "GroupID",
                principalTable: "tblGroupsAuth",
                principalColumn: "GroupID",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_tblGroupFormPermissions_tblGroupsAuth_GroupID",
                table: "tblGroupFormPermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_tblGroupPermissions_tblGroupsAuth_GroupID",
                table: "tblGroupPermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_tblUsers_tblGroupsAuth_GroupID",
                table: "tblUsers");

            migrationBuilder.DropTable(
                name: "tblPOSCashier");

            migrationBuilder.DropTable(
                name: "WebGroupMenuPermissions");

            migrationBuilder.DropTable(
                name: "WebGroupQuickActionPermissions");

            migrationBuilder.DropTable(
                name: "WebFormMenus");

            migrationBuilder.DropTable(
                name: "WebQuickActions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_tblGroupsAuth",
                table: "tblGroupsAuth");

            migrationBuilder.DropColumn(
                name: "PaymentStatus",
                table: "tblStockMovHeader");

            migrationBuilder.DropColumn(
                name: "ReferenceInvoice",
                table: "tblStockMovHeader");

            migrationBuilder.AlterColumn<int>(
                name: "VOIDSTTS",
                table: "tblStockMovHeader",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "TrxVAT",
                table: "tblStockMovHeader",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "TrxTotal",
                table: "tblStockMovHeader",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "TrxNetAmount",
                table: "tblStockMovHeader",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "TrxDiscountValue",
                table: "tblStockMovHeader",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "TrxDiscount",
                table: "tblStockMovHeader",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "TrxDate",
                table: "tblStockMovHeader",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "TrxDate",
                table: "tblStockMovement",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "LoginTime",
                table: "tblSessionLogs",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true,
                oldDefaultValueSql: "GETDATE()");

            migrationBuilder.AlterColumn<int>(
                name: "GroupID",
                table: "tblGroupsAuth",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int")
                .OldAnnotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddColumn<int>(
                name: "ID",
                table: "tblGroupsAuth",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "tblGroupsAuth",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ModifiedBy",
                table: "tblGroupsAuth",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "tblConfig",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETDATE()",
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true,
                oldDefaultValueSql: "GETDATE()");

            migrationBuilder.AddPrimaryKey(
                name: "PK_tblGroupsAuth",
                table: "tblGroupsAuth",
                column: "ID");

            migrationBuilder.AddForeignKey(
                name: "FK_tblGroupFormPermissions_tblGroupsAuth_GroupID",
                table: "tblGroupFormPermissions",
                column: "GroupID",
                principalTable: "tblGroupsAuth",
                principalColumn: "ID",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_tblGroupPermissions_tblGroupsAuth_GroupID",
                table: "tblGroupPermissions",
                column: "GroupID",
                principalTable: "tblGroupsAuth",
                principalColumn: "ID",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_tblUsers_tblGroupsAuth_GroupID",
                table: "tblUsers",
                column: "GroupID",
                principalTable: "tblGroupsAuth",
                principalColumn: "ID",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
