using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using AccountingSystem.Web.Utilities;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class ChangeMyPasswordController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<ChangeMyPasswordController> _logger;

        public ChangeMyPasswordController(IConfiguration configuration, ILogger<ChangeMyPasswordController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// Display the change password form
        /// </summary>
        public IActionResult Index()
        {
            var model = new ChangeMyPasswordViewModel
            {
                Username = User.Identity?.Name ?? ""
            };
            return View(model);
        }

        /// <summary>
        /// Process password change request
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Index(ChangeMyPasswordViewModel model)
        {
            var currentUsername = User.Identity?.Name;
            if (string.IsNullOrEmpty(currentUsername))
            {
                ModelState.AddModelError("", "لم يتم العثور على معلومات المستخدم الحالي");
                return View(model);
            }

            // Check if passwords match (VB.NET behavior)
            if (model.NewPassword != model.ConfirmPassword)
            {
                ModelState.AddModelError("", "عفوا كلمة المرور الجديدة غير متطابقة");
                // Clear new password fields and focus on new password (like VB.NET)
                model.NewPassword = "";
                model.ConfirmPassword = "";
                ViewBag.FocusField = "NewPassword";
                return View(model);
            }

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var connectionString = _configuration.GetConnectionString("AccountingDatabase");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // First, verify the old password
                    var verifyQuery = "SELECT SN FROM tblUsers WHERE Username = @Username";
                    using (var verifyCommand = new SqlCommand(verifyQuery, connection))
                    {
                        verifyCommand.Parameters.AddWithValue("@Username", currentUsername);
                        
                        using (var reader = await verifyCommand.ExecuteReaderAsync())
                        {
                            if (!await reader.ReadAsync())
                            {
                                ModelState.AddModelError("", "لم يتم العثور على المستخدم");
                                return View(model);
                            }

                            var userSN = Convert.ToInt32(reader["SN"]);
                            reader.Close();

                            // Verify old password using compatibility method
                            if (!await VerifyOldPasswordAsync(connection, currentUsername, model.OldPassword))
                            {
                                ModelState.AddModelError("", "كلمة المرور خاطئة");
                                // Focus on old password field (like VB.NET)
                                ViewBag.FocusField = "OldPassword";
                                return View(model);
                            }

                            // Update password with C# hash (new passwords will be C# compatible)
                            var updateQuery = @"
                                UPDATE tblUsers 
                                SET Password = @NewPassword, 
                                    ModifiedBy = @ModifiedBy, 
                                    ModifiedOn = GETDATE() 
                                WHERE SN = @SN";

                            using (var updateCommand = new SqlCommand(updateQuery, connection))
                            {
                                var hashedNewPassword = VBNetHashCompatibility.HashPasswordForVBNet(model.NewPassword);
                                
                                updateCommand.Parameters.AddWithValue("@NewPassword", hashedNewPassword);
                                updateCommand.Parameters.AddWithValue("@ModifiedBy", currentUsername);
                                updateCommand.Parameters.AddWithValue("@SN", userSN);

                                await updateCommand.ExecuteNonQueryAsync();
                            }
                        }
                    }
                }

                _logger.LogInformation("Password changed successfully for user {Username}", currentUsername);
                TempData["Success"] = "تم تغيير كلمة المرور بنجاح";
                
                // Clear the form and focus on old password field (like VB.NET)
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user {Username}", currentUsername);
                ModelState.AddModelError("", "حدث خطأ أثناء تغيير كلمة المرور");
                return View(model);
            }
        }

        /// <summary>
        /// Verify old password using VB.NET compatibility
        /// </summary>
        private async Task<bool> VerifyOldPasswordAsync(SqlConnection connection, string username, string oldPassword)
        {
            try
            {
                // Get stored password hash
                var query = "SELECT Password FROM tblUsers WHERE Username = @Username";
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    var storedHash = await command.ExecuteScalarAsync() as string;
                    
                    if (string.IsNullOrEmpty(storedHash))
                        return false;

                    // Use VB.NET hash compatibility utility for password verification
                    return VBNetHashCompatibility.VerifyVBNetPassword(oldPassword, storedHash);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying old password for user {Username}", username);
                return false;
            }
        }


    }

    /// <summary>
    /// View model for change password form
    /// </summary>
    public class ChangeMyPasswordViewModel
    {
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور القديمة مطلوبة")]
        [Display(Name = "كلمة المرور القديمة")]
        public string OldPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور الجديدة مطلوبة")]
        [StringLength(100, MinimumLength = 4, ErrorMessage = "كلمة المرور يجب أن تكون بين 4 و 100 حرف")]
        [Display(Name = "كلمة المرور الجديدة")]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
        [Compare("NewPassword", ErrorMessage = "كلمة المرور الجديدة غير متطابقة")]
        [Display(Name = "تأكيد كلمة المرور الجديدة")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}
