-- Setup configuration values for thermal receipt
-- This script will create the necessary configuration entries

-- Check if tblConfig table exists and create it if needed
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tblConfig')
BEGIN
    CREATE TABLE tblConfig (
        StoreName NVARCHAR(100) NULL,
        VATRegNo NVARCHAR(50) NULL,
        AddressFooter NVARCHAR(500) NULL,
        CompanyName NVARCHAR(100) NULL,
        Phone NVARCHAR(20) NULL,
        Email NVARCHAR(100) NULL
    );
END

-- Insert default configuration values if they don't exist
IF NOT EXISTS (SELECT 1 FROM tblConfig)
BEGIN
    INSERT INTO tblConfig (StoreName, VATRegNo, AddressFooter, CompanyName, Phone, Email)
    VALUES (
        N'نظام السلطان للمبيعات والمحاسبة',
        N'123456789012345',
        N'الرياض، المملكة العربية السعودية',
        N'نظام السلطان للمبيعات والمحاسبة',
        N'+966123456789',
        N'<EMAIL>'
    );
END
ELSE
BEGIN
    -- Update existing values if they are NULL or empty
    UPDATE tblConfig 
    SET StoreName = CASE WHEN StoreName IS NULL OR StoreName = '' THEN N'نظام السلطان للمبيعات والمحاسبة' ELSE StoreName END,
        VATRegNo = CASE WHEN VATRegNo IS NULL OR VATRegNo = '' THEN N'123456789012345' ELSE VATRegNo END,
        AddressFooter = CASE WHEN AddressFooter IS NULL OR AddressFooter = '' THEN N'الرياض، المملكة العربية السعودية' ELSE AddressFooter END,
        CompanyName = CASE WHEN CompanyName IS NULL OR CompanyName = '' THEN N'نظام السلطان للمبيعات والمحاسبة' ELSE CompanyName END,
        Phone = CASE WHEN Phone IS NULL OR Phone = '' THEN N'+966123456789' ELSE Phone END,
        Email = CASE WHEN Email IS NULL OR Email = '' THEN N'<EMAIL>' ELSE Email END;
END

-- Verify the configuration
SELECT 
    'Configuration Values' as Status,
    StoreName,
    VATRegNo,
    AddressFooter,
    CompanyName,
    Phone,
    Email
FROM tblConfig;
