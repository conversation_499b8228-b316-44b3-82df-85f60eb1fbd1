﻿Public Class frmLoginNew

    Dim CurrentLang As String = "EN"

    Private Sub frmLoginNew_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        'Load saved credentials
        txtUsername.Text = My.Settings.LastUsername
        chkRemember.Checked = My.Settings.RememberMe
        If chkRemember.Checked Then txtPassword.Text = My.Settings.LastPassword

        ApplyLanguage()
    End Sub

    Private Sub ApplyLanguage()
        If CurrentLang = "EN" Then
            Me.RightToLeft = RightToLeft.No
            Me.RightToLeftLayout = False
            lblTitle.Text = "Login"
            lblUsername.Text = "Username"
            lblPassword.Text = "Password"
            chkRemember.Text = "Remember Me"
            btnLogin.Text = "Login"
            btnLangSwitch.Text = "AR"
        Else
            Me.RightToLeft = RightToLeft.Yes
            Me.RightToLeftLayout = True
            lblTitle.Text = "تسجيل الدخول"
            lblUsername.Text = "اسم المستخدم"
            lblPassword.Text = "كلمة المرور"
            chkRemember.Text = "تذكرني"
            btnLogin.Text = "دخول"
            btnLangSwitch.Text = "EN"
        End If
    End Sub

    Private Sub btnLangSwitch_Click(sender As Object, e As EventArgs) Handles btnLangSwitch.Click
        CurrentLang = If(CurrentLang = "EN", "AR", "EN")
        ApplyLanguage()
    End Sub

    Private Sub btnLogin_Click(sender As Object, e As EventArgs) Handles btnLogin.Click
        ' Replace this with real authentication
        If txtUsername.Text = "admin" And txtPassword.Text = "1234" Then
            If chkRemember.Checked Then
                My.Settings.LastUsername = txtUsername.Text
                My.Settings.LastPassword = txtPassword.Text
                My.Settings.RememberMe = True
            Else
                My.Settings.LastUsername = ""
                My.Settings.LastPassword = ""
                My.Settings.RememberMe = False
            End If
            My.Settings.Save()

            ' Login successful
            frmMain.Show()
            Me.Hide()
        Else
            MessageBox.Show("Invalid username or password", "Login Failed", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub


End Class
