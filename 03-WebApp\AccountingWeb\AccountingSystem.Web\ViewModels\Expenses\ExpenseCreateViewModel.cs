using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace AccountingSystem.Web.ViewModels.Expenses
{
    public class ExpenseCreateViewModel
    {
        [Display(Name = "تاريخ الفاتورة")]
        [DataType(DataType.Date)]
        public DateTime InvoiceDate { get; set; } = DateTime.Today;

        [Display(Name = "الصندوق/الكاشير (دائن)")]
        public string? CreditCashier { get; set; }

        [Required]
        [Display(Name = "حساب المصروف (مدين)")]
        public string DebitExpense { get; set; } = string.Empty;

        [Required]
        [Display(Name = "المبلغ")]
        [DataType(DataType.Currency)]
        public decimal InvoiceAmount { get; set; }

        [Display(Name = "ضريبة القيمة المضافة")]
        public decimal TaxAmount { get; set; }

        [Display(Name = "رقم المورد")]
        public long? VendorNo { get; set; }

        [Display(Name = "رقم فاتورة المورد")]
        public string? VendorInvoiceNo { get; set; }

        [Display(Name = "اسم المورد")]
        public string? VendorName { get; set; }

        [Display(Name = "الرقم الضريبي للمورد")]
        public string? VendorVATRN { get; set; }

        [Display(Name = "الموظف المشتري")]
        public int? EmployeeBuyer { get; set; }

        [Display(Name = "الملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "المستودع")]
        public string? Store { get; set; }

        [Display(Name = "المرفق (صورة أو PDF)")]
        public IFormFile? PhotoFile { get; set; }

        public bool IgnoreDuplicate { get; set; } = false;

        // Dropdowns
        public List<(string code, string name)> CashierAccounts { get; set; } = new();
        public List<(string code, string name)> ExpenseAccounts { get; set; } = new();
        public List<(long id, string name, string? vat)> Vendors { get; set; } = new();
        public List<(int id, string name)> Employees { get; set; } = new();
        public List<string> Warehouses { get; set; } = new();

        // Result/Warnings
        public string? DuplicateWarning { get; set; }
        public string? ResultMessage { get; set; }
        public bool? SaveSuccess { get; set; }
    }
}

