﻿Imports System.Data.SqlClient
Public Class frmItemsTree

    Private Sub GetParentNode(accID As String)
        'Dim node As TreeNode = TreeView1.Nodes.Find(accID, True)

        'If node Is Nothing Then
        '    Return Nothing
        'Else
        '    While node.Parent Is Not Nothing
        '        node = node.Parent
        '    End While
        '    Return node
        'End If
    End Sub
    Private Sub LoadTreeView()
        'Dim sql As String = "SELECT AccID, AccName, ParentAcc FROM Accounts ORDER BY AccID"
        ' Dim cmd As New SqlCommand(sql, Con)
        ' Dim dr As SqlDataReader

        ' Try
        '     Con.Open()
        '     cmd.ExecuteNonQuery()
        '     dr = cmd.ExecuteReader()

        '     While dr.Read()
        '         Dim node As New TreeNode(dr.GetString(1))
        '         node.Tag = dr.GetString(0)
        '         node.Parent = GetParentNode(dr.GetString(2))
        '         TreeView1.Nodes.Add(node)
        '     End While
        ' Finally
        '     dr.<PERSON>()
        '     cmd.Dispose()
        '     Con.Close()
        ' End Try
    End Sub

    Private Sub TreeView1_AfterSelect(sender As Object, e As TreeViewEventArgs)
        Dim node As TreeNode = e.Node
        Dim accID As String = node.Tag.ToString()
        Dim sql As String = "SELECT AccName, ParentAcc, Level FROM Accounts WHERE AccID = @AccID"
        Dim cmd As New SqlCommand(sql, Con)

        Try
            Con.Open()

            cmd.Parameters.AddWithValue("@AccID", accID)
            'Dim dr As SqlDatadim reader As SqlDataReader cmd.ExecuteReader()
            Dim reader As SqlDataReader = cmd.ExecuteReader
            While reader.Read()
                txtAccName.Text = reader.GetString(0)
                txtParentAcc.Text = reader.GetString(1)
                txtLevel.Text = reader.GetString(2)
            End While
        Finally

            cmd.Dispose()
            Con.Close()

        End Try
    End Sub
    Private Sub btnNew_Click(sender As Object, e As EventArgs)
        Dim parentAccID As String = txtParentAcc.Text
        Dim sql As String = "SELECT MAX(Level) FROM Accounts WHERE ParentAcc = @ParentAccID"
        Dim cmd As New SqlCommand(sql, Con)

        Try
            Con.Open()
            cmd.Parameters.AddWithValue("@ParentAccID", parentAccID)
            Dim level As Integer = cmd.ExecuteScalar() + 1
            txtLevel.Text = level.ToString()
            txtAccName.Text = ""
            txtAccName.Focus()
        Finally
            cmd.Dispose()
            Con.Close()
        End Try
    End Sub
    Private Sub btnupdate_Click(sender As Object, e As EventArgs)
        Dim accID As String = txtAccID.Text
        Dim accName As String = txtAccName.Text
        Dim parentAcc As String = txtParentAcc.Text
        Dim level As Integer = Convert.ToInt32(txtLevel.Text)

        If accID = "" Then
            Dim sql As String = "INSERT INTO Accounts (AccName, ParentAcc, Level) VALUES (@AccName, @ParentAcc, @Level)"
            Dim cmd As New SqlCommand(sql, Con)

            Try
                Con.Open()
                cmd.Parameters.AddWithValue("@AccName", accName)
                cmd.Parameters.AddWithValue("@ParentAcc", parentAcc)
                cmd.Parameters.AddWithValue("@Level", level)
                cmd.ExecuteNonQuery()
                LoadTreeView()
                ClearFields()
            Finally
                cmd.Dispose()
                Con.Close()
            End Try
        Else
            Dim sql As String = "UPDATE Accounts SET AccName = @AccName, ParentAcc = @ParentAcc, Level = @Level WHERE AccID = @AccID"
            Dim cmd As New SqlCommand(sql, Con)

            Try
                Con.Open()
                cmd.Parameters.AddWithValue("@AccName", accName)
                cmd.Parameters.AddWithValue("@ParentAcc", parentAcc)
                cmd.Parameters.AddWithValue("@Level", level)
                cmd.Parameters.AddWithValue("@AccID", accID)
                cmd.ExecuteNonQuery()
                LoadTreeView()
            Finally
                cmd.Dispose()
                Con.Close()
            End Try
        End If
    End Sub
    Private Sub btnDelete_Click(sender As Object, e As EventArgs)
        Dim accID As String = txtAccID.Text
        Dim sql As String = "DELETE FROM Accounts WHERE AccID = @AccID"
        Dim cmd As New SqlCommand(sql, Con)

        Try
            Con.Open()
            cmd.Parameters.AddWithValue("@AccID", accID)
            cmd.ExecuteNonQuery()
            LoadTreeView()
            ClearFields()
        Finally
            cmd.Dispose()
            Con.Close()
        End Try
    End Sub
    Private Sub ClearFields()
        txtAccID.Text = ""
        txtAccName.Text = ""
        txtParentAcc.Text = ""
        txtLevel.Text = ""
    End Sub


    Private Sub TreeView1_AfterSelect_1(sender As Object, e As TreeViewEventArgs) Handles TreeView1.AfterSelect

    End Sub
   

    Private Sub frmItemsTree_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        Dim sql As String = "SELECT RootID, RootName FROM tblRoots WHERE ParentID = 1141"
        Dim cmd As New SqlCommand(sql, Con)
        Dim dr As SqlDataReader

        Try
            Con.Open()
            cmd.ExecuteNonQuery()
            dr = cmd.ExecuteReader()

            While dr.Read()
                ComboBox1.Items.Add(dr.GetString(1) + "-" + dr.Item(0).ToString)
            End While
        Finally
            dr.Close()
            cmd.Dispose()
            Con.Close()
        End Try

        ComboBox1.AutoCompleteMode = AutoCompleteMode.SuggestAppend
        ComboBox1.AutoCompleteSource = AutoCompleteSource.ListItems


    End Sub

    Private Sub ComboBox1_KeyUp(sender As Object, e As KeyEventArgs) Handles ComboBox1.KeyUp
        Dim searchText As String = ComboBox1.Text.Trim()

        If searchText.Length > 0 Then
            ComboBox1.Items.Clear()

            Dim sql As String = "SELECT RootID, RootName FROM tblRoots WHERE ParentID = 1141 AND (RootName LIKE '%" & searchText & "%' OR RootID LIKE '%" & searchText & "%')"
            Dim cmd As New SqlCommand(sql, Con)
            Dim dr As SqlDataReader

            Try
                Con.Open()
                cmd.ExecuteNonQuery()
                dr = cmd.ExecuteReader()

                While dr.Read()
                    ComboBox1.Items.Add(dr.GetString(1) + "-" + dr.Item(0).ToString)
                End While
            Finally
                dr.Close()
                cmd.Dispose()
                Con.Close()
            End Try
        End If
    End Sub

    Private Sub ComboBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBox1.SelectedIndexChanged

    End Sub

    Private Sub ComboBox1_SelectionChangeCommitted(sender As Object, e As EventArgs) Handles ComboBox1.SelectionChangeCommitted
        Dim searchText As String = ComboBox1.Text.Trim()

        If searchText.Length > 0 Then
            ComboBox1.Items.Clear()

            Dim sql As String = "SELECT RootID, RootName FROM tblRoots WHERE ParentID = 1141 AND (RootName LIKE '%" & searchText & "%' OR RootID LIKE '%" & searchText & "%')"
            Dim cmd As New SqlCommand(sql, Con)
            Dim dr As SqlDataReader

            Try
                Con.Open()
                cmd.ExecuteNonQuery()
                dr = cmd.ExecuteReader()

                While dr.Read()
                    ComboBox1.Items.Add(dr.GetString(1) + "-" + dr.GetString(0).ToString)
                End While
            Finally
                dr.Close()
                cmd.Dispose()
                Con.Close()
            End Try
        End If
    End Sub
End Class