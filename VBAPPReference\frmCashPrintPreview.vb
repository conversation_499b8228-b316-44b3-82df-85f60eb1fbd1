﻿Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports CrystalDecisions.Windows.Forms
Imports System.Data.SqlClient

Public Class frmCashPrintPreview

    Private Sub frmPrintPreview_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        'CheckPrinter()
        'Try
        '    If PrtRPTCheck = "True" Then
        '        Shell(String.Format("rundll32 printui.dll,PrintUIEntry /y /n ""{0}""", PrinterReports))
        '    End If
        'Catch ex As Exception

        'End Try
    End Sub
    Private Sub frmPrintPreview_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.Size = New Size(frmRPTWidth, frmRPTHeight)

        If PrintType = "CashPay" Then
            Dim RPT As New rptCashPay
            UpdateCRDataSource(RPT)
            RPT.Refresh()
            RPT.SetParameterValue(0, JESN)
            CrystalReportViewer2.ReportSource = RPT

        ElseIf PrintType = "CashRec" Then
            Dim RPT As New rptCashRec
            UpdateCRDataSource(RPT)
            RPT.Refresh()
            RPT.SetParameterValue(0, JESN)
            CrystalReportViewer2.ReportSource = RPT

        ElseIf PrintType = "GLTrx" Then
            Dim RPT As New rptGLTrx
            UpdateCRDataSource(RPT)
            RPT.Refresh()
            RPT.SetParameterValue(0, JESN)
            CrystalReportViewer2.ReportSource = RPT

        End If

    End Sub


End Class