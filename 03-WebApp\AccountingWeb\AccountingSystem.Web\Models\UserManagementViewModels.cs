using System.ComponentModel.DataAnnotations;

namespace AccountingSystem.Web.Models
{
    /// <summary>
    /// View model for displaying user information in lists and details
    /// </summary>
    public class UserViewModel
    {
        public int SN { get; set; }
        
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; } = string.Empty;
        
        [Display(Name = "المجموعة")]
        public int GroupID { get; set; }
        
        [Display(Name = "اسم المجموعة")]
        public string GroupName { get; set; } = string.Empty;
        
        [Display(Name = "المتجر الافتراضي")]
        public string? DefaultStore { get; set; }
        
        [Display(Name = "يمكن تغيير المتجر")]
        public bool StoreChange { get; set; }
        
        [Display(Name = "العميل الافتراضي")]
        public string? DefaultCustomer { get; set; }
        
        [Display(Name = "يمكن تغيير العميل")]
        public bool CustomerChange { get; set; }
        
        [Display(Name = "الكاشير الافتراضي")]
        public string? DefaultCashier { get; set; }
        
        [Display(Name = "يمكن تغيير الكاشير")]
        public bool CashierChange { get; set; }
        
        [Display(Name = "يمكن تغيير سعر الفاتورة")]
        public bool ChangeInvoicePrice { get; set; }
        
        [Display(Name = "أقصى نسبة خصم")]
        public decimal? MaxDiscountPercent { get; set; }
        
        [Display(Name = "تاريخ الإنشاء")]
        public DateTime? CreatedOn { get; set; }
        
        [Display(Name = "أنشئ بواسطة")]
        public string? CreatedBy { get; set; }
        
        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedOn { get; set; }
        
        [Display(Name = "عدل بواسطة")]
        public string? ModifiedBy { get; set; }
    }

    /// <summary>
    /// View model for creating new users
    /// </summary>
    public class CreateUserViewModel
    {
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [Display(Name = "اسم المستخدم")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
        public string Username { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [Display(Name = "كلمة المرور")]
        [StringLength(100, MinimumLength = 4, ErrorMessage = "كلمة المرور يجب أن تكون بين 4 و 100 حرف")]
        public string Password { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
        [Display(Name = "تأكيد كلمة المرور")]
        [Compare("Password", ErrorMessage = "كلمة المرور غير متطابقة")]
        public string ConfirmPassword { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "المجموعة مطلوبة")]
        [Display(Name = "المجموعة")]
        public int GroupID { get; set; }
        
        [Display(Name = "المتجر الافتراضي")]
        public string? DefaultStore { get; set; }
        
        [Display(Name = "يمكن تغيير المتجر")]
        public bool StoreChange { get; set; }
        
        [Display(Name = "العميل الافتراضي")]
        public string? DefaultCustomer { get; set; }
        
        [Display(Name = "يمكن تغيير العميل")]
        public bool CustomerChange { get; set; }
        
        [Display(Name = "الكاشير الافتراضي")]
        public string? DefaultCashier { get; set; }
        
        [Display(Name = "يمكن تغيير الكاشير")]
        public bool CashierChange { get; set; }
        
        [Display(Name = "يمكن تغيير سعر الفاتورة")]
        public bool ChangeInvoicePrice { get; set; }
        
        [Display(Name = "أقصى نسبة خصم")]
        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal? MaxDiscountPercent { get; set; }
    }

    /// <summary>
    /// View model for editing existing users
    /// </summary>
    public class EditUserViewModel
    {
        public int SN { get; set; }
        
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "المجموعة مطلوبة")]
        [Display(Name = "المجموعة")]
        public int GroupID { get; set; }
        
        [Display(Name = "المتجر الافتراضي")]
        public string? DefaultStore { get; set; }
        
        [Display(Name = "يمكن تغيير المتجر")]
        public bool StoreChange { get; set; }
        
        [Display(Name = "العميل الافتراضي")]
        public string? DefaultCustomer { get; set; }
        
        [Display(Name = "يمكن تغيير العميل")]
        public bool CustomerChange { get; set; }
        
        [Display(Name = "الكاشير الافتراضي")]
        public string? DefaultCashier { get; set; }
        
        [Display(Name = "يمكن تغيير الكاشير")]
        public bool CashierChange { get; set; }
        
        [Display(Name = "يمكن تغيير سعر الفاتورة")]
        public bool ChangeInvoicePrice { get; set; }
        
        [Display(Name = "أقصى نسبة خصم")]
        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal? MaxDiscountPercent { get; set; }
    }

    /// <summary>
    /// View model for changing user passwords
    /// </summary>
    public class ChangePasswordViewModel
    {
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "كلمة المرور الجديدة مطلوبة")]
        [Display(Name = "كلمة المرور الجديدة")]
        [StringLength(100, MinimumLength = 4, ErrorMessage = "كلمة المرور يجب أن تكون بين 4 و 100 حرف")]
        public string NewPassword { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
        [Display(Name = "تأكيد كلمة المرور")]
        [Compare("NewPassword", ErrorMessage = "كلمة المرور غير متطابقة")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// View model for user group/role information
    /// </summary>
    public class UserGroupViewModel
    {
        public int GroupID { get; set; }
        public string GroupName { get; set; } = string.Empty;
    }

    /// <summary>
    /// View model for dropdown options (stores, customers, cashiers)
    /// </summary>
    public class DropdownOptionViewModel
    {
        public string Value { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
    }

    /// <summary>
    /// Main view model for the user management page
    /// </summary>
    public class UserManagementViewModel
    {
        public List<UserViewModel> Users { get; set; } = new();
        public List<UserGroupViewModel> Groups { get; set; } = new();
        public List<DropdownOptionViewModel> Stores { get; set; } = new();
        public List<DropdownOptionViewModel> Customers { get; set; } = new();
        public List<DropdownOptionViewModel> Cashiers { get; set; } = new();
        
        // For the forms
        public CreateUserViewModel CreateUser { get; set; } = new();
        public EditUserViewModel EditUser { get; set; } = new();
        public ChangePasswordViewModel ChangePassword { get; set; } = new();
    }
}
