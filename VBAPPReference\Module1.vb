﻿Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Runtime.Remoting.Metadata.W3cXsd2001
Imports System.Web.Services.Description
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports CrystalDecisions.Web
Imports CrystalDecisions.Windows.Forms
Imports Microsoft.Data
Imports System.Globalization
Imports System.Threading
Imports System.Windows.Forms

Module Module1
    'Public Con As New SQLConnection("dsn=DBCON;uid=sa;pwd=******;MultipleActiveResultSets=True")
    Public UserName As String = ""
    'Public reader As SqlDataReader
    'Public dr As SqlDataReader
    Public CustEdit As Int64 = 0
    Public CustNo As Integer = 0
    Public VendEdit As Int64 = 0
    Public VendNo As String = ""
    Public InvNo As Int64 = 0
    Public InvNoForReceipt As Int64 = 0
    Public InvNoForPayment As Int64 = 0
    Public InvNoForPrint As Int64 = 0
    Public isTestDeploy As Int64 = 0
    Public SoldTY As Int64 = 0
    Public DeviceSN As String = ""
    Public Comp As String = ""
    Public NewSerialItemNo As Int64 = 0
    'Public ItemNo As Int64 = 0
    Public ItemNo As Int64 = 0
    Public UserType As String = ""
    Public Store As String = ""
    Public StoreForReport As String = ""
    Public ArabicAmount As String = 0
    Public SNInStore As Int64 = 0
    Public IsDuplicateSN As Int64 = 0
    Public DateFrom As Date
    Public DateTo As Date
    Public DateStrFrom As String
    Public DateStrTo As String
    Public Cashier As String = ""
    Public TrxType As String = ""
    Public IsLimitedUsers As Int64 = 0
    Public LineSN As Int64 = 0
    Public UpdateSN As Int64 = 0
    Public frmRPTHeight As Int64 = 0
    Public frmRPTWidth As Int64 = 0
    Public frmHeight As Int64 = 0
    Public frmWidth As Int64 = 0
    Public StoreName As String = ""
    Public VATReg As String = ""
    Public DBBackupLocation As String = ""
    Public PrintType As String = ""
    Public UnitCost As Decimal = 0
    Public UnitPrice As Decimal = 0
    Public GrossProfit As Decimal = 0
    Public SNEnables As String = ""
    Public NegativeAllow As String = ""
    Public ItemDescription As String = ""
    Public ItemUnit As String = ""
    Public ItemDescription2 As String = ""
    Public Price As Decimal = 0
    Public PriceForReceipt As Decimal = 0
    Public PriceForPayment As Decimal = 0
    Public EnteredItemBySN As Integer = 0

    Public InvoiceNo As String = ""
    Public InvoiceType As String = ""
    Public SNRequired As Int64 = 0
    Public SNEntered As Int64 = 0
    Public SNMissed As String = ""

    Public IsNewCustomer As String = ""

    Public ItemNoForReport As Int64 = 0
    Public ItemsCategory As Int64 = 0

    Public SearchItemFor As String = ""
    Public SearchForReference As String = ""

    Public CustomerSearchForm As String = ""

    Public AccountSearchForm As String = ""

    Public VendorSearchForm As String = ""

    Public RefernceInvoiceType As String = ""
    Public ReferenceInvoiceNo As Integer = 0
    Public ItemSN As Int64 = 0
    Public RowIn As Integer = 0
    Public ColIn As Integer = 0

    Public LinkStoreCash As Int64 = 0
    Public UserPurchasePaymentType As Int64 = 0
    Public UserSalesPaymentType As Int64 = 0

    Public ItemClassLevels As Int64 = 0
    Public ItemClassL1 As String = ""
    Public ItemClassL2 As String = ""
    Public ItemClassL3 As String = ""
    Public ItemClassL4 As String = ""
    Public ItemClassL5 As String = ""

    Public PrinterLabel As String = ""
    Public PrinterInvoice As String = ""
    Public PrinterReports As String = ""
    Public AllowUpdatePurchasing As String = ""
    Public AllowUpdateSales As String = ""
    Public AllowDeletePurchasing As String = ""
    Public AllowDeleteSales As String = ""
    Public PrtLBLCheck As String = ""
    Public PrtINVCheck As String = ""
    Public PrtRPTCheck As String = ""
    Public TrxDebit As String = ""
    Public TrxCredit As String = ""
    Public TrxVAT As String = ""
    Public JESN As Int64 = 0
    Public AccInq As String = ""
    Public AccNo As Integer = 0
    Public AccDescription As String = ""
    Public IsAccInq As Int64 = 0
    Public EmpNo As Int64 = 0
    Public RegionSN As Int64 = 0
    Public ParentID As Int64 = 0
    Public ParentID2 As Int64 = 0
    Public ParentID3 As Int64 = 0
    Public RootLevel As Int64 = 0
    Public SegmentCode As String = ""
    Public CashierSN As Int64 = 0
    Public LevelSN As Int64 = 0
    Public CashPayPrint As String = ""
    Public CashRecPrint As String = ""

    Public AccountNo As String = ""
    Public AccountParent As String = ""
    Public AccountLevel As Int64 = 0
    Public IsUpdate As Boolean
    Public TrxNetAmount As Decimal = 0

    Public ConServerName As String = ""
    Public ConDatabaseName As String = ""
    Public ConUserName As String = ""
    Public ConPassword As String = ""
    Public ConModel As String = ""

    Public ChangeInvoicePrice As Integer = 0


    Public MaxDiscountPercent As Decimal = 0
    Public grossAmountBeforeDiscount As Decimal = 0

    'Public Sub ForceGregorianCalendar(ByRef dtp As DateTimePicker)
    '    ' Create and configure the culture
    '    Dim customCulture As New CultureInfo("ar-SA")
    '    Dim dtFormat As DateTimeFormatInfo = customCulture.DateTimeFormat.Clone()
    '    dtFormat.Calendar = New GregorianCalendar()
    '    customCulture.DateTimeFormat = dtFormat

    '    ' Set thread culture (optional, if needed globally)
    '    Thread.CurrentThread.CurrentCulture = customCulture
    '    Thread.CurrentThread.CurrentUICulture = customCulture

    '    ' Set display format
    '    dtp.Format = DateTimePickerFormat.Custom
    '    dtp.CustomFormat = "dd/MM/yyyy" ' or "yyyy-MM-dd" as preferred
    '    'dtp.CustomFormat = "yyyy-MM-dd" ' or "yyyy-MM-dd" as preferred
    'End Sub

    Public Sub ForceGregorianCalendar(ByRef dtp As DateTimePicker)
        ' Use a stable culture like en-US instead of ar-SA
        Dim customCulture As New CultureInfo("ar-SA")
        Dim dtFormat As DateTimeFormatInfo = customCulture.DateTimeFormat.Clone()
        dtFormat.Calendar = New GregorianCalendar()
        customCulture.DateTimeFormat = dtFormat

        ' Set the display format
        dtp.Format = DateTimePickerFormat.Custom
        dtp.CustomFormat = "dd/MM/yyyy" ' Or your preferred format

        ' Optional: apply RTL fix if layout is affected
        dtp.RightToLeft = RightToLeft.No
        dtp.RightToLeftLayout = False
    End Sub
    Public Sub ForceGregorianForAllPickers(container As Control)
        For Each ctrl As Control In container.Controls
            If TypeOf ctrl Is DateTimePicker Then
                ForceGregorianCalendar(CType(ctrl, DateTimePicker))
            ElseIf ctrl.HasChildren Then
                ForceGregorianForAllPickers(ctrl)
            End If
        Next
    End Sub






    Public Function ExecuteScalar(cmd As SqlCommand) As Object
        If Con.State <> ConnectionState.Open Then Con.Open()
        Dim result As Object = cmd.ExecuteScalar()
        If Con.State = ConnectionState.Open Then Con.Close()
        Return result
    End Function

    Sub Auth()
        Dim authcmd As SqlCommand = New SqlCommand("select GroupName,GroupID from UsersGroup where Username like '" & UserName & "'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()                          ' To Apply Authentication For User Is Login
        End If

        Dim reader As SqlDataReader = authcmd.ExecuteReader
        reader.Read()
        If reader.HasRows Then
            UserType = reader.Item(1).ToString.Trim
            If UserType = "Admin" Then
                '    frmMain.btnReports.Visible = True
                '    frmMain.btnSettings.Visible = True
                '    frmMain.btnCards.Visible = True
                '    frmMain.btnCashTrx.Visible = True
                '    frmMain.btnWholeInvIn.Visible = True
                '    frmMain.btnSearchSN.Visible = True
                '    'frmMain.btnReportStock.Visible = True
                '    'frmMain.btnCashDailyStatement.Visible = False
                '    'frmMain.btnVendorStatment.Visible = False
                '    frmMain.btnItemsCard.Visible = False
                '    frmMain.btnReports1.Visible = True
                '    'frmMain.btnDeleteCash.Visible = False
                '    'frmMain.btnCreditStatement.Visible = False
                '    'frmMain.btnPartnerBalance.Visible = False

            ElseIf UserType = "LimitedEntry" Then
                '    frmMain.btnTrxOutNonSales.Visible = False
                '    frmMain.btnSettings.Visible = False
                '    frmMain.btnWholeInvIn.Visible = True
                '    frmMain.btnCashTrx.Visible = True
                '    frmMain.btnSearch.Visible = True
                '    frmMain.btnCards.Visible = False
                '    frmMain.btnReports.Visible = False
                '    frmMain.btnSearchSN.Visible = False
                '    frmMain.btnReports1.Visible = True
                '    'frmMain.btnReportStock.Visible = True
                '    'frmMain.btnCashDailyStatement.Visible = True
                '    'frmMain.btnVendorStatment.Visible = True
                '    frmMain.btnItemsCard.Visible = True
                '    frmMain.btnDeleteCash.Visible = True
                '    frmMain.btnCreditStatement.Visible = True
                '    frmMain.btnPartnerBalance.Visible = True

            ElseIf UserType = "Limited" Then
                '    frmMain.btnTrxOutNonSales.Visible = False
                '    frmMain.btnSettings.Visible = False
                '    frmMain.btnWholeInvIn.Visible = False
                '    frmMain.btnCashTrx.Visible = False
                '    frmMain.btnSearch.Visible = False
                '    frmMain.btnCards.Visible = False
                '    frmMain.btnReports.Visible = False

                '    frmMain.btnReports1.Visible = True
                '    frmMain.btnCreditStatement.Visible = True
                '    frmMain.btnPartnerBalance.Visible = False

                '    frmMain.btnSearchSN.Visible = True
                '    frmMain.btnReportStock.Visible = True
                '    frmMain.btnCashDailyStatement.Visible = False
                '    frmMain.btnVendorStatment.Visible = False
                '    frmMain.btnItemsCard.Visible = False
                '    frmMain.btnDeleteCash.Visible = False
                '    frmMain.btnCreditStatement.Visible = False
                '    frmMain.btnPartnerBalance.Visible = False

            End If
        Else
            Application.Exit()
        End If
        reader.Close()
    End Sub

    Function Str2Int(ByVal InStrng As Object) As String
        Dim StrLn As Integer
        Dim Cntr As Integer
        Dim NewStr As String
        Str2Int = ""
        StrLn = Len(InStrng)
        If StrLn = 0 Then Exit Function
        NewStr = ""
        For Cntr = 1 To StrLn
            Select Case Mid(InStrng, Cntr, 1)
                Case "0" To "z"
                    NewStr = NewStr & Asc(Mid(InStrng, Cntr, 1))
            End Select
        Next Cntr
        Str2Int = NewStr
    End Function
    'اﻟﺘﺸﻮﯾﺶ داﻟﺔ        
    Public Function Obfuscate(ByVal origText As String) As String
        Dim textBytes As Byte() = System.Text.Encoding.Unicode.GetBytes(origText)
        For counter As Integer = 0 To textBytes.Length - 1
            If (textBytes(counter) > 31) And (textBytes(counter) < 127) Then
                textBytes(counter) += CByte(counter Mod 31 + 1)
                If (textBytes(counter) > 126) Then textBytes(counter) -= CByte(95)
            End If
        Next counter
        Return System.Text.Encoding.Unicode.GetChars(textBytes)
    End Function

    Sub OpenConnection()
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
    End Sub
    Public Function GetCashierRootID(con As SqlConnection) As Long
        ' Placeholder: you should get the cashier account RootID from user or POS config
        Dim cmd As New SqlCommand("SELECT TOP 1 RootID FROM tblRoots WHERE ParentID = (SELECT AccountNo FROM tblGLConfig WHERE EntryReferenceModule = 'نقدية')", con)
        Dim result = cmd.ExecuteScalar()
        If result IsNot Nothing Then Return Convert.ToInt64(result)
        Return 0
    End Function

    Sub CloseConnection()
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Public Sub UpdateCRDataSource(UpdateRPT As CrystalDecisions.CrystalReports.Engine.ReportDocument)
        Dim ConnectionElement As String = My.Settings.database & "-" & My.Settings.server & "-" & My.Settings.id & "-" & My.Settings.passowrd & "-" & My.Settings.model

        Dim CrTables As Tables
        Dim crtableLogoninfo As New TableLogOnInfo
        Dim crConnectionInfo As New ConnectionInfo
        Try
            crConnectionInfo.ServerName = My.Settings.server
            crConnectionInfo.DatabaseName = My.Settings.database
            'If My.Settings.model = "SQL" Then
            crConnectionInfo.Password = My.Settings.passowrd
                crConnectionInfo.UserID = My.Settings.id
                UpdateRPT.SetDatabaseLogon(My.Settings.id, My.Settings.passowrd)
            ' MsgBox(ConStr)
            'MsgBox("SQL Mode " & ConnectionElement)
            'Else
            'crConnectionInfo.Password = ""
            '    crConnectionInfo.UserID = ""
            '    UpdateRPT.SetDatabaseLogon("", "", My.Settings.server, My.Settings.database, True)
            '    MsgBox("Windows Mode " & ConnectionElement)
            'End If

            CrTables = UpdateRPT.Database.Tables
            For Each CrTable In CrTables
                crtableLogoninfo = CrTable.LogOnInfo
                crtableLogoninfo.ConnectionInfo = crConnectionInfo
                CrTable.ApplyLogOnInfo(crtableLogoninfo)
            Next
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Public Function GetDefaultPaymentMethodID() As Integer
        Dim defaultID As Integer = 0

        Try
            Using con As New SqlConnection(ConStr)
                Dim cmd As New SqlCommand("SELECT TOP 1 Pay_mthd FROM tblPayMethod", con)
                con.Open()
                Dim result = cmd.ExecuteScalar()
                If result IsNot DBNull.Value AndAlso result IsNot Nothing Then
                    defaultID = Convert.ToInt32(result)
                End If
            End Using
        Catch ex As Exception
            MessageBox.Show("Error loading default payment method: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return defaultID
    End Function



    Public Function CheckStockAvailabilityByUnit(itemNo As Long, store As String, requiredQty As Decimal, inputUofM As String) As Integer
        Dim isAvailable As Integer = 0 ' Default: Not Available
        Dim baseUofM As String = "" ' To store the base unit name
        Dim convertedQty As Decimal = requiredQty ' Default to original quantity

        Try
            ' Step 1: Convert required quantity to base unit (this now ensures reader closes)
            convertedQty = ConvertToBaseUnit(itemNo, requiredQty, inputUofM, baseUofM)

            ' Step 2: Check stock availability using the base unit quantity
            Dim query As String = "SELECT dbo.CheckStockAvailability(@ItemNo, @Store, @RequiredQty)"

            Using conn As New SqlConnection(ConStr)
                conn.Open()

                Using cmd As New SqlCommand(query, conn)
                    ' Add parameters
                    cmd.Parameters.Add("@ItemNo", SqlDbType.BigInt).Value = itemNo
                    cmd.Parameters.Add("@Store", SqlDbType.NVarChar, 255).Value = store
                    cmd.Parameters.Add("@RequiredQty", SqlDbType.Int).Value = Convert.ToInt32(convertedQty) ' Use converted quantity

                    ' Execute function & get result
                    Dim result As Object = cmd.ExecuteScalar()

                    ' Convert result to Integer (1=True, 0=False)
                    If result IsNot DBNull.Value AndAlso result IsNot Nothing Then
                        isAvailable = Convert.ToInt32(result)
                    End If
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("Error checking stock: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return isAvailable ' Returns 1 if available, 0 if not available
    End Function


    Public Function ConvertToBaseUnit(itemNo As Long, inputQty As Decimal, inputUofM As String, ByRef baseUofM As String) As Decimal
        Dim convertedQty As Decimal = inputQty
        Dim query As String = "SELECT UofM, AUofM, AUofMX, AUofM2, AUofMX2, AUofM3, AUofMX3 
                           FROM tblItems WHERE ItemNo = @ItemNo"

        Try
            Using conn As New SqlConnection(ConStr)
                conn.Open() ' Open new connection
                Using cmd As New SqlCommand(query, conn)
                    cmd.Parameters.AddWithValue("@ItemNo", itemNo)

                    ' Execute query
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then
                            ' Get base unit
                            baseUofM = reader("UofM").ToString()

                            ' If input unit is already the base unit, return the same quantity
                            If inputUofM = baseUofM Then
                                Return inputQty
                            End If

                            ' Check and apply conversion factors
                            If Not IsDBNull(reader("AUofM")) AndAlso inputUofM = reader("AUofM").ToString() Then
                                convertedQty = inputQty * If(IsDBNull(reader("AUofMX")), 1, Convert.ToDecimal(reader("AUofMX")))
                            ElseIf Not IsDBNull(reader("AUofM2")) AndAlso inputUofM = reader("AUofM2").ToString() Then
                                convertedQty = inputQty * If(IsDBNull(reader("AUofMX2")), 1, Convert.ToDecimal(reader("AUofMX2")))
                            ElseIf Not IsDBNull(reader("AUofM3")) AndAlso inputUofM = reader("AUofM3").ToString() Then
                                convertedQty = inputQty * If(IsDBNull(reader("AUofMX3")), 1, Convert.ToDecimal(reader("AUofMX3")))
                            End If
                        End If
                    End Using ' Close reader
                End Using
            End Using ' Close connection
        Catch ex As Exception
            MessageBox.Show("Error converting unit: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return convertedQty ' Return the converted quantity
    End Function

    Public Function GetConversionFactorToBaseUofM(itemNo As Long, inputUofM As String) As Decimal
        Dim conversionFactor As Decimal = 1D ' Default to 1 if no conversion found
        Dim query As String = "SELECT Coversion_to_Base FROM UnitsPricesView WHERE ItemNo = @ItemNo AND Sales_Unit = @Sales_Unit"

        Try
            Using conn As New SqlConnection(ConStr)
                conn.Open()
                Using cmd As New SqlCommand(query, conn)
                    cmd.Parameters.AddWithValue("@ItemNo", itemNo)
                    cmd.Parameters.AddWithValue("@Sales_Unit", inputUofM)

                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then

                            conversionFactor = If(IsDBNull(reader("Coversion_to_Base")), 1D, Convert.ToDecimal(reader("Coversion_to_Base")))
                        End If
                    End Using
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("Error retrieving conversion factor: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return conversionFactor
    End Function



    Public Function ConvertToArabic(ByVal Str As String) As String
        Dim IntStr As String = ""
        Dim Frac As String = ""
        Dim Result As String = ""
        Dim L As Integer = Str.Length ' To Get The Length Of The Original Text 
        Dim M As Integer = Str.IndexOf(".") ' To Get The location Of Decimal Sign
        If M > 0 Then
            IntStr = Str.Remove(M, L - M)  'To Get Number Without Fractions
            Frac = Str.Remove(0, M + 1)    'To Get Number With Fractions
        ElseIf M = 0 Then : Frac = Str.Remove(0, M + 1)
        ElseIf M < 0 Then : IntStr = Str : End If
        '===============================
        If IntStr <> Nothing Then IntStr = Get_IntStr_A(IntStr)
        'my addation Is here 
        '----------------------------------
        If Frac.Length = 1 Then Frac += "0"
        '----------------------------------
        If Frac <> Nothing Then Frac = Get_IntStr_A(Frac)
        If IntStr <> Nothing Then Result = "# " & IntStr & " ريـال "
        If Frac <> Nothing Then Result &= "و " & Frac & " هللة  "
        Result &= "فقط لا غير " & "#"
        '===============================

        Return Result
    End Function

    Private Function Get_IntStr_A(ByRef S As String) As String
        Dim Result As String
        '=============================================
        'Chek If S >= 11 And <= 19
        If Val(S) >= 11 And Val(S) <= 19 Then
            S = Get_ValuesBN_11_19_A(S)
            S = S.Remove(0, 1) : Return S : Exit Function
        End If
        '===============================================
        Dim I As Integer
        ' Dim ROnes, RTens, RHun, RThus, RTenThus, RHunThus, RMln, RTenMln, RHunMln As String

        Dim ROnes As String = "" : Dim RTens As String = "" : Dim RHun As String = ""
        Dim RThus As String = "" : Dim RTenThus As String = "" : Dim RHunThus As String = ""
        Dim RMln As String = "" : Dim RTenMln As String = "" : Dim RHunMln As String = ""
        Dim SSS As String = "" : Dim J As Integer


        Dim L As Integer = S.Length
        For I = S.Length - 1 To 0 Step -1
            If Val(S.Chars(I)) > 0 Then
                Select Case I
                    '===============================================
                    Case L - 1 : ROnes = Get_Ones_A(S.Chars(I))
                        '===============================================
                    Case L - 2
                        For J = 0 To 7
                            If L = J + 2 Then SSS = S.Substring(J, 2)
                        Next
                        If Val(SSS) >= 11 And Val(SSS) <= 19 Then
                            RTens = Get_ValuesBN_11_19_A(SSS) : ROnes = Nothing
                        Else : RTens = Get_Tens_A(S.Chars(I)) : End If
                        '===============================================
                    Case L - 3 : RHun = Get_Hundreds_A(S.Chars(I))
                        '===============================================
                    Case L - 4 : RThus = Get_Thousands_A(S.Chars(I))
                        '===============================================
                End Select
                If L > 4 Then
                    Select Case I
                        '===============================================
                        Case L - 4 : RThus = Get_Ones_A(S.Chars(I))
                            If (S.Chars(L - 5)) = "0" Then
                                RThus &= " ألف "
                            End If
                            '===============================================
                        Case L - 5
                            For J = 0 To 4
                                If L = J + 5 Then SSS = S.Substring(J, 2)
                            Next
                            If Val(SSS) >= 11 And Val(SSS) <= 19 Then
                                RTenThus = Get_ValuesBN_11_19_A(SSS) : RThus = Nothing
                            Else : RTenThus = Get_Tens_A(S.Chars(I)) : End If
                            RTenThus &= " ألف "
                            '===============================================
                        Case L - 6 : RHunThus = Get_Hundreds_A(S.Chars(I))
                            If RTenThus = Nothing Then RHunThus &= " ألف "
                            '===============================================
                        Case L - 7
                            If L = 7 Then : RMln = Get_Ones_A(S.Chars(I)) & " مليون "
                            Else : RMln = Get_Ones_A(S.Chars(I)) : End If
                            '===============================================
                        Case L - 8
                            For J = 0 To 2
                                If L = J + 8 Then SSS = S.Substring(J, 2)
                            Next
                            If Val(SSS) >= 11 And Val(SSS) <= 19 Then
                                RTenMln = Get_ValuesBN_11_19_A(SSS) : RMln = Nothing
                            Else : RTenMln = Get_Tens_A(S.Chars(I)) : End If
                            If L = 8 Then RTenMln &= " مليون "
                            '===============================================
                        Case L - 9 : RHunMln = Get_Hundreds_A(S.Chars(I))
                            If L = 9 Then
                                RHunMln &= RMln & RTenMln & " مليون "
                                RTenMln = Nothing : RMln = Nothing
                            End If
                            '===============================================
                    End Select
                End If
            End If
        Next
        Result = RHunMln & RMln & RTenMln & RHunThus & RThus & RTenThus & RHun & ROnes & RTens
        Result = Result.Remove(0, 1)
        Dim RR As String
        Dim II As Integer = Result.IndexOf("*")
        If II >= 0 Then : RR = Result.Replace("*", " و")
        Else : RR = Result
        End If
        Return RR
    End Function

    Private Function Get_ValuesBN_11_19_A(ByVal S As String) As String
        '=============================================
        Select Case Val(S)
            Case 11 : S = "*أحد عشر"
            Case 12 : S = "*إثنـى عشر"
            Case 13 : S = "*ثلاثة عشر"
            Case 14 : S = "*أربعة عشر"
            Case 15 : S = "*خمسة عشر"
            Case 16 : S = "*ستة عشر"
            Case 17 : S = "*سبعة عشر"
            Case 18 : S = "*ثمانية عشر"
            Case 19 : S = "*تسعة عشر"
        End Select
        Return S
        '=============================================
    End Function

    Private Function Get_Ones_A(ByVal S As String) As String
        '=============================================
        Select Case Val(S)
            Case 1 : S = "*واحد"
            Case 2 : S = "*إثنـين"
            Case 3 : S = "*ثلاثة"
            Case 4 : S = "*أربعة"
            Case 5 : S = "*خمسة"
            Case 6 : S = "*ستة"
            Case 7 : S = "*سبعة"
            Case 8 : S = "*ثمانية"
            Case 9 : S = "*تسعة"
        End Select
        Return S
        '===============================================
    End Function

    Private Function Get_Tens_A(ByVal S As String) As String
        '=============================================
        Select Case Val(S)
            Case 1 : S = "*عشره"
            Case 2 : S = "*عشرون"
            Case 3 : S = "*ثلاثون"
            Case 4 : S = "*أربعون"
            Case 5 : S = "*خمسون"
            Case 6 : S = "*ستون"
            Case 7 : S = "*سبعون"
            Case 8 : S = "*ثمانون"
            Case 9 : S = "*تسعون"
        End Select
        Return S
        '===============================================
    End Function

    Private Function Get_Hundreds_A(ByVal S As String) As String
        '=============================================
        Select Case Val(S)
            Case 1 : S = "*مائه"
            Case 2 : S = "*مائتان"
            Case 3 : S = "*ثلاثمائه"
            Case 4 : S = "*أربعمائه"
            Case 5 : S = "*خمسائه"
            Case 6 : S = "*ستمائه"
            Case 7 : S = "*سبعمائه"
            Case 8 : S = "*ثمانمائه"
            Case 9 : S = "*تسعمائه"
        End Select
        Return S
        '===============================================
    End Function

    Private Function Get_Thousands_A(ByVal S As String) As String
        '=============================================
        Select Case Val(S)
            Case 1 : S = "*ألف"
            Case 2 : S = "*الفان"
            Case 3 : S = "*ثلاثة آلاف"
            Case 4 : S = "*أربعة آلاف"
            Case 5 : S = "*خمسة آلاف"
            Case 6 : S = "*ستة آلاف"
            Case 7 : S = "*سبعة آلاف"
            Case 8 : S = "*ثمانية آلاف"
            Case 9 : S = "*تسعة آلاف"
        End Select
        Return S
        '===============================================
    End Function
    '============================================================================
    '============================================================================
    '============================================================================
    '============================================================================

    Public Function ConvertToEnglish(ByVal Str As String)
        Dim IntStr As String = "" : Dim Frac As String = ""
        Dim Result As String = ""
        Dim L As Integer = Str.Length ' To Get The Length Of The Original Text 
        Dim M As Integer = Str.IndexOf(".") ' To Get The location Of Decimal Sign
        If M > 0 Then
            IntStr = Str.Remove(M, L - M) 'To Get Number Without Fractions
            Frac = Str.Remove(0, M + 1)   'To Get Numbre of Fractions
        ElseIf M = 0 Then : Frac = Str.Remove(0, M + 1)
        ElseIf M < 0 Then : IntStr = Str : End If
        '===============================
        If IntStr <> Nothing Then IntStr = Get_IntStr_E(IntStr)
        If Frac <> Nothing Then Frac = Get_IntStr_E(Frac)
        If IntStr <> Nothing Then Result = "Only " & IntStr & " Riyals "
        If Frac <> Nothing Then Result &= ", " & Frac & " Halalah  "
        '===============================
        Return Result
    End Function

    Private Function Get_IntStr_E(ByRef S As String) As String
        Dim Result As String
        '=============================================
        'Chek If S >= 11 And <= 19
        If Val(S) >= 11 And Val(S) <= 19 Then
            S = Get_ValuesBN_11_19_E(S)
            S = S.Remove(0, 1) : Return S : Exit Function
        End If
        '===============================================
        Dim I As Integer
        Dim ROnes As String = "" : Dim RTens As String = "" : Dim RHun As String = ""
        Dim RThus As String = "" : Dim RTenThus As String = "" : Dim RHunThus As String = ""
        Dim RMln As String = "" : Dim RTenMln As String = "" : Dim RHunMln As String = ""
        Dim SSS As String = "" : Dim J As Integer

        Dim L As Integer = S.Length
        For I = S.Length - 1 To 0 Step -1
            If Val(S.Chars(I)) > 0 Then
                Select Case I
                    '===============================================
                    Case L - 1 : ROnes = Get_Ones_E(S.Chars(I))
                        '===============================================
                    Case L - 2
                        For J = 0 To 7
                            If L = J + 2 Then SSS = S.Substring(J, 2)
                        Next
                        If Val(SSS) >= 11 And Val(SSS) <= 19 Then
                            RTens = Get_ValuesBN_11_19_E(SSS) : ROnes = Nothing
                        Else : RTens = Get_Tens_E(S.Chars(I)) : End If
                        '===============================================
                    Case L - 3 : RHun = Get_Hundreds_E(S.Chars(I))
                        '===============================================
                    Case L - 4 : RThus = Get_Thousands_E(S.Chars(I))
                        '===============================================
                End Select
                If L > 4 Then
                    Select Case I
                        '===============================================
                        Case L - 4 : RThus = Get_Ones_E(S.Chars(I))
                            RThus &= " Thousand "
                            '===============================================
                        Case L - 5
                            For J = 0 To 4
                                If L = J + 5 Then SSS = S.Substring(J, 2)
                            Next
                            If Val(SSS) >= 11 And Val(SSS) <= 19 Then
                                RTenThus = Get_ValuesBN_11_19_E(SSS) : RThus = Nothing
                            Else : RTenThus = Get_Tens_E(S.Chars(I)) : End If
                            If RThus = Nothing Then RTenThus &= " Thousand "
                            '===============================================
                        Case L - 6 : RHunThus = Get_Hundreds_E(S.Chars(I))
                            If (RTenThus = Nothing) And (RThus = Nothing) Then RHunThus &= " Thousand "
                            '===============================================
                        Case L - 7
                            If L = 7 Then : RMln = Get_Ones_E(S.Chars(I))
                            Else : RMln = Get_Ones_E(S.Chars(I)) : End If
                            RMln &= " Million "
                            '===============================================
                        Case L - 8
                            For J = 0 To 2
                                If L = J + 8 Then SSS = S.Substring(J, 2)
                            Next
                            If Val(SSS) >= 11 And Val(SSS) <= 19 Then
                                RTenMln = Get_ValuesBN_11_19_E(SSS) : RMln = Nothing
                            Else : RTenMln = Get_Tens_E(S.Chars(I)) : End If

                            If (RMln = Nothing) Then RTenMln &= " Million "
                            '===============================================
                        Case L - 9 : RHunMln = Get_Hundreds_E(S.Chars(I))
                            If L = 9 Then
                                If (RMln = Nothing) And (RTenMln = Nothing) Then RHunMln &= " Million "
                            End If
                            '===============================================
                    End Select
                End If
            End If
        Next
        Result = RHunMln & RTenMln & RMln & RHunThus & RTenThus & RThus & RHun & RTens & ROnes
        Result = Result.Remove(0, 1)
        Dim RR As String
        Dim II As Integer = Result.IndexOf("*")
        If II >= 0 Then : RR = Result.Replace("*", " ")
        Else : RR = Result
        End If
        Return RR
    End Function

    Private Function Get_ValuesBN_11_19_E(ByVal S As String) As String
        '=============================================
        Select Case Val(S)
            Case 11 : S = "*Eleven"
            Case 12 : S = "*Twelve"
            Case 13 : S = "*Therteen"
            Case 14 : S = "*Fourteen"
            Case 15 : S = "*Fifteen"
            Case 16 : S = "*Sixteen"
            Case 17 : S = "*Seventeen"
            Case 18 : S = "*Eighteen"
            Case 19 : S = "*Nineteen"
        End Select
        Return S
        '=============================================
    End Function

    Private Function Get_Ones_E(ByVal S As String) As String
        '=============================================
        Select Case Val(S)
            Case 1 : S = "*One"
            Case 2 : S = "*Two"
            Case 3 : S = "*Three"
            Case 4 : S = "*Four"
            Case 5 : S = "*Five"
            Case 6 : S = "*Six"
            Case 7 : S = "*Seven"
            Case 8 : S = "*Eight"
            Case 9 : S = "*Nine"
        End Select
        Return S
        '===============================================
    End Function

    Private Function Get_Tens_E(ByVal S As String) As String
        '=============================================
        Select Case Val(S)
            Case 1 : S = "*Ten"
            Case 2 : S = "*Twenty"
            Case 3 : S = "*Thirty"
            Case 4 : S = "*Fourty"
            Case 5 : S = "*Fifty"
            Case 6 : S = "*Sixty"
            Case 7 : S = "*Seventy"
            Case 8 : S = "*Eighty"
            Case 9 : S = "*Ninety"
        End Select
        Return S
        '===============================================
    End Function

    Private Function Get_Hundreds_E(ByVal S As String) As String
        '=============================================
        Select Case Val(S)
            Case 1 : S = "*One Hundred"
            Case 2 : S = "*Two Hundred"
            Case 3 : S = "*Three Hundred"
            Case 4 : S = "*Four Hundred"
            Case 5 : S = "*Five Hundred"
            Case 6 : S = "*Six Hundred"
            Case 7 : S = "*Seven Hundred"
            Case 8 : S = "*Eight Hundred"
            Case 9 : S = "*Nine Hundred"
        End Select
        Return S
        '===============================================
    End Function

    Private Function Get_Thousands_E(ByVal S As String) As String
        '=============================================
        Select Case Val(S)
            Case 1 : S = "*One Thousand"
            Case 2 : S = "*Two Thousand"
            Case 3 : S = "*Three Thousand"
            Case 4 : S = "*Four Thousand"
            Case 5 : S = "*Five Thousand"
            Case 6 : S = "*Six Thousand"
            Case 7 : S = "*Seven Thousand"
            Case 8 : S = "*Eight Thousand"
            Case 9 : S = "*Nine Thousand"
        End Select
        Return S
        '===============================================
    End Function

End Module

