﻿Imports System.Data.SqlClient
Public Class frmAccountSearch
    Private Sub frmAccountSearch_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        txtSearch.Focus()
    End Sub

    Sub SearchBox()
        Dim keyword As String = txtSearch.Text.Trim()
        If keyword = "" Then
            CustomersLoad()
            Return
        End If

        Dim ds As DataSet = New DataSet
        Dim cmd As New SqlClient.SqlCommand("select AccountNo as [رقم الحساب],AccountDescription as [وصف الحساب] from AccountChart where CAST(AccountNo AS NVARCHAR) like @kw or AccountDescription like @kw order by AccountNo", New SqlClient.SqlConnection(Constr))
        cmd.Parameters.AddWithValue("@kw", "%" & keyword & "%")
        Dim da As New SqlClient.SqlDataAdapter(cmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Sub CustomersLoad()
        Dim ds As DataSet = New DataSet
        Dim usercmd As New SqlCommand("select AccountNo as [رقم الحساب],AccountDescription as [وصف الحساب] from AccountChart order by AccountNo", Con)
        Dim da As New SqlDataAdapter(usercmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        SearchBox()
    End Sub

    Private Sub DataGridView1_CellDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If Val(DataGridView1.CurrentRow.Cells(0).Value) <> 0 Then
            AccountNo = DataGridView1.CurrentRow.Cells(0).Value
            DataGridView1.DataSource = ""
            txtSearch.Text = ""
            Me.Close()
            If AccountSearchForm = "frmJEStatement" Then
                frmJEStatement.BringToFront()
                frmJEStatement.cmbxPartnerNo.SelectedValue = AccountNo
                AccountSearchForm = ""
            ElseIf AccountSearchForm = "frmJETrx_Credit" Then
                frmJETrx.BringToFront()
                frmJETrx.cmbxCreditAccount.SelectedValue = AccountNo
                AccountSearchForm = ""
            ElseIf AccountSearchForm = "frmJETrx_Debit" Then
                frmJETrx.BringToFront()
                frmJETrx.cmbxDebitAccount.SelectedValue = AccountNo
                AccountSearchForm = ""
            ElseIf AccountSearchForm = "frmTrxCashRec" Then
                frmTrxCashRec.BringToFront()
                frmTrxCashRec.cmbxAccount.SelectedValue = AccountNo
                AccountSearchForm = ""
            ElseIf AccountSearchForm = "frmTrxCashPay" Then
                frmTrxCashPay.BringToFront()
                frmTrxCashPay.cmbxAccount.SelectedValue = AccountNo
                AccountSearchForm = ""

            ElseIf AccountSearchForm = "frmSearchJE" Then
                frmSearchJE.BringToFront()
                frmSearchJE.cmbxAccount.SelectedValue = AccountNo
                AccountSearchForm = ""



            End If
        End If
    End Sub
End Class