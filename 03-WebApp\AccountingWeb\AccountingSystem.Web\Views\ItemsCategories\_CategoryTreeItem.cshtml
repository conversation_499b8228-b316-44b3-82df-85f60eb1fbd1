@model AccountingSystem.Models.ItemsCategory

<li>
    <div class="tree-item">
        <span><i class="fas fa-folder text-warning"></i> @Model.Name</span>
        <div class="actions">
            <button class="btn btn-sm btn-outline-success" onclick="openCreateModal(@Model.Id)" title="إضافة تصنيف فرعي">
                <i class="fas fa-plus"></i>
            </button>
            <button class="btn btn-sm btn-outline-primary" onclick="openEditModal(@Model.Id, '@Model.Name')" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="confirmDelete(@Model.Id)" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    </div>
    @if (Model.Children.Any())
    {
        <ul>
            @foreach (var child in Model.Children)
            {
                @await Html.PartialAsync("_CategoryTreeItem", child)
            }
        </ul>
    }
</li> 