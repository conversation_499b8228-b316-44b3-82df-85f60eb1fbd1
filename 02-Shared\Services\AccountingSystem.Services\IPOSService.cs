using AccountingSystem.Models;

namespace AccountingSystem.Services
{
    public interface IPOSService
    {
        Task<int> GetNextInvoiceNumberAsync();
        Task<POSInvoice?> GetCurrentInvoiceAsync();
        Task<POSInvoice> CreateNewInvoiceAsync(string username, string store);
        Task<POSInvoiceItem> AddItemToInvoiceAsync(int invoiceNo, long itemNo, decimal quantity, decimal unitPrice, string username, string store);
        Task<bool> UpdateInvoiceItemAsync(int invoiceNo, int lineSN, decimal quantity, decimal unitPrice, string username);
        Task<bool> DeleteInvoiceItemAsync(int invoiceNo, int lineSN, string username);
        Task<POSInvoice> SaveInvoiceAsync(int invoiceNo, string username);
        Task<POSInvoice> CompleteInvoiceAsync(int invoiceNo, decimal cashAmount, decimal cardAmount, string username);
        Task<bool> ClearInvoiceItemsAsync(int invoiceNo);
        Task<List<POSInvoice>> SearchInvoicesAsync(string? invoiceNo, string? customer, string? user, string? warehouse, DateTime? date);
        Task<List<POSInvoice>> SearchInvoicesAdvancedAsync(
            string? invoiceNo,
            string? customer,
            string? user,
            string? store,
            DateTime? dateFrom,
            DateTime? dateTo,
            string? invoiceType,
            string? paymentMethod,
            string? reference,
            long? accountNo,
            string? notes
        );

        // Legacy lines mode (InvoiceSearchByItem) returning distinct headers
        Task<List<POSInvoice>> SearchInvoiceLinesAsync(
            string? invoiceNo,
            string? customer,
            string? user,
            string? store,
            DateTime? dateFrom,
            DateTime? dateTo,
            string? invoiceType,
            string? paymentMethod,
            string? reference,
            long? accountNo,
            string? notes,
            string? itemCode
        );

        // Exact legacy lines SELECT * (InvoiceSearchByItem) as a DataTable for UI rendering
        Task<System.Data.DataTable> SearchInvoiceLinesRawAsync(
            string? invoiceNo,
            string? user,
            string? store,
            DateTime? dateFrom,
            DateTime? dateTo,
            string? invoiceType,
            string? paymentMethod,
            string? reference,
            long? accountNo,
            string? notes,
            string? itemCode
        );

        // Map of invoice number -> user (from legacy view InvoicesForSearch)
        Task<Dictionary<int, string>> GetLegacyInvoiceUsersAsync(
            string? invoiceNo,
            string? user,
            string? store,
            DateTime? dateFrom,
            DateTime? dateTo,
            string? invoiceType,
            string? paymentMethod,
            string? reference,
            long? accountNo,
            string? notes);

        Task<List<POSInvoiceItem>> GetInvoiceItemsAsync(int invoiceNo);
        Task<List<POSPayment>> GetInvoicePaymentsAsync(int invoiceNo);
        Task<POSInvoice?> GetInvoiceByNumberAsync(int invoiceNo);
        Task<string> GetItemDescriptionAsync(long itemNo);
        Task<List<Item>> SearchItemsAsync(string searchTerm);
        Task<Item?> GetItemByBarcodeAsync(string barcode);
        Task<List<Store>> GetStoresAsync();
        Task<List<ChartOfAccount>> GetCustomersAsync();
        Task<InvoiceToolSetting?> GetPOSSettingsAsync();
        Task<List<Item>> GetFavoriteItemsAsync(string username);
        Task<PurchaseUserAuthorization> GetUserAuthorizationAsync(string username);
        Task<Store?> GetUserDefaultStoreAsync(string username);
        Task<bool> CanUserChangeStoreAsync(string username);
        Task<bool> ValidateActiveSessionAsync(string? username = null, string? store = null);
        Task<BarcodeProcessResult> ProcessBarcodeAsync(string barcode, int invoiceNo, string store, string username);

        // Additional methods from implementation
        Task<decimal> CalculateVATAsync(decimal amount, decimal vatPercentage, bool isPriceIncludeVAT);
        Task<decimal> CalculateDiscountAsync(decimal amount, decimal discountPercentage);
        Task<byte[]?> GenerateQRCodeAsync(string sellerName, string vatReg, string timestamp, string total, string vatAmount);
        Task<POSSession?> GetActiveSessionAsync(string? username = null, string? store = null);
        Task<string> GetThermalReceiptContentAsync(int invoiceNo);
        Task<byte[]> GenerateThermalReceiptAsync(int invoiceNo);
        Task<(List<Item> items, List<Store> stores)> GetPOSInitDataAsync();
        Task<List<string>> GetStoreNamesAsync();
        Task<List<string>> GetCashierNamesAsync();
        Task<List<string>> GetPaymentMethodNamesAsync();
        Task<bool> CanUserChangeCustomerAsync(string username);
        Task<bool> CanUserChangePriceAsync(string username);
        Task<decimal> GetUserMaxDiscountPercentAsync(string username);
        Task<bool> UpdateInvoiceCustomerAsync(int invoiceNo, long? customerId, string customerName, string customerPhone, string username);
        Task<bool> UpdateInvoiceStoreAsync(int invoiceNo, string store, string username);
        Task<long?> GetCashierAccountForStoreAsync(string store);

        // Release SN back to pool if invoice is empty
        Task<bool> ReleaseInvoiceIfEmptyAsync(int invoiceNo, string username);
        Task<int> ReleaseUserEmptyInvoicesAsync(string username);

        /// <summary>
        /// Debug method to get all stores and cashiers for troubleshooting
        /// </summary>
        Task<object> GetStoresAndCashiersDebugAsync();

        // GL Integration Methods
        Task<bool> CreateSalesInvoiceGLEntryAsync(int invoiceNo);
        Task<bool> CreatePaymentGLEntryAsync(int invoiceNo);
        Task<bool> CreateCOGSGLEntryAsync(int invoiceNo);
        Task<bool> CreateAllGLEntriesAsync(int invoiceNo);
    }
}

