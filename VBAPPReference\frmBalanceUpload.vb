﻿Imports System.Data.SqlClient
Imports System.IO
Imports System.Data
Imports System.Linq

Public Class frmBalanceUpload
    Private dtBalances As DataTable
    Private openingBalanceAccount As String = ""

    Private Sub frmBalanceUpload_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' Initialize DataTable first
            InitializeDataTable()

            ' Initialize form controls
            InitializeForm()

            ' Get opening balance account from configuration
            GetOpeningBalanceAccount()

            ' Set default date to today
            dtpEntryDate.Value = DateTime.Today
            ForceGregorianForAllPickers(Me)
            UpdateStatus("جاهز لاستيراد ملف Excel")
        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل النموذج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub InitializeDataTable()
        ' Initialize DataTable for balances
        dtBalances = New DataTable()
        dtBalances.Columns.Add("OldCode", GetType(String))
        dtBalances.Columns.Add("NewCode", GetType(String))
        dtBalances.Columns.Add("AccountName", GetType(String))
        dtBalances.Columns.Add("OriginalBalance", GetType(Decimal))
        dtBalances.Columns.Add("Status", GetType(String))
    End Sub

    Private Sub InitializeForm()
        ' Bind to grid
        dgvBalances.AutoGenerateColumns = False
        dgvBalances.DataSource = dtBalances

        ' Setup column data binding
        colOldCode.DataPropertyName = "OldCode"
        colNewCode.DataPropertyName = "NewCode"
        colAccountName.DataPropertyName = "AccountName"
        colOriginalBalance.DataPropertyName = "OriginalBalance"
        colStatus.DataPropertyName = "Status"

        ' Format balance column
        colOriginalBalance.DefaultCellStyle.Format = "N2"
        colOriginalBalance.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight

        ' Configure ComboBox column properties
        colNewCode.DisplayStyle = DataGridViewComboBoxDisplayStyle.DropDownButton
        colNewCode.AutoComplete = True
        colNewCode.FlatStyle = FlatStyle.Flat

        ' Initial button states
        btnValidateMapping.Enabled = False
        btnProcessBalances.Enabled = False
        btnExportReport.Enabled = False
    End Sub

    Private Sub GetOpeningBalanceAccount()
        Try
            Using conn As New SqlConnection(ConStr)
                conn.Open()
                Dim cmd As New SqlCommand("SELECT TOP 1 AccountNo FROM tblGLConfig WHERE EntryReferenceModule = N'رصيد افتتاحي'", conn)
                Dim result = cmd.ExecuteScalar()

                If result IsNot Nothing Then
                    openingBalanceAccount = result.ToString()
                Else
                    MessageBox.Show("لم يتم العثور على حساب الرصيد الافتتاحي في إعدادات النظام", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                End If
            End Using
        Catch ex As Exception
            MessageBox.Show($"خطأ في الحصول على حساب الرصيد الافتتاحي: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnImportExcel_Click(sender As Object, e As EventArgs) Handles btnImportExcel.Click
        Try
            Dim openFileDialog As New OpenFileDialog()
            openFileDialog.Filter = "Excel Files|*.xlsx;*.xls|All Files|*.*"
            openFileDialog.Title = "اختر ملف Excel للاستيراد"

            If openFileDialog.ShowDialog() = DialogResult.OK Then
                ImportExcelFile(openFileDialog.FileName)
            End If
        Catch ex As Exception
            MessageBox.Show($"خطأ في استيراد الملف: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            UpdateStatus("جاهز")
            progressBar.Visible = False
        End Try
    End Sub

    Private Sub ImportExcelFile(filePath As String)
        UpdateStatus("جاري استيراد ملف Excel...")
        progressBar.Visible = True
        progressBar.Value = 0

        Try
            ' Clear existing data
            dtBalances.Clear()

            ' Use OleDB to read Excel file instead of Interop
            Dim connectionString As String = GetExcelConnectionString(filePath)

            Using conn As New System.Data.OleDb.OleDbConnection(connectionString)
                conn.Open()

                ' Get sheet name
                Dim schemaTable As DataTable = conn.GetOleDbSchemaTable(System.Data.OleDb.OleDbSchemaGuid.Tables, Nothing)
                Dim sheetName As String = schemaTable.Rows(0)("TABLE_NAME").ToString()

                ' Read data
                Dim query As String = $"SELECT * FROM [{sheetName}]"
                Using adapter As New System.Data.OleDb.OleDbDataAdapter(query, conn)
                    Dim excelData As New DataTable()
                    adapter.Fill(excelData)

                    If excelData.Rows.Count > 0 Then
                        ' Find columns automatically
                        Dim accountCodeCol As Integer = FindColumnIndex(excelData, {"الكود", "AccountNo", "Account_Code", "Code", "كود الحساب"})
                        Dim balanceCol As Integer = FindColumnIndex(excelData, {"الرصيد", "Balance", "Amount", "المبلغ", "الرصيد الحالي"})

                        If accountCodeCol = -1 OrElse balanceCol = -1 Then
                            MessageBox.Show("لم يتم العثور على الأعمدة المطلوبة في ملف Excel. يرجى التأكد من وجود أعمدة الكود والرصيد.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                            Return
                        End If

                        ' Import data
                        For i As Integer = 0 To excelData.Rows.Count - 1
                            Dim excelRow As DataRow = excelData.Rows(i)
                            Dim accountCode As String = GetCellStringValue(excelRow(accountCodeCol))
                            Dim balanceValue As String = GetCellStringValue(excelRow(balanceCol))

                            If Not String.IsNullOrWhiteSpace(accountCode) AndAlso Not String.IsNullOrWhiteSpace(balanceValue) Then
                                Dim balance As Decimal = 0
                                If Decimal.TryParse(balanceValue, balance) AndAlso balance <> 0 Then
                                    ' Try to parse accountCode as Long since CustomerNo/VendorNo are bigint
                                    Dim accountCodeLong As Long = 0
                                    If Long.TryParse(accountCode.Trim(), accountCodeLong) Then
                                        Dim row As DataRow = dtBalances.NewRow()
                                        row("OldCode") = accountCodeLong.ToString() ' Store as string for display
                                        row("NewCode") = DBNull.Value
                                        row("AccountName") = ""
                                        row("OriginalBalance") = balance
                                        row("Status") = "في انتظار الربط"
                                        dtBalances.Rows.Add(row)
                                    End If
                                End If
                            End If

                            ' Update progress
                            If excelData.Rows.Count > 0 Then
                                progressBar.Value = CInt((i + 1) / excelData.Rows.Count * 100)
                            End If
                            System.Windows.Forms.Application.DoEvents()
                        Next
                    End If
                End Using
            End Using

            UpdateStatus($"تم استيراد {dtBalances.Rows.Count} سجل بنجاح")
            UpdateSummary()

            ' Enable validation button
            btnValidateMapping.Enabled = dtBalances.Rows.Count > 0

        Catch ex As Exception
            MessageBox.Show($"خطأ في استيراد الملف: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function GetExcelConnectionString(filePath As String) As String
        Dim fileExtension As String = Path.GetExtension(filePath).ToLower()
        If fileExtension = ".xlsx" Then
            Return $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={filePath};Extended Properties=""Excel 12.0 Xml;HDR=YES;IMEX=1"""
        Else
            Return $"Provider=Microsoft.Jet.OLEDB.4.0;Data Source={filePath};Extended Properties=""Excel 8.0;HDR=YES;IMEX=1"""
        End If
    End Function

    Private Function FindColumnIndex(dataTable As DataTable, columnNames As String()) As Integer
        Try
            For col As Integer = 0 To dataTable.Columns.Count - 1
                Dim columnName As String = dataTable.Columns(col).ColumnName.ToLower()
                For Each columnNameToCheck As String In columnNames
                    If columnName.Contains(columnNameToCheck.ToLower()) Then
                        Return col
                    End If
                Next
            Next
        Catch
        End Try
        Return -1
    End Function

    Private Function GetCellStringValue(cellValue As Object) As String
        If cellValue IsNot Nothing AndAlso Not IsDBNull(cellValue) Then
            Return cellValue.ToString()
        End If
        Return ""
    End Function

    Private Sub btnValidateMapping_Click(sender As Object, e As EventArgs) Handles btnValidateMapping.Click
        Try
            UpdateStatus("جاري التحقق من ربط الحسابات...")
            progressBar.Visible = True
            progressBar.Value = 0

            ValidateAccountMapping()

            UpdateStatus("تم الانتهاء من التحقق من الربط")
            UpdateSummary()

            ' Check if all accounts are mapped
            Dim unmappedCount As Integer = 0
            For Each row As DataRow In dtBalances.Rows
                If String.IsNullOrEmpty(row("NewCode")?.ToString()) Then
                    unmappedCount += 1
                End If
            Next

            If unmappedCount = 0 Then
                btnProcessBalances.Enabled = True
                MessageBox.Show("تم ربط جميع الحسابات بنجاح. يمكنك الآن معالجة الأرصدة.", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                MessageBox.Show($"يوجد {unmappedCount} حساب غير مربوط. يرجى تصحيح الربط قبل المعالجة.", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If

        Catch ex As Exception
            MessageBox.Show($"خطأ في التحقق من الربط: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            progressBar.Visible = False
        End Try
    End Sub

    Private Sub ValidateAccountMapping()
        Dim tablePrefix As String = If(rbCustomer.Checked, "tblCustomers", "tblVendors")

        ' First populate the ComboBox with available accounts
        PopulateAccountComboBox(tablePrefix)

        For i As Integer = 0 To dtBalances.Rows.Count - 1
            Dim row As DataRow = dtBalances.Rows(i)
            Dim oldCode As String = row("OldCode").ToString()

            ' Try to find mapping in database
            Dim newCode As String = GetNewAccountCode(tablePrefix, oldCode)

            If Not String.IsNullOrEmpty(newCode) Then
                row("NewCode") = newCode
                row("AccountName") = GetAccountName(tablePrefix, newCode)
                row("Status") = "مربوط"

                ' Update the grid row directly to avoid ComboBox validation issues
                Try
                    dgvBalances.Rows(i).Cells("colNewCode").Value = newCode
                    dgvBalances.Rows(i).Cells("colAccountName").Value = row("AccountName").ToString()
                    dgvBalances.Rows(i).Cells("colStatus").Value = "مربوط"
                Catch ex As Exception
                    ' If ComboBox validation fails, just update the DataTable
                    ' The grid will sync when the ComboBox is properly populated
                End Try
            Else
                row("NewCode") = DBNull.Value
                row("AccountName") = ""
                row("Status") = "غير مربوط"

                Try
                    dgvBalances.Rows(i).Cells("colNewCode").Value = ""
                    dgvBalances.Rows(i).Cells("colAccountName").Value = ""
                    dgvBalances.Rows(i).Cells("colStatus").Value = "غير مربوط"
                Catch ex As Exception
                    ' Ignore ComboBox validation errors during population
                End Try
            End If

            ' Update progress
            progressBar.Value = CInt((i + 1) / dtBalances.Rows.Count * 100)
            System.Windows.Forms.Application.DoEvents()
        Next
    End Sub

    Private Function GetNewAccountCode(tablePrefix As String, oldCode As String) As String
        Try
            Using conn As New SqlConnection(ConStr)
                conn.Open()
                Dim accountCodeField As String = If(tablePrefix = "tblCustomers", "CustomerNo", "VendorNo")

                ' Convert oldCode to Long since CustomerNo/VendorNo are bigint
                Dim oldCodeLong As Long = 0
                If Not Long.TryParse(oldCode, oldCodeLong) Then
                    Return "" ' Invalid number format
                End If

                Dim sql As String = $"SELECT {accountCodeField} FROM {tablePrefix} WHERE OldCode = @OldCode"
                Dim cmd As New SqlCommand(sql, conn)
                cmd.Parameters.AddWithValue("@OldCode", oldCodeLong)

                Dim result = cmd.ExecuteScalar()
                If result IsNot Nothing Then
                    Return result.ToString()
                End If
            End Using
        Catch ex As Exception
            ' Log error but continue
        End Try
        Return ""
    End Function

    Private Function GetAccountName(tablePrefix As String, accountCode As String) As String
        Try
            Using conn As New SqlConnection(ConStr)
                conn.Open()
                Dim nameField As String = If(tablePrefix = "tblCustomers", "CustomerName", "VendorName")
                Dim accountCodeField As String = If(tablePrefix = "tblCustomers", "CustomerNo", "VendorNo")

                ' Convert accountCode to Long since CustomerNo/VendorNo are bigint
                Dim accountCodeLong As Long = 0
                If Not Long.TryParse(accountCode, accountCodeLong) Then
                    Return "" ' Invalid number format
                End If

                Dim sql As String = $"SELECT {nameField} FROM {tablePrefix} WHERE {accountCodeField} = @AccountCode"
                Dim cmd As New SqlCommand(sql, conn)
                cmd.Parameters.AddWithValue("@AccountCode", accountCodeLong)

                Dim result = cmd.ExecuteScalar()
                If result IsNot Nothing Then
                    Return result.ToString()
                End If
            End Using
        Catch ex As Exception
            ' Log error but continue
        End Try
        Return ""
    End Function

    Private Sub PopulateAccountComboBox(tablePrefix As String)
        Try
            Using conn As New SqlConnection(ConStr)
                conn.Open()
                Dim nameField As String = If(tablePrefix = "tblCustomers", "CustomerName", "VendorName")
                Dim accountCodeField As String = If(tablePrefix = "tblCustomers", "CustomerNo", "VendorNo")
                Dim sql As String = $"SELECT {accountCodeField}, {nameField} FROM {tablePrefix} ORDER BY {nameField}"
                Dim cmd As New SqlCommand(sql, conn)
                Dim dt As New DataTable()
                Dim adapter As New SqlDataAdapter(cmd)
                adapter.Fill(dt)

                ' Convert the bigint column to string for display purposes
                Dim displayDt As New DataTable()
                displayDt.Columns.Add("AccountCode", GetType(String))
                displayDt.Columns.Add("AccountName", GetType(String))

                ' Add empty option first
                Dim emptyRow As DataRow = displayDt.NewRow()
                emptyRow("AccountCode") = ""
                emptyRow("AccountName") = "-- اختر حساب --"
                displayDt.Rows.Add(emptyRow)

                ' Add data rows
                For Each row As DataRow In dt.Rows
                    Dim displayRow As DataRow = displayDt.NewRow()
                    displayRow("AccountCode") = row(accountCodeField).ToString()
                    displayRow("AccountName") = row(nameField).ToString()
                    displayDt.Rows.Add(displayRow)
                Next

                colNewCode.DataSource = displayDt
                colNewCode.DisplayMember = "AccountName"
                colNewCode.ValueMember = "AccountCode"
            End Using
        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل قائمة الحسابات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnProcessBalances_Click(sender As Object, e As EventArgs) Handles btnProcessBalances.Click
        Try
            ' Validate all accounts are mapped
            Dim unmappedCount As Integer = 0
            For Each row As DataRow In dtBalances.Rows
                If String.IsNullOrEmpty(row("NewCode")?.ToString()) Then
                    unmappedCount += 1
                End If
            Next

            If unmappedCount > 0 Then
                MessageBox.Show($"يوجد {unmappedCount} حساب غير مربوط. يرجى ربط جميع الحسابات قبل المعالجة.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            ' Show confirmation with summary
            Dim message As String = BuildConfirmationMessage()
            If MessageBox.Show(message, "تأكيد معالجة الأرصدة", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                ProcessBalances()
            End If

        Catch ex As Exception
            MessageBox.Show($"خطأ في معالجة الأرصدة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function BuildConfirmationMessage() As String
        Dim totalRecords As Integer = dtBalances.Rows.Count
        Dim totalDebits As Decimal = CalculateTotalDebits()
        Dim totalCredits As Decimal = CalculateTotalCredits()

        Return $"هل أنت متأكد من معالجة الأرصدة؟" & vbCrLf & vbCrLf &
               $"عدد السجلات: {totalRecords}" & vbCrLf &
               $"إجمالي المدين: {totalDebits:N2}" & vbCrLf &
               $"إجمالي الدائن: {totalCredits:N2}" & vbCrLf &
               $"تاريخ القيد: {dtpEntryDate.Value:yyyy/MM/dd}" & vbCrLf & vbCrLf &
               "سيتم إنشاء قيود يومية لجميع الأرصدة."
    End Function

    Private Sub ProcessBalances()
        UpdateStatus("جاري معالجة الأرصدة...")
        progressBar.Visible = True
        progressBar.Value = 0

        Try
            Using conn As New SqlConnection(ConStr)
                conn.Open()
                Using transaction As SqlTransaction = conn.BeginTransaction()
                    Try
                        For i As Integer = 0 To dtBalances.Rows.Count - 1
                            Dim row As DataRow = dtBalances.Rows(i)
                            CreateJournalEntry(conn, transaction, row)

                            ' Update progress
                            progressBar.Value = CInt((i + 1) / dtBalances.Rows.Count * 100)
                            System.Windows.Forms.Application.DoEvents()
                        Next

                        transaction.Commit()
                        MessageBox.Show("تم معالجة جميع الأرصدة بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

                        ' Enable export and reset form
                        btnExportReport.Enabled = True
                        btnProcessBalances.Enabled = False

                    Catch ex As Exception
                        transaction.Rollback()
                        Throw
                    End Try
                End Using
            End Using

        Finally
            progressBar.Visible = False
            UpdateStatus("تم الانتهاء من المعالجة")
        End Try
    End Sub

    Private Sub CreateJournalEntry(conn As SqlConnection, transaction As SqlTransaction, row As DataRow)
        Dim accountCode As String = row("NewCode").ToString()
        Dim balance As Decimal = CDec(row("OriginalBalance"))
        Dim entryRef As String = $"رصيد افتتاحي - {row("AccountName")} - {row("OldCode")}"

        ' Determine debit/credit based on customer/vendor and balance sign
        Dim fromAccount As String, toAccount As String

        If rbCustomer.Checked Then
            If balance >= 0 Then
                ' Customer positive balance: Debit Customer, Credit Opening
                fromAccount = openingBalanceAccount
                toAccount = accountCode
            Else
                ' Customer negative balance: Credit Customer, Debit Opening
                fromAccount = accountCode
                toAccount = openingBalanceAccount
                balance = Math.Abs(balance)
            End If
        Else ' Vendor
            If balance >= 0 Then
                ' Vendor positive balance: Credit Vendor, Debit Opening
                fromAccount = accountCode
                toAccount = openingBalanceAccount
            Else
                ' Vendor negative balance: Debit Vendor, Credit Opening
                fromAccount = openingBalanceAccount
                toAccount = accountCode
                balance = Math.Abs(balance)
            End If
        End If

        ' Create journal entry manually instead of using SP to avoid SCOPE_IDENTITY issues
        Dim jeHeaderID As Integer

        ' Insert Journal Header
        Dim headerSql As String = "INSERT INTO tbl_Acc_JournalHeader (EntryDate, EntryReference, EntryType, CreatedBy, CreatedOn) " &
                                 "VALUES (@EntryDate, @EntryReference, N'قيد يدوي', @CreatedBy, GETDATE()); SELECT SCOPE_IDENTITY();"

        Using cmdHeader As New SqlCommand(headerSql, conn, transaction)
            cmdHeader.Parameters.AddWithValue("@EntryDate", dtpEntryDate.Value.Date)
            cmdHeader.Parameters.AddWithValue("@EntryReference", entryRef)
            cmdHeader.Parameters.AddWithValue("@CreatedBy", UserName)

            jeHeaderID = Convert.ToInt32(cmdHeader.ExecuteScalar())
        End Using

        ' Insert Journal Lines
        ' 1. Credit line (From Account)
        Dim creditSql As String = "INSERT INTO tbl_Acc_JournalLines (JEHeaderID, AccountCode, Debit, Credit, Description) " &
                                 "VALUES (@JEHeaderID, @FromAccountCode, 0, @Amount, N'دائن - الحساب المحول منه')"

        Using cmdCredit As New SqlCommand(creditSql, conn, transaction)
            cmdCredit.Parameters.AddWithValue("@JEHeaderID", jeHeaderID)
            cmdCredit.Parameters.AddWithValue("@FromAccountCode", fromAccount)
            cmdCredit.Parameters.AddWithValue("@Amount", balance)
            cmdCredit.ExecuteNonQuery()
        End Using

        ' 2. Debit line (To Account)
        Dim debitSql As String = "INSERT INTO tbl_Acc_JournalLines (JEHeaderID, AccountCode, Debit, Credit, Description) " &
                                "VALUES (@JEHeaderID, @ToAccountCode, @Amount, 0, N'مدين - الحساب المحول إليه')"

        Using cmdDebit As New SqlCommand(debitSql, conn, transaction)
            cmdDebit.Parameters.AddWithValue("@JEHeaderID", jeHeaderID)
            cmdDebit.Parameters.AddWithValue("@ToAccountCode", toAccount)
            cmdDebit.Parameters.AddWithValue("@Amount", balance)
            cmdDebit.ExecuteNonQuery()
        End Using
    End Sub

    Private Function CalculateTotalDebits() As Decimal
        Dim total As Decimal = 0
        For Each row As DataRow In dtBalances.Rows
            Dim balance As Decimal = CDec(row("OriginalBalance"))

            If rbCustomer.Checked Then
                If balance >= 0 Then total += balance
            Else ' Vendor
                If balance < 0 Then total += Math.Abs(balance)
            End If
        Next
        Return total
    End Function

    Private Function CalculateTotalCredits() As Decimal
        Dim total As Decimal = 0
        For Each row As DataRow In dtBalances.Rows
            Dim balance As Decimal = CDec(row("OriginalBalance"))

            If rbCustomer.Checked Then
                If balance < 0 Then total += Math.Abs(balance)
            Else ' Vendor
                If balance >= 0 Then total += balance
            End If
        Next
        Return total
    End Function

    Private Sub UpdateSummary()
        lblRecordsCount.Text = dtBalances.Rows.Count.ToString()
        lblDebitsAmount.Text = CalculateTotalDebits().ToString("N2")
        lblCreditsAmount.Text = CalculateTotalCredits().ToString("N2")
    End Sub

    Private Sub UpdateStatus(status As String)
        lblStatus.Text = status
        System.Windows.Forms.Application.DoEvents()
    End Sub

    Private Sub dgvBalances_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles dgvBalances.CellValueChanged
        ' Handle manual account code changes
        If e.ColumnIndex = colNewCode.Index AndAlso e.RowIndex >= 0 Then
            Try
                Dim newCode As String = dgvBalances.Rows(e.RowIndex).Cells(e.ColumnIndex).Value?.ToString()
                If Not String.IsNullOrEmpty(newCode) Then
                    Dim tablePrefix As String = If(rbCustomer.Checked, "tblCustomers", "tblVendors")
                    Dim accountName As String = GetAccountName(tablePrefix, newCode)
                    dgvBalances.Rows(e.RowIndex).Cells("colAccountName").Value = accountName
                    dgvBalances.Rows(e.RowIndex).Cells("colStatus").Value = "مربوط يدوياً"

                    ' Update summary
                    UpdateSummary()

                    ' Check if all accounts are mapped
                    Dim unmappedCount As Integer = 0
                    For Each row As DataRow In dtBalances.Rows
                        If String.IsNullOrEmpty(row("NewCode")?.ToString()) Then
                            unmappedCount += 1
                        End If
                    Next
                    btnProcessBalances.Enabled = unmappedCount = 0
                End If
            Catch ex As Exception
                ' Handle ComboBox validation error
                MessageBox.Show($"خطأ في تحديد الحساب: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                ' Reset the cell value
                dgvBalances.Rows(e.RowIndex).Cells(e.ColumnIndex).Value = ""
            End Try
        End If
    End Sub

    Private Sub dgvBalances_DataError(sender As Object, e As DataGridViewDataErrorEventArgs) Handles dgvBalances.DataError
        ' Handle data validation errors in the DataGridView
        If e.ColumnIndex = colNewCode.Index Then
            MessageBox.Show("قيمة غير صحيحة للحساب. يرجى اختيار حساب من القائمة.", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            ' Cancel the error and set a default value
            e.Cancel = True
            dgvBalances.Rows(e.RowIndex).Cells(e.ColumnIndex).Value = ""
        End If
    End Sub

    Private Sub rbCustomer_CheckedChanged(sender As Object, e As EventArgs) Handles rbCustomer.CheckedChanged, rbVendor.CheckedChanged
        ' Clear data when switching between customer/vendor
        If dtBalances IsNot Nothing AndAlso dtBalances.Rows.Count > 0 Then
            If MessageBox.Show("سيتم مسح البيانات المحملة. هل تريد المتابعة؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                dtBalances.Clear()
                btnValidateMapping.Enabled = False
                btnProcessBalances.Enabled = False
                btnExportReport.Enabled = False
                UpdateSummary()
            Else
                ' Revert selection
                If sender Is rbCustomer Then
                    rbVendor.Checked = True
                Else
                    rbCustomer.Checked = True
                End If
            End If
        End If
    End Sub

    Private Sub btnExportReport_Click(sender As Object, e As EventArgs) Handles btnExportReport.Click
        Try
            Dim saveFileDialog As New SaveFileDialog()
            saveFileDialog.Filter = "Excel Files|*.xlsx"
            saveFileDialog.Title = "حفظ تقرير الأرصدة"
            saveFileDialog.FileName = $"تقرير_الأرصدة_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"

            If saveFileDialog.ShowDialog() = DialogResult.OK Then
                ExportToExcel(saveFileDialog.FileName)
                MessageBox.Show("تم تصدير التقرير بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ExportToExcel(filePath As String)
        ' Implementation for Excel export
        ' This would export the current grid data to Excel file
        UpdateStatus("جاري تصدير التقرير...")
        ' Add Excel export logic here
        UpdateStatus("تم تصدير التقرير")
    End Sub
End Class