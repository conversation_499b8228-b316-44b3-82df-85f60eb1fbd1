using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    [Table("tblToolsInvoice")]
    public class InvoiceToolSetting
    {
        [Key]
        [Column("InvoiceType")]
        [StringLength(50)]
        public string InvoiceType { get; set; }

        [Column("VATIncludedChangeable")]
        public string? VATIncludedChangeable { get; set; }

        [Column("NonVatInvoiceChangeable")]
        public string? NonVatInvoiceChangeable { get; set; }

        [Column("ReferenceMandatory")]
        public string? ReferenceMandatory { get; set; }

        [Column("MaterialAccountNo")]
        public int? MaterialAccountNo { get; set; }

        [Column("DiscountAccountNo")]
        public int? DiscountAccountNo { get; set; }

        [Column("InvoicePrinter")]
        public string? InvoicePrinter { get; set; }

        [Column("PrintOption")]
        public int? PrintOption { get; set; }

        [Column("MandatoryVendorVATReg")]
        public string? MandatoryVendorVATReg { get; set; }

        [Column("DefAccountNo")]
        public int? DefAccountNo { get; set; }

        [Column("DefPaymentType")]
        public string? DefPaymentType { get; set; }

        [Column("DefCashier")]
        public int? DefCashier { get; set; }

        [Column("DefStores")]
        public string? DefStores { get; set; }

        [Column("DefPriceIncludeVAT")]
        public string? DefPriceIncludeVAT { get; set; }

        [Column("DefNonVATInvoice")]
        public string? DefNonVATInvoice { get; set; }

        [Column("CreatedBy")]
        public string? CreatedBy { get; set; }

        [Column("CreatedOn")]
        public DateTime? CreatedOn { get; set; }

        [Column("ModifiedBy")]
        public string? ModifiedBy { get; set; }

        [Column("ModifiedOn")]
        public DateTime? ModifiedOn { get; set; }
    }
} 