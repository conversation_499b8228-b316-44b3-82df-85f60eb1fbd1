using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AccountingSystem.Data;
using AccountingSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public class ItemsCategoryService : IItemsCategoryService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<ItemsCategoryService> _logger;

        public ItemsCategoryService(AccountingDbContext context, ILogger<ItemsCategoryService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<ItemsCategory>> GetCategoriesAsTreeAsync()
        {
            var allCategories = await _context.ItemsCategories
                .OrderBy(c => c.Name)
                .ToListAsync();
            
            // Build the tree structure
            var lookup = allCategories.ToDictionary(c => c.Id);
            var rootCategories = new List<ItemsCategory>();

            foreach (var category in allCategories)
            {
                if (category.ParentId.HasValue && lookup.TryGetValue(category.ParentId.Value, out var parent))
                {
                    parent.Children.Add(category);
                }
                else
                {
                    rootCategories.Add(category);
                }
            }
            return rootCategories;
        }

        public async Task<ItemsCategory> GetCategoryByIdAsync(int id)
        {
            return await _context.ItemsCategories.FindAsync(id);
        }
        
        public async Task<int> GetNextIdAsync()
        {
            if (await _context.ItemsCategories.AnyAsync())
            {
                return await _context.ItemsCategories.MaxAsync(c => c.Id) + 1;
            }
            return 1;
        }

        public async Task<bool> CreateCategoryAsync(ItemsCategory category, string currentUser)
        {
            try
            {
                category.Id = await GetNextIdAsync();
                category.CreatedBy = currentUser;
                category.CreatedOn = DateTime.Now;

                // Determine level
                if (category.ParentId.HasValue)
                {
                    var parent = await GetCategoryByIdAsync(category.ParentId.Value);
                    category.Level = parent != null ? parent.Level + 1 : 0;
                }
                else
                {
                    category.Level = 0; // Root level
                }

                _context.ItemsCategories.Add(category);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating item category.");
                return false;
            }
        }

        public async Task<bool> UpdateCategoryAsync(ItemsCategory category, string currentUser)
        {
            var existingCategory = await _context.ItemsCategories.FindAsync(category.Id);
            if (existingCategory == null) return false;

            existingCategory.Name = category.Name;
            // ParentId change is complex and can break the tree, handle with care or disallow.
            // For now, we only allow name changes.
            existingCategory.ModifiedBy = currentUser;
            existingCategory.ModifiedOn = DateTime.Now;

            try
            {
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating item category.");
                return false;
            }
        }

        public async Task<(bool success, string errorMessage)> DeleteCategoryAsync(int id)
        {
            var category = await _context.ItemsCategories.Include(c => c.Children).FirstOrDefaultAsync(c => c.Id == id);
            if (category == null) return (false, "التصنيف غير موجود.");
            
            if (category.Children.Any())
            {
                return (false, "لا يمكن حذف هذا التصنيف لأنه يحتوي على تصنيفات فرعية.");
            }
            
            // Further checks can be added here to see if the category is used in any items.

            try
            {
                _context.ItemsCategories.Remove(category);
                await _context.SaveChangesAsync();
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting item category.");
                return (false, "حدث خطأ أثناء الحذف.");
            }
        }
    }
} 