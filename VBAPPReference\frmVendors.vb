﻿Imports System.Data.SqlClient
Imports ZXing
Public Class frmVendors
    Dim RegionSN As Int64 = 0
    Dim RegionDes As String = ""
    Dim Salesman As String = ""
    Dim ParentAccountCode As String = ""

    Private Sub frmVendors_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        ' frmCards.Enabled = True
    End Sub

    Private Sub frmStores_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        VendorsLoad()
        LoadParentAccountCode()
        ClearFields()
    End Sub

    Private Sub FillComboBoxWithStatus()
        Dim query As String = "select Vendor_Status from tblVendorStatus order by Vendor_Status"
        Dim Vendor_Status As New List(Of String)()

        Using Con As New SqlConnection(Constr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Vendor_Status.Add(category)
                        End While
                        If reader.IsClosed <> True Then
                            reader.Close()
                        End If
                    End Using

                    cmbxStatus.DataSource = Vendor_Status
                    cmbxStatus.SelectedIndex = -1

                Catch ex As Exception
                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub

    Private Sub FillComboBoxWithPaymentMethod()
        Dim query As String = "select Payment_Method from tblPaymentMethod order by Payment_Method"
        Dim Payment_Method As New List(Of String)()


        Using Con As New SqlConnection(Constr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Payment_Method.Add(category)
                        End While
                        If reader.IsClosed <> True Then
                            reader.Close()
                        End If
                    End Using

                    cmbxPaymentMethod.DataSource = Payment_Method
                    cmbxPaymentMethod.SelectedIndex = -1

                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub
    Private Sub FillComboBoxWithLocalKSA()
        Dim query As String = "select Local_KSA from tblLocalKSA order by Local_KSA"
        Dim Local_KSA As New List(Of String)()


        Using Con As New SqlConnection(Constr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Local_KSA.Add(category)
                        End While
                        If reader.IsClosed <> True Then
                            reader.Close()
                        End If
                    End Using

                    cmbxLocal.DataSource = Local_KSA
                    cmbxLocal.SelectedIndex = -1


                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub

    Private Sub FillComboBoxWithShops()
        Dim query As String = "select Shop_Text from tblShops order by Shop_Text"
        Dim Shop_Text As New List(Of String)()


        Using Con As New SqlConnection(Constr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Shop_Text.Add(category)
                        End While
                        If reader.IsClosed <> True Then
                            reader.Close()
                        End If
                    End Using

                    cmbxShop.DataSource = Shop_Text
                    cmbxShop.SelectedIndex = -1

                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub

    Sub VendorsLoad()
        Dim ds As DataSet = New DataSet
        Dim usercmd As New SqlCommand("SELECT VendorNo as [رقم المورد], VendorName as [اسم المورد], FirstName as [الاسم الأول], LastName as [الاسم الاخير], Mobile as [رقم الجوال], Phone as [رقم الهاتف], Email as [البريد الإلكتروني], StreetAddress1 as [العنوان], StreetAddress2 as [العنوان2], City as [المدينة], Region as [المنطفة], PostalCode as [الرمز البريدي], PaymentMethod as [طريقة الدفع], CreditLimit as [الحد الإئتماني], PaymentTerm as [شروط الدفع], ContactPerson as [شخص التواصل], CR as [السجل التجاري], VATRegNo as [الرقم الضريبي], Shop as [المتجر], Status as [الحالة], LocalVendor as [نوع المورد], Notes as [ملاحظات] FROM tblVendors  order by VendorNo", Con)
        Dim da As New SqlDataAdapter(usercmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Sub GetSerial()
        Try
            Dim SelectCMD As New SqlCommand("Select (Max(RootID)) + 1 as SN from tblRoots where ParentID = " & ParentID & "", Con)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader
            If reader.Read Then
                If reader.Item("SN").ToString <> "" Then
                    VendNo = Val(reader.Item("SN").ToString)
                Else
                    VendNo = 1
                End If
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If reader.IsClosed <> True Then
                reader.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub

    Function GetNextVendorNo() As Integer
        Dim cmd As New SqlCommand("SELECT ISNULL(MAX(VendorNo),0) + 1 FROM tblRoots where ParentID = " & ParentID & "", Con)
        If Con.State <> ConnectionState.Open Then Con.Open()
        Dim nextNo As Integer = cmd.ExecuteScalar()
        Con.Close()
        Return nextNo
    End Function
    Sub CheckRegionBySN()
        Try
            Dim SelectCMD As New SqlCommand("Select RegionDescription from tblRegions where SN = " & Val(RegionSN) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader
            If reader.Read Then
                RegionDes = Trim(reader.Item(0).ToString)
            Else
                RegionDes = ""
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If reader.IsClosed <> True Then
                reader.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Sub CheckRegionByDesc()
        Try
            Dim SelectCMD As New SqlCommand("Select SN from tblRegions where RegionDescription = '" & Trim(RegionDes) & "'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader
            If reader.Read Then
                RegionSN = Val(reader.Item(0).ToString)
            Else
                RegionSN = 0
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If reader.IsClosed <> True Then
                reader.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Sub CheckEmpByName()
        Try
            Dim SelectCMD As New SqlCommand("Select EmpNo from tblEmployees where Emp_Name = '" & Trim(Salesman) & "'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader
            If reader.Read Then
                EmpNo = Val(reader.Item(0).ToString)
            Else
                EmpNo = 0
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If reader.IsClosed <> True Then
                reader.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Sub CheckEmpByNo()
        Try
            Dim SelectCMD As New SqlCommand("Select Emp_Name from tblEmployees where EmpNo = " & Val(EmpNo) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader
            If reader.Read Then
                Salesman = Trim(reader.Item(0).ToString)
            Else
                Salesman = ""
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If reader.IsClosed <> True Then
                reader.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Function GenerateNextVendorAccountCode(parentCode As String) As String
        Dim nextSegment As String = "000001"
        Try
            Dim cmd As New SqlCommand("SELECT MAX(CAST(SUBSTRING(AccountCode, @StartIndex, 6) AS INT)) FROM tbl_Acc_Accounts WHERE ParentAccountCode = @ParentCode", Con)
            cmd.Parameters.AddWithValue("@ParentCode", parentCode)
            cmd.Parameters.AddWithValue("@StartIndex", parentCode.Length + 1)
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim result = cmd.ExecuteScalar()
            If result IsNot DBNull.Value AndAlso result IsNot Nothing Then
                nextSegment = (CInt(result) + 1).ToString("D6")
            End If
            Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في توليد كود الحساب: " & ex.Message)
            Con.Close()
        End Try
        Return parentCode & nextSegment
    End Function

    Sub InsertVendorAccount(vendorName As String, ByRef generatedAccountCode As String)
        generatedAccountCode = GenerateNextVendorAccountCode(ParentAccountCode)
        Dim cmd As New SqlCommand("INSERT INTO tbl_Acc_Accounts (AccountCode, SegmentCode, AccountName, ParentAccountCode, AccountLevel, IsPosting, AccountType, AccountNature, OpeningBalance, Notes, CreatedBy, CreatedOn) VALUES (@AccountCode, @SegmentCode, @AccountName, @ParentAccountCode, @AccountLevel, @IsPosting, @AccountType, @AccountNature, @OpeningBalance, @Notes, @CreatedBy, GETDATE())", Con)
        cmd.Parameters.AddWithValue("@AccountCode", generatedAccountCode)
        cmd.Parameters.AddWithValue("@SegmentCode", generatedAccountCode.Substring(ParentAccountCode.Length))
        cmd.Parameters.AddWithValue("@AccountName", vendorName)
        cmd.Parameters.AddWithValue("@ParentAccountCode", ParentAccountCode)
        cmd.Parameters.AddWithValue("@AccountLevel", generatedAccountCode.Split("."c).Length)
        cmd.Parameters.AddWithValue("@IsPosting", True)
        cmd.Parameters.AddWithValue("@AccountType", "Liability")
        cmd.Parameters.AddWithValue("@AccountNature", "Credit")
        cmd.Parameters.AddWithValue("@OpeningBalance", 0)
        cmd.Parameters.AddWithValue("@Notes", "حساب آلي للمورد")
        cmd.Parameters.AddWithValue("@CreatedBy", UserName)

        If Con.State <> ConnectionState.Open Then Con.Open()
        cmd.ExecuteNonQuery()
        Con.Close()
    End Sub

    Sub UpdateVendorAccount(vendorName As String, accountCode As String)
        Dim cmd As New SqlCommand("UPDATE tbl_Acc_Accounts SET AccountName = @AccountName, ModifiedBy = @ModifiedBy, ModifiedOn = GETDATE() WHERE AccountCode = @AccountCode", Con)
        cmd.Parameters.AddWithValue("@AccountName", vendorName)
        cmd.Parameters.AddWithValue("@ModifiedBy", UserName)
        cmd.Parameters.AddWithValue("@AccountCode", accountCode)

        If Con.State <> ConnectionState.Open Then Con.Open()
        cmd.ExecuteNonQuery()
        Con.Close()
    End Sub

    Sub LoadParentAccountCode()
        Try
            Dim SelectCMD As New SqlCommand("SELECT AccountNo FROM tblGLConfig WHERE EntryReferenceModule = 'موردون'", Con)
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read() Then
                ParentAccountCode = reader("AccountNo").ToString()
            End If
            reader.Close()
            Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل الحساب الرئيسي للموردين: " & ex.Message)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If txtVendorNo.Text = "" Then
            Dim newAccountCode As String = ""
            InsertVendorAccount(txtVendorName.Text.Trim(), newAccountCode)

            Dim insertCmd As New SqlCommand("INSERT INTO tblVendors (VendorNo, VendorName, FirstName, LastName, Mobile, Phone, Email, StreetAddress1, StreetAddress2, City, Region, PostalCode, PaymentMethod, CreditLimit, PaymentTerm, ContactPerson, CR, VATRegNo,Shop , Status, LocalVendor, Notes, CreatedBy, CreatedOn) VALUES (@VendorNo, @VendorName, @FirstName, @LastName, @Mobile, @Phone, @Email, @StreetAddress1, @StreetAddress2, @City, @Region, @PostalCode, @PaymentMethod, @CreditLimit, @PaymentTerm, @ContactPerson, @CR, @VATRegNo, @Shop, @Status, @LocalVendor, @Notes, @CreatedBy, GETDATE())", Con)
            insertCmd.Parameters.AddWithValue("@VendorNo", newAccountCode)
            insertCmd.Parameters.AddWithValue("@VendorName", txtVendorName.Text.Trim())
            insertCmd.Parameters.AddWithValue("@FirstName", txtFirstName.Text.Trim())
            insertCmd.Parameters.AddWithValue("@LastName", txtLastName.Text.Trim())
            insertCmd.Parameters.AddWithValue("@Mobile", txtMobile.Text.Trim())
            insertCmd.Parameters.AddWithValue("@Phone", txtPhone.Text.Trim())
            insertCmd.Parameters.AddWithValue("@Email", txtEmail.Text.Trim())
            insertCmd.Parameters.AddWithValue("@StreetAddress1", txtStreetAddress1.Text.Trim())
            insertCmd.Parameters.AddWithValue("@StreetAddress2", txtStreetAddress2.Text.Trim())
            insertCmd.Parameters.AddWithValue("@City", txtCity.Text.Trim())
            insertCmd.Parameters.AddWithValue("@Region", txtRegion.Text.Trim())
            insertCmd.Parameters.AddWithValue("@PostalCode", txtPostalCode.Text.Trim())
            insertCmd.Parameters.AddWithValue("@PaymentMethod", cmbxPaymentMethod.Text.Trim())
            insertCmd.Parameters.AddWithValue("@CreditLimit", Val(txtCreditLimit.Text))
            insertCmd.Parameters.AddWithValue("@PaymentTerm", txtPaymentTerm.Text.Trim())
            insertCmd.Parameters.AddWithValue("@ContactPerson", txtContacts.Text.Trim())
            insertCmd.Parameters.AddWithValue("@CR", txtCR.Text.Trim())
            insertCmd.Parameters.AddWithValue("@VATRegNo", txtVATReg.Text.Trim())
            insertCmd.Parameters.AddWithValue("@Shop", cmbxShop.Text.Trim())
            insertCmd.Parameters.AddWithValue("@Status", cmbxStatus.Text.Trim())
            insertCmd.Parameters.AddWithValue("@LocalVendor", cmbxLocal.Text.Trim())
            insertCmd.Parameters.AddWithValue("@Notes", txtNotes.Text.Trim())
            insertCmd.Parameters.AddWithValue("@CreatedBy", UserName)


            If Con.State <> ConnectionState.Open Then Con.Open()
            insertCmd.ExecuteNonQuery()
            Con.Close()
            MsgBox("تم إضافة المورد بنجاح", MsgBoxStyle.Information, "نظام السلطان")

        Else
            Dim updateCmd As New SqlCommand("UPDATE tblVendors SET VendorName=@VendorName, FirstName=@FirstName, LastName=@LastName, Mobile=@Mobile, Phone=@Phone, Email=@Email, StreetAddress1=@StreetAddress1, StreetAddress2=@StreetAddress2, City=@City, Region=@Region, PostalCode=@PostalCode, PaymentMethod=@PaymentMethod, CreditLimit=@CreditLimit, PaymentTerm=@PaymentTerm, ContactPerson=@ContactPerson, CR=@CR, VATRegNo=@VATRegNo, Shop=@Shop, Status=@Status, LocalVendor=@LocalVendor, Notes=@Notes, ModifiedBy=@ModifiedBy, ModifiedOn=GETDATE() WHERE VendorNo=@VendorNo", Con)
            updateCmd.Parameters.AddWithValue("@VendorNo", txtVendorNo.Text)
            updateCmd.Parameters.AddWithValue("@VendorName", txtVendorName.Text.Trim())
            updateCmd.Parameters.AddWithValue("@FirstName", txtFirstName.Text.Trim())
            updateCmd.Parameters.AddWithValue("@LastName", txtLastName.Text.Trim())
            updateCmd.Parameters.AddWithValue("@Mobile", txtMobile.Text.Trim())
            updateCmd.Parameters.AddWithValue("@Phone", txtPhone.Text.Trim())
            updateCmd.Parameters.AddWithValue("@Email", txtEmail.Text.Trim())
            updateCmd.Parameters.AddWithValue("@StreetAddress1", txtStreetAddress1.Text.Trim())
            updateCmd.Parameters.AddWithValue("@StreetAddress2", txtStreetAddress2.Text.Trim())
            updateCmd.Parameters.AddWithValue("@City", txtCity.Text.Trim())
            updateCmd.Parameters.AddWithValue("@Region", txtRegion.Text.Trim())
            updateCmd.Parameters.AddWithValue("@PostalCode", txtPostalCode.Text.Trim())
            updateCmd.Parameters.AddWithValue("@PaymentMethod", cmbxPaymentMethod.Text.Trim())
            updateCmd.Parameters.AddWithValue("@CreditLimit", Val(txtCreditLimit.Text))
            updateCmd.Parameters.AddWithValue("@PaymentTerm", txtPaymentTerm.Text.Trim())
            updateCmd.Parameters.AddWithValue("@ContactPerson", txtContacts.Text.Trim())
            updateCmd.Parameters.AddWithValue("@CR", txtCR.Text.Trim())
            updateCmd.Parameters.AddWithValue("@VATRegNo", txtVATReg.Text.Trim())
            updateCmd.Parameters.AddWithValue("@Shop", cmbxShop.Text.Trim())
            updateCmd.Parameters.AddWithValue("@Status", cmbxStatus.Text.Trim())
            updateCmd.Parameters.AddWithValue("@LocalVendor", cmbxLocal.Text.Trim())
            updateCmd.Parameters.AddWithValue("@Notes", txtNotes.Text.Trim())
            updateCmd.Parameters.AddWithValue("@ModifiedBy", UserName)

            If Con.State <> ConnectionState.Open Then Con.Open()
            updateCmd.ExecuteNonQuery()
            Con.Close()

            ' تحديث الحساب في الدليل المحاسبي
            UpdateVendorAccount(txtVendorName.Text.Trim(), txtVendorNo.Text.Trim())
            MsgBox("تم تعديل بيانات المورد بنجاح", MsgBoxStyle.Information, "نظام السلطان")

        End If

        VendorsLoad()
        ClearFields()
    End Sub


    Dim EntryCheck As Int64 = 0

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If txtVendorNo.Text <> "" Then
            Dim accountCode As String = ""
            Dim checkCmd As New SqlCommand("SELECT COUNT(*) FROM tblGLTrx WHERE AccountNo = @VendorNo", Con)
            checkCmd.Parameters.AddWithValue("@VendorNo", txtVendorNo.Text)
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim result = checkCmd.ExecuteScalar()
            If result IsNot Nothing Then accountCode = result.ToString()
            Con.Close()

            If result > 0 Then
                MessageBox.Show("لا يمكن حذف المورد لوجود حركات مالية مرتبطة به.")
                Exit Sub
            End If

            If MessageBox.Show("هل أنت متأكد من حذف المورد؟", "تأكيد الحذف", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                Dim delCmd As New SqlCommand("DELETE FROM tblVendors WHERE VendorNo = @VendorNo", Con)
                delCmd.Parameters.AddWithValue("@VendorNo", txtVendorNo.Text)
                accountCode = txtVendorNo.Text

                ' الحذف من دليل الحسابات
                Dim deleteAccountCmd As New SqlCommand("DELETE FROM tbl_Acc_Accounts WHERE AccountCode = @AccountCode", Con)
                deleteAccountCmd.Parameters.AddWithValue("@AccountCode", accountCode)


                If Con.State <> ConnectionState.Open Then Con.Open()
                delCmd.ExecuteNonQuery()
                deleteAccountCmd.ExecuteNonQuery()
                Con.Close()

                MessageBox.Show("تم حذف المورد بنجاح")
                VendorsLoad()
                ClearFields()
            End If
        End If
    End Sub
    Function GenerateVendorNumber() As Integer
        Dim cmd As New SqlCommand("SELECT ISNULL(MAX(VendorNo),0) + 1 FROM tblVendors", Con)
        If Con.State <> ConnectionState.Open Then Con.Open()
        Dim nextNo As Integer = cmd.ExecuteScalar()
        Con.Close()
        Return nextNo
    End Function
    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        ClearFields()
    End Sub
    'Private Sub DataGridView1_CellClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellClick
    '    If DataGridView1.SelectedRows.Count - 1 >= 0 Then
    '        txtVendorNo.Text = DataGridView1.SelectedRows(0).Cells(0).Value
    '    End If
    'End Sub
    Private Sub DataGridView1_CellDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If Val(DataGridView1.CurrentRow.Cells(0).Value) <> 0 Then
            txtVendorNo.Text = DataGridView1.CurrentRow.Cells(0).Value
        End If
    End Sub
    Sub ClearFields()
        txtVendorNo.Clear()
        txtVendorName.Clear()
        txtFirstName.Clear()
        txtLastName.Clear()
        txtPhone.Clear()
        txtMobile.Clear()
        txtEmail.Clear()
        txtStreetAddress1.Clear()
        txtStreetAddress2.Clear()
        txtCity.Clear()
        txtPaymentTerm.Clear()
        txtContacts.Clear()
        txtCR.Clear()
        txtVATReg.Clear()
        txtRegion.Clear()
        txtPostalCode.Clear()
        txtCreditLimit.Clear()
        txtNotes.Clear()
        cmbxPaymentMethod.SelectedIndex = -1
        cmbxShop.SelectedIndex = -1
        cmbxStatus.SelectedIndex = -1
        cmbxLocal.SelectedIndex = -1
        txtVendorName.Focus()
        FillComboBoxWithLocalKSA()
        FillComboBoxWithPaymentMethod()
        FillComboBoxWithShops()
        FillComboBoxWithStatus()
    End Sub

    Private Sub txtVendorNo_TextChanged(sender As Object, e As EventArgs) Handles txtVendorNo.TextChanged
        Try
            If Val(txtVendorNo.Text) <> 0 Then
                Dim SearchCMD As New SqlCommand("select * from tblVendors where VendorNo = " & Val(txtVendorNo.Text) & " ", Con)

                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                Dim reader As SqlDataReader = SearchCMD.ExecuteReader
                If reader.Read Then
                    txtVendorName.Text = reader("VendorName").ToString()
                    txtFirstName.Text = reader("FirstName").ToString()
                    txtLastName.Text = reader("LastName").ToString()
                    txtMobile.Text = reader("Mobile").ToString()
                    txtPhone.Text = reader("Phone").ToString()
                    txtEmail.Text = reader("Email").ToString()
                    txtStreetAddress1.Text = reader("StreetAddress1").ToString()
                    txtStreetAddress2.Text = reader("StreetAddress2").ToString()
                    txtCity.Text = reader("City").ToString()
                    txtRegion.Text = reader("Region").ToString()
                    txtPostalCode.Text = reader("PostalCode").ToString()
                    txtCreditLimit.Text = reader("CreditLimit").ToString()
                    txtPaymentTerm.Text = reader("PaymentTerm").ToString()
                    txtContacts.Text = reader("ContactPerson").ToString()
                    txtCR.Text = reader("CR").ToString()
                    txtVATReg.Text = reader("VATRegNo").ToString()
                    cmbxShop.Text = reader("Shop").ToString()
                    cmbxStatus.Text = reader("Status").ToString()
                    cmbxLocal.Text = reader("LocalVendor").ToString()
                    txtNotes.Text = reader("Notes").ToString()
                    cmbxPaymentMethod.Text = reader("PaymentMethod").ToString()


                Else
                    ClearFields()
                End If
                If reader.IsClosed Then
                Else
                    reader.Close()
                End If
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If
        Catch ex As Exception
            Exit Sub
        End Try
    End Sub
    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        SearchBox()
    End Sub
    Sub SearchBox()
        Dim keyword As String = txtSearch.Text.Trim()
        If keyword = "" Then
            VendorsLoad()
            Return
        End If

        Dim ds As DataSet = New DataSet
        Dim cmd As New SqlClient.SqlCommand("SELECT VendorNo as [رقم المورد], VendorName as [اسم المورد], FirstName as [الاسم الأول], LastName as [الاسم الاخير], Mobile as [رقم الجوال], Phone as [رقم الهاتف], Email as [البريد الإلكتروني], StreetAddress1 as [العنوان], StreetAddress2 as [العنوان2], City as [المدينة], Region as [المنطفة], PostalCode as [الرمز البريدي], PaymentMethod as [طريقة الدفع], CreditLimit as [الحد الإئتماني], PaymentTerm as [شروط الدفع], ContactPerson as [شخص التواصل], CR as [السجل التجاري], VATRegNo as [الرقم الضريبي], Shop as [المتجر], Status as [الحالة], LocalVendor as [نوع المورد], Notes as [ملاحظات] FROM tblVendors where CAST(VendorNo AS NVARCHAR) like @kw or VendorName LIKE @kw or FirstName LIKE @kw or LastName LIKE @kw order by VendorNo", New SqlClient.SqlConnection(Constr))
        cmd.Parameters.AddWithValue("@kw", "%" & keyword & "%")
        Dim da As New SqlClient.SqlDataAdapter(cmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub


End Class