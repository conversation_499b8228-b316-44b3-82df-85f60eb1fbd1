﻿Imports System.Data.SqlClient
Module connection

    'Public ConStr As String = "Server=" + My.Settings.server + ";Database=" + My.Settings.database + ";User Id=" + My.Settings.id + ";Password=" + My.Settings.passowrd + ";TrustServerCertificate=True"
    'Public ConStr As String = "Server=174.138.185.119,1433;Database=SULTDB;User Id=sa;Password=******;TrustServerCertificate=True"

    Public ConStr As String = ""

    'Public ConStr As String = "Data Source= LAPTOP\ALSULTAN;Initial Catalog = Sales_SystemDB;Integrated Security = True"

    Public Con As New SqlClient.SqlConnection(ConStr)
    'Public mode As String = My.Settings.model

    Public Sub connection()



        'If My.Settings.model <> "SQL" Then

        '    'ConStr = "Data Source= " + My.Settings.server + ";Initial Catalog = " + My.Settings.database + ";Integrated Security = True"

        'Else

        '    'ConStr = "Data Source= " + My.Settings.server + ";Initial Catalog = " + My.Settings.database + ";TrustServerCertificate=True; User ID = " + My.Settings.id + "; Password = " + My.Settings.passowrd + ""
        '    ConStr = "Server=" + My.Settings.server + ";Database=" + My.Settings.database + ";User Id=" + My.Settings.id + ";Password=" + My.Settings.passowrd + ";TrustServerCertificate=True"

        'End If

    End Sub


   


    Public Function GetMaxID(TableName, CoulmnName) As Integer

        Dim dt As New DataTable
        Dim adp As New SqlDataAdapter
        dt.Clear()
        adp = New SqlDataAdapter("select MAX(" & CoulmnName & ") from " & TableName & "", Con)
        adp.Fill(dt)

        Dim autoNumber As Integer

        If IsDBNull(dt(0)(0)) = True Then
            autoNumber = 1
        Else
            autoNumber = dt(0)(0) + 1
        End If

        Return autoNumber

    End Function

    Public Sub fillcmb_category(ByVal cmb As ComboBox)
        Dim DT As New DataTable
        Dim DA As New SqlDataAdapter
        DT.Clear()
        DA = New SqlDataAdapter("Select * FROM category ", Con)
        DA.Fill(DT)
        If DT.Rows.Count > 0 Then
            cmb.DataSource = DT
            cmb.DisplayMember = "CatName"
            cmb.ValueMember = "CatID"
        Else
            cmb.DataSource = Nothing
        End If
    End Sub

    Public Sub fillcmb_Units(ByVal cmb As ComboBox)
        Dim DT As New DataTable
        Dim DA As New SqlDataAdapter
        DT.Clear()
        DA = New SqlDataAdapter("Select * FROM Units ", con)
        DA.Fill(DT)
        If DT.Rows.Count > 0 Then
            cmb.DataSource = DT
            cmb.DisplayMember = "UnitName"
            cmb.ValueMember = "UnitID"
        Else
            cmb.DataSource = Nothing
        End If
    End Sub
    Public Sub fillcmb_Imprters(ByVal cmb As ComboBox)
        Dim DT As New DataTable
        Dim DA As New SqlDataAdapter
        DT.Clear()
        DA = New SqlDataAdapter("Select * FROM Imprters ", con)
        DA.Fill(DT)
        If DT.Rows.Count > 0 Then
            cmb.DataSource = DT
            cmb.DisplayMember = "ImprterName"
            cmb.ValueMember = "ImprterID"
        Else
            cmb.DataSource = Nothing
        End If
    End Sub

    
   
End Module
