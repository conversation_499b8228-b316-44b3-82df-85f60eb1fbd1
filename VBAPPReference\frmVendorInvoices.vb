﻿Imports System.Data.SqlClient
Public Class frmVendorInvoices
    Private Sub frmVendorInvoices_Load(sender As Object, e As EventArgs) Handles Me.Load
        VendorsLoad()
        ShopsLoad()
        'EmployeesLoad()
        ForceGregorianForAllPickers(Me)
        dtpFrom.Value = New DateTime(DateTime.Now.Year, DateTime.Now.Month - 1, 1) ' Set to first day of current month
    End Sub

    Sub VendorsLoad()
        Dim CMD As New SqlCommand("Select AccountCode,AccountName from tbl_Acc_Accounts where ParentAccountCode = (Select AccountNo from tblGLConfig where EntryReferenceModule = 'موردون') order by AccountCode", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountName + ' - ' + CONVERT(AccountCode, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxPartnerNo.DataSource = dt
        cmbxPartnerNo.DisplayMember = "DisplayText" ' Show Name
        cmbxPartnerNo.ValueMember = "AccountCode" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Private Sub ShopsLoad()
        Dim query As String = "select Shop_Text from tblShops order by Shop_Text"
        Dim Shop_Text As New List(Of String)()


        Using Con As New SqlConnection(Constr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Shop_Text.Add(category)
                        End While
                    End Using

                    cmbxShop.DataSource = Shop_Text
                    cmbxShop.SelectedIndex = -1

                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub


    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Try
            Dim query As String = "SELECT * FROM vw_Vendor_Items WHERE EntryDate BETWEEN @FromDate AND @ToDate"
            Dim conditions As New List(Of String)

            If Not String.IsNullOrEmpty(cmbxPartnerNo.Text) Then
                conditions.Add("PartnerNo = @PartnerNo")
            End If



            If Not String.IsNullOrEmpty(cmbxStatus.Text) Then
                conditions.Add("Status = @Status")
            End If

            If Not String.IsNullOrEmpty(cmbxShop.Text) Then
                conditions.Add("Shop = @Shop")
            End If

            If conditions.Count > 0 Then
                query &= " AND " & String.Join(" AND ", conditions)
            End If

            Using con As New SqlConnection(Constr)
                Using cmd As New SqlCommand(query, con)
                    cmd.Parameters.AddWithValue("@FromDate", dtpFrom.Value.Date)
                    cmd.Parameters.AddWithValue("@ToDate", dtpTo.Value.Date)

                    If Not String.IsNullOrEmpty(cmbxPartnerNo.Text) Then
                        cmd.Parameters.AddWithValue("@PartnerNo", cmbxPartnerNo.SelectedValue)
                    End If



                    If Not String.IsNullOrEmpty(cmbxStatus.Text) Then
                        cmd.Parameters.AddWithValue("@Status", cmbxStatus.Text)
                    End If

                    If Not String.IsNullOrEmpty(cmbxShop.Text) Then
                        cmd.Parameters.AddWithValue("@Shop", cmbxShop.Text)
                    End If

                    Dim dt As New DataTable()
                    Dim da As New SqlDataAdapter(cmd)
                    da.Fill(dt)
                    DataGridView1.DataSource = dt

                    ' Totals
                    Dim totalSales As Decimal = 0
                    Dim totalPayments As Decimal = 0
                    Dim totalOutstanding As Decimal = 0

                    For Each row As DataRow In dt.Rows
                        totalSales += Convert.ToDecimal(If(IsDBNull(row("InvoiceAmount")), 0, row("InvoiceAmount")))
                        totalPayments += Convert.ToDecimal(If(IsDBNull(row("PaidAmount")), 0, row("PaidAmount")))
                        totalOutstanding += Convert.ToDecimal(If(IsDBNull(row("OutStandingAmount")), 0, row("OutstandingAmount")))
                    Next

                    ' Assign to TextBoxes
                    txtTotalSales.Text = totalSales.ToString("N2")
                    txtTotalPayments.Text = totalPayments.ToString("N2")
                    txtTotalOpen.Text = totalOutstanding.ToString("N2")
                    txtBalances.Text = (totalSales - totalPayments).ToString("N2")
                End Using
            End Using

            ' After setting DataSource
            DataGridView1.Columns("DocumentNumber").HeaderText = "رقم المستند"
            DataGridView1.Columns("DocumentType").HeaderText = "نوع المستند"
            DataGridView1.Columns("PartnerNo").HeaderText = "رقم المورد"
            DataGridView1.Columns("PartnerName").HeaderText = "اسم المورد"
            DataGridView1.Columns("Shop").HeaderText = "المتجر"
            DataGridView1.Columns("EntryDate").HeaderText = "التاريخ"
            DataGridView1.Columns("EntryReference").HeaderText = "المرجع"
            DataGridView1.Columns("InvoiceAmount").HeaderText = "اجمالي الفاتورة"
            DataGridView1.Columns("PaidAmount").HeaderText = "المدفوع"
            DataGridView1.Columns("OutstandingAmount").HeaderText = "الرصيد المتبقي"
            DataGridView1.Columns("Status").HeaderText = "الحالة"


            ' Apply row colors
            For Each row As DataGridViewRow In DataGridView1.Rows
                If Not row.IsNewRow AndAlso Not IsDBNull(row.Cells("Status").Value) Then
                    Dim status As String = row.Cells("Status").Value.ToString().ToUpper()

                    Select Case status
                        Case "PAID"
                            row.DefaultCellStyle.BackColor = Color.LightGreen
                        Case "PARTIALLY_PAID"
                            row.DefaultCellStyle.BackColor = Color.Orange
                        Case "UNPAID"
                            row.DefaultCellStyle.BackColor = Color.LightCoral
                    End Select
                End If
            Next
        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message)
        End Try
    End Sub

    Private Sub btnCashReceipt_Click(sender As Object, e As EventArgs) Handles btnCashPayment.Click
        If DataGridView1.SelectedRows.Count = 1 Then
            If Val(DataGridView1.SelectedRows(0).Cells(0).Value.ToString()) <> 0 Then
                InvNoForPayment = Val(DataGridView1.SelectedRows(0).Cells(0).Value.ToString())
                PriceForPayment = Val(DataGridView1.SelectedRows(0).Cells(9).Value.ToString())
                frmTrxCashPay.ShowDialog()
            End If
        End If
    End Sub
    Private Sub btnAllocate_Click(sender As Object, e As EventArgs) Handles btnAllocate.Click
        If DataGridView1.SelectedRows.Count <> 2 Then
            MessageBox.Show("يرجى تحديد صفين فقط: أحدهما فاتورة أو مذكرة مدينة، والآخر دفعة.", "خطأ في التخصيص", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        Dim invoiceNo As String = ""
        Dim paymentJEID As Integer = -1

        For Each row As DataGridViewRow In DataGridView1.SelectedRows
            Dim docType As String = row.Cells("DocumentType").Value.ToString().ToUpper()
            Dim docNumber As String = row.Cells("DocumentNumber").Value.ToString()

            If docType = "INVOICE" Then
                invoiceNo = docNumber

            ElseIf docType = "MEMO" Then
                ' MEMO treated as invoice but prefixed with JE-
                If docNumber.StartsWith("JE-") Then
                    invoiceNo = docNumber.Substring(3) ' Remove "JE-"

                Else
                    MessageBox.Show("رقم مذكرة المدينة يجب أن يبدأ بـ 'JE-'.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Exit Sub
                End If

            ElseIf docType = "PAYMENT" Then
                If docNumber.StartsWith("JE-") Then
                    Dim idStr As String = docNumber.Substring(3)
                    If Integer.TryParse(idStr, paymentJEID) = False Then
                        MessageBox.Show("رقم مستند الدفع غير صالح.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                        Exit Sub
                    End If
                Else
                    MessageBox.Show("رقم مستند الدفع يجب أن يبدأ بـ 'JE-'.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Exit Sub
                End If
            End If
        Next

        If invoiceNo = "" OrElse paymentJEID = -1 Then
            MessageBox.Show("تأكد من أن أحد المستندين هو فاتورة أو مذكرة، والآخر هو دفعة.", "خطأ في التحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        Try
            Using con As New SqlConnection(Constr)
                Using cmd As New SqlCommand("sp_AllocateOpenVendorPaymentToInvoice", con)
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.Parameters.AddWithValue("@InvoiceNo", invoiceNo)
                    cmd.Parameters.AddWithValue("@PaymentJEID", paymentJEID)
                    cmd.Parameters.AddWithValue("@ModifiedBy", UserName)

                    con.Open()
                    cmd.ExecuteNonQuery()
                End Using
            End Using

            MessageBox.Show("تم التخصيص بنجاح.", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            btnSearch.PerformClick()

        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء عملية التخصيص: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click

        If DataGridView1.SelectedRows.Count = 1 Then
            If DataGridView1.SelectedRows(0).Cells(0).Value.StartsWith("JE-") Then
                MsgBox("يجب تحديد مستند من نوع فاتورة فقط", MsgBoxStyle.Critical, "نظام السلطان")
                Exit Sub
            End If
            PrintType = "PurchaseInvoice"
            If PrintType <> "" Then
                InvNoForPrint = DataGridView1.SelectedRows(0).Cells(0).Value
                frmPrintPreview.MdiParent = frmMain
                frmPrintPreview.Show()
            End If
        Else
            MsgBox("يجب تحديد سطر واحد فقط لطباعة نسخة من الفاتورة", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

    Private Sub btnPartnerSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPartnerSearch.Click
        VendorSearchForm = "frmVendorInvoices"
        frmVendorSearch.ShowDialog()
    End Sub
End Class
