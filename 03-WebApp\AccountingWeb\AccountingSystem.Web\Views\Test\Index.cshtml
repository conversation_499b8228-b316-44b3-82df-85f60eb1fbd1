@{
    ViewData["Title"] = "Application Test";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3><i class="fas fa-check-circle"></i> Application Status Test</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h4>@ViewBag.Message</h4>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Database Connectivity</h5>
                                </div>
                                <div class="card-body">
                                    <p class="@(ViewBag.DatabaseStatus.ToString().StartsWith("✅") ? "text-success" : "text-danger")">
                                        @ViewBag.DatabaseStatus
                                    </p>
                                    @if (ViewBag.DatabaseInfo != null)
                                    {
                                        <p class="text-info">@ViewBag.DatabaseInfo</p>
                                    }
                                    @if (ViewBag.ConnectionString != null)
                                    {
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <strong>Connection String:</strong><br>
                                                @ViewBag.ConnectionString
                                            </small>
                                        </div>
                                    }
                                    @if (ViewBag.ErrorDetails != null)
                                    {
                                        <div class="mt-2">
                                            <details>
                                                <summary class="text-danger">Error Details (Click to expand)</summary>
                                                <pre class="text-danger small mt-2">@ViewBag.ErrorDetails</pre>
                                            </details>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>System Information</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>Server:</strong> 174.138.185.119</p>
                                    <p><strong>Database:</strong> SULTDB</p>
                                    <p><strong>Environment:</strong> Development</p>
                                    <p><strong>Time:</strong> @DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>Next Steps:</h5>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <i class="fas fa-database text-primary"></i>
                                Database connectivity test
                                <span class="badge bg-success ms-2">Complete</span>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-user text-warning"></i>
                                Authentication system restoration
                                <span class="badge bg-warning ms-2">Pending</span>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-tachometer-alt text-info"></i>
                                Dashboard functionality
                                <span class="badge bg-info ms-2">Pending</span>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-mobile-alt text-secondary"></i>
                                UI/UX testing
                                <span class="badge bg-secondary ms-2">Pending</span>
                            </li>
                        </ul>
                    </div>

                    <div class="mt-4">
                        <a href="/Test/DatabaseTest" class="btn btn-primary">
                            <i class="fas fa-database"></i> Test Database API
                        </a>
                        <a href="/Home/Index" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Go to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .list-group-item {
        border: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .list-group-item:last-child {
        border-bottom: none;
    }
</style>
