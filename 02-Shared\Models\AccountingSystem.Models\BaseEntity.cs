using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    /// <summary>
    /// Base entity class with common properties
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }

        // Note: These properties don't exist in actual tblUsers table
        // public DateTime CreatedDate { get; set; } = DateTime.Now;
        // public DateTime? ModifiedDate { get; set; }
        // public bool IsActive { get; set; } = true;
        // [Timestamp]
        // public byte[]? RowVersion { get; set; }

        public string? CreatedBy { get; set; }
        public string? ModifiedBy { get; set; }
    }

    /// <summary>
    /// User entity matching tblUsers table structure
    /// </summary>
    public class User : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string Password { get; set; } = string.Empty; // Hashed password

        // Note: These properties don't exist in actual tblUsers table
        // [StringLength(100)]
        // public string? FullName { get; set; }

        // [StringLength(100)]
        // [EmailAddress]
        // public string? Email { get; set; }

        public int? GroupID { get; set; }

        // Note: These properties don't exist in actual tblUsers table
        // public bool IsLocked { get; set; } = false;
        // public int FailedLoginAttempts { get; set; } = 0;
        // public DateTime? LastLoginDate { get; set; }
        // public DateTime? LastPasswordChange { get; set; }

        // POS-specific fields matching actual database schema
        [StringLength(50)]
        public string? DefaultStore { get; set; }

        public bool StoreChange { get; set; } = true;

        public long? DefaultCustomer { get; set; }

        public bool CustomerChange { get; set; } = true;

        public int? DefaultCashier { get; set; }

        public bool CashierChange { get; set; } = true;

        public bool ChangeInvoicePrice { get; set; } = false;

        [Column(TypeName = "decimal(5,2)")]
        public decimal? MaxDiscountPercent { get; set; }

        // Navigation properties
        public virtual UserGroup? Group { get; set; }
        public virtual ICollection<SessionLog> SessionLogs { get; set; } = new List<SessionLog>();
    }

    /// <summary>
    /// User group entity matching tblGroupsAuth table structure
    /// </summary>
    [Table("tblGroupsAuth")]
    public class UserGroup // : BaseEntity (remove inheritance if Id is only in BaseEntity)
    {
        [Key]
        [Column("GroupID")]
        public int GroupID { get; set; }

        [Required]
        [StringLength(100)]
        public string GroupName { get; set; } = string.Empty;

        // Note: Description column does not exist in actual tblGroupsAuth table
        // [StringLength(500)]
        // public string? Description { get; set; }

        // Navigation properties
        public virtual ICollection<User> Users { get; set; } = new List<User>();
        public virtual ICollection<GroupPermission> Permissions { get; set; } = new List<GroupPermission>();

        // Remove or mark Id as [NotMapped] if inherited from BaseEntity
        [NotMapped]
        public int Id { get; set; }
    }

    /// <summary>
    /// Group permissions for role-based access control
    /// </summary>
    public class GroupPermission : BaseEntity
    {
        public int GroupID { get; set; }

        [Required]
        [StringLength(100)]
        public string PermissionName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ModuleName { get; set; } = string.Empty;

        public bool CanView { get; set; } = false;
        public bool CanAdd { get; set; } = false;
        public bool CanEdit { get; set; } = false;
        public bool CanDelete { get; set; } = false;
        public bool CanPrint { get; set; } = false;

        // Navigation properties
        public virtual UserGroup Group { get; set; } = null!;
    }

    /// <summary>
    /// Session logging entity for tracking user sessions
    /// </summary>
    public class SessionLog : BaseEntity
    {
        public int SessionID { get; set; }

        [StringLength(50)]
        public string? Username { get; set; }

        [Required]
        [StringLength(20)]
        public string LoginStatus { get; set; } = string.Empty; // SUCCESS, FAILED

        [StringLength(100)]
        public string? MachineName { get; set; }

        [StringLength(100)]
        public string? MachineUser { get; set; }

        [StringLength(50)]
        public string? SystemUsername { get; set; }

        [StringLength(45)]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        [StringLength(255)]
        public string? SessionToken { get; set; }

        public DateTime? LoginTime { get; set; } = DateTime.Now;

        public DateTime? LogoutTime { get; set; }

        [StringLength(500)]
        public string? FailureReason { get; set; }

        // Note: IsActive property doesn't exist in actual tblSessionLogs table
        // public new bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual User? User { get; set; }
    }



    /// <summary>
    /// Example Account entity for Chart of Accounts
    /// </summary>
    public class Account : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string AccountNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string AccountName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string AccountType { get; set; } = string.Empty; // Asset, Liability, Equity, Revenue, Expense

        public int? ParentAccountId { get; set; }

        public decimal Balance { get; set; }

        public bool IsSystemAccount { get; set; }

        // Navigation properties
        public virtual Account? ParentAccount { get; set; }
        public virtual ICollection<Account> SubAccounts { get; set; } = new List<Account>();
        public virtual ICollection<Transaction> DebitTransactions { get; set; } = new List<Transaction>();
        public virtual ICollection<Transaction> CreditTransactions { get; set; } = new List<Transaction>();
    }

    /// <summary>
    /// Example Transaction entity for Journal Entries
    /// </summary>
    public class Transaction : BaseEntity
    {
        public DateTime? TransactionDate { get; set; }

        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        [StringLength(50)]
        public string? ReferenceNumber { get; set; }

        public int DebitAccountId { get; set; }

        public int CreditAccountId { get; set; }

        [Range(0.01, double.MaxValue)]
        public decimal Amount { get; set; }

        // Navigation properties
        public virtual Account DebitAccount { get; set; } = null!;
        public virtual Account CreditAccount { get; set; } = null!;
    }

    /// <summary>
    /// Example Invoice entity
    /// </summary>
    public class Invoice : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        public DateTime? InvoiceDate { get; set; }

        public DateTime? DueDate { get; set; }

        public int CustomerId { get; set; }

        public decimal SubTotal { get; set; }

        public decimal TaxAmount { get; set; }

        public decimal TotalAmount { get; set; }

        public decimal PaidAmount { get; set; }

        public decimal BalanceAmount { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Sent, Paid, Overdue, Cancelled

        [StringLength(500)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual Customer Customer { get; set; } = null!;
        public virtual ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();
    }

    /// <summary>
    /// Example Invoice Item entity
    /// </summary>
    public class InvoiceItem : BaseEntity
    {
        public int InvoiceId { get; set; }

        [Required]
        [StringLength(100)]
        public string Description { get; set; } = string.Empty;

        public decimal Quantity { get; set; }

        public decimal UnitPrice { get; set; }

        public decimal LineTotal { get; set; }

        // Navigation properties
        public virtual Invoice Invoice { get; set; } = null!;
    }

    /// <summary>
    /// Form permission entity matching tblForms table structure
    /// </summary>
    public class FormPermission : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string FormName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? FormTitle { get; set; }

        [StringLength(100)]
        public string? ModuleName { get; set; }

        // Navigation properties
        public virtual ICollection<GroupFormPermission> GroupPermissions { get; set; } = new List<GroupFormPermission>();
    }

    /// <summary>
    /// Group form permission entity matching tblGroupFormPermissions table structure
    /// </summary>
    public class GroupFormPermission : BaseEntity
    {
        public int GroupID { get; set; }

        public int FormID { get; set; }

        public bool CanView { get; set; } = true;
        public bool CanAdd { get; set; } = false;
        public bool CanEdit { get; set; } = false;
        public bool CanDelete { get; set; } = false;
        public bool CanPrint { get; set; } = false;

        // Navigation properties
        public virtual FormPermission Form { get; set; } = null!;
    }

    /// <summary>
    /// System Configuration Entity
    /// </summary>
    public class SystemConfig
    {
        public int Id { get; set; }
        public string ConfigKey { get; set; } = string.Empty;
        public string ConfigValue { get; set; } = string.Empty;
        public string? Description { get; set; }
        public DateTime? CreatedDate { get; set; } = DateTime.Now;
        public DateTime? ModifiedDate { get; set; }
        public string? CreatedBy { get; set; }
        public string? ModifiedBy { get; set; }
    }

    /// <summary>
    /// GL Configuration Entity (matching tblGLConfig)
    /// </summary>
    public class GLConfig
    {
        public string EntryReferenceModule { get; set; } = string.Empty;
        public int AccountNo { get; set; }
        // Note: Description and IsActive properties are ignored as they don't exist in the actual database table
        // The actual table has: EntryReferenceModule, AccountNo, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    }
}
