﻿Imports System.IO
Imports System.Text
Imports System.Convert
Imports System.Drawing.Imaging
Imports QRCoder


Public Class frmTest

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click

        sellername = TextBox1.Text
        vatregistration = TextBox2.Text
        timestamp = TextBox3.Text
        invoiceamount = TextBox4.Text
        vatAmount = TextBox5.Text

        Dim saudiConvertion = New SaudiConvertion()
        Dim QRString = saudiConvertion.GetData()
        TextBox6.Text = QRString

        Dim gen As New QRCodeGenerator
        Dim qrCodeData = gen.CreateQrCode(QRString, QRCodeGenerator.ECCLevel.Q)
        Dim code As New QRCode(qrCodeData)
        Dim qrCodeImage As Bitmap = New Bitmap(code.GetGraphic(20))
        PictureBox1.Image = code.GetGraphic(20)
        Dim imageData As Byte() = ImageToByteArray(qrCodeImage)


    End Sub

    Private Sub btnCopy_Click(sender As Object, e As EventArgs) Handles btnCopy.Click
        If PictureBox1.Image IsNot Nothing Then
            Clipboard.SetImage(PictureBox1.Image)
            MessageBox.Show("Image copied to clipboard.")
        Else
            MessageBox.Show("No image to copy.")
        End If

    End Sub





End Class