﻿' VB.NET Utility Library - مركزية وظائف مساعدة لمشاريعك

Imports System.Drawing
Imports System.IO
Imports System.Text
Imports QRCoder
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared

Public Module CommonFunctions

    ' تعريف هيكل الإدخال المحاسبي
    Public Class JournalEntry
        Public Property AccountNo As String
        Public Property Amount As Decimal
        Public Property DRCR As String ' "DR" or "CR"
    End Class

    ' إدخال سطر محاسبي
    Public Sub InsertJEEntry(jesn <PERSON>, accountNo As String, amount As Decimal, drcr As String)
        Dim cmd As New SqlClient.SqlCommand("INSERT INTO tblGLTrx (JESN, AccountNo, Amount, DRCR) VALUES (@HeaderID, @AccountNo, @Amount, @DRCR)", New SqlClient.SqlConnection(ConStr))
        cmd.Connection.Open()
        cmd.Parameters.AddWithValue("@HeaderID", jesn)
        cmd.Parameters.AddWithValue("@AccountNo", accountNo)
        cmd.Parameters.AddWithValue("@Amount", amount)
        cmd.Parameters.AddWithValue("@DRCR", drcr)
        cmd.ExecuteNonQuery()
        cmd.Connection.Close()
    End Sub

    ' تحميل خريطة الحسابات
    Public Function LoadGLAccounts() As Dictionary(Of String, String)
        Dim accounts As New Dictionary(Of String, String)
        Using conn As New SqlClient.SqlConnection(ConStr)
            conn.Open()
            Dim cmd As New SqlClient.SqlCommand("SELECT EntryReferenceModule, AccountNo FROM tblGLConfig", conn)
            Using rdr = cmd.ExecuteReader()
                While rdr.Read()
                    accounts(rdr("EntryReferenceModule").ToString()) = rdr("AccountNo").ToString()
                End While
            End Using
        End Using
        Return accounts
    End Function

    ' التحقق من وجود قيد محاسبي مسبقًا
    Public Function CheckIfJEExists(trxNo As Long, refType As String) As Boolean
        Using cmd As New SqlClient.SqlCommand("SELECT COUNT(*) FROM tblJEHeader WHERE ReferenceTrx = @Trx AND ReferenceType = @RefType", New SqlClient.SqlConnection(ConStr))
            cmd.Parameters.AddWithValue("@Trx", trxNo)
            cmd.Parameters.AddWithValue("@RefType", refType)
            cmd.Connection.Open()
            Dim result As Integer = CInt(cmd.ExecuteScalar())
            Return result > 0
        End Using
    End Function

    ' التحقق من توازن القيد قبل الإدخال
    Public Function IsJournalBalanced(entries As List(Of JournalEntry)) As Boolean
        Dim totalDr As Decimal = entries.Where(Function(e) e.DRCR = "DR").Sum(Function(e) e.Amount)
        Dim totalCr As Decimal = entries.Where(Function(e) e.DRCR = "CR").Sum(Function(e) e.Amount)
        Return Math.Round(totalDr, 2) = Math.Round(totalCr, 2)
    End Function

    ' إنشاء قيد محاسبي شامل المبيعات والخصم والضريبة
    'Public Sub CreateJEHeaderAndEntry(trxNo As Long, totalAmount As Decimal, Optional discountAmount As Decimal = 0, Optional vatAmountInput As Decimal = 0, Optional grossSalesBeforeDiscount As Decimal = 0)
    '    If CheckIfJEExists(trxNo, "مبيعات") Then Exit Sub

    '    Dim JESN As Long
    '    Dim paymentType As String = "نقدي"
    '    Dim isCash As Boolean = (paymentType = "نقدي")
    '    Dim accountMap As Dictionary(Of String, String) = LoadGLAccounts()
    '    Dim customerAccount As String = If(isCash, accountMap("نقدية"), accountMap("عملاء"))

    '    Dim insertSQL As String = "INSERT INTO tblJEHeader (TrxDate, Amount, Reference, ReferenceTrx, ReferenceType, PostStatus, CreatedBy, CreatedOn) " &
    '        "VALUES (GETDATE(), @Amt, @Ref, @Trx, 'مبيعات', 'Open', @User, GETDATE()); SELECT SCOPE_IDENTITY()"
    '    Dim insertCmd As New SqlClient.SqlCommand(insertSQL, New SqlClient.SqlConnection(ConStr))
    '    insertCmd.Connection.Open()
    '    insertCmd.Parameters.AddWithValue("@Amt", totalAmount)
    '    insertCmd.Parameters.AddWithValue("@Ref", "فاتورة مبيعات - " & trxNo)
    '    insertCmd.Parameters.AddWithValue("@Trx", trxNo)
    '    insertCmd.Parameters.AddWithValue("@User", "System")
    '    JESN = Val(insertCmd.ExecuteScalar())
    '    insertCmd.Connection.Close()

    '    ' تحميل تكلفة البضاعة المباعة
    '    Dim itemsSQL As String = "SELECT T.ItemType, T.UnitPurchasePrice, (-I.TrxQTY) AS TrxQTY, (T.UnitPurchasePrice * I.TrxQTY) AS PurchasePrice " &
    '        "FROM tblStockMovement I INNER JOIN tblItems T ON I.ItemNo = T.ItemNo WHERE I.DocNo = @Trx"
    '    Dim itemsCmd As New SqlClient.SqlCommand(itemsSQL, New SqlClient.SqlConnection(ConStr))
    '    itemsCmd.Parameters.AddWithValue("@Trx", trxNo)

    '    Dim journalEntries As New List(Of JournalEntry)
    '    itemsCmd.Connection.Open()
    '    Using rdr As SqlClient.SqlDataReader = itemsCmd.ExecuteReader()
    '        While rdr.Read()
    '            Dim itemType As String = rdr("ItemType").ToString()
    '            Dim itemCost As Decimal = Val(rdr("PurchasePrice"))

    '            If itemType = "مخزني" Then
    '                journalEntries.Add(New JournalEntry With {
    '                    .AccountNo = accountMap("تكلفة المبيعات"),
    '                    .Amount = itemCost,
    '                    .DRCR = "DR"
    '                })
    '                journalEntries.Add(New JournalEntry With {
    '                    .AccountNo = accountMap("المخزون"),
    '                    .Amount = itemCost,
    '                    .DRCR = "CR"
    '                })
    '            End If
    '        End While
    '    End Using
    '    itemsCmd.Connection.Close()

    '    ' ✅ قيد المبيعات = صافي المبيعات + الخصم (أي قبل الضريبة)
    '    Dim netSalesBeforeVAT As Decimal = totalAmount - vatAmountInput

    '    journalEntries.Add(New JournalEntry With {
    '        .AccountNo = accountMap("مبيعات"),
    '        .Amount = netSalesBeforeVAT + discountAmount,
    '        .DRCR = "CR"
    '    })

    '    ' ✅ الخصم (إن وجد)
    '    If discountAmount > 0 Then
    '        journalEntries.Add(New JournalEntry With {
    '            .AccountNo = accountMap("خصم مبيعات"),
    '            .Amount = discountAmount,
    '            .DRCR = "DR"
    '        })
    '    End If

    '    ' ✅ الضريبة
    '    If vatAmountInput > 0 Then
    '        journalEntries.Add(New JournalEntry With {
    '            .AccountNo = accountMap("القيمة المضافة المحصلة"),
    '            .Amount = vatAmountInput,
    '            .DRCR = "CR"
    '        })
    '    End If

    '    ' ✅ العميل / الصندوق
    '    journalEntries.Add(New JournalEntry With {
    '        .AccountNo = customerAccount,
    '        .Amount = totalAmount,
    '        .DRCR = "DR"
    '    })

    '    ' ✅ التحقق من توازن القيد
    '    If Not IsJournalBalanced(journalEntries) Then
    '        Throw New Exception("خطأ: القيد غير متوازن، يرجى مراجعة المدين والدائن")
    '    End If

    '    ' تنفيذ جميع القيود
    '    For Each entry In journalEntries
    '        InsertJEEntry(JESN, entry.AccountNo, entry.Amount, entry.DRCR)
    '    Next
    'End Sub


    ' عرض نتائج البحث كأزرار في لوحة الأصناف
    Public Sub SearchItemsAndDisplay(panel As Control, keyword As String, clickHandler As EventHandler)
        panel.Controls.Clear()

        Dim dt As New DataTable()
        Dim cmd As New SqlClient.SqlCommand("
        SELECT ItemNo, ItemDescription 
        FROM tblItems 
        WHERE ItemNo LIKE @KW OR ItemDescription LIKE @KW
    ", New SqlClient.SqlConnection(ConStr))
        cmd.Parameters.AddWithValue("@KW", "%" & keyword & "%")
        Dim da As New SqlClient.SqlDataAdapter(cmd)
        da.Fill(dt)

        For Each row As DataRow In dt.Rows
            Dim btn As New Button()
            btn.Text = row("ItemNo").ToString() & " " & row("ItemDescription").ToString()
            btn.Tag = row("ItemNo")
            btn.Width = 160
            btn.Height = 50
            btn.BackColor = Color.LightGreen
            btn.TextAlign = ContentAlignment.MiddleCenter
            AddHandler btn.Click, clickHandler
            panel.Controls.Add(btn)
        Next
    End Sub


    'Public Sub CreateUniversalJE(trxNo As Long, referenceType As String)
    '    If CheckIfJEExists(trxNo, referenceType) Then Exit Sub

    '    Dim JESN As Long
    '    Dim accountMap As Dictionary(Of String, String) = LoadGLAccounts()

    '    ' Normalize keys
    '    If Not accountMap.ContainsKey("دائنون") AndAlso accountMap.ContainsKey("موردون") Then
    '        accountMap("دائنون") = accountMap("موردون")
    '    End If
    '    If Not accountMap.ContainsKey("رصيد افتتاحي") AndAlso accountMap.ContainsKey("مخزون أول المدة") Then
    '        accountMap("رصيد افتتاحي") = accountMap("مخزون أول المدة")
    '    End If
    '    If Not accountMap.ContainsKey("خصم مشتريات") AndAlso accountMap.ContainsKey("الخصم المكتسب") Then
    '        accountMap("خصم مشتريات") = accountMap("الخصم المكتسب")
    '    End If
    '    If Not accountMap.ContainsKey("ضريبة مدخلات") AndAlso accountMap.ContainsKey("القيمة المضافة المدفوعة") Then
    '        accountMap("ضريبة مدخلات") = accountMap("القيمة المضافة المدفوعة")
    '    End If

    '    ' Step 1: Retrieve financial data from tblStockMovHeader
    '    Dim netAmount As Decimal = 0
    '    Dim discountAmount As Decimal = 0
    '    'Dim vatAmount As Decimal = 0
    '    Dim grossAmountBeforeDiscount As Decimal = 0
    '    Dim partnerAccountNo As String = ""
    '    Dim paymentType As String = "نقدي"
    '    Dim isCash As Boolean = True

    '    Dim query As String = "SELECT TrxNetAmount, TrxDiscountValue, TrxVAT, TrxTotal, PaymentMethod, PartnerNo FROM tblStockMovHeader WHERE TrxNo = @Trx AND TrxType = @Type"
    '    Using cmd As New SqlClient.SqlCommand(query, New SqlClient.SqlConnection(ConStr))
    '        cmd.Parameters.AddWithValue("@Trx", trxNo)
    '        cmd.Parameters.AddWithValue("@Type", referenceType)
    '        cmd.Connection.Open()
    '        Using rdr = cmd.ExecuteReader()
    '            If rdr.Read() Then
    '                netAmount = Val(rdr("TrxNetAmount"))
    '                discountAmount = If(IsDBNull(rdr("TrxDiscountValue")), 0, Val(rdr("TrxDiscountValue")))
    '                vatAmount = If(IsDBNull(rdr("TrxVAT")), 0, Val(rdr("TrxVAT")))
    '                grossAmountBeforeDiscount = Val(rdr("TrxTotal")) + discountAmount ' Since TrxTotal is after discount but before VAT
    '                paymentType = rdr("PaymentMethod").ToString()
    '                partnerAccountNo = rdr("PartnerNo").ToString()
    '                isCash = (paymentType = "نقدي")
    '            End If
    '        End Using
    '        cmd.Connection.Close()
    '    End Using

    '    ' Step 2: Create header
    '    Dim insertSQL As String = "INSERT INTO tblJEHeader (TrxDate, Amount, Reference, ReferenceTrx, ReferenceType, PostStatus, CreatedBy, CreatedOn) " &
    '                              "VALUES (GETDATE(), @Amt, @Ref, @Trx, @RefType, 'Open', @User, GETDATE()); SELECT SCOPE_IDENTITY()"
    '    Using insertCmd As New SqlClient.SqlCommand(insertSQL, New SqlClient.SqlConnection(ConStr))
    '        insertCmd.Parameters.AddWithValue("@Amt", netAmount + vatAmount)
    '        insertCmd.Parameters.AddWithValue("@Ref", "قيد " & referenceType & " - " & trxNo)
    '        insertCmd.Parameters.AddWithValue("@Trx", trxNo)
    '        insertCmd.Parameters.AddWithValue("@RefType", referenceType)
    '        insertCmd.Parameters.AddWithValue("@User", "System")
    '        insertCmd.Connection.Open()
    '        JESN = Val(insertCmd.ExecuteScalar())
    '        insertCmd.Connection.Close()
    '    End Using

    '    ' Step 3: Load cost (inventory items only)
    '    Dim totalInventoryCost As Decimal = 0
    '    Dim costSQL As String = "SELECT SUM(T.UnitPurchasePrice * I.TrxQTY) AS TotalCost " &
    '                           "FROM tblStockMovement I INNER JOIN tblItems T ON I.ItemNo = T.ItemNo " &
    '                           "WHERE I.DocNo = @Trx AND T.ItemType = 'مخزني'  and I.TrxType=@Type"
    '    Using costCmd As New SqlClient.SqlCommand(costSQL, New SqlClient.SqlConnection(ConStr))
    '        costCmd.Parameters.AddWithValue("@Trx", trxNo)
    '        costCmd.Parameters.AddWithValue("@Type", referenceType)

    '        costCmd.Connection.Open()
    '        totalInventoryCost = Val(costCmd.ExecuteScalar())
    '        costCmd.Connection.Close()
    '    End Using

    '    ' Step 4: Build journal entries
    '    Dim journalEntries As New List(Of JournalEntry)

    '    Select Case referenceType
    '        Case "مبيعات"
    '            journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("تكلفة المبيعات"), .Amount = totalInventoryCost, .DRCR = "DR"})
    '            journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("المخزون"), .Amount = totalInventoryCost, .DRCR = "CR"})
    '            journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("مبيعات"), .Amount = grossAmountBeforeDiscount, .DRCR = "CR"})
    '            If discountAmount > 0 Then
    '                journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("خصم مبيعات"), .Amount = discountAmount, .DRCR = "DR"})
    '            End If
    '            If vatAmount > 0 Then
    '                journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("الضريبة"), .Amount = vatAmount, .DRCR = "CR"})
    '            End If
    '            journalEntries.Add(New JournalEntry With {.AccountNo = If(isCash, accountMap("نقدية"), partnerAccountNo), .Amount = netAmount, .DRCR = "DR"})

    '        Case "مرتجع مبيعات"
    '            journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("المخزون"), .Amount = totalInventoryCost, .DRCR = "DR"})
    '            journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("تكلفة المبيعات"), .Amount = totalInventoryCost, .DRCR = "CR"})
    '            journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("مبيعات"), .Amount = grossAmountBeforeDiscount, .DRCR = "DR"})
    '            If discountAmount > 0 Then
    '                journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("خصم مبيعات"), .Amount = discountAmount, .DRCR = "CR"})
    '            End If
    '            If vatAmount > 0 Then
    '                journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("الضريبة"), .Amount = vatAmount, .DRCR = "DR"})
    '            End If
    '            journalEntries.Add(New JournalEntry With {.AccountNo = If(isCash, accountMap("نقدية"), partnerAccountNo), .Amount = netAmount, .DRCR = "CR"})

    '        Case "مشتريات"
    '            journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("المخزون"), .Amount = grossAmountBeforeDiscount, .DRCR = "DR"})
    '            If discountAmount > 0 Then
    '                journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("خصم مشتريات"), .Amount = discountAmount, .DRCR = "CR"})
    '            End If
    '            If vatAmount > 0 Then
    '                journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("ضريبة مدخلات"), .Amount = vatAmount, .DRCR = "DR"})
    '            End If
    '            journalEntries.Add(New JournalEntry With {.AccountNo = If(isCash, accountMap("نقدية"), partnerAccountNo), .Amount = netAmount, .DRCR = "CR"})

    '        Case "مرتجع مشتريات"
    '            journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("المخزون"), .Amount = grossAmountBeforeDiscount, .DRCR = "CR"})
    '            If discountAmount > 0 Then
    '                journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("خصم مشتريات"), .Amount = discountAmount, .DRCR = "DR"})
    '            End If
    '            If vatAmount > 0 Then
    '                journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("ضريبة مدخلات"), .Amount = vatAmount, .DRCR = "CR"})
    '            End If
    '            journalEntries.Add(New JournalEntry With {.AccountNo = If(isCash, accountMap("نقدية"), partnerAccountNo), .Amount = netAmount, .DRCR = "DR"})

    '        Case "رصيد افتتاحي"
    '            journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("المخزون"), .Amount = netAmount, .DRCR = "DR"})
    '            journalEntries.Add(New JournalEntry With {.AccountNo = accountMap("رصيد افتتاحي"), .Amount = netAmount, .DRCR = "CR"})
    '    End Select

    '    If Not IsJournalBalanced(journalEntries) Then
    '        Throw New Exception("القيد غير موزون: " & referenceType)
    '    End If

    '    For Each entry In journalEntries
    '        InsertJEEntry(JESN, entry.AccountNo, entry.Amount, entry.DRCR)
    '    Next
    'End Sub








End Module
