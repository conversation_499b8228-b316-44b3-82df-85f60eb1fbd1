using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using System.Text;
using System.Data;
using System.IO;
using ClosedXML.Excel;
using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Helpers;
using Microsoft.Extensions.Logging;
using System.Globalization;


namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class SalesController : Controller
    {
        private readonly IPOSService _posService;
        private readonly ICustomerService _customerService;
        private readonly ILogger<SalesController> _logger;

        public SalesController(IPOSService posService, ICustomerService customerService, ILogger<SalesController> logger)
        {
            _posService = posService;
            _customerService = customerService;
            _logger = logger;
        }

        public IActionResult Index()
        {
            return View();
        }

        public IActionResult Invoice()
        {
            ViewBag.PageTitle = "فواتير المبيعات";
            ViewBag.Message = "صفحة فواتير المبيعات - قيد التطوير";
            return View();
        }

        public IActionResult Return()
        {
            ViewBag.PageTitle = "مرتجع المبيعات";
            ViewBag.Message = "صفحة مرتجع المبيعات - قيد التطوير";
            return View();
        }

        public IActionResult Create()
        {
            ViewBag.PageTitle = "إنشاء فاتورة مبيعات جديدة";
            return View();
        }

        public IActionResult Edit(long id)
        {
            ViewBag.PageTitle = "تعديل فاتورة المبيعات";
            ViewBag.InvoiceId = id;
            return View();
        }

        public IActionResult Details(long id)
        {
            ViewBag.PageTitle = "تفاصيل فاتورة المبيعات";
            ViewBag.InvoiceId = id;
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> Search(
            string? invoiceNo = null,
            string? customer = null,
            string? user = null,
            string? warehouse = null,
            string? store = null,
            DateTime? date = null,
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            string? invoiceType = null,
            string? paymentMethod = null,
            string? reference = null,
            long? accountNo = null,
            string? notes = null,
            string? mode = null,
            string? itemCode = null)
        {
            // Backward compatibility: if only single date provided, use it as both from/to
            if (date.HasValue)
            {
                dateFrom ??= date.Value.Date;
                dateTo ??= date.Value.Date;
            }

            // Prefer explicit store param; fallback to warehouse if provided
            var effectiveStore = string.IsNullOrWhiteSpace(store) ? warehouse : store;

            List<AccountingSystem.Models.POSInvoice> invoices;
            if (!string.IsNullOrWhiteSpace(mode) && mode.Equals("lines", StringComparison.OrdinalIgnoreCase))
            {
                // Use exact legacy SELECT * for lines (InvoiceSearchByItem) and render via ViewBag
                var linesTable = await _posService.SearchInvoiceLinesRawAsync(
                    invoiceNo, user, effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes, itemCode);
                ViewBag.LinesTable = linesTable;
                invoices = new List<AccountingSystem.Models.POSInvoice>();
            }
            else
            {
                invoices = await _posService.SearchInvoicesAdvancedAsync(
                    invoiceNo, customer, user, effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes);
                // For correct user display, get mapping from legacy view
                var userMap = await _posService.GetLegacyInvoiceUsersAsync(
                    invoiceNo, user, effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes);
                ViewBag.UserMap = userMap;
            }

            // Load dropdown data sequentially to avoid concurrent DbContext operations
            var storeOptions = await _posService.GetStoreNamesAsync();
            var userOptions = await _posService.GetCashierNamesAsync();
            var paymentOptions = await _posService.GetPaymentMethodNamesAsync();
            var customers = await _customerService.GetCustomersAsync("");

            ViewBag.StoreOptions = storeOptions;
            ViewBag.UserOptions = userOptions;
            ViewBag.PaymentOptions = paymentOptions;
            // Limit customers to a reasonable number for the dropdown
            ViewBag.CustomerOptions = customers?.Take(200).ToList();

            // Keep ViewBags for UI binding
            ViewBag.InvoiceNo = invoiceNo;
            ViewBag.Customer = customer;
            ViewBag.User = user;
            ViewBag.Warehouse = warehouse;
            ViewBag.Store = store;
            ViewBag.Date = date;
            ViewBag.DateFrom = dateFrom;
            ViewBag.DateTo = dateTo;
            ViewBag.InvoiceType = invoiceType;
            ViewBag.PaymentMethod = paymentMethod;
            ViewBag.Reference = reference;
            ViewBag.AccountNo = accountNo;
            ViewBag.Notes = notes;
            ViewBag.Mode = mode;
            ViewBag.ItemCode = itemCode;

            ViewBag.TotalSales = invoices.Sum(i => i.TrxNetAmount ?? 0);
            return View(invoices);
        }


        [HttpGet]
        public async Task<IActionResult> ExportLines(
            string? invoiceNo = null,
            string? user = null,
            string? warehouse = null,
            string? store = null,
            DateTime? date = null,
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            string? invoiceType = null,
            string? paymentMethod = null,
            string? reference = null,
            long? accountNo = null,
            string? notes = null,
            string? itemCode = null,
            string? format = null)
        {
            if (date.HasValue) { dateFrom ??= date.Value.Date; dateTo ??= date.Value.Date; }
            // Fallback parse for dates if binder missed due to culture
            if (!dateFrom.HasValue && !string.IsNullOrWhiteSpace(Request.Query["dateFrom"]))
            {
                if (DateTime.TryParseExact(Request.Query["dateFrom"], "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dfx)) dateFrom = dfx;
            }
            if (!dateTo.HasValue && !string.IsNullOrWhiteSpace(Request.Query["dateTo"]))
            {
                if (DateTime.TryParseExact(Request.Query["dateTo"], "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dtx)) dateTo = dtx;
            }
            var effectiveStore = string.IsNullOrWhiteSpace(store) ? warehouse : store;
            _logger.LogInformation("ExportLines raw query: {Query}", Request.QueryString.Value);
            _logger.LogInformation("ExportLines filters: store={Store}, effStore={Eff}, from={From}, to={To}, type={Type}, user={User}, acc={Acc}, item={Item}, ref={Ref}, notes={Notes}, inv={Inv}", store, effectiveStore, dateFrom, dateTo, invoiceType, user, accountNo, itemCode, reference, notes, invoiceNo);
            // Query exact legacy view
            var dt = await _posService.SearchInvoiceLinesRawAsync(
                invoiceNo, user, effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes, itemCode);
            _logger.LogInformation("ExportLines result rows: {Count}", dt?.Rows?.Count);

            var fmt = (format ?? "csv").ToLowerInvariant();
            var ts = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            if (fmt == "xlsx")
            {
                using var wb = new XLWorkbook();
                wb.Worksheets.Add(dt, "Lines");
                using var ms = new MemoryStream();
                wb.SaveAs(ms);
                var bytesX = ms.ToArray();
                return File(bytesX, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"SalesLines_{ts}.xlsx");
            }
            else if (fmt == "pdf")
            {
                QuestPDF.Settings.License = LicenseType.Community;
                var pdfBytes = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4.Landscape());
                        page.Margin(20);
                        page.DefaultTextStyle(t => t.FontSize(10));
                        page.Content().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                for (int i = 0; i < dt.Columns.Count; i++) columns.RelativeColumn();
                            });

                            table.Header(header =>
                            {
                                for (int i = 0; i < dt.Columns.Count; i++)
                                    header.Cell().Padding(3).BorderBottom(1).Background("#f0f0f0").Text(dt.Columns[i].ColumnName);
                            });

                            foreach (DataRow row in dt.Rows)
                            {
                                for (int i = 0; i < dt.Columns.Count; i++)
                                    table.Cell().Padding(2).BorderBottom(0.5f).Text(row[i]?.ToString() ?? "");
                            }
                        });
                    });
                }).GeneratePdf();
                return File(pdfBytes, "application/pdf", $"SalesLines_{ts}.pdf");
            }
            else
            {
                // CSV (UTF-8 BOM)
                var sb = new StringBuilder();
                for (int i = 0; i < dt.Columns.Count; i++)
                {
                    if (i > 0) sb.Append(',');
                    sb.Append('"').Append(dt.Columns[i].ColumnName.Replace("\"", "\"\"")).Append('"');
                }
                sb.AppendLine();
                foreach (DataRow row in dt.Rows)
                {
                    for (int i = 0; i < dt.Columns.Count; i++)
                    {
                        if (i > 0) sb.Append(',');
                        var cell = row[i]?.ToString() ?? string.Empty;
                        sb.Append('"').Append(cell.Replace("\"", "\"\"")).Append('"');
                    }
                    sb.AppendLine();
                }
                var bytes = Encoding.UTF8.GetPreamble().Concat(Encoding.UTF8.GetBytes(sb.ToString())).ToArray();
                return File(bytes, "text/csv; charset=utf-8", $"SalesLines_{ts}.csv");
            }
        }

        [HttpGet]
        public async Task<IActionResult> PrintSalesLines(
            string? invoiceNo = null,
            string? user = null,
            string? warehouse = null,
            string? store = null,
            DateTime? date = null,
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            string? invoiceType = null,
            string? paymentMethod = null,
            string? reference = null,
            long? accountNo = null,
            string? notes = null,
            string? itemCode = null)
        {
            if (date.HasValue) { dateFrom ??= date.Value.Date; dateTo ??= date.Value.Date; }
            if (!dateFrom.HasValue && !string.IsNullOrWhiteSpace(Request.Query["dateFrom"]))
            {
                if (DateTime.TryParseExact(Request.Query["dateFrom"], "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dfx)) dateFrom = dfx;
            }
            if (!dateTo.HasValue && !string.IsNullOrWhiteSpace(Request.Query["dateTo"]))
            {
                if (DateTime.TryParseExact(Request.Query["dateTo"], "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dtx)) dateTo = dtx;
            }
            var effectiveStore = string.IsNullOrWhiteSpace(store) ? warehouse : store;
            _logger.LogInformation("PrintSalesLines raw query: {Query}", Request.QueryString.Value);
            _logger.LogInformation("PrintSalesLines filters: store={Store}, effStore={Eff}, from={From}, to={To}, type={Type}, user={User}, acc={Acc}, item={Item}, ref={Ref}, notes={Notes}, inv={Inv}", store, effectiveStore, dateFrom, dateTo, invoiceType, user, accountNo, itemCode, reference, notes, invoiceNo);
            var dt = await _posService.SearchInvoiceLinesRawAsync(
                invoiceNo, user, effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes, itemCode);
            _logger.LogInformation("PrintSalesLines result rows: {Count}", dt?.Rows?.Count);
            ViewBag.Filters = new { invoiceNo, user, store = effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes, itemCode };
            return View("PrintSalesLines", dt);
        }

        [HttpGet]
        public async Task<IActionResult> ExportInvoices(
            string? invoiceNo = null,
            string? customer = null,
            string? user = null,
            string? warehouse = null,
            string? store = null,
            DateTime? date = null,
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            string? invoiceType = null,
            string? paymentMethod = null,
            string? reference = null,
            long? accountNo = null,
            string? notes = null,
            string? mode = null,
            string? itemCode = null,
            string? format = null)
        {
            if (date.HasValue) { dateFrom ??= date.Value.Date; dateTo ??= date.Value.Date; }
            if (!dateFrom.HasValue && !string.IsNullOrWhiteSpace(Request.Query["dateFrom"]))
            {
                if (DateTime.TryParseExact(Request.Query["dateFrom"], "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dfx)) dateFrom = dfx;
            }
            if (!dateTo.HasValue && !string.IsNullOrWhiteSpace(Request.Query["dateTo"]))
            {
                if (DateTime.TryParseExact(Request.Query["dateTo"], "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dtx)) dateTo = dtx;
            }
            var effectiveStore = string.IsNullOrWhiteSpace(store) ? warehouse : store;
            _logger.LogInformation("ExportInvoices raw query: {Query}", Request.QueryString.Value);
            _logger.LogInformation("ExportInvoices filters: store={Store}, effStore={Eff}, from={From}, to={To}, type={Type}, user={User}, acc={Acc}, ref={Ref}, notes={Notes}, inv={Inv}, cust={Cust}", store, effectiveStore, dateFrom, dateTo, invoiceType, user, accountNo, reference, notes, invoiceNo, customer);
            var invoices = await _posService.SearchInvoicesAdvancedAsync(
                invoiceNo, customer, user, effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes);
            // Use legacy view mapping for user display consistency
            var userMap = await _posService.GetLegacyInvoiceUsersAsync(
                invoiceNo, user, effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes);
            _logger.LogInformation("ExportInvoices result count: {Count}", invoices?.Count);

            var fmt = (format ?? "csv").ToLowerInvariant();
            var ts = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            if (fmt == "xlsx")
            {
                using var wb = new XLWorkbook();
                var ws = wb.Worksheets.Add("Invoices");
                // header
                ws.Cell(1,1).Value = "رقم الفاتورة";
                ws.Cell(1,2).Value = "التاريخ";
                ws.Cell(1,3).Value = "العميل";
                ws.Cell(1,4).Value = "المستخدم";
                ws.Cell(1,5).Value = "المستودع";
                ws.Cell(1,6).Value = "الإجمالي النهائي";
                int r = 2;
                foreach (var inv in invoices)
                {
                    var userVal = userMap != null && userMap.TryGetValue(inv.TrxNo, out var u) ? u : (inv.Cashier ?? string.Empty);
                    ws.Cell(r,1).Value = inv.TrxNo;
                    ws.Cell(r,2).Value = inv.TrxDate?.ToString("yyyy-MM-dd") ?? string.Empty;
                    ws.Cell(r,3).Value = string.IsNullOrWhiteSpace(inv.PartnerName) ? "عميل غير محدد" : inv.PartnerName;
                    ws.Cell(r,4).Value = userVal;
                    ws.Cell(r,5).Value = inv.Store ?? string.Empty;
                    ws.Cell(r,6).Value = (inv.TrxNetAmount ?? 0).ToString("N2");
                    r++;
                }
                ws.Columns().AdjustToContents();
                using var ms = new MemoryStream();
                wb.SaveAs(ms);
                return File(ms.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"SalesInvoices_{ts}.xlsx");
            }
            else if (fmt == "pdf")
            {
                QuestPDF.Settings.License = LicenseType.Community;
                var pdfBytes = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4.Landscape());
                        page.Margin(20);
                        page.DefaultTextStyle(t => t.FontSize(10));
                        page.Content().Table(table =>
                        {
                            table.ColumnsDefinition(cols => { cols.RelativeColumn(); cols.RelativeColumn(); cols.RelativeColumn(); cols.RelativeColumn(); cols.RelativeColumn(); cols.RelativeColumn(); });
                            table.Header(header =>
                            {
                                string[] heads = {"رقم الفاتورة","التاريخ","العميل","المستخدم","المستودع","الإجمالي النهائي"};
                                foreach (var h in heads)
                                    header.Cell().Padding(3).BorderBottom(1).Background("#f0f0f0").Text(h);
                            });
                            foreach (var inv in invoices)
                            {
                                var userVal = userMap != null && userMap.TryGetValue(inv.TrxNo, out var u) ? u : (inv.Cashier ?? "");
                                table.Cell().Padding(2).BorderBottom(0.5f).Text(inv.TrxNo.ToString());
                                table.Cell().Padding(2).BorderBottom(0.5f).Text(inv.TrxDate?.ToString("yyyy-MM-dd") ?? "");
                                table.Cell().Padding(2).BorderBottom(0.5f).Text(string.IsNullOrWhiteSpace(inv.PartnerName) ? "عميل غير محدد" : inv.PartnerName);
                                table.Cell().Padding(2).BorderBottom(0.5f).Text(userVal);
                                table.Cell().Padding(2).BorderBottom(0.5f).Text(inv.Store ?? "");
                                table.Cell().Padding(2).BorderBottom(0.5f).Text((inv.TrxNetAmount ?? 0).ToString("N2"));
                            }
                        });
                    });
                }).GeneratePdf();
                return File(pdfBytes, "application/pdf", $"SalesInvoices_{ts}.pdf");
            }
            else
            {
                // CSV
                var sb = new StringBuilder();
                sb.AppendLine("رقم الفاتورة,التاريخ,العميل,المستخدم,المستودع,الإجمالي النهائي");
                foreach (var inv in invoices)
                {
                    var userVal = userMap != null && userMap.TryGetValue(inv.TrxNo, out var u) ? u : (inv.Cashier ?? string.Empty);
                    string dateStr = inv.TrxDate?.ToString("yyyy-MM-dd") ?? string.Empty;
                    string line = string.Join(',', new[]
                    {
                        Quote(inv.TrxNo.ToString()),
                        Quote(dateStr),
                        Quote(string.IsNullOrWhiteSpace(inv.PartnerName) ? "عميل غير محدد" : inv.PartnerName),
                        Quote(userVal),
                        Quote(inv.Store ?? string.Empty),
                        Quote((inv.TrxNetAmount ?? 0).ToString("N2"))
                    });
                    sb.AppendLine(line);
                }
                var bytes = Encoding.UTF8.GetPreamble().Concat(Encoding.UTF8.GetBytes(sb.ToString())).ToArray();
                return File(bytes, "text/csv; charset=utf-8", $"SalesInvoices_{ts}.csv");
            }

            static string Quote(string s) => $"\"{s.Replace("\"", "\"\"")}\"";
        }

        [HttpGet]
        public async Task<IActionResult> PrintSalesHeaders(
            string? invoiceNo = null,
            string? customer = null,
            string? user = null,
            string? warehouse = null,
            string? store = null,
            DateTime? date = null,
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            string? invoiceType = null,
            string? paymentMethod = null,
            string? reference = null,
            long? accountNo = null,
            string? notes = null)
        {
            if (date.HasValue) { dateFrom ??= date.Value.Date; dateTo ??= date.Value.Date; }
            if (!dateFrom.HasValue && !string.IsNullOrWhiteSpace(Request.Query["dateFrom"]))
            {
                if (DateTime.TryParseExact(Request.Query["dateFrom"], "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dfx)) dateFrom = dfx;
            }
            if (!dateTo.HasValue && !string.IsNullOrWhiteSpace(Request.Query["dateTo"]))
            {
                if (DateTime.TryParseExact(Request.Query["dateTo"], "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dtx)) dateTo = dtx;
            }
            var effectiveStore = string.IsNullOrWhiteSpace(store) ? warehouse : store;
            _logger.LogInformation("PrintSalesHeaders raw query: {Query}", Request.QueryString.Value);
            _logger.LogInformation("PrintSalesHeaders filters: store={Store}, effStore={Eff}, from={From}, to={To}, type={Type}, user={User}, acc={Acc}, ref={Ref}, notes={Notes}, inv={Inv}, cust={Cust}", store, effectiveStore, dateFrom, dateTo, invoiceType, user, accountNo, reference, notes, invoiceNo, customer);
            var invoices = await _posService.SearchInvoicesAdvancedAsync(
                invoiceNo, customer, user, effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes);
            var userMap = await _posService.GetLegacyInvoiceUsersAsync(
                invoiceNo, user, effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes);
            _logger.LogInformation("PrintSalesHeaders result count: {Count}", invoices?.Count);
            ViewBag.UserMap = userMap;
            ViewBag.Filters = new { invoiceNo, customer, user, store = effectiveStore, dateFrom, dateTo, invoiceType, paymentMethod, reference, accountNo, notes };
            return View("PrintSalesHeaders", invoices);
        }



    }
}
