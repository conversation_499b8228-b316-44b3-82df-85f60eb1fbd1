﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmBalanceUpload
    Inherits System.Windows.Forms.Form

    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    Private components As System.ComponentModel.IContainer

    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.pnlHeader = New System.Windows.Forms.Panel()
        Me.lblTitle = New System.Windows.Forms.Label()
        Me.pnlControls = New System.Windows.Forms.Panel()
        Me.grpSettings = New System.Windows.Forms.GroupBox()
        Me.rbVendor = New System.Windows.Forms.RadioButton()
        Me.rbCustomer = New System.Windows.Forms.RadioButton()
        Me.dtpEntryDate = New System.Windows.Forms.DateTimePicker()
        Me.lblDate = New System.Windows.Forms.Label()
        Me.pnlActions = New System.Windows.Forms.Panel()
        Me.btnExportReport = New System.Windows.Forms.Button()
        Me.btnProcessBalances = New System.Windows.Forms.Button()
        Me.btnValidateMapping = New System.Windows.Forms.Button()
        Me.btnImportExcel = New System.Windows.Forms.Button()
        Me.pnlGrid = New System.Windows.Forms.Panel()
        Me.dgvBalances = New System.Windows.Forms.DataGridView()
        Me.colOldCode = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.colNewCode = New System.Windows.Forms.DataGridViewComboBoxColumn()
        Me.colAccountName = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.colOriginalBalance = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.colStatus = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.pnlSummary = New System.Windows.Forms.Panel()
        Me.grpSummary = New System.Windows.Forms.GroupBox()
        Me.lblTotalCredits = New System.Windows.Forms.Label()
        Me.lblTotalDebits = New System.Windows.Forms.Label()
        Me.lblTotalRecords = New System.Windows.Forms.Label()
        Me.lblCreditsAmount = New System.Windows.Forms.Label()
        Me.lblDebitsAmount = New System.Windows.Forms.Label()
        Me.lblRecordsCount = New System.Windows.Forms.Label()
        Me.pnlStatus = New System.Windows.Forms.Panel()
        Me.progressBar = New System.Windows.Forms.ProgressBar()
        Me.lblStatus = New System.Windows.Forms.Label()
        Me.pnlHeader.SuspendLayout()
        Me.pnlControls.SuspendLayout()
        Me.grpSettings.SuspendLayout()
        Me.pnlActions.SuspendLayout()
        Me.pnlGrid.SuspendLayout()
        CType(Me.dgvBalances, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlSummary.SuspendLayout()
        Me.grpSummary.SuspendLayout()
        Me.pnlStatus.SuspendLayout()
        Me.SuspendLayout()
        '
        'pnlHeader
        '
        Me.pnlHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.pnlHeader.Controls.Add(Me.lblTitle)
        Me.pnlHeader.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlHeader.Location = New System.Drawing.Point(0, 0)
        Me.pnlHeader.Name = "pnlHeader"
        Me.pnlHeader.Size = New System.Drawing.Size(1200, 60)
        Me.pnlHeader.TabIndex = 0
        '
        'lblTitle
        '
        Me.lblTitle.Dock = System.Windows.Forms.DockStyle.Fill
        Me.lblTitle.Font = New System.Drawing.Font("Segoe UI", 16.0!, System.Drawing.FontStyle.Bold)
        Me.lblTitle.ForeColor = System.Drawing.Color.White
        Me.lblTitle.Location = New System.Drawing.Point(0, 0)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(1200, 60)
        Me.lblTitle.TabIndex = 0
        Me.lblTitle.Text = "رفع الأرصدة الافتتاحية - العملاء والموردين"
        Me.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlControls
        '
        Me.pnlControls.BackColor = System.Drawing.Color.FromArgb(CType(CType(248, Byte), Integer), CType(CType(249, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.pnlControls.Controls.Add(Me.grpSettings)
        Me.pnlControls.Controls.Add(Me.pnlActions)
        Me.pnlControls.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlControls.Location = New System.Drawing.Point(0, 60)
        Me.pnlControls.Name = "pnlControls"
        Me.pnlControls.Padding = New System.Windows.Forms.Padding(20, 15, 20, 15)
        Me.pnlControls.Size = New System.Drawing.Size(1200, 100)
        Me.pnlControls.TabIndex = 1
        '
        'grpSettings
        '
        Me.grpSettings.Controls.Add(Me.rbVendor)
        Me.grpSettings.Controls.Add(Me.rbCustomer)
        Me.grpSettings.Controls.Add(Me.dtpEntryDate)
        Me.grpSettings.Controls.Add(Me.lblDate)
        Me.grpSettings.Dock = System.Windows.Forms.DockStyle.Left
        Me.grpSettings.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.grpSettings.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.grpSettings.Location = New System.Drawing.Point(20, 15)
        Me.grpSettings.Name = "grpSettings"
        Me.grpSettings.Size = New System.Drawing.Size(320, 70)
        Me.grpSettings.TabIndex = 0
        Me.grpSettings.TabStop = False
        Me.grpSettings.Text = "إعدادات الرفع"
        '
        'rbVendor
        '
        Me.rbVendor.AutoSize = True
        Me.rbVendor.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.rbVendor.Location = New System.Drawing.Point(190, 45)
        Me.rbVendor.Name = "rbVendor"
        Me.rbVendor.Size = New System.Drawing.Size(63, 19)
        Me.rbVendor.TabIndex = 3
        Me.rbVendor.Text = "موردين"
        Me.rbVendor.UseVisualStyleBackColor = True
        '
        'rbCustomer
        '
        Me.rbCustomer.AutoSize = True
        Me.rbCustomer.Checked = True
        Me.rbCustomer.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.rbCustomer.Location = New System.Drawing.Point(190, 25)
        Me.rbCustomer.Name = "rbCustomer"
        Me.rbCustomer.Size = New System.Drawing.Size(56, 19)
        Me.rbCustomer.TabIndex = 2
        Me.rbCustomer.TabStop = True
        Me.rbCustomer.Text = "عملاء"
        Me.rbCustomer.UseVisualStyleBackColor = True
        '
        'dtpEntryDate
        '
        Me.dtpEntryDate.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.dtpEntryDate.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.dtpEntryDate.Location = New System.Drawing.Point(15, 35)
        Me.dtpEntryDate.Name = "dtpEntryDate"
        Me.dtpEntryDate.Size = New System.Drawing.Size(120, 23)
        Me.dtpEntryDate.TabIndex = 1
        '
        'lblDate
        '
        Me.lblDate.AutoSize = True
        Me.lblDate.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.lblDate.Location = New System.Drawing.Point(15, 20)
        Me.lblDate.Name = "lblDate"
        Me.lblDate.Size = New System.Drawing.Size(62, 15)
        Me.lblDate.TabIndex = 0
        Me.lblDate.Text = "تاريخ القيد:"
        '
        'pnlActions
        '
        Me.pnlActions.Controls.Add(Me.btnExportReport)
        Me.pnlActions.Controls.Add(Me.btnProcessBalances)
        Me.pnlActions.Controls.Add(Me.btnValidateMapping)
        Me.pnlActions.Controls.Add(Me.btnImportExcel)
        Me.pnlActions.Dock = System.Windows.Forms.DockStyle.Right
        Me.pnlActions.Location = New System.Drawing.Point(360, 15)
        Me.pnlActions.Name = "pnlActions"
        Me.pnlActions.Size = New System.Drawing.Size(820, 70)
        Me.pnlActions.TabIndex = 1
        '
        'btnExportReport
        '
        Me.btnExportReport.BackColor = System.Drawing.Color.FromArgb(CType(CType(155, Byte), Integer), CType(CType(89, Byte), Integer), CType(CType(182, Byte), Integer))
        Me.btnExportReport.Enabled = False
        Me.btnExportReport.FlatAppearance.BorderSize = 0
        Me.btnExportReport.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnExportReport.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnExportReport.ForeColor = System.Drawing.Color.White
        Me.btnExportReport.Location = New System.Drawing.Point(50, 20)
        Me.btnExportReport.Name = "btnExportReport"
        Me.btnExportReport.Size = New System.Drawing.Size(150, 35)
        Me.btnExportReport.TabIndex = 3
        Me.btnExportReport.Text = "تصدير التقرير"
        Me.btnExportReport.UseVisualStyleBackColor = False
        '
        'btnProcessBalances
        '
        Me.btnProcessBalances.BackColor = System.Drawing.Color.FromArgb(CType(CType(231, Byte), Integer), CType(CType(76, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.btnProcessBalances.Enabled = False
        Me.btnProcessBalances.FlatAppearance.BorderSize = 0
        Me.btnProcessBalances.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnProcessBalances.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnProcessBalances.ForeColor = System.Drawing.Color.White
        Me.btnProcessBalances.Location = New System.Drawing.Point(220, 20)
        Me.btnProcessBalances.Name = "btnProcessBalances"
        Me.btnProcessBalances.Size = New System.Drawing.Size(150, 35)
        Me.btnProcessBalances.TabIndex = 2
        Me.btnProcessBalances.Text = "معالجة الأرصدة"
        Me.btnProcessBalances.UseVisualStyleBackColor = False
        '
        'btnValidateMapping
        '
        Me.btnValidateMapping.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(7, Byte), Integer))
        Me.btnValidateMapping.Enabled = False
        Me.btnValidateMapping.FlatAppearance.BorderSize = 0
        Me.btnValidateMapping.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnValidateMapping.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnValidateMapping.ForeColor = System.Drawing.Color.White
        Me.btnValidateMapping.Location = New System.Drawing.Point(390, 20)
        Me.btnValidateMapping.Name = "btnValidateMapping"
        Me.btnValidateMapping.Size = New System.Drawing.Size(150, 35)
        Me.btnValidateMapping.TabIndex = 1
        Me.btnValidateMapping.Text = "التحقق من الربط"
        Me.btnValidateMapping.UseVisualStyleBackColor = False
        '
        'btnImportExcel
        '
        Me.btnImportExcel.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(204, Byte), Integer), CType(CType(113, Byte), Integer))
        Me.btnImportExcel.FlatAppearance.BorderSize = 0
        Me.btnImportExcel.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnImportExcel.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnImportExcel.ForeColor = System.Drawing.Color.White
        Me.btnImportExcel.Location = New System.Drawing.Point(560, 20)
        Me.btnImportExcel.Name = "btnImportExcel"
        Me.btnImportExcel.Size = New System.Drawing.Size(150, 35)
        Me.btnImportExcel.TabIndex = 0
        Me.btnImportExcel.Text = "استيراد Excel"
        Me.btnImportExcel.UseVisualStyleBackColor = False
        '
        'pnlGrid
        '
        Me.pnlGrid.BackColor = System.Drawing.Color.White
        Me.pnlGrid.Controls.Add(Me.dgvBalances)
        Me.pnlGrid.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlGrid.Location = New System.Drawing.Point(0, 160)
        Me.pnlGrid.Name = "pnlGrid"
        Me.pnlGrid.Padding = New System.Windows.Forms.Padding(20, 10, 20, 10)
        Me.pnlGrid.Size = New System.Drawing.Size(1200, 310)
        Me.pnlGrid.TabIndex = 2
        '
        'dgvBalances
        '
        Me.dgvBalances.AllowUserToAddRows = False
        Me.dgvBalances.AllowUserToDeleteRows = False
        Me.dgvBalances.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.dgvBalances.BackgroundColor = System.Drawing.Color.White
        Me.dgvBalances.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.dgvBalances.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal
        Me.dgvBalances.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(73, Byte), Integer), CType(CType(94, Byte), Integer))
        DataGridViewCellStyle1.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        DataGridViewCellStyle1.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(73, Byte), Integer), CType(CType(94, Byte), Integer))
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgvBalances.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.dgvBalances.ColumnHeadersHeight = 40
        Me.dgvBalances.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.colOldCode, Me.colNewCode, Me.colAccountName, Me.colOriginalBalance, Me.colStatus})
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.White
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(255, Byte), Integer))
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgvBalances.DefaultCellStyle = DataGridViewCellStyle2
        Me.dgvBalances.Dock = System.Windows.Forms.DockStyle.Fill
        Me.dgvBalances.EnableHeadersVisualStyles = False
        Me.dgvBalances.GridColor = System.Drawing.Color.FromArgb(CType(CType(231, Byte), Integer), CType(CType(231, Byte), Integer), CType(CType(231, Byte), Integer))
        Me.dgvBalances.Location = New System.Drawing.Point(20, 10)
        Me.dgvBalances.MultiSelect = False
        Me.dgvBalances.Name = "dgvBalances"
        Me.dgvBalances.RowHeadersVisible = False
        Me.dgvBalances.RowTemplate.Height = 35
        Me.dgvBalances.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgvBalances.Size = New System.Drawing.Size(1160, 290)
        Me.dgvBalances.TabIndex = 0
        '
        'colOldCode
        '
        Me.colOldCode.FillWeight = 15.0!
        Me.colOldCode.HeaderText = "الكود القديم"
        Me.colOldCode.Name = "colOldCode"
        Me.colOldCode.ReadOnly = True
        '
        'colNewCode
        '
        Me.colNewCode.FillWeight = 20.0!
        Me.colNewCode.HeaderText = "رقم الحساب الجديد"
        Me.colNewCode.Name = "colNewCode"
        '
        'colAccountName
        '
        Me.colAccountName.FillWeight = 35.0!
        Me.colAccountName.HeaderText = "اسم الحساب"
        Me.colAccountName.Name = "colAccountName"
        Me.colAccountName.ReadOnly = True
        '
        'colOriginalBalance
        '
        Me.colOriginalBalance.FillWeight = 15.0!
        Me.colOriginalBalance.HeaderText = "الرصيد"
        Me.colOriginalBalance.Name = "colOriginalBalance"
        Me.colOriginalBalance.ReadOnly = True
        '
        'colStatus
        '
        Me.colStatus.FillWeight = 15.0!
        Me.colStatus.HeaderText = "الحالة"
        Me.colStatus.Name = "colStatus"
        Me.colStatus.ReadOnly = True
        '
        'pnlSummary
        '
        Me.pnlSummary.BackColor = System.Drawing.Color.FromArgb(CType(CType(248, Byte), Integer), CType(CType(249, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.pnlSummary.Controls.Add(Me.grpSummary)
        Me.pnlSummary.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlSummary.Location = New System.Drawing.Point(0, 470)
        Me.pnlSummary.Name = "pnlSummary"
        Me.pnlSummary.Padding = New System.Windows.Forms.Padding(20, 10, 20, 10)
        Me.pnlSummary.Size = New System.Drawing.Size(1200, 80)
        Me.pnlSummary.TabIndex = 3
        '
        'grpSummary
        '
        Me.grpSummary.Controls.Add(Me.lblTotalCredits)
        Me.grpSummary.Controls.Add(Me.lblTotalDebits)
        Me.grpSummary.Controls.Add(Me.lblTotalRecords)
        Me.grpSummary.Controls.Add(Me.lblCreditsAmount)
        Me.grpSummary.Controls.Add(Me.lblDebitsAmount)
        Me.grpSummary.Controls.Add(Me.lblRecordsCount)
        Me.grpSummary.Dock = System.Windows.Forms.DockStyle.Fill
        Me.grpSummary.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.grpSummary.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.grpSummary.Location = New System.Drawing.Point(20, 10)
        Me.grpSummary.Name = "grpSummary"
        Me.grpSummary.Size = New System.Drawing.Size(1160, 60)
        Me.grpSummary.TabIndex = 0
        Me.grpSummary.TabStop = False
        Me.grpSummary.Text = "ملخص الأرصدة"
        '
        'lblTotalCredits
        '
        Me.lblTotalCredits.AutoSize = True
        Me.lblTotalCredits.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.lblTotalCredits.Location = New System.Drawing.Point(600, 25)
        Me.lblTotalCredits.Name = "lblTotalCredits"
        Me.lblTotalCredits.Size = New System.Drawing.Size(91, 15)
        Me.lblTotalCredits.TabIndex = 4
        Me.lblTotalCredits.Text = "إجمالي الدائن:"
        '
        'lblTotalDebits
        '
        Me.lblTotalDebits.AutoSize = True
        Me.lblTotalDebits.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.lblTotalDebits.Location = New System.Drawing.Point(350, 25)
        Me.lblTotalDebits.Name = "lblTotalDebits"
        Me.lblTotalDebits.Size = New System.Drawing.Size(87, 15)
        Me.lblTotalDebits.TabIndex = 2
        Me.lblTotalDebits.Text = "إجمالي المدين:"
        '
        'lblTotalRecords
        '
        Me.lblTotalRecords.AutoSize = True
        Me.lblTotalRecords.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.lblTotalRecords.Location = New System.Drawing.Point(20, 25)
        Me.lblTotalRecords.Name = "lblTotalRecords"
        Me.lblTotalRecords.Size = New System.Drawing.Size(84, 15)
        Me.lblTotalRecords.TabIndex = 0
        Me.lblTotalRecords.Text = "عدد السجلات:"
        '
        'lblCreditsAmount
        '
        Me.lblCreditsAmount.AutoSize = True
        Me.lblCreditsAmount.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.lblCreditsAmount.ForeColor = System.Drawing.Color.FromArgb(CType(CType(231, Byte), Integer), CType(CType(76, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.lblCreditsAmount.Location = New System.Drawing.Point(700, 25)
        Me.lblCreditsAmount.Name = "lblCreditsAmount"
        Me.lblCreditsAmount.Size = New System.Drawing.Size(28, 15)
        Me.lblCreditsAmount.TabIndex = 5
        Me.lblCreditsAmount.Text = "0.00"
        '
        'lblDebitsAmount
        '
        Me.lblDebitsAmount.AutoSize = True
        Me.lblDebitsAmount.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.lblDebitsAmount.ForeColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(204, Byte), Integer), CType(CType(113, Byte), Integer))
        Me.lblDebitsAmount.Location = New System.Drawing.Point(450, 25)
        Me.lblDebitsAmount.Name = "lblDebitsAmount"
        Me.lblDebitsAmount.Size = New System.Drawing.Size(28, 15)
        Me.lblDebitsAmount.TabIndex = 3
        Me.lblDebitsAmount.Text = "0.00"
        '
        'lblRecordsCount
        '
        Me.lblRecordsCount.AutoSize = True
        Me.lblRecordsCount.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.lblRecordsCount.ForeColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.lblRecordsCount.Location = New System.Drawing.Point(120, 25)
        Me.lblRecordsCount.Name = "lblRecordsCount"
        Me.lblRecordsCount.Size = New System.Drawing.Size(13, 15)
        Me.lblRecordsCount.TabIndex = 1
        Me.lblRecordsCount.Text = "0"
        '
        'pnlStatus
        '
        Me.pnlStatus.BackColor = System.Drawing.Color.FromArgb(CType(CType(248, Byte), Integer), CType(CType(249, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.pnlStatus.Controls.Add(Me.progressBar)
        Me.pnlStatus.Controls.Add(Me.lblStatus)
        Me.pnlStatus.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlStatus.Location = New System.Drawing.Point(0, 550)
        Me.pnlStatus.Name = "pnlStatus"
        Me.pnlStatus.Size = New System.Drawing.Size(1200, 40)
        Me.pnlStatus.TabIndex = 4
        '
        'progressBar
        '
        Me.progressBar.Location = New System.Drawing.Point(850, 10)
        Me.progressBar.Name = "progressBar"
        Me.progressBar.Size = New System.Drawing.Size(330, 20)
        Me.progressBar.Style = System.Windows.Forms.ProgressBarStyle.Continuous
        Me.progressBar.TabIndex = 1
        Me.progressBar.Visible = False
        '
        'lblStatus
        '
        Me.lblStatus.AutoSize = True
        Me.lblStatus.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.lblStatus.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblStatus.Location = New System.Drawing.Point(20, 12)
        Me.lblStatus.Name = "lblStatus"
        Me.lblStatus.Size = New System.Drawing.Size(30, 15)
        Me.lblStatus.TabIndex = 0
        Me.lblStatus.Text = "جاهز"
        '
        'frmBalanceUpload
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 15.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.White
        Me.ClientSize = New System.Drawing.Size(1200, 590)
        Me.Controls.Add(Me.pnlGrid)
        Me.Controls.Add(Me.pnlSummary)
        Me.Controls.Add(Me.pnlStatus)
        Me.Controls.Add(Me.pnlControls)
        Me.Controls.Add(Me.pnlHeader)
        Me.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New System.Drawing.Size(1000, 600)
        Me.Name = "frmBalanceUpload"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "نظام رفع الأرصدة الافتتاحية - العملاء والموردين"
        Me.pnlHeader.ResumeLayout(False)
        Me.pnlControls.ResumeLayout(False)
        Me.grpSettings.ResumeLayout(False)
        Me.grpSettings.PerformLayout()
        Me.pnlActions.ResumeLayout(False)
        Me.pnlGrid.ResumeLayout(False)
        CType(Me.dgvBalances, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlSummary.ResumeLayout(False)
        Me.grpSummary.ResumeLayout(False)
        Me.grpSummary.PerformLayout()
        Me.pnlStatus.ResumeLayout(False)
        Me.pnlStatus.PerformLayout()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents pnlHeader As Panel
    Friend WithEvents lblTitle As Label
    Friend WithEvents pnlControls As Panel
    Friend WithEvents grpSettings As GroupBox
    Friend WithEvents rbVendor As RadioButton
    Friend WithEvents rbCustomer As RadioButton
    Friend WithEvents dtpEntryDate As DateTimePicker
    Friend WithEvents lblDate As Label
    Friend WithEvents pnlActions As Panel
    Friend WithEvents btnImportExcel As Button
    Friend WithEvents btnValidateMapping As Button
    Friend WithEvents btnProcessBalances As Button
    Friend WithEvents btnExportReport As Button
    Friend WithEvents pnlGrid As Panel
    Friend WithEvents dgvBalances As DataGridView
    Friend WithEvents colOldCode As DataGridViewTextBoxColumn
    Friend WithEvents colNewCode As DataGridViewComboBoxColumn
    Friend WithEvents colAccountName As DataGridViewTextBoxColumn
    Friend WithEvents colOriginalBalance As DataGridViewTextBoxColumn
    Friend WithEvents colStatus As DataGridViewTextBoxColumn
    Friend WithEvents pnlSummary As Panel
    Friend WithEvents grpSummary As GroupBox
    Friend WithEvents lblTotalRecords As Label
    Friend WithEvents lblRecordsCount As Label
    Friend WithEvents lblTotalDebits As Label
    Friend WithEvents lblDebitsAmount As Label
    Friend WithEvents lblTotalCredits As Label
    Friend WithEvents lblCreditsAmount As Label
    Friend WithEvents pnlStatus As Panel
    Friend WithEvents lblStatus As Label
    Friend WithEvents progressBar As ProgressBar
End Class