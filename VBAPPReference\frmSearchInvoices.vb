﻿Imports System.Data.SqlClient
Imports Excel = Microsoft.Office.Interop.Excel
Imports System.Runtime.InteropServices
Public Class frmSearchInvoices

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click

        Dim invType As String = Trim(cmbxInvoiceType.Text)

        If invType <> "" AndAlso invType <> "مبيعات" AndAlso invType <> "مرتجع مبيعات" Then
            Exit Sub
        End If



        Dim CMDString As String = ""

        If rdbInvoices.Checked = True Then
            CMDString = "Select * from InvoicesForSearch where [تاريخ الفاتورة] >= '" & Format(dtpFrom.Value.Date, "yyyy-MM-dd") & "' and [تاريخ الفاتورة] <= '" & Format(dtpTo.Value.Date, "yyyy-MM-dd") & "' and [نوع الفاتورة] in ('مبيعات','مرتجع مبيعات')"

            If Val(txtInvoiceNo.Text) <> 0 Then
                CMDString += " and [رقم الفاتورة] = " & Val(txtInvoiceNo.Text) & ""
            End If
            If invType <> "" Then
                CMDString += " and [نوع الفاتورة] = '" & invType & "'"

            End If
            If Trim(cmbxStore.Text) <> "" Then
                CMDString += " And [المخزن] = '" & Trim(cmbxStore.Text) & "'"
            End If
            If Val(cmbxAccountNo.Text) <> 0 Then
                CMDString += " and [رقم الحساب] = " & Val(cmbxAccountNo.Text) & ""
            End If
            If Val(txtVATReg.Text) <> 0 Then
                CMDString += " and [رقم التسجيل الضريبي] = " & Val(txtVATReg.Text) & ""
            End If
            If Val(txtTotal.Text) <> 0 Then
                CMDString += " and [مجموع الفاتورة] = " & Val(txtTotal.Text) & ""
            End If
            If Val(txtVAT.Text) <> 0 Then
                CMDString += " and [الضريبة] = " & Val(txtVAT.Text) & ""
            End If
            If Val(txtInvoiceValue.Text) <> 0 Then
                CMDString += " and [الإجمالي شامل الضريبة] = " & Val(txtInvoiceValue.Text) & ""
            End If
            If Trim(cmbxPaymentMethod.Text) <> "" Then
                CMDString += " and [طريقة الدفع] = '" & Trim(cmbxPaymentMethod.Text) & "'"
            End If
            If Trim(cmbxUser.Text) <> "" Then
                CMDString += " and [المستخدم] = '" & Trim(cmbxUser.Text) & "'"
            End If
            If Trim(cmbxEmployee.Text) <> "" Then
                CMDString += " and [المندوب] = '" & Trim(cmbxEmployee.Text) & "'"
            End If
            If Trim(txtNotes.Text) <> "" Then
                CMDString += " and [ملاحظات] = '" & Trim(txtNotes.Text) & "'"
            End If
            If Trim(txtReference.Text) <> "" Then
                CMDString += " and [رقم مرجع الفاتورة] = '" & Trim(txtReference.Text) & "'"
            End If

            CMDString += "  order by [رقم الفاتورة] desc"

            If CMDString <> "" Then
                Dim ds As DataSet = New DataSet
                Dim CMD As New SqlCommand
                CMD.CommandText = CMDString
                CMD.Connection = Con
                Dim da As New SqlDataAdapter(CMD)
                ds.Clear()
                da.Fill(ds)
                DataGridView1.DataSource = ds.Tables(0)
            Else
                DataGridView1.DataSource = ""
            End If
        ElseIf rdbInvoiceLines.Checked = True Then
            CMDString = "SELECT * from InvoiceSearchByItem where [تاريخ الفاتورة] >= '" & Format(dtpFrom.Value.Date, "yyyy-MM-dd") & "' and [تاريخ الفاتورة] <= '" & Format(dtpTo.Value.Date, "yyyy-MM-dd") & "' and [نوع الفاتورة] in ('مبيعات','مرتجع مبيعات')"

            If Val(txtInvoiceNo.Text) <> 0 Then
                CMDString += " and [رقم الفاتورة] = " & Val(txtInvoiceNo.Text) & ""
            End If
            If Trim(cmbxInvoiceType.Text) <> "" Then
                CMDString += " and [نوع الفاتورة] = '" & Trim(cmbxInvoiceType.Text) & "'"
            End If
            If Trim(cmbxStore.Text) <> "" Then
                CMDString += " and [المخزن] = '" & Trim(cmbxStore.Text) & "'"
            End If
            If Val(cmbxAccountNo.Text) <> 0 Then
                CMDString += " and [رقم الحساب] = " & Val(cmbxAccountNo.Text) & ""
            End If
            If Val(txtVATReg.Text) <> 0 Then
                CMDString += " and [رقم التسجيل الضريبي] = " & Val(txtVATReg.Text) & ""
            End If
            If Val(txtTotal.Text) <> 0 Then
                CMDString += " and [مجموع الفاتورة] = " & Val(txtTotal.Text) & ""
            End If
            If Val(txtVAT.Text) <> 0 Then
                CMDString += " and [الضريبة] = " & Val(txtVAT.Text) & ""
            End If
            If Val(txtInvoiceValue.Text) <> 0 Then
                CMDString += " and [الإجمالي شامل الضريبة] = " & Val(txtInvoiceValue.Text) & ""
            End If
            If Trim(cmbxPaymentMethod.Text) <> "" Then
                CMDString += " and [طريقة الدفع] = '" & Trim(cmbxPaymentMethod.Text) & "'"
            End If
            If Trim(cmbxUser.Text) <> "" Then
                CMDString += " and [المستخدم] = '" & Trim(cmbxUser.Text) & "'"
            End If
            If Trim(cmbxEmployee.Text) <> "" Then
                CMDString += " and [المندوب] = '" & Trim(cmbxEmployee.Text) & "'"
            End If
            If Trim(txtNotes.Text) <> "" Then
                CMDString += " and [ملاحظات] = '" & Trim(txtNotes.Text) & "'"
            End If
            If Trim(txtReference.Text) <> "" Then
                CMDString += " and [رقم مرجع الفاتورة] = '" & Trim(txtReference.Text) & "'"
            End If

            If Trim(txtSearch.Text) <> "" Then
                CMDString += " and [رقم الصنف] = '" & Trim(txtSearch.Text) & "'"
            End If

            If CMDString <> "" Then
                Dim ds As DataSet = New DataSet
                Dim CMD As New SqlCommand
                CMD.CommandText = CMDString
                CMD.Connection = Con
                Dim da As New SqlDataAdapter(CMD)
                ds.Clear()
                da.Fill(ds)
                DataGridView1.DataSource = ds.Tables(0)
            Else
                DataGridView1.DataSource = ""
            End If


        End If
    End Sub

    Private Sub frmSearch_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        'frmMain.Enabled = True
    End Sub
    Private Sub FillComboBoxWithEmployees()
        Dim CMD As New SqlCommand("Select EmployeeNo,Emp_Name from tblEmployees order by EmployeeNo", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "Emp_Name + ' - ' + CONVERT(EmployeeNo, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxEmployee.DataSource = dt
        cmbxEmployee.DisplayMember = "DisplayText" ' Show Name
        cmbxEmployee.ValueMember = "EmployeeNo" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        cmbxEmployee.Text = ""
    End Sub
    Private Sub frmSearch_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AccountsLoad()
        StoresLoad()
        DateLoad()
        rdbInvoices.Checked = True
        cmbxInvoiceType.SelectedIndex = 0
        If UserType = "Limited" Then
            cmbxUser.Items.Clear()
            cmbxUser.Items.Add(UserName)
            cmbxUser.Text = UserName
            cmbxUser.Enabled = False
        Else
            UsersLoad()
            cmbxUser.Enabled = True
        End If
        ForceGregorianForAllPickers(Me)
        FillComboBoxWithEmployees()
        LoadUserAuthorization(UserName) ' Pass the logged-in username
    End Sub
    Sub LoadUserAuthorization(ByVal username As String)
        Try
            Dim query As String = "SELECT GroupID, DefaultStore, StoreChange, DefaultCustomer, CustomerChange, DefaultCashier, CashierChange, ChangeInvoicePrice, MaxDiscountPercent 
                               FROM tblUsers WHERE Username = @Username"

            Dim cmd As New SqlCommand(query, Con)
            cmd.Parameters.AddWithValue("@Username", username)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            Dim reader As SqlDataReader = cmd.ExecuteReader()

            If reader.Read() Then
                Dim isAdmin As Boolean = (Not IsDBNull(reader("GroupID")) AndAlso Convert.ToInt32(reader("GroupID")) = 1)

                ' If the user is in GroupID = 1 (Admin), grant full access
                If isAdmin Then

                Else
                    ' Apply Default Store
                    If Not IsDBNull(reader("DefaultStore")) Then
                        cmbxStore.SelectedItem = reader("DefaultStore").ToString()
                    End If



                End If
            End If

            reader.Close()

            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If

        Catch ex As Exception
            MessageBox.Show("Error loading user authorization: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Sub UsersLoad()
        Dim CMD As New SqlCommand("Select Distinct([المستخدم]) from InvoicesForSearch order by [المستخدم]", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxUser.Items.Clear()
        Do While reader.Read
            cmbxUser.Items.Add(reader.Item(0).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Sub AccountsLoad()
        Dim CMD As New SqlCommand("Select Distinct([رقم الحساب]) from InvoicesForSearch order by [رقم الحساب]", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxAccountNo.Items.Clear()
        Do While reader.Read
            cmbxAccountNo.Items.Add(reader.Item(0).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Sub StoresLoad()
        Dim CMD As New SqlCommand("Select Store from tblStores Order by Store", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxStore.Items.Clear()
        Do While reader.Read
            cmbxStore.Items.Add(reader.Item("Store").ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If

    End Sub
    Sub DateLoad()
        'Dim CMD As New SqlCommand("Select Min([تاريخ الفاتورة]) from InvoicesForSearch", Con)

        'If Con.State <> ConnectionState.Open Then
        '    Con.Open()
        'End If
        'Dim reader As SqlDataReader = CMD.ExecuteReader
        'If reader.Read Then
        '    If Not IsDBNull(reader.Item(0)) Then
        '        dtpFrom.Value = CDate(reader.Item(0))
        '    Else
        '        dtpFrom.Value = Date.Today ' or any default value you prefer
        '    End If
        'End If
        'If reader.IsClosed Then
        'Else
        '    reader.Close()
        'End If
        'If Con.State <> ConnectionState.Closed Then
        '    Con.Close()
        'End If
        dtpFrom.Value = Date.Today.Subtract(New TimeSpan(30, 0, 0, 0)) ' Set default to 30 days ago

    End Sub
    Private Sub txtSN_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        rdbInvoices.Checked = True
    End Sub
    Private Sub btnPrintInvoice_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrintInvoice.Click
        If DataGridView1.SelectedRows.Count - 1 >= 0 Then
            If DataGridView1.SelectedRows(0).Cells(1).Value = "مبيعات" Then
                PrintType = "SalesInvoice"

            ElseIf DataGridView1.SelectedRows(0).Cells(1).Value = "مرتجع مبيعات" Then
                PrintType = "SalesReturnInvoice"
                'ElseIf DataGridView1.SelectedRows(0).Cells(1).Value = "مشتريات" Then
                '    PrintType = "PurchaseInvoice"
                'ElseIf DataGridView1.SelectedRows(0).Cells(1).Value = "مرتجع مشتريات" Then
                '    PrintType = "ReturnPurchaseInvoice"
                'ElseIf DataGridView1.SelectedRows(0).Cells(1).Value = "رصيد افتتاحي" Then
                '    PrintType = "OpenStockInvoice"
            Else
                PrintType = ""
            End If

            If PrintType <> "" Then
                InvNoForPrint = DataGridView1.SelectedRows(0).Cells(2).Value
                InvoiceType = DataGridView1.SelectedRows(0).Cells(1).Value
                frmPrintPreview.MdiParent = frmMain
                frmPrintPreview.Show()
            End If
        Else
            MsgBox("يجب تحديد سطر واحد فقط على الأقل لطباعة نسخة من الفاتورة", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

    Private Sub btnPrintPOSInvoice_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrintPOSInvoice.Click
        If DataGridView1.SelectedRows.Count - 1 >= 0 Then
            If DataGridView1.SelectedRows(0).Cells(1).Value <> "مبيعات" Then
                MsgBox("يمكن طباعة فاتورة المبيعات المبسطة فقط", MsgBoxStyle.Critical, "نظام السلطان")
                Exit Sub
            End If
            'If DataGridView1.SelectedRows(0).Cells(4).Value <> " " Then
            '    MsgBox("يمكن طباعة فاتورة المبيعات المبسطة فقط", MsgBoxStyle.Critical, "نظام السلطان")
            '    Exit Sub
            'End If
            If Not IsDBNull(DataGridView1.SelectedRows(0).Cells(4).Value) AndAlso
                Not String.IsNullOrWhiteSpace(DataGridView1.SelectedRows(0).Cells(4).Value.ToString()) Then

                MsgBox("يمكن طباعة فاتورة المبيعات المبسطة فقط", MsgBoxStyle.Critical, "نظام السلطان")
                Exit Sub
            End If


            PrintType = "POSInvoice"
            InvNoForPrint = DataGridView1.SelectedRows(0).Cells(2).Value
            frmPrintPreview.MdiParent = frmMain
            frmPrintPreview.Show()
        Else
            MsgBox("يجب تحديد سطر واحد فقط على الأقل لطباعة نسخة من الفاتورة", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

    Private Sub btnItemSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnItemSearch.Click
        SearchItemFor = "SalesSearch"
        frmItemSearch.ShowDialog()

    End Sub

    Private Sub rdbInvoices_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbInvoices.CheckedChanged
        If rdbInvoices.Checked = True Then
            txtSearch.Enabled = False
            btnItemSearch.Enabled = False
        ElseIf rdbInvoiceLines.Checked = True Then
            txtSearch.Enabled = True
            btnItemSearch.Enabled = True
        End If
    End Sub

    Private Sub rdbInvoiceLines_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbInvoiceLines.CheckedChanged
        If rdbInvoices.Checked = True Then
            txtSearch.Enabled = False
            btnItemSearch.Enabled = False
        ElseIf rdbInvoiceLines.Checked = True Then
            txtSearch.Enabled = True
            btnItemSearch.Enabled = True
        End If
    End Sub
    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        ExportToExcelAndSave()
    End Sub


    Private Sub ExportToExcelAndSave()
        If DataGridView1.Rows.Count = 0 Then
            MessageBox.Show("لا يوجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim saveFileDialog As New SaveFileDialog()
        saveFileDialog.Filter = "Excel Workbook|*.xlsx"
        saveFileDialog.Title = "حدد مكان حفظ الملف"
        saveFileDialog.FileName = "تقرير المبيعات.xlsx"

        If saveFileDialog.ShowDialog() = DialogResult.OK Then
            Dim xlApp As Excel.Application = Nothing
            Dim xlWorkbook As Excel.Workbook = Nothing
            Dim xlWorksheet As Excel.Worksheet = Nothing

            Try
                xlApp = New Excel.Application()
                xlWorkbook = xlApp.Workbooks.Add()
                xlWorksheet = CType(xlWorkbook.Sheets(1), Excel.Worksheet)

                ' Header row
                Dim colIndex As Integer = 1
                For Each col As DataGridViewColumn In DataGridView1.Columns
                    If col.Visible Then
                        xlWorksheet.Cells(1, colIndex).Value = col.HeaderText
                        colIndex += 1
                    End If
                Next

                ' Data rows
                Dim rowIndex As Integer = 2
                For Each row As DataGridViewRow In DataGridView1.Rows
                    If Not row.IsNewRow Then
                        colIndex = 1
                        For Each col As DataGridViewColumn In DataGridView1.Columns
                            If col.Visible Then
                                xlWorksheet.Cells(rowIndex, colIndex).Value = row.Cells(col.Index).Value?.ToString()
                                colIndex += 1
                            End If
                        Next
                        rowIndex += 1
                    End If
                Next

                ' Save the file
                xlWorkbook.SaveAs(saveFileDialog.FileName)
                MessageBox.Show("تم حفظ الملف بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)

            Catch ex As Exception
                MessageBox.Show("خطأ أثناء التصدير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Finally
                ' Cleanup Excel COM objects
                If Not IsNothing(xlWorkbook) Then xlWorkbook.Close(False)
                If Not IsNothing(xlApp) Then xlApp.Quit()
                Marshal.ReleaseComObject(xlWorksheet)
                Marshal.ReleaseComObject(xlWorkbook)
                Marshal.ReleaseComObject(xlApp)
            End Try
        End If
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        InvoiceType = Trim(cmbxInvoiceType.Text)
        If InvoiceType = "" Then
            MsgBox("يجب تحديد نوع الفاتورة قبل طباعة التقرير", MsgBoxStyle.Critical, "نظام السلطان")
            Exit Sub
        End If
        If rdbInvoiceLines.Checked = True Then
            PrintType = "SalesLines"
        ElseIf rdbInvoices.Checked = True Then
            PrintType = "SalesHeader"
        End If

        DateStrFrom = dtpFrom.Value.ToString("yyyy-MM-dd")
        DateStrTo = dtpTo.Value.ToString("yyyy-MM-dd")
        InvoiceType = cmbxInvoiceType.Text
        frmPrintPreview.MdiParent = frmMain
        frmPrintPreview.Show()


    End Sub

End Class