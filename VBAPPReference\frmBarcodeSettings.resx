﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnSave.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6BJREFUWEe1
        V3tMVNkZv7vbZkuWtkZRYsLDREEew8AMAzPDMMyACMNLBhQRZaKAT4YARfEZAmTXJdooioor7oYKZgO+
        dttorJq4gi8UW8TbAVNNVDTZiBEcjVT9o7/m+2buMOBst7ttT/LLPffcc7/v9/3OOd/9riA42gfOq9Q+
        FgTB69/Bz8/Pa2hoiEF95zu/EAThl5PwodO+BKm5+typr6/Pam1t/WNzc/Pl27dv97969WpwZGRkwBNe
        vHgx0N/fP3DixAmGzWYbbGho6A8ICLgVERFxSy6XM2Sy8L/k5+dfr6ysvFxRUXGlurr6yrZt2/5UVVW1
        YAKJ2tpac2dnB06ePInDhw/jwYMH+LHWdqQNs2fPxpw5c3DkyBG0trYiKCgIsbGxUKvV0Go03C+0WFBR
        UYF169Zh9erVWLNmDYqLi5GUlJQjKfBhU1NTFzlvaWnB7t270dfXhzdv3sBut3sEtYNffIHg4GDGwYMH
        0dzcjJCQEGg0GgaRiI6ORkJCAgxGA7RaLWJiVFAoFIiJiYFKpbosCMJHRMC7sbHRdvToUTayY8cO3Lx5
        kx0NDw97xNu3b9mpTCZj0Hv79+/nvlaj5chVKhUUSiUTiYuLg0argUbtUIXGIiMjBwRB+DUR+E1NTY3Y
        1taGpqYmbP9sO65evYpnz57hyZMnHvHypR2HDh3iCCmiAwcO8LtyuRw6nc4Fipqu8fHxDhJOZegaERFh
        EwTht0zAYrGItbW1aGxsRH19Pbq7u/H06VM8evQIQ0NDDPf+6OgoR61UKjnqvXv3sgIxKhUSExMZRqMR
        hgQD9Hq9iwAR8kiAFNiwYQNvki1btuDChQu4f/8+7t69i8HBQQwMDLhA9/fu3eM5pAItRVdXF3bu3MmO
        UlNTkZqSipSUFMyfPx8Gg4HHJUU8EqirqxMpgrKyMixZsgREpqamBitXrkRBQQFKS0tRXl6OVatWYfny
        5byTrVara3eXFJcgNzcXGRkZjPT0dJjSTEwmMdH44wQ2b94s7tq1i+Unw7m5C2E2m+moYOHChbAUWpjY
        4sWLYTKZkJmZCXN2NkdJmDdvHkdLDulK97QMBqORT4FeHw+daw/Evk+gurpabGhoYPk3btzIjki6rKws
        FBUVo7CwEEsKCpCfn4+8vDwmQJHS8+zsbGSkZzAxiUBycjKTp31ABNw3oXQKZDLZOIGqqirx008/Q3V1
        NUdKERCJnJwclpMc0/2iRYtYERpLS0tj50SCJTeZkJIyrgARcCigh04X75KfcgCRmEDAarWKdXV1WLp0
        qcv5ihUrsGzZMixYsIBlzjabmRA5ougzsxwqEKToaZ4jeucSGBynQBen4zwg5QciER4ePk4gKytLtFgs
        bIg2XVFRESuR54yYopR2NUUuLQH13Z3PT3Zbf8m52+Yjx5Q7iER4aOg4AZPJJNKmSzOZePPROi/Oy+OI
        yTk5JMMEcubu2D1yyfkPrT05ptxBJEJCQsYJpKamiuSMDBFzWjcySE4INEZrSudbIkLPJUhrLkXuyTkl
        KWW0ElEKBWfPuXPnTiRgzslhiYl9vD6eXyKQfCTlvORxJzTHmGh0ZT1X5nPK/p5zp/RKpQJRUVGM9wmY
        zUyAjJFMkeFhiImMgFoRiTiVEnp1DAxxaiRo1TBo1TDGaWDUORGn4bEETSzP08VGQ6tSQKOMQmyUHGEh
        c/k7wXYjI5lAcLAbAdoDOTkOAsS6rLQU3168gq8v9qDju+tO9KCTcOkGjhG6JsE53nmJ5jjmdly87pj/
        zbcwJOj5u0FEiERwcPBkAjmOI5QQj0u9/dAf/TsC9/Rizr5bDjRJ6GXMngRp3DXP+Z7P73vw1d1/4KsD
        exEYGOiZQFpamki5nBRI0Mfjjm0ATX8Wsf4Pl7C5vftnY1N7N37X+h3++mgUrV+2ICAggFIw5HIiEDRO
        IDMzU6QsR5mMNsuAzQa8fY2x0WGMjT77LzGMf757g6Z9++DvIiCn8m1iIqJ0S0tAE0RRxOvXr7ko+V/g
        3bt3XLD4+/vzPiAfEwiYzWaRPjh0fkNDQ3Hnzh2MjIzg8ePH71VDPxVkY2xsDHv27PlhAnl5eSJ95+nM
        UmFJRenz58+5+iED0tW9L1VH7nCf4z6PCFCx6+fnR98AyCJktAn/JhH4ZFFubn9JSQmvDSnQ29vLxefD
        hw9doFKdMHnsP3lGBS5VTEQgLCyMVQgKChKpICYCH8tksj768oWHh/GEa9eu8dpJtaAEdwc/BVRDbv/8
        c5cCFKifn99tQRB+RQQ+mjZtWh/V9yQ/TTh79ixsNhsrcePGDUZPT8/PBtnaunUrAgMD2D790Pj4+PQ7
        f92ED3x9fa8G+PvzOaWHVKKfO3eO/5SOHz+OY8eOMTo7OydcJ/fd4T5+5swZriNnzpzJ9ikhTZ8+/Zrr
        18zb23sLyTNr1iye0N7ejvPnz+PUqVMuY+7o6OjwOOZpnHD69GkXAfqVo9Pg7e29jZ07m4+Xl9ehqVOn
        fu/r6/ty7dq19k2bNtkrKyvt5eXl9rKyMobVanVdPaHM6vnZ+vXr7UlJSfYpU6a8nDFjxvdeXl4tgiBM
        dydA7RNBEOIEQcgQBCH9/wSyTT7IF7d/AUikbuca3SIPAAAAAElFTkSuQmCC
</value>
  </data>
</root>