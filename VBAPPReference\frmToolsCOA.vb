﻿Imports System.Data.SqlClient
Public Class frmToolsCOA
    Dim sql As String
    Dim ds As New DataSet
    Dim dt As New DataTable
    Dim da As New SqlDataAdapter
    Dim dv As New DataView
    Dim cmd As New SqlCommand
    Dim nodeAuthor As TreeNode
    Dim nodeTitle As TreeNode
    Private Sub CreateTree(ByVal TV As TreeView)
        sql = "SELECT RootID, RootName, ParentID, RootLevel, convert(nvarchar(MAX),RootID) + ' : ' + RootName as MyRoot FROM tblRoots Order By RootID"
        ds = New DataSet
        dt = New DataTable
        da = New SqlDataAdapter(sql, Con)
        da.Fill(ds, "tblRoots")
        dv = New DataView(ds.Tables("tblRoots"))
        dt = dv.ToTable

        Dim MaxLevel1 As Int64 = CInt(dt.Compute("MAX(RootLevel)", ""))

        Dim i, j As Int64

        For i = 0 To MaxLevel1
            Dim Rows1() As DataRow = dt.Select("RootLevel = " & i)
            For j = 0 To Rows1.Count - 1
                Dim ID1 As String = Rows1(j).Item("RootID").ToString
                Dim Name1 As String = Rows1(j).Item("RootName").ToString
                Dim Parent1 As String = Rows1(j).Item("ParentID").ToString
                Dim FullID As String = Rows1(j).Item("MyRoot").ToString

                If Parent1 = "-1" Then
                    TV.Nodes.Add(ID1, ID1 & " : " & Name1)
                Else
                    Dim TreeNodes1() As TreeNode = TV.Nodes.Find(Parent1, True)
                    If TreeNodes1.Length > 0 Then
                        TreeNodes1(0).Nodes.Add(ID1, ID1 & " : " & Name1)

                    End If
                End If
            Next
        Next
    End Sub

    Private Sub Button2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Button2.Click
        TV.Nodes.Clear()
        CreateTree(TV)
        TV.ExpandAll()
        TV.SelectedNode = TV.Nodes(0)
        For Each ctrl As Control In Me.GroupBox1.Controls
            If TypeOf ctrl Is TextBox Then
                ctrl.Text = ""
            End If
        Next
        IsUpdate = True
    End Sub


    Private Sub جديدToolStripMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles جديدToolStripMenuItem.Click, btnNew.Click
        Dim str As String = ""
        Dim NameStr As String = ""
        If TV.Nodes.Count > 0 Then
            If TV.SelectedNode.Text = "دليل الحسابات" Then
                txtParentAcc.Text = "0"
                txtParentName.Text = "دليل الحسابات"
                Exit Sub
            End If
            For i As Int64 = 0 To TV.SelectedNode.Text.Length - 1
                If TV.SelectedNode.Text.ToCharArray(i, 1) = " " Then
                    If TV.SelectedNode.Text.ToCharArray(i + 1, 1) = ":" Then
                        Exit For
                    End If
                End If
                str += TV.SelectedNode.Text.ToCharArray(i, 1)
                NameStr = TV.SelectedNode.Text.ToString.Substring(str.Length + 2, TV.SelectedNode.Text.ToString.Length - str.Length - 2)
            Next
            txtAccID.Text = ""
            txtParentAcc.Text = str
            txtParentName.Text = NameStr
            txtAccName.Text = ""
            'ckbxBlock.Checked = False
            sql = "SELECT RootID, RootName, ParentID, RootLevel FROM tblRoots WHERE ParentID = @ParentID ORDER BY RootID DESC"
            Dim cmd As New SqlCommand(sql, Con)
            cmd.Parameters.AddWithValue("@ParentID", txtParentAcc.Text)
            da = New SqlDataAdapter(cmd)
            ds = New DataSet
            dt = New DataTable
            da.Fill(ds, "tblRoots")
            dv = New DataView(ds.Tables("tblRoots"))
            dt = dv.ToTable
            txtAccID.Text = GetNextChildID(txtParentAcc.Text)
            txtLevel.Text = (Val(txtLevel.Text) + 1).ToString()
        End If
        IsUpdate = False
    End Sub

    Private Sub TV_NodeMouseClick(ByVal sender As Object, ByVal e As TreeNodeMouseClickEventArgs) Handles TV.NodeMouseClick
        If e.Button = Windows.Forms.MouseButtons.Right Then
            TV.SelectedNode = e.Node
        End If
    End Sub



    Private Sub TV_AfterSelect(ByVal sender As Object, ByVal e As TreeViewEventArgs) Handles TV.AfterSelect
        If TV.Focused Then
            Dim str As String = ""
            Dim NameStr As String = ""
            If TV.SelectedNode.Text = "دليل الحسابات" Then
                Exit Sub
            End If
            For i As Int64 = 0 To TV.SelectedNode.Text.Length - 1
                If TV.SelectedNode.Text.ToCharArray(i, 1) = " " Then
                    If TV.SelectedNode.Text.ToCharArray(i + 1, 1) = ":" Then
                        Exit For
                    End If
                End If
                str += TV.SelectedNode.Text.ToCharArray(i, 1)
                NameStr = TV.SelectedNode.Text.ToString.Substring(str.Length + 2, TV.SelectedNode.Text.ToString.Length - str.Length - 2)
            Next
            txtAccID.Text = str
            txtAccName.Text = NameStr
            GetAccountDetails()
        End If
    End Sub
    Sub GetAccountDetails()
        Try
            If txtAccID.Text.Trim <> "" Then
                Dim query As String = "SELECT RootName, ParentID, AccountFinal, AccountType, RootLevel FROM tblRoots WHERE RootID = @ID"
                Using cmd As New SqlCommand(query, Con)
                    cmd.Parameters.AddWithValue("@ID", txtAccID.Text)

                    If Con.State <> ConnectionState.Open Then Con.Open()

                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then
                            txtAccName.Text = reader("RootName").ToString
                            cmbxFinalAccount.Text = reader("AccountFinal").ToString
                            cmbxAccountType.Text = reader("AccountType").ToString
                            AccountParent = reader("ParentID").ToString
                            txtLevel.Text = reader("RootLevel").ToString
                            txtParentAcc.Text = AccountParent
                        Else
                            txtAccName.Clear()
                            cmbxFinalAccount.Text = ""
                            cmbxAccountType.Text = "كلاهما"
                            txtParentAcc.Text = ""
                            txtLevel.Text = ""
                        End If
                    End Using
                End Using
            End If
        Catch ex As Exception
            ' Optional: Log or display the error
        Finally
            If Con.State <> ConnectionState.Closed Then Con.Close()
        End Try
    End Sub


    Private Sub تعديلToolStripMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles تعديلToolStripMenuItem.Click, bntUpdate.Click
        If txtAccID.Text = "" Then
            Exit Sub
        End If
        If IsUpdate = True Then
            'Dim UpdateCMD As New SQLCommand("Update tblRoots set RootName = '" & txtAccName.Text.Trim & "',RootBlocked = '" & ckbxBlock.Checked & "',ModifiedBy = '" & UserName & "',ModifiedOn= GetDate() where RootID = " & CInt(txtAccID.Text) & "", Con)
            Dim UpdateCMD As New SqlCommand("Update tblRoots set RootName = '" & txtAccName.Text.Trim & "',AccountFinal='" & cmbxFinalAccount.Text.Trim & "',AccountType = '" & cmbxAccountType.Text.Trim & "',ModifiedBy = '" & UserName & "',ModifiedOn= GetDate() where RootID = " & CInt(txtAccID.Text) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            Button2_Click(Nothing, Nothing)
        Else
            Dim InsertCMD As New SqlCommand("Insert Into tblRoots (RootID,RootName,ParentID,RootLevel,AccountFinal,AccountType,CreatedBy,CreatedOn) Values (" & CInt(txtAccID.Text) & ",'" & txtAccName.Text.Trim & "'," & CInt(txtParentAcc.Text) & "," & CInt(txtLevel.Text) & ",'" & cmbxFinalAccount.Text.Trim & "','" & cmbxAccountType.Text.Trim & "','" & UserName & "',GetDate())", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            InsertCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If

            Button2_Click(Nothing, Nothing)
        End If

    End Sub

    Private Sub حذفToolStripMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles حذفToolStripMenuItem.Click, btnDelete.Click
        If txtAccID.Text = "" Then
            Exit Sub
        End If
        If IsUpdate = True Then
            If TV.SelectedNode.Text = "دليل الحسابات" Then
                Exit Sub
            End If
            If MsgBox("هل تريد حذف الحساب؟", MsgBoxStyle.YesNo, "دليل الحسابات") = MsgBoxResult.Yes Then
                Dim checkChildren As New SqlCommand("SELECT COUNT(*) FROM tblRoots WHERE ParentID = @ID", Con)
                checkChildren.Parameters.AddWithValue("@ID", txtAccID.Text)
                Con.Open()
                Dim childCount As Integer = Convert.ToInt32(checkChildren.ExecuteScalar())
                Con.Close()

                If childCount > 0 Then
                    MsgBox("Cannot delete account with child accounts.", MsgBoxStyle.Exclamation)
                    Exit Sub
                End If

                cmd = New SqlCommand("DELETE FROM tblRoots WHERE RootId=" & CInt(txtAccID.Text), Con)
                Con.Open()
                cmd.ExecuteNonQuery()
                Con.Close()
                Button2_Click(Nothing, Nothing)
            End If
        End If

    End Sub

    Private Sub الغاءToolStripMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles الغاءToolStripMenuItem.Click, btnCancel.Click
        Button2_Click(Nothing, Nothing)
    End Sub

    Private Sub خروجToolStripMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles خروجToolStripMenuItem.Click, btnClose.Click
        If MsgBox("هل تريد الخروج", MsgBoxStyle.YesNo, "دليل الحسابات") = MsgBoxResult.Yes Then
            End
        End If
    End Sub

    Private Sub Form1_Load(ByVal sender As Object, ByVal e As EventArgs) Handles MyBase.Load
        Button2_Click(Nothing, Nothing)
    End Sub



    Private Sub txtParentAcc_TextChanged(sender As Object, e As EventArgs) Handles txtParentAcc.TextChanged
        Try
            If txtParentAcc.Text.Trim <> "" Then
                Dim query As String = "SELECT RootName FROM tblRoots WHERE RootID = @ID"
                Using cmd As New SqlCommand(query, Con)
                    cmd.Parameters.AddWithValue("@ID", txtParentAcc.Text)

                    If Con.State <> ConnectionState.Open Then Con.Open()

                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then
                            txtParentName.Text = reader("RootName").ToString
                        Else
                            txtParentName.Text = ""
                        End If
                    End Using
                End Using
            End If
        Catch ex As Exception
            txtParentName.Text = ""
        Finally
            If Con.State <> ConnectionState.Closed Then Con.Close()
        End Try
    End Sub


    Private Function GetNextChildID(ByVal parentID As String) As String
        Dim nextID As String = ""
        Try
            Dim query As String = "SELECT RootID FROM tblRoots WHERE ParentID = @ParentID ORDER BY RootID DESC"
            Using cmd As New SqlCommand(query, Con)
                cmd.Parameters.AddWithValue("@ParentID", parentID)
                If Con.State <> ConnectionState.Open Then Con.Open()
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    If reader.Read() Then
                        Dim lastID As String = reader.GetString(0)
                        Dim lastPart As String = lastID.Substring(parentID.Length).TrimStart("."c)
                        Dim lastNum As Integer
                        If Integer.TryParse(lastPart, lastNum) Then
                            nextID = parentID & "." & (lastNum + 1).ToString("D3") ' padded to 3 digits
                        Else
                            nextID = parentID & ".001"
                        End If
                    Else
                        nextID = parentID & ".001"
                    End If
                End Using
            End Using
        Catch ex As Exception
            nextID = parentID & ".001"
        Finally
            If Con.State <> ConnectionState.Closed Then Con.Close()
        End Try
        Return nextID
    End Function

End Class
