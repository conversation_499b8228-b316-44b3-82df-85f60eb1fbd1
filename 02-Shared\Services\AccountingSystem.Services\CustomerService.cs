using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public class CustomerService : ICustomerService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<CustomerService> _logger;

        public CustomerService(AccountingDbContext context, ILogger<CustomerService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<Customer>> GetCustomersAsync(string searchTerm = "")
        {
            var query = _context.Customers.AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(c => 
                    c.CustomerName.Contains(searchTerm) || 
                    c.CustomerNo.ToString().Contains(searchTerm) ||
                    c.FirstName.Contains(searchTerm) ||
                    c.LastName.Contains(searchTerm) ||
                    (c.Mobile != null && c.Mobile.ToString().Contains(searchTerm))
                );
            }

            return await query.OrderBy(c => c.CustomerName).ToListAsync();
        }

        public async Task<Customer?> GetCustomerByNoAsync(long customerNo)
        {
            return await _context.Customers.FindAsync(customerNo);
        }

        public async Task<long> GetNextCustomerNoAsync()
        {
            if (await _context.Customers.AnyAsync())
            {
                return await _context.Customers.MaxAsync(c => c.CustomerNo) + 1;
            }
            return 1;
        }

        public async Task<string?> GetCustomerParentAccountCodeAsync()
        {
            try
            {
                var config = await _context.GLConfigs
                    .Where(g => g.EntryReferenceModule == "عملاء")
                    .FirstOrDefaultAsync();
                
                return config?.AccountNo.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer parent account code");
                return null;
            }
        }

        public async Task<string> GenerateNextCustomerAccountCodeAsync(string parentCode)
        {
            string nextSegment = "000001";
            try
            {
                var startIndex = parentCode.Length + 1;
                var maxAccountCode = await _context.ChartOfAccounts
                    .Where(a => a.ParentAccountCode == parentCode)
                    .Select(a => a.AccountCode)
                    .ToListAsync();

                if (maxAccountCode.Any())
                {
                    var maxSegment = maxAccountCode
                        .Where(code => code.Length >= startIndex + 5) // Ensure we have enough characters
                        .Select(code => 
                        {
                            var segment = code.Substring(startIndex - 1, 6);
                            return int.TryParse(segment, out int result) ? result : 0;
                        })
                        .DefaultIfEmpty(0)
                        .Max();

                    nextSegment = (maxSegment + 1).ToString("D6");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating next customer account code");
            }

            return parentCode + nextSegment;
        }

        public async Task<(bool success, string? accountCode)> CreateCustomerAsync(Customer customer, string currentUser)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Get parent account code for customers
                var parentAccountCode = await GetCustomerParentAccountCodeAsync();
                if (string.IsNullOrEmpty(parentAccountCode))
                {
                    _logger.LogError("Customer parent account code not found in GL configuration");
                    return (false, null);
                }

                // Generate customer number if not provided
                if (customer.CustomerNo == 0)
                {
                    customer.CustomerNo = await GetNextCustomerNoAsync();
                }

                // Check if customer number already exists
                if (await _context.Customers.AnyAsync(c => c.CustomerNo == customer.CustomerNo))
                {
                    return (false, null);
                }

                // Generate account code for the customer
                var generatedAccountCode = await GenerateNextCustomerAccountCodeAsync(parentAccountCode);

                // Create account in Chart of Accounts
                var customerAccount = new ChartOfAccount
                {
                    AccountCode = generatedAccountCode,
                    SegmentCode = generatedAccountCode.Substring(parentAccountCode.Length),
                    AccountName = customer.CustomerName ?? $"Customer {customer.CustomerNo}",
                    ParentAccountCode = parentAccountCode,
                    AccountLevel = generatedAccountCode.Split('.').Length,
                    IsPosting = true,
                    AccountType = "Asset",
                    AccountNature = "Debit",
                    OpeningBalance = 0,
                    Notes = "حساب آلي للعميل",
                    CreatedBy = currentUser,
                    CreatedOn = DateTime.Now
                };

                _context.ChartOfAccounts.Add(customerAccount);

                // Set customer audit fields
                customer.CreatedBy = currentUser;
                customer.CreatedOn = DateTime.Now;

                // Add customer
                _context.Customers.Add(customer);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation($"Customer {customer.CustomerNo} created successfully with account code {generatedAccountCode}");
                return (true, generatedAccountCode);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error creating customer {customer.CustomerNo}");
                return (false, null);
            }
        }

        public async Task<bool> UpdateCustomerAsync(Customer customer, string currentUser)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingCustomer = await _context.Customers.FindAsync(customer.CustomerNo);
                if (existingCustomer == null)
                {
                    return false;
                }

                // Update customer fields
                existingCustomer.CustomerName = customer.CustomerName;
                existingCustomer.FirstName = customer.FirstName;
                existingCustomer.LastName = customer.LastName;
                existingCustomer.Mobile = customer.Mobile;
                existingCustomer.Phone = customer.Phone;
                existingCustomer.Email = customer.Email;
                existingCustomer.StreetAddress1 = customer.StreetAddress1;
                existingCustomer.StreetAddress2 = customer.StreetAddress2;
                existingCustomer.City = customer.City;
                existingCustomer.Region = customer.Region;
                existingCustomer.PostalCode = customer.PostalCode;
                existingCustomer.PaymentMethod = customer.PaymentMethod;
                existingCustomer.CreditLimit = customer.CreditLimit;
                existingCustomer.PaymentTerm = customer.PaymentTerm;
                existingCustomer.ContactPerson = customer.ContactPerson;
                existingCustomer.CR = customer.CR;
                existingCustomer.VATRegNo = customer.VATRegNo;
                existingCustomer.Shop = customer.Shop;
                existingCustomer.Status = customer.Status;
                existingCustomer.LocalCustomer = customer.LocalCustomer;
                existingCustomer.EmployeeNo = customer.EmployeeNo;
                existingCustomer.Notes = customer.Notes;
                existingCustomer.BuildingNo = customer.BuildingNo;
                existingCustomer.AdditionalNo = customer.AdditionalNo;
                existingCustomer.District = customer.District;
                existingCustomer.ModifiedBy = currentUser;
                existingCustomer.ModifiedOn = DateTime.Now;

                // Update corresponding account name if customer name changed
                if (existingCustomer.CustomerName != customer.CustomerName)
                {
                    var parentAccountCode = await GetCustomerParentAccountCodeAsync();
                    if (!string.IsNullOrEmpty(parentAccountCode))
                    {
                        var customerAccount = await _context.ChartOfAccounts
                            .Where(a => a.ParentAccountCode == parentAccountCode && 
                                       a.AccountName.Contains(existingCustomer.CustomerName ?? ""))
                            .FirstOrDefaultAsync();

                        if (customerAccount != null)
                        {
                            customerAccount.AccountName = customer.CustomerName ?? $"Customer {customer.CustomerNo}";
                            customerAccount.ModifiedBy = currentUser;
                            customerAccount.ModifiedOn = DateTime.Now;
                        }
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation($"Customer {customer.CustomerNo} updated successfully");
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error updating customer {customer.CustomerNo}");
                return false;
            }
        }

        public async Task<bool> DeleteCustomerAsync(long customerNo)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var customer = await _context.Customers.FindAsync(customerNo);
                if (customer == null)
                {
                    return false;
                }

                // Check if customer is used in any transactions (you may want to add this logic)
                // For now, we'll allow deletion

                // Find and delete the corresponding account
                var parentAccountCode = await GetCustomerParentAccountCodeAsync();
                if (!string.IsNullOrEmpty(parentAccountCode))
                {
                    var customerAccount = await _context.ChartOfAccounts
                        .Where(a => a.ParentAccountCode == parentAccountCode && 
                                   a.AccountName.Contains(customer.CustomerName ?? ""))
                        .FirstOrDefaultAsync();

                    if (customerAccount != null)
                    {
                        _context.ChartOfAccounts.Remove(customerAccount);
                    }
                }

                _context.Customers.Remove(customer);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation($"Customer {customerNo} deleted successfully");
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error deleting customer {customerNo}");
                return false;
            }
        }
    }
}
