# ALSULTAN Accounting Web Migration – Overview

This document provides a high-level picture of the migration from the legacy VB.NET application to the modern ASP.NET Core web app, and explains how progress is tracked in the project. It is the single entry point for onboarding and planning.

- Progress log file: `Migration-Progress.md`
- Tech stack reference: `04-Documentation/Technical-Stack-Summary.md`
- POS migration reference: `04-Documentation/POS-Migration-Reference.md`
- POS UX and analysis: `04-Documentation/frmPOS-Analysis.md`, `04-Documentation/frmPOS-UserJourney.md`

## Goals
- Preserve full compatibility with the existing SQL Server schema (no breaking schema changes).
- Migrate core modules from VB.NET to ASP.NET Core MVC with Arabic RTL UI.
- Centralize business logic in Services with EF Core as the primary DAL.
- Maintain zero-downtime coexistence with the legacy app during transition.

## Scope (initial wave)
- POS (sales) workflows including barcode, VAT/discount, payments, receipts, GL posting.
- Sales search and printouts.
- Purchases (create, list; returns next).
- Dashboard KPIs and quick navigation.

## Current Architecture (summary)
- ASP.NET Core 9.0 MVC, EF Core (SQL Server), layered solution: `Models`, `Data`, `Services`, `Web`.
- Database-first mapping; legacy naming preserved.
- Invoices: Table-per-hierarchy (TPH) on headers and lines (`TrxType` discriminator: "مبيعات" / "مشتريات").

## Done vs Pending (high level)
- Done:
  - POS operational; GL posting via stored procedures (auto + manual retry endpoints).
  - Purchase Create/Index working; sidebar mapped to real pages.
  - Dashboard “مبيعات اليوم” deep-link to Sales Search (today prefilter).
  - EF fixes for date nulls; app builds/runs.
- Pending:
  - Purchase Return workflow; unify dashboard data access on EF.
  - Optimize searches (projections/paging), remove N+1 patterns.
  - Add tests; add indices; sweep nullability warnings.

## Progress tracking
- All incremental work is logged in `Migration-Progress.md` per chat/session.
- Each entry records: summary, changes, decisions, open issues, next actions, and impacted artifacts.

## Reusable prompts

### Prompt 1: Update Progress (append to `Migration-Progress.md`)
```
You are updating the migration progress log at `Migration-Progress.md`.
Append a new entry using this template:

## [Session YYYY-MM-DD HH:mm]
- Summary: <2-5 lines>
- Changes:
  - <code/behavior change>
- Decisions:
  - <decision>
- Open Issues:
  - <issue>
- Next Actions:
  - <action>
- Artifacts Changed:
  - `<relative/path>`

Ensure you only append at the end of the file; do not overwrite older entries.
```

### Prompt 2: Start New Chat (plan next work from progress)
```
Read `Migration-Overview.md` and `Migration-Progress.md`.
1) Summarize the last entry’s state in 5 bullets.
2) Propose a focused plan for the next 1–2 days (small, testable steps), referencing impacted files.
3) List risks/unknowns and how to de-risk.
4) Confirm Arabic RTL UI and DB compatibility remain hard requirements.
Return a concise, actionable plan.
```

---
Last updated: 2025-08-10
