@using AccountingSystem.Web.Controllers
@model SystemSetupViewModel
@{
    ViewData["Title"] = "إعدادات النظام";
    Layout = "_Layout";
}

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-cogs"></i>
                    إعدادات النظام
                </h1>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Required Fields Notice -->
    <div class="alert alert-info" role="alert">
        <i class="fas fa-info-circle"></i>
        <strong>ملاحظة:</strong> الحقول المميزة بعلامة <span class="text-danger">*</span> مطلوبة ويجب ملؤها.
    </div>

    <form asp-action="Index" method="post" class="needs-validation" novalidate>
        @Html.AntiForgeryToken()
        
        <div class="row">
            <!-- Store Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-store"></i>
                            معلومات المتجر
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="StoreName" class="form-label fw-bold">
                                        <i class="fas fa-building"></i>
                                        اسم المتجر
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="StoreName" class="form-control" placeholder="أدخل اسم المتجر" required />
                                    <span asp-validation-for="StoreName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="VATRegNo" class="form-label fw-bold">
                                        <i class="fas fa-receipt"></i>
                                        رقم التسجيل الضريبي
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="VATRegNo" class="form-control" placeholder="أدخل رقم التسجيل الضريبي" required />
                                    <span asp-validation-for="VATRegNo" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="AddressFooter" class="form-label fw-bold">
                                <i class="fas fa-map-marker-alt"></i>
                                عنوان التقرير
                                <span class="text-danger">*</span>
                            </label>
                            <textarea asp-for="AddressFooter" class="form-control" rows="3"
                                      placeholder="أدخل العنوان الذي سيظهر في أسفل التقارير" required></textarea>
                            <span asp-validation-for="AddressFooter" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <!-- Item Configuration -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-boxes"></i>
                            إعدادات الأصناف
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ItemDescription1" class="form-label fw-bold">
                                        وصف الصنف 1
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="ItemDescription1" class="form-control" placeholder="مثال: اللون" required />
                                    <span asp-validation-for="ItemDescription1" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ItemDescription2" class="form-label fw-bold">
                                        وصف الصنف 2
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="ItemDescription2" class="form-control" placeholder="مثال: الحجم" required />
                                    <span asp-validation-for="ItemDescription2" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <h6 class="fw-bold mt-4 mb-3">
                            <i class="fas fa-layer-group"></i>
                            مستويات تصنيف الأصناف
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ItemLevel1" class="form-label">
                                        المستوى 1
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="ItemLevel1" class="form-control" placeholder="مثال: المجموعة الرئيسية" required />
                                    <span asp-validation-for="ItemLevel1" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ItemLevel2" class="form-label">
                                        المستوى 2
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="ItemLevel2" class="form-control" placeholder="مثال: المجموعة الفرعية" required />
                                    <span asp-validation-for="ItemLevel2" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="ItemLevel3" class="form-label">
                                        المستوى 3
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="ItemLevel3" class="form-control" placeholder="مثال: النوع" required />
                                    <span asp-validation-for="ItemLevel3" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="ItemLevel4" class="form-label">
                                        المستوى 4
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="ItemLevel4" class="form-control" placeholder="مثال: الطراز" required />
                                    <span asp-validation-for="ItemLevel4" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="ItemLevel5" class="form-label">
                                        المستوى 5
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="ItemLevel5" class="form-control" placeholder="مثال: التفاصيل" required />
                                    <span asp-validation-for="ItemLevel5" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logo Management -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-image"></i>
                            شعار المتجر
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="logo-container mb-3">
                            <img id="logoPreview" src="/SystemSetup/GetLogo" alt="شعار المتجر" 
                                 class="img-fluid rounded shadow" style="max-height: 200px; max-width: 100%;" 
                                 onerror="this.src='/images/no-logo.png'" />
                        </div>
                        
                        <div class="mb-3">
                            <input type="file" id="logoFile" accept="image/*" class="form-control" style="display: none;" />
                            <button type="button" class="btn btn-primary me-2" onclick="document.getElementById('logoFile').click()">
                                <i class="fas fa-upload"></i>
                                رفع شعار جديد
                            </button>
                            @if (Model.HasLogo)
                            {
                                <button type="button" class="btn btn-danger" onclick="deleteLogo()">
                                    <i class="fas fa-trash"></i>
                                    حذف الشعار
                                </button>
                            }
                        </div>
                        
                        <div class="form-text">
                            <small>
                                <i class="fas fa-info-circle"></i>
                                أنواع الملفات المدعومة: JPG, PNG, GIF<br>
                                الحد الأقصى للحجم: 5 ميجابايت
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <a href="/SimpleDashboard" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                العودة للوحة التحكم
                            </a>
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-save"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <script>
        // Handle logo file upload
        document.getElementById('logoFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                uploadLogo(file);
            }
        });

        function uploadLogo(file) {
            const formData = new FormData();
            formData.append('logoFile', file);
            formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());

            // Show loading
            const preview = document.getElementById('logoPreview');
            const originalSrc = preview.src;
            preview.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+جاري الرفع...</dGV4dD48L3N2Zz4=';

            fetch('/SystemSetup/UploadLogo', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Refresh logo with timestamp to avoid cache
                    preview.src = '/SystemSetup/GetLogo?' + new Date().getTime();
                    showAlert('success', data.message);
                    
                    // Show delete button if not already visible
                    location.reload();
                } else {
                    preview.src = originalSrc;
                    showAlert('danger', data.message);
                }
            })
            .catch(error => {
                preview.src = originalSrc;
                showAlert('danger', 'حدث خطأ أثناء رفع الشعار');
                console.error('Error:', error);
            });
        }

        function deleteLogo() {
            if (confirm('هل أنت متأكد من رغبتك في حذف الشعار؟')) {
                const formData = new FormData();
                formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());

                fetch('/SystemSetup/DeleteLogo', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('logoPreview').src = '/images/no-logo.png';
                        showAlert('success', data.message);
                        location.reload();
                    } else {
                        showAlert('danger', data.message);
                    }
                })
                .catch(error => {
                    showAlert('danger', 'حدث خطأ أثناء حذف الشعار');
                    console.error('Error:', error);
                });
            }
        }

        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // Insert at the top of the container
            const container = document.querySelector('.container-fluid');
            const firstChild = container.firstElementChild;
            const alertDiv = document.createElement('div');
            alertDiv.innerHTML = alertHtml;
            container.insertBefore(alertDiv.firstElementChild, firstChild.nextSibling);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>

    <style>
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #dee2e6;
        }

        .page-title {
            margin: 0;
            color: #2c3e50;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: none;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .logo-container {
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 2px dashed #dee2e6;
        }

        .alert {
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .btn {
            border-radius: 8px;
        }

        .form-text small {
            color: #6c757d;
        }
    </style>
}
