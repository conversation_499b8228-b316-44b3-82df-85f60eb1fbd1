﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmStockAdjustment
    Inherits System.Windows.Forms.Form

    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    Private components As System.ComponentModel.IContainer

    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.pnlHeader = New System.Windows.Forms.Panel()
        Me.lblTitle = New System.Windows.Forms.Label()
        Me.pnlControls = New System.Windows.Forms.Panel()
        Me.grpSettings = New System.Windows.Forms.GroupBox()
        Me.cmbxStore = New System.Windows.Forms.ComboBox()
        Me.lblWarehouse = New System.Windows.Forms.Label()
        Me.dtpAdjustmentDate = New System.Windows.Forms.DateTimePicker()
        Me.lblDate = New System.Windows.Forms.Label()
        Me.pnlActions = New System.Windows.Forms.Panel()
        Me.btnExportReport = New System.Windows.Forms.Button()
        Me.btnProcessAdjustment = New System.Windows.Forms.Button()
        Me.btnImportExcel = New System.Windows.Forms.Button()
        Me.btnLoadItems = New System.Windows.Forms.Button()
        Me.pnlGrid = New System.Windows.Forms.Panel()
        Me.DGVItems = New System.Windows.Forms.DataGridView()
        Me.pnlStatus = New System.Windows.Forms.Panel()
        Me.progressBar = New System.Windows.Forms.ProgressBar()
        Me.lblStatus = New System.Windows.Forms.Label()
        Me.pnlHeader.SuspendLayout()
        Me.pnlControls.SuspendLayout()
        Me.grpSettings.SuspendLayout()
        Me.pnlActions.SuspendLayout()
        Me.pnlGrid.SuspendLayout()
        CType(Me.DGVItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlStatus.SuspendLayout()
        Me.SuspendLayout()
        '
        'pnlHeader
        '
        Me.pnlHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.pnlHeader.Controls.Add(Me.lblTitle)
        Me.pnlHeader.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlHeader.Location = New System.Drawing.Point(0, 0)
        Me.pnlHeader.Name = "pnlHeader"
        Me.pnlHeader.Size = New System.Drawing.Size(1000, 60)
        Me.pnlHeader.TabIndex = 0
        '
        'lblTitle
        '
        Me.lblTitle.Dock = System.Windows.Forms.DockStyle.Fill
        Me.lblTitle.Font = New System.Drawing.Font("Segoe UI", 16.0!, System.Drawing.FontStyle.Bold)
        Me.lblTitle.ForeColor = System.Drawing.Color.White
        Me.lblTitle.Location = New System.Drawing.Point(0, 0)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(1000, 60)
        Me.lblTitle.TabIndex = 0
        Me.lblTitle.Text = "تسوية الجرد المخزني"
        Me.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlControls
        '
        Me.pnlControls.BackColor = System.Drawing.Color.FromArgb(CType(CType(248, Byte), Integer), CType(CType(249, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.pnlControls.Controls.Add(Me.grpSettings)
        Me.pnlControls.Controls.Add(Me.pnlActions)
        Me.pnlControls.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlControls.Location = New System.Drawing.Point(0, 60)
        Me.pnlControls.Name = "pnlControls"
        Me.pnlControls.Padding = New System.Windows.Forms.Padding(20, 15, 20, 15)
        Me.pnlControls.Size = New System.Drawing.Size(1000, 100)
        Me.pnlControls.TabIndex = 1
        '
        'grpSettings
        '
        Me.grpSettings.Controls.Add(Me.cmbxStore)
        Me.grpSettings.Controls.Add(Me.lblWarehouse)
        Me.grpSettings.Controls.Add(Me.dtpAdjustmentDate)
        Me.grpSettings.Controls.Add(Me.lblDate)
        Me.grpSettings.Dock = System.Windows.Forms.DockStyle.Left
        Me.grpSettings.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.grpSettings.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.grpSettings.Location = New System.Drawing.Point(20, 15)
        Me.grpSettings.Name = "grpSettings"
        Me.grpSettings.Size = New System.Drawing.Size(300, 70)
        Me.grpSettings.TabIndex = 0
        Me.grpSettings.TabStop = False
        Me.grpSettings.Text = "إعدادات التسوية"
        '
        'cmbxStore
        '
        Me.cmbxStore.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cmbxStore.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.cmbxStore.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.cmbxStore.Location = New System.Drawing.Point(85, 25)
        Me.cmbxStore.Name = "cmbxStore"
        Me.cmbxStore.Size = New System.Drawing.Size(130, 23)
        Me.cmbxStore.TabIndex = 1
        '
        'lblWarehouse
        '
        Me.lblWarehouse.AutoSize = True
        Me.lblWarehouse.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.lblWarehouse.Location = New System.Drawing.Point(15, 28)
        Me.lblWarehouse.Name = "lblWarehouse"
        Me.lblWarehouse.Size = New System.Drawing.Size(44, 15)
        Me.lblWarehouse.TabIndex = 0
        Me.lblWarehouse.Text = "المخزن:"
        '
        'dtpAdjustmentDate
        '
        Me.dtpAdjustmentDate.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.dtpAdjustmentDate.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.dtpAdjustmentDate.Location = New System.Drawing.Point(85, 48)
        Me.dtpAdjustmentDate.Name = "dtpAdjustmentDate"
        Me.dtpAdjustmentDate.Size = New System.Drawing.Size(130, 23)
        Me.dtpAdjustmentDate.TabIndex = 3
        '
        'lblDate
        '
        Me.lblDate.AutoSize = True
        Me.lblDate.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.lblDate.Location = New System.Drawing.Point(15, 51)
        Me.lblDate.Name = "lblDate"
        Me.lblDate.Size = New System.Drawing.Size(41, 15)
        Me.lblDate.TabIndex = 2
        Me.lblDate.Text = "التاريخ:"
        '
        'pnlActions
        '
        Me.pnlActions.Controls.Add(Me.btnExportReport)
        Me.pnlActions.Controls.Add(Me.btnProcessAdjustment)
        Me.pnlActions.Controls.Add(Me.btnImportExcel)
        Me.pnlActions.Controls.Add(Me.btnLoadItems)
        Me.pnlActions.Dock = System.Windows.Forms.DockStyle.Right
        Me.pnlActions.Location = New System.Drawing.Point(340, 15)
        Me.pnlActions.Name = "pnlActions"
        Me.pnlActions.Size = New System.Drawing.Size(640, 70)
        Me.pnlActions.TabIndex = 1
        '
        'btnExportReport
        '
        Me.btnExportReport.BackColor = System.Drawing.Color.FromArgb(CType(CType(155, Byte), Integer), CType(CType(89, Byte), Integer), CType(CType(182, Byte), Integer))
        Me.btnExportReport.FlatAppearance.BorderSize = 0
        Me.btnExportReport.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnExportReport.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnExportReport.ForeColor = System.Drawing.Color.White
        Me.btnExportReport.Location = New System.Drawing.Point(30, 20)
        Me.btnExportReport.Name = "btnExportReport"
        Me.btnExportReport.Size = New System.Drawing.Size(140, 35)
        Me.btnExportReport.TabIndex = 3
        Me.btnExportReport.Text = "تصدير التقرير"
        Me.btnExportReport.UseVisualStyleBackColor = False
        '
        'btnProcessAdjustment
        '
        Me.btnProcessAdjustment.BackColor = System.Drawing.Color.FromArgb(CType(CType(231, Byte), Integer), CType(CType(76, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.btnProcessAdjustment.FlatAppearance.BorderSize = 0
        Me.btnProcessAdjustment.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnProcessAdjustment.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnProcessAdjustment.ForeColor = System.Drawing.Color.White
        Me.btnProcessAdjustment.Location = New System.Drawing.Point(180, 20)
        Me.btnProcessAdjustment.Name = "btnProcessAdjustment"
        Me.btnProcessAdjustment.Size = New System.Drawing.Size(140, 35)
        Me.btnProcessAdjustment.TabIndex = 2
        Me.btnProcessAdjustment.Text = "تسوية الفروقات"
        Me.btnProcessAdjustment.UseVisualStyleBackColor = False
        '
        'btnImportExcel
        '
        Me.btnImportExcel.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(204, Byte), Integer), CType(CType(113, Byte), Integer))
        Me.btnImportExcel.FlatAppearance.BorderSize = 0
        Me.btnImportExcel.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnImportExcel.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnImportExcel.ForeColor = System.Drawing.Color.White
        Me.btnImportExcel.Location = New System.Drawing.Point(330, 20)
        Me.btnImportExcel.Name = "btnImportExcel"
        Me.btnImportExcel.Size = New System.Drawing.Size(140, 35)
        Me.btnImportExcel.TabIndex = 1
        Me.btnImportExcel.Text = "استيراد Excel"
        Me.btnImportExcel.UseVisualStyleBackColor = False
        '
        'btnLoadItems
        '
        Me.btnLoadItems.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.btnLoadItems.FlatAppearance.BorderSize = 0
        Me.btnLoadItems.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnLoadItems.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnLoadItems.ForeColor = System.Drawing.Color.White
        Me.btnLoadItems.Location = New System.Drawing.Point(480, 20)
        Me.btnLoadItems.Name = "btnLoadItems"
        Me.btnLoadItems.Size = New System.Drawing.Size(140, 35)
        Me.btnLoadItems.TabIndex = 0
        Me.btnLoadItems.Text = "تحميل الأصناف"
        Me.btnLoadItems.UseVisualStyleBackColor = False
        '
        'pnlGrid
        '
        Me.pnlGrid.BackColor = System.Drawing.Color.White
        Me.pnlGrid.Controls.Add(Me.DGVItems)
        Me.pnlGrid.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlGrid.Location = New System.Drawing.Point(0, 160)
        Me.pnlGrid.Name = "pnlGrid"
        Me.pnlGrid.Padding = New System.Windows.Forms.Padding(20, 10, 20, 10)
        Me.pnlGrid.Size = New System.Drawing.Size(1000, 400)
        Me.pnlGrid.TabIndex = 2
        '
        'DGVItems
        '
        Me.DGVItems.AllowUserToAddRows = False
        Me.DGVItems.AllowUserToDeleteRows = False
        Me.DGVItems.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.DGVItems.BackgroundColor = System.Drawing.Color.White
        Me.DGVItems.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.DGVItems.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal
        Me.DGVItems.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle3.BackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(73, Byte), Integer), CType(CType(94, Byte), Integer))
        DataGridViewCellStyle3.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(CType(CType(52, Byte), Integer), CType(CType(73, Byte), Integer), CType(CType(94, Byte), Integer))
        DataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DGVItems.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle3
        Me.DGVItems.ColumnHeadersHeight = 40
        DataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle4.BackColor = System.Drawing.Color.White
        DataGridViewCellStyle4.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        DataGridViewCellStyle4.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        DataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(255, Byte), Integer))
        DataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        DataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.DGVItems.DefaultCellStyle = DataGridViewCellStyle4
        Me.DGVItems.Dock = System.Windows.Forms.DockStyle.Fill
        Me.DGVItems.EnableHeadersVisualStyles = False
        Me.DGVItems.GridColor = System.Drawing.Color.FromArgb(CType(CType(231, Byte), Integer), CType(CType(231, Byte), Integer), CType(CType(231, Byte), Integer))
        Me.DGVItems.Location = New System.Drawing.Point(20, 10)
        Me.DGVItems.MultiSelect = False
        Me.DGVItems.Name = "DGVItems"
        Me.DGVItems.RowHeadersVisible = False
        Me.DGVItems.RowTemplate.Height = 35
        Me.DGVItems.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.DGVItems.Size = New System.Drawing.Size(960, 380)
        Me.DGVItems.TabIndex = 0
        '
        'pnlStatus
        '
        Me.pnlStatus.BackColor = System.Drawing.Color.FromArgb(CType(CType(248, Byte), Integer), CType(CType(249, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.pnlStatus.Controls.Add(Me.progressBar)
        Me.pnlStatus.Controls.Add(Me.lblStatus)
        Me.pnlStatus.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlStatus.Location = New System.Drawing.Point(0, 560)
        Me.pnlStatus.Name = "pnlStatus"
        Me.pnlStatus.Size = New System.Drawing.Size(1000, 40)
        Me.pnlStatus.TabIndex = 3
        '
        'progressBar
        '
        Me.progressBar.Location = New System.Drawing.Point(700, 10)
        Me.progressBar.Name = "progressBar"
        Me.progressBar.Size = New System.Drawing.Size(280, 20)
        Me.progressBar.Style = System.Windows.Forms.ProgressBarStyle.Continuous
        Me.progressBar.TabIndex = 1
        Me.progressBar.Visible = False
        '
        'lblStatus
        '
        Me.lblStatus.AutoSize = True
        Me.lblStatus.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.lblStatus.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblStatus.Location = New System.Drawing.Point(20, 12)
        Me.lblStatus.Name = "lblStatus"
        Me.lblStatus.Size = New System.Drawing.Size(30, 15)
        Me.lblStatus.TabIndex = 0
        Me.lblStatus.Text = "جاهز"
        '
        'frmStockAdjustment
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 15.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.White
        Me.ClientSize = New System.Drawing.Size(1000, 600)
        Me.Controls.Add(Me.pnlGrid)
        Me.Controls.Add(Me.pnlStatus)
        Me.Controls.Add(Me.pnlControls)
        Me.Controls.Add(Me.pnlHeader)
        Me.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New System.Drawing.Size(800, 600)
        Me.Name = "frmStockAdjustment"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "نظام تسوية الجرد المخزني"
        Me.pnlHeader.ResumeLayout(False)
        Me.pnlControls.ResumeLayout(False)
        Me.grpSettings.ResumeLayout(False)
        Me.grpSettings.PerformLayout()
        Me.pnlActions.ResumeLayout(False)
        Me.pnlGrid.ResumeLayout(False)
        CType(Me.DGVItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlStatus.ResumeLayout(False)
        Me.pnlStatus.PerformLayout()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents pnlHeader As Panel
    Friend WithEvents lblTitle As Label
    Friend WithEvents pnlControls As Panel
    Friend WithEvents grpSettings As GroupBox
    Friend WithEvents cmbxStore As ComboBox
    Friend WithEvents lblWarehouse As Label
    Friend WithEvents dtpAdjustmentDate As DateTimePicker
    Friend WithEvents lblDate As Label
    Friend WithEvents pnlActions As Panel
    Friend WithEvents btnLoadItems As Button
    Friend WithEvents btnImportExcel As Button
    Friend WithEvents btnProcessAdjustment As Button
    Friend WithEvents btnExportReport As Button
    Friend WithEvents pnlGrid As Panel
    Friend WithEvents DGVItems As DataGridView
    Friend WithEvents pnlStatus As Panel
    Friend WithEvents lblStatus As Label
    Friend WithEvents progressBar As ProgressBar
End Class