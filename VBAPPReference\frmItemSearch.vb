﻿Imports System.Data.SqlClient
Public Class frmItemSearch

    Sub ItemsLoad()
        Dim ds As DataSet = New DataSet
        Dim SearchString As String = ""
        SearchString = "Select ItemNo as [رقم الصنف],ItemDescription as [وصف الصنف] from tblItems where ItemNo <> 0 "
        If txtItemNo.Text.Trim <> "" Then
            SearchString += " and ItemNo = " & Val(txtItemNo.Text) & ""
        End If
        If txtItemDescription.Text.Trim <> "" Then
            SearchString += " and ItemDescription like '%" & txtItemDescription.Text.Trim & "%'"
        End If
        SearchString += " order by ItemNo"
        Dim SearchCMD As New SqlCommand()
        SearchCMD.Connection = Con
        SearchCMD.CommandText = SearchString
        Dim da As New SqlDataAdapter(SearchCMD)
        ds.Clear()
        'CloseReader()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub
    Private Sub DataGridView1_CellDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If Val(DataGridView1.CurrentRow.Cells(0).Value) <> 0 Then
            ItemSN = DataGridView1.CurrentRow.Cells(0).Value
            ItemDescription = DataGridView1.CurrentRow.Cells(1).Value
            Me.Close()
            If SearchItemFor = "Purchase" Then
                frmPurchaseInvoiceTrx.BringToFront()
                frmPurchaseInvoiceTrx.txtItemNo.Focus()
                'frmPurchaseInvoiceTrx.txtItemNo.Text = ItemSN
                'frmPurchaseInvoiceTrx.txtItemDescription.Text = ItemDescription
                'frmPurchaseInvoiceTrx.txtUnitPrice.Focus()

            ElseIf SearchItemFor = "SalesReturn" Then
                frmSalesReturnInvoiceTrx.BringToFront()
                frmSalesReturnInvoiceTrx.txtItemNo.Focus()
                frmSalesReturnInvoiceTrx.txtItemNo.Text = ItemSN
                frmSalesReturnInvoiceTrx.txtUnitPrice.Focus()

            ElseIf SearchItemFor = "OpenStock" Then
                frmOpenStockInvoice.BringToFront()
                frmOpenStockInvoice.txtItemNo.Focus()
                frmOpenStockInvoice.txtItemNo.Text = ItemSN
                frmOpenStockInvoice.txtUnitPrice.Focus()

            ElseIf SearchItemFor = "Sales" Then
                frmSalesInvoiceTrx.BringToFront()
                frmSalesInvoiceTrx.txtItemNo.Focus()
                frmSalesInvoiceTrx.txtItemNo.Text = ItemSN
                frmSalesInvoiceTrx.txtUnitPrice.Focus()
                '
            ElseIf SearchItemFor = "PurchaseReturn" Then
                frmPurchasingReturnInvoice.BringToFront()
                frmPurchasingReturnInvoice.txtItemNo.Focus()
                frmPurchasingReturnInvoice.txtItemNo.Text = ItemSN
                frmPurchasingReturnInvoice.txtUnitPrice.Focus()

            ElseIf SearchItemFor = "SalesSearch" Then
                frmSearchInvoices.BringToFront()
                frmSearchInvoices.txtSearch.Focus()
                frmSearchInvoices.txtSearch.Text = ItemSN

            ElseIf SearchItemFor = "PurchaseSearch" Then
                frmSearchPurchase.BringToFront()
                frmSearchPurchase.txtSearch.Focus()
                frmSearchPurchase.txtSearch.Text = ItemSN

            ElseIf SearchItemFor = "StockSearch" Then
                frmSearchStock.BringToFront()
                frmSearchStock.txtSearch.Focus()
                frmSearchStock.txtSearch.Text = ItemSN

            ElseIf SearchItemFor = "ItemStock" Then
                frmItemStock.BringToFront()
                frmItemStock.txtItemNo.Focus()
                frmItemStock.txtItemNo.Text = ItemSN
                frmItemStock.txtItemDescription.Text = ItemDescription


            End If
        End If
    End Sub
    Private Sub txtSN_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then

        End If
    End Sub
    Private Sub txtSNRequired_TextChanged(sender As Object, e As EventArgs) Handles txtItemNo.TextChanged
        ItemsLoad()
    End Sub
    Private Sub txtItemDescription_TextChanged(sender As Object, e As EventArgs) Handles txtItemDescription.TextChanged
        ItemsLoad()
    End Sub
    Private Sub frmItemSearch_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ItemsLoad()
        txtItemDescription.Clear()
        txtItemNo.Clear()
        txtItemDescription.Focus()
    End Sub

    Private Sub DataGridView1_CellContentClick(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView1.CellContentClick

    End Sub
End Class