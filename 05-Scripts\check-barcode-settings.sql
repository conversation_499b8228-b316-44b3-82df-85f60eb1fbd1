-- Check existing barcode settings
SELECT * FROM tblBarcodeSettings;

-- Insert default barcode settings for weight processing if not exists
IF NOT EXISTS (SELECT 1 FROM tblBarcodeSettings WHERE Shop = 'Main Store')
BEGIN
    INSERT INTO tblBarcodeSettings (
        Shop, 
        BarcodeType, 
        EnableEmbeddedWeight, 
        EmbeddedFormat, 
        WeightDivisor, 
        CurrencyDivisor, 
        Notes, 
        CreatedBy, 
        CreatedOn
    ) VALUES (
        'Main Store',
        'Weight',
        1, -- Enable embedded weight
        'xxxxxwwwww', -- 5 digits for item, 5 digits for weight
        1000, -- Weight divisor (grams to kg)
        100, -- Currency divisor
        'Default weight barcode settings for 2700009004709 format',
        'System',
        GETDATE()
    );
END

-- Check if item 90047 exists (from barcode 2700009004709)
SELECT * FROM tblItems WHERE ItemNo = 90047;

-- If item doesn't exist, create a test item
IF NOT EXISTS (SELECT 1 FROM tblItems WHERE ItemNo = 90047)
BEGIN
    INSERT INTO tblItems (
        ItemNo,
        ItemDescription,
        Barcode,
        UnitSalesPrice,
        UofM,
        Status,
        CreatedBy,
        CreatedOn
    ) VALUES (
        90047,
        'Test Weight Item',
        '2700009004709',
        10.00,
        'KG',
        1,
        'System',
        GETDATE()
    );
END

-- Show final barcode settings
SELECT * FROM tblBarcodeSettings; 