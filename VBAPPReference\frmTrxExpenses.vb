﻿Imports System.Data.SqlClient
Imports Microsoft.Win32
Imports System.IO

Public Class frmTrxExpenses
    Public Recipient As String = ""
    Public TrxType As String = ""

    ' Public variables for printing functionality
    Public PrintType As String = ""
    Public InvoiceNo As Integer = 0

    Private Sub frmJETrx_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        DebitAccountsLoad()
        ForceGregorianForAllPickers(Me)

        VendorsLoad()
        CashierLoad()
        EmployeesLoad()
    End Sub

    Private Sub EmployeesLoad()
        Dim CMD As New SqlCommand("Select EmployeeNo,Emp_Name from tblEmployees order by EmployeeNo", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "Emp_Name + ' - ' + CONVERT(EmployeeNo, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxEmployee.DataSource = dt
        cmbxEmployee.DisplayMember = "DisplayText" ' Show Name
        cmbxEmployee.ValueMember = "EmployeeNo" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Sub CashierLoad()
        Dim CMD As New SqlCommand("SELECT AccountCode, AccountName FROM tbl_Acc_Accounts WHERE ParentAccountCode = (SELECT AccountNo FROM tblGLConfig WHERE EntryReferenceModule = 'نقدية') ORDER BY AccountCode", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()

        dt.Load(reader)
        reader.Close()

        ' Bind the ComboBox to DataTable
        cmbxCreditAccount.DataSource = dt
        cmbxCreditAccount.DisplayMember = "AccountName" ' Show Name
        cmbxCreditAccount.ValueMember = "AccountCode" ' Store ID

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Private Sub txtAmount_TextChanged(sender As Object, e As EventArgs) Handles txtAmount.TextChanged
        ' Skip validation if the field is empty or being cleared
        If String.IsNullOrWhiteSpace(txtAmount.Text) Then
            txtVATAmount.Clear()
            Return
        End If

        ' Validate numeric input
        Dim amount As Decimal
        If Not Decimal.TryParse(txtAmount.Text, amount) Then
            MsgBox("الرجاء إدخال قيمة رقمية صحيحة للمبلغ", MsgBoxStyle.Critical, "خطأ")
            txtAmount.Clear()
            txtAmount.Focus()
            Return
        End If

        ' Calculate VAT amount (15% VAT included in total)
        ' Formula: VAT = Total - (Total / 1.15)
        Dim vatAmount As Decimal = amount - (amount / 1.15D)
        Dim roundedVAT As Decimal = Math.Round(vatAmount, 2)

        ' Format VAT amount - show decimals only if needed
        If roundedVAT = Math.Truncate(roundedVAT) Then
            txtVATAmount.Text = roundedVAT.ToString("F0") ' No decimals for whole numbers
        Else
            txtVATAmount.Text = roundedVAT.ToString("F2") ' 2 decimals for fractional numbers
        End If
    End Sub

    Private Sub txtVATAmount_TextChanged(sender As Object, e As EventArgs) Handles txtVATAmount.TextChanged
        ' Skip validation if the field is empty or being cleared
        If String.IsNullOrWhiteSpace(txtVATAmount.Text) Then
            Return
        End If

        ' Validate numeric input
        Dim vatAmount As Decimal
        If Not Decimal.TryParse(txtVATAmount.Text, vatAmount) Then
            MsgBox("الرجاء إدخال قيمة رقمية صحيحة لضريبة القيمة المضافة", MsgBoxStyle.Critical, "خطأ")
            txtVATAmount.Clear()
            txtVATAmount.Focus()
            Return
        End If

        ' Only calculate total amount if the main amount field is empty
        ' This prevents circular updates when user is typing in the amount field
        If String.IsNullOrWhiteSpace(txtAmount.Text) AndAlso vatAmount > 0 Then
            ' Calculate total amount from VAT
            ' Formula: Total = VAT * 1.15 / 0.15 = VAT / 0.15 * 1.15 = VAT * 7.6667
            Dim totalAmount As Decimal = vatAmount / 0.15D
            Dim roundedTotal As Decimal = Math.Round(totalAmount, 2)

            ' Format total amount - show decimals only if needed
            If roundedTotal = Math.Truncate(roundedTotal) Then
                txtAmount.Text = roundedTotal.ToString("F0") ' No decimals for whole numbers
            Else
                txtAmount.Text = roundedTotal.ToString("F2") ' 2 decimals for fractional numbers
            End If
        End If
    End Sub

    ' Optional: Add these helper methods for better user experience
    Private Sub txtAmount_Leave(sender As Object, e As EventArgs) Handles txtAmount.Leave
        ' Format the amount field when user leaves it - preserve original format
        If Not String.IsNullOrWhiteSpace(txtAmount.Text) Then
            Dim amount As Decimal
            If Decimal.TryParse(txtAmount.Text, amount) Then
                Dim roundedAmount As Decimal = Math.Round(amount, 2)

                ' Only show decimals if the original input had decimals or if rounding created them
                If txtAmount.Text.Contains(".") OrElse roundedAmount <> Math.Truncate(roundedAmount) Then
                    txtAmount.Text = roundedAmount.ToString("F2")
                Else
                    txtAmount.Text = roundedAmount.ToString("F0") ' Keep as integer if user entered integer
                End If
            End If
        End If
    End Sub

    Private Sub txtVATAmount_Leave(sender As Object, e As EventArgs) Handles txtVATAmount.Leave
        ' Format the VAT amount field when user leaves it - preserve original format
        If Not String.IsNullOrWhiteSpace(txtVATAmount.Text) Then
            Dim vatAmount As Decimal
            If Decimal.TryParse(txtVATAmount.Text, vatAmount) Then
                Dim roundedVAT As Decimal = Math.Round(vatAmount, 2)

                ' Only show decimals if the original input had decimals or if rounding created them
                If txtVATAmount.Text.Contains(".") OrElse roundedVAT <> Math.Truncate(roundedVAT) Then
                    txtVATAmount.Text = roundedVAT.ToString("F2")
                Else
                    txtVATAmount.Text = roundedVAT.ToString("F0") ' Keep as integer if user entered integer
                End If
            End If
        End If
    End Sub

    ' Optional: Prevent invalid characters from being typed
    Private Sub txtAmount_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtAmount.KeyPress
        ' Allow only numbers, decimal point, backspace, and delete
        If Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> "."c AndAlso e.KeyChar <> Chr(8) Then
            e.Handled = True
            Return
        End If

        ' Block decimal point if text is empty, "0", or "00"
        If e.KeyChar = "."c Then
            Dim currentText As String = txtAmount.Text.Trim()

            ' Block if already contains decimal point
            If currentText.Contains(".") Then
                e.Handled = True
                Return
            End If

            ' Block decimal if field is empty, "0", or "00"
            If String.IsNullOrEmpty(currentText) OrElse currentText = "0" OrElse currentText = "00" Then
                e.Handled = True
                Return
            End If
        End If
    End Sub

    Private Sub txtVATAmount_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtVATAmount.KeyPress
        ' Allow only numbers, decimal point, backspace, and delete
        If Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> "."c AndAlso e.KeyChar <> Chr(8) Then
            e.Handled = True
            Return
        End If

        ' Block decimal point if text is empty, "0", or "00"
        If e.KeyChar = "."c Then
            Dim currentText As String = txtVATAmount.Text.Trim()

            ' Block if already contains decimal point
            If currentText.Contains(".") Then
                e.Handled = True
                Return
            End If

            ' Block decimal if field is empty, "0", or "00"
            If String.IsNullOrEmpty(currentText) OrElse currentText = "0" OrElse currentText = "00" Then
                e.Handled = True
                Return
            End If
        End If
    End Sub

    Private Sub cmbxPartnerNo_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbxPartnerNo.SelectedIndexChanged
        Try
            If cmbxPartnerNo.SelectedItem IsNot Nothing Then
                Dim drv As DataRowView = CType(cmbxPartnerNo.SelectedItem, DataRowView)
                txtVendorName.Text = drv("AccountName").ToString()
                VendorDetailsLoad()
            Else
                txtVendorName.Clear()
                txtVendorVATRN.Clear()
            End If
        Catch ex As Exception
            txtVendorName.Clear()
            txtVendorVATRN.Clear()
        End Try
    End Sub

    Sub VendorDetailsLoad()
        If cmbxPartnerNo.SelectedItem IsNot Nothing Then
            Dim SelectCMD As New SqlCommand("select VATRegNo from tblVendors where VendorNo = " & cmbxPartnerNo.SelectedValue & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                txtVendorVATRN.Text = Trim(reader.Item(0).ToString)
            Else
                txtVendorVATRN.Text = ""
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If reader.IsClosed = False Then
                reader.Close()
            End If
        Else
            txtVendorName.Clear()
            txtVendorVATRN.Clear()
        End If
    End Sub

    Sub DebitAccountsLoad()
        Dim CMD As New SqlCommand("Select AccountCode,AccountName from tbl_Acc_Accounts where ParentAccountCode = (SELECT AccountNo FROM tblGLConfig WHERE EntryReferenceModule = 'المصروفات') order by AccountCode", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountName + ' - ' + CONVERT(AccountCode, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxDebitAccount.DataSource = dt
        cmbxDebitAccount.DisplayMember = "DisplayText" ' Show Name
        cmbxDebitAccount.ValueMember = "AccountCode" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        cmbxDebitAccount.SelectedIndex = 1
    End Sub

    Sub VendorsLoad()
        Dim CMD As New SqlCommand("Select AccountCode,AccountName from tbl_Acc_Accounts where ParentAccountCode = (Select AccountNo from tblGLConfig where EntryReferenceModule = 'موردون') order by AccountCode", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountName + ' - ' + CONVERT(AccountCode, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxPartnerNo.DataSource = dt
        cmbxPartnerNo.DisplayMember = "DisplayText" ' Show Name
        cmbxPartnerNo.ValueMember = "AccountCode" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Private Sub btnPhotoDelete_Click(sender As Object, e As EventArgs) Handles btnPhotoDelete.Click
        If MsgBox("هل أنت متأكد من رغبتك حذف الصورة ؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
            Me.PictureBox1.Image = Nothing
            txtPhoto.Clear()
        End If
    End Sub

    Private Sub btnPhotoSelect_Click(sender As Object, e As EventArgs) Handles btnPhotoSelect.Click
        Dim ofdRestore As New OpenFileDialog
        Try
            OpenFileDialog1.InitialDirectory = "C:\"
            ofdRestore.Filter = "JPEG files (*.jpg)|*.jpg|PNG files (*.png)|*.png|GIF files (*.gif)|*.gif|All files (*.*)|*.*"
            ofdRestore.ShowDialog()
            If ofdRestore.FileName <> "" Then
                txtPhoto.Text = ofdRestore.FileName
                Dim pictureData As Byte() = File.ReadAllBytes(txtPhoto.Text)
                Dim stream As New IO.MemoryStream(pictureData)
                Me.PictureBox1.Image = Image.FromStream(stream)
                stream.Dispose()
            End If

        Catch ex As Exception
            PictureBox1.Image = Nothing
            MsgBox("لم يتم تحميل الصورة ", MsgBoxStyle.Critical, "النظام")
        End Try
    End Sub

    Sub CreditAccountsLoad()
        Dim CMD As New SqlCommand("Select AccountNo,AccountDescription from AccountChart order by ParentID,AccountNo", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountDescription + ' - ' + CONVERT(AccountNo, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxCreditAccount.DataSource = dt
        cmbxCreditAccount.DisplayMember = "DisplayText" ' Show Name
        cmbxCreditAccount.ValueMember = "AccountNo" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Sub ClearFields()
        txtAmount.Clear()
        txtNote.Clear()
        txtVATAmount.Clear()
        txtVendorName.Clear()
        txtVendorVATRN.Clear()
        txtPhoto.Clear()

        ' Clear VendorInvoiceNo if you have a textbox for it
        ' Uncomment the line below when you add txtVendorInvoiceNo textbox
        txtVendorInvoiceNo.Clear()

        PictureBox1.Image = Nothing

        ' Reset combo boxes to first item or clear selection
        If cmbxDebitAccount.Items.Count > 0 Then cmbxDebitAccount.SelectedIndex = 0
        If cmbxCreditAccount.Items.Count > 0 Then cmbxCreditAccount.SelectedIndex = 0
        If cmbxPartnerNo.Items.Count > 0 Then cmbxPartnerNo.SelectedIndex = -1
        If cmbxEmployee.Items.Count > 0 Then cmbxEmployee.SelectedIndex = -1

        txtAmount.Focus()
    End Sub

    ' NEW: Enhanced Save Button with Duplicate Validation and VAT Warning
    Private Sub btnSave_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSave.Click
        Try
            ' Comprehensive Form Validation
            If Not ValidateForm() Then
                Return
            End If

            ' VAT Warning - warn user if VAT amount is NOT maintained (zero)
            Dim vatAmount As Decimal = 0
            If Not String.IsNullOrEmpty(txtVATAmount.Text) Then
                Decimal.TryParse(txtVATAmount.Text, vatAmount)
            End If

            ' Warning when VAT is zero or not maintained
            If vatAmount = 0 Then
                Dim result As MsgBoxResult = MsgBox("لم يتم إدخال ضريبة القيمة المضافة" & vbCrLf &
                                                   "هل تريد المتابعة بدون ضريبة؟",
                                                   MsgBoxStyle.YesNo + MsgBoxStyle.Question, "تأكيد عدم وجود ضريبة")
                If result = MsgBoxResult.No Then
                    txtVATAmount.Focus()
                    Return
                End If
            End If

            ' Get form values
            Dim invoiceDate As Date = Date.Today

            ' Fixed: Credit account should be from cashier combo, not credit account combo
            Dim creditCashier As String = ""
            If cmbxCreditAccount.SelectedValue IsNot Nothing Then
                creditCashier = cmbxCreditAccount.SelectedValue.ToString()
            End If

            Dim debitExpense As String = ""
            If cmbxDebitAccount.SelectedValue IsNot Nothing Then
                debitExpense = cmbxDebitAccount.SelectedValue.ToString()
            End If

            Dim invoiceAmount As Decimal = Convert.ToDecimal(txtAmount.Text)
            Dim taxAmount As Decimal = vatAmount

            Dim vendorNo As Long? = Nothing
            If cmbxPartnerNo.SelectedValue IsNot Nothing Then
                vendorNo = Convert.ToInt64(cmbxPartnerNo.SelectedValue)
            End If

            ' Handle VendorInvoiceNo - bypass blank values
            Dim vendorInvoiceNo As String = ""
            ' TODO: Add a textbox for vendor invoice number (txtVendorInvoiceNo)
            ' For now, you can manually set it or add the textbox to your form
            ' If you have txtVendorInvoiceNo textbox, uncomment the line below:
            ' If Not String.IsNullOrWhiteSpace(txtVendorInvoiceNo.Text) Then
            '     vendorInvoiceNo = txtVendorInvoiceNo.Text.Trim()
            ' End If

            ' Temporary: Ask user for vendor invoice number if needed
            ' You can remove this after adding the textbox
            'If MsgBox("هل تريد إدخال رقم فاتورة المورد؟", MsgBoxStyle.YesNo, "رقم فاتورة المورد") = MsgBoxResult.Yes Then
            vendorInvoiceNo = txtVendorInvoiceNo.Text.Trim()
            'End If

            Dim vendorName As String = txtVendorName.Text.Trim()

            ' Handle VendorVATRN - bypass blank values
            Dim vendorVATRN As String = ""
            If Not String.IsNullOrWhiteSpace(txtVendorVATRN.Text) Then
                vendorVATRN = txtVendorVATRN.Text.Trim()
            End If

            Dim employeeBuyer As Integer? = Nothing
            If cmbxEmployee.SelectedValue IsNot Nothing Then
                employeeBuyer = Convert.ToInt32(cmbxEmployee.SelectedValue)
            End If

            ' Handle Notes - bypass blank values
            Dim notes As String = ""
            If Not String.IsNullOrWhiteSpace(txtNote.Text) Then
                notes = txtNote.Text.Trim()
            End If

            ' Handle Photo - bypass blank values
            Dim photoPath As String = ""
            If Not String.IsNullOrWhiteSpace(txtPhoto.Text) AndAlso File.Exists(txtPhoto.Text.Trim()) Then
                photoPath = txtPhoto.Text.Trim()
            End If

            Dim createdBy As String = UserName

            ' Call the insert function with duplicate check
            Dim insertResult As InvoiceResult = InsertExpenseInvoiceWithValidation(
                invoiceDate,
                creditCashier,
                debitExpense,
                invoiceAmount,
                taxAmount,
                vendorNo,
                vendorInvoiceNo,
                vendorName,
                vendorVATRN,
                employeeBuyer,
                notes,
                photoPath,
                createdBy,
                False
            )

            ' Handle null result
            If insertResult Is Nothing Then
                MsgBox("حدث خطأ في معالجة البيانات", MsgBoxStyle.Critical, "خطأ")
                Return
            End If

            ' Handle duplicate validation result
            If Not insertResult.Success AndAlso Not String.IsNullOrEmpty(insertResult.DuplicateWarning) Then
                ' Show duplicate warning and ask for confirmation
                Dim duplicateResult As MsgBoxResult = MsgBox(insertResult.DuplicateWarning & vbCrLf & vbCrLf & "هل تريد المتابعة بالرغم من ذلك؟",
                                                           MsgBoxStyle.YesNo + MsgBoxStyle.Critical, "تحذير تكرار فاتورة المورد")

                If duplicateResult = MsgBoxResult.Yes Then
                    ' User approved, save with ignore duplicate flag
                    insertResult = InsertExpenseInvoiceWithValidation(
                        invoiceDate,
                        creditCashier,
                        debitExpense,
                        invoiceAmount,
                        taxAmount,
                        vendorNo,
                        vendorInvoiceNo,
                        vendorName,
                        vendorVATRN,
                        employeeBuyer,
                        notes,
                        photoPath,
                        createdBy,
                        True
                    )

                    ' Handle null result from second attempt
                    If insertResult Is Nothing Then
                        MsgBox("حدث خطأ في معالجة البيانات", MsgBoxStyle.Critical, "خطأ")
                        Return
                    End If
                Else
                    Return
                End If
            End If

            ' Show final result message and handle printing
            If insertResult.Success Then
                MsgBox(insertResult.Message, MsgBoxStyle.Information, "نجح")

                ' Ask user if they want to print the transaction
                'If MsgBox("هل ترغب في طباعة الفاتورة؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
                '    ' Set public variables for printing
                '    PrintType = "CashPay" ' Changed from PurchaseInvoice to ExpenseInvoice
                '    JESN = insertResult.InvoiceID ' Set the invoice number to public variable

                '    ' Show print preview form
                '    frmPrintPreview.MdiParent = frmMain
                '    frmPrintPreview.Show()
                'End If

                ClearFields()
            Else
                MsgBox(insertResult.Message, MsgBoxStyle.Critical, "خطأ")
            End If

        Catch ex As Exception
            MsgBox("حدث خطأ غير متوقع: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        End Try
    End Sub

    ' NEW: Form Validation Function
    Private Function ValidateForm() As Boolean
        ' Validate Vendor Name
        If String.IsNullOrEmpty(txtVendorName.Text.Trim()) Then
            MsgBox("الرجاء إدخال اسم المورد", MsgBoxStyle.Critical, "خطأ")
            txtVendorName.Focus()
            Return False
        End If

        ' Validate Debit Expense Account
        If cmbxDebitAccount.SelectedValue Is Nothing Then
            MsgBox("الرجاء اختيار حساب المصروفات", MsgBoxStyle.Critical, "خطأ")
            cmbxDebitAccount.Focus()
            Return False
        End If

        ' Validate Credit Account
        If cmbxCreditAccount.SelectedValue Is Nothing Then
            MsgBox("الرجاء اختيار الحساب الدائن", MsgBoxStyle.Critical, "خطأ")
            cmbxCreditAccount.Focus()
            Return False
        End If

        ' Validate Amount
        Dim invoiceAmount As Decimal
        If Not Decimal.TryParse(txtAmount.Text, invoiceAmount) OrElse invoiceAmount <= 0 Then
            MsgBox("الرجاء إدخال مبلغ صحيح للفاتورة", MsgBoxStyle.Critical, "خطأ")
            txtAmount.Focus()
            Return False
        End If

        ' Validate VAT Amount (if entered)
        If Not String.IsNullOrEmpty(txtVATAmount.Text) Then
            Dim vatAmount As Decimal
            If Not Decimal.TryParse(txtVATAmount.Text, vatAmount) Then
                MsgBox("الرجاء إدخال مبلغ صحيح لضريبة القيمة المضافة", MsgBoxStyle.Critical, "خطأ")
                txtVATAmount.Focus()
                Return False
            End If
        End If

        If Not String.IsNullOrEmpty(txtVATAmount.Text) Then
            If String.IsNullOrEmpty(txtVendorVATRN.Text.Trim()) Then
                MsgBox("لم يتم ادخال الرقم الضريبي للمورد ؟", MsgBoxStyle.Critical, "تنبيه")
                txtVendorVATRN.Focus()
                Return False
            End If
        End If

        Return True
    End Function

    ' NEW: Enhanced Expense Invoice Insertion Function with Validation
    Public Function InsertExpenseInvoiceWithValidation(
        invoiceDate As Date,
        creditCashier As String,
        debitExpense As String,
        invoiceAmount As Decimal,
        taxAmount As Decimal,
        vendorNo As Long?,
        vendorInvoiceNo As String,
        vendorName As String,
        vendorVATRN As String,
        employeeBuyer As Integer?,
        notes As String,
        photoPath As String,
        createdBy As String,
        ignoreDuplicate As Boolean
    ) As InvoiceResult

        Dim result As New InvoiceResult()

        Try
            Using command As New SqlCommand("sp_InsertExpenseInvoice", Con)
                command.CommandType = CommandType.StoredProcedure

                ' Add input parameters
                command.Parameters.AddWithValue("@Invoice_Date", invoiceDate)
                command.Parameters.AddWithValue("@CreditCashier", If(String.IsNullOrEmpty(creditCashier), "حساب الموردين", creditCashier))
                command.Parameters.AddWithValue("@DebitExpense", debitExpense)
                command.Parameters.AddWithValue("@Invoice_amount", invoiceAmount)
                command.Parameters.AddWithValue("@Tax_amount", taxAmount)
                command.Parameters.AddWithValue("@VendorNo", If(vendorNo.HasValue, vendorNo.Value, DBNull.Value))
                command.Parameters.AddWithValue("@VendorInvoiceNo", If(String.IsNullOrEmpty(vendorInvoiceNo), DBNull.Value, vendorInvoiceNo))
                command.Parameters.AddWithValue("@VendorName", vendorName)
                command.Parameters.AddWithValue("@VendorVATRN", If(String.IsNullOrEmpty(vendorVATRN), DBNull.Value, vendorVATRN))
                command.Parameters.AddWithValue("@EmployeeBuyer", If(employeeBuyer.HasValue, employeeBuyer.Value, DBNull.Value))
                command.Parameters.AddWithValue("@Notes", If(String.IsNullOrEmpty(notes), DBNull.Value, notes))
                command.Parameters.AddWithValue("@CreatedBy", createdBy)
                command.Parameters.AddWithValue("@IgnoreDuplicate", ignoreDuplicate)
                command.Parameters.AddWithValue("@CreateJournalEntry", True) ' Always create journal entry

                ' Handle photo attachment
                If Not String.IsNullOrEmpty(photoPath) AndAlso File.Exists(photoPath) Then
                    Dim photoBytes() As Byte = File.ReadAllBytes(photoPath)
                    command.Parameters.AddWithValue("@Attached_photo", photoBytes)
                Else
                    command.Parameters.Add("@Attached_photo", SqlDbType.VarBinary).Value = DBNull.Value
                End If

                ' Add output parameters
                Dim newInvoiceIDParam As New SqlParameter("@NewInvoiceID", SqlDbType.Int)
                newInvoiceIDParam.Direction = ParameterDirection.Output
                command.Parameters.Add(newInvoiceIDParam)

                Dim resultMessageParam As New SqlParameter("@ResultMessage", SqlDbType.NVarChar, 500)
                resultMessageParam.Direction = ParameterDirection.Output
                command.Parameters.Add(resultMessageParam)

                Dim duplicateWarningParam As New SqlParameter("@DuplicateWarning", SqlDbType.NVarChar, 500)
                duplicateWarningParam.Direction = ParameterDirection.Output
                command.Parameters.Add(duplicateWarningParam)

                ' Execute the command
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If

                command.ExecuteNonQuery()

                ' Get the results
                result.InvoiceID = Convert.ToInt32(newInvoiceIDParam.Value)
                result.Message = resultMessageParam.Value.ToString()
                result.DuplicateWarning = If(duplicateWarningParam.Value Is DBNull.Value, "", duplicateWarningParam.Value.ToString())
                result.Success = (result.InvoiceID > 0)

                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End Using

        Catch ex As Exception
            result.Success = False
            result.InvoiceID = 0
            result.Message = "خطأ في الاتصال بقاعدة البيانات: " & ex.Message
            result.DuplicateWarning = ""

            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End Try

        Return result
    End Function


    ' NEW: Enhanced Result class to hold the return values including duplicate warning
    Public Class InvoiceResult
        Public Property Success As Boolean
        Public Property InvoiceID As Integer
        Public Property Message As String
        Public Property DuplicateWarning As String = ""
    End Class

    Private Sub btnvendorSearch_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnVendorSearch.Click
        VendorSearchForm = "frmTrxExpenses"
        frmVendorSearch.ShowDialog()
    End Sub

End Class