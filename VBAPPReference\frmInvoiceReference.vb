﻿Imports System.Data.SqlClient
Public Class frmInvoiceReference

    Sub InvoiceTypeLoad()
        Dim ds As DataSet = New DataSet
        Dim SearchString As String = "Select InvoiceType as [نوع الفاتورة] from tblToolsInvoice order by InvoiceType"
        Dim SearchCMD As New SQLCommand()
        SearchCMD.Connection = Con
        SearchCMD.CommandText = SearchString
        Dim da As New SQLDataAdapter(SearchCMD)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub


    Private Sub DataGridView1_CellDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If DataGridView1.CurrentRow.Cells(0).Value <> 0 Then
            RefernceInvoiceType = DataGridView1.CurrentRow.Cells(0).Value
            ReferenceInvoiceNo = InputBox("فضلًا أدخل رقم الفاتورة", "رقم فاتورة المرجع", "0")
            If ReferenceInvoiceNo <> 0 Then
                Dim ReferenceText As String = "فاتورة " & RefernceInvoiceType & " - " & ReferenceInvoiceNo
                Me.Close()
                If SearchForReference = "مشتريات" Then
                    'frmPurchaseInvoiceTrx.BringToFront()
                    'frmPurchaseInvoiceTrx.txtReferenceNo.Text = ReferenceText
                ElseIf SearchForReference = "مرتجع مبيعات" Then
                    frmSalesReturnInvoiceTrx.BringToFront()
                    frmSalesReturnInvoiceTrx.txtReferenceNo.Text = ReferenceText
                ElseIf SearchForReference = "رصيد افتتاحي" Then
                    'frmOpenStockInvoiceTrx.BringToFront()
                    'frmOpenStockInvoiceTrx.txtReferenceNo.Text = ReferenceText
                ElseIf SearchForReference = "مبيعات" Then
                    frmSalesInvoiceTrx.BringToFront()
                    frmSalesInvoiceTrx.txtReferenceNo.Text = ReferenceText
                ElseIf SearchForReference = "مرتجع مشتريات" Then
                    frmPurchasingReturnInvoice.BringToFront()
                    frmPurchasingReturnInvoice.txtReferenceNo.Text = ReferenceText
                End If
            Else
                MsgBox("رقم الفاتورة المدخل غير صحيح", MsgBoxStyle.Critical, "نظام السلطان")
                Exit Sub
            End If

        End If
    End Sub
    Private Sub frmInvoiceReference_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InvoiceTypeLoad()
    End Sub
End Class