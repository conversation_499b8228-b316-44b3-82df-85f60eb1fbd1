﻿Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports CrystalDecisions.Windows.Forms
Imports System.Data.SqlClient

Public Class frmCashStatementPrint
    Private Sub frmCashStatementPrint_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Dim CMDCheck As New SQLCommand("Select AccountNo from AccountChart where AccountDescription = '" & Cashier & "'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMDCheck.ExecuteReader
        If reader.Read Then
            AccNo = Val(reader.Item(0).ToString)
        Else
            AccNo = 0
        End If
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If

        Me.Size = New Size(frmRPTWidth, frmRPTHeight)
        Dim RPT As New rptCashStatement
        UpdateCRDataSource(RPT)
        RPT.Refresh()
        RPT.ParameterFields(0).CurrentValues.Clear()
        RPT.ParameterFields(0).CurrentValues.AddRange(DateFrom, DateTo, RangeBoundType.BoundInclusive, RangeBoundType.BoundInclusive)
        RPT.SetParameterValue(1, AccNo)
        RPT.SetParameterValue(2, TrxType)
        AccNo = 0
        CrystalReportViewer2.ReportSource = RPT

    End Sub


End Class