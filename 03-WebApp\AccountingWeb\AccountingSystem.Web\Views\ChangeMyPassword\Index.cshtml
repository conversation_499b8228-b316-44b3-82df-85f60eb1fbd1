@using AccountingSystem.Web.Controllers
@model ChangeMyPasswordViewModel
@{
    ViewData["Title"] = "تغيير كلمة المرور الخاصة بي";
    Layout = "_Layout";
}

<div class="container-fluid" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-key"></i>
                        تغيير كلمة المرور الخاصة بي
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Alert Messages -->
                    @if (TempData["Success"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i>
                            @TempData["Success"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-danger animate-error" role="alert">
                            <i class="fas fa-exclamation-circle"></i>
                            @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                            {
                                <div>@error.ErrorMessage</div>
                            }
                        </div>
                    }

                    <form asp-action="Index" method="post" class="needs-validation" novalidate>
                        @Html.AntiForgeryToken()
                        
                        <!-- Current User Info -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">المستخدم الحالي</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control" value="@Model.Username" readonly />
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- Old Password -->
                        <div class="mb-3">
                            <label asp-for="OldPassword" class="form-label fw-bold">
                                <i class="fas fa-lock"></i>
                                كلمة المرور القديمة
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-key"></i>
                                </span>
                                <input asp-for="OldPassword" type="password" class="form-control" 
                                       placeholder="أدخل كلمة المرور الحالية" required />
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('OldPassword')">
                                    <i class="fas fa-eye" id="OldPassword-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="OldPassword" class="text-danger"></span>
                        </div>

                        <!-- New Password -->
                        <div class="mb-3">
                            <label asp-for="NewPassword" class="form-label fw-bold">
                                <i class="fas fa-lock"></i>
                                كلمة المرور الجديدة
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-key"></i>
                                </span>
                                <input asp-for="NewPassword" type="password" class="form-control" 
                                       placeholder="أدخل كلمة المرور الجديدة" required />
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('NewPassword')">
                                    <i class="fas fa-eye" id="NewPassword-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="NewPassword" class="text-danger"></span>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                كلمة المرور يجب أن تكون بين 4 و 100 حرف
                            </div>
                        </div>

                        <!-- Confirm Password -->
                        <div class="mb-4">
                            <label asp-for="ConfirmPassword" class="form-label fw-bold">
                                <i class="fas fa-lock"></i>
                                تأكيد كلمة المرور الجديدة
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-key"></i>
                                </span>
                                <input asp-for="ConfirmPassword" type="password" class="form-control" 
                                       placeholder="أعد إدخال كلمة المرور الجديدة" required />
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('ConfirmPassword')">
                                    <i class="fas fa-eye" id="ConfirmPassword-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="/SimpleDashboard" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Tips -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-shield-alt"></i>
                        نصائح الأمان
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            استخدم كلمة مرور قوية تحتوي على أرقام وحروف
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            لا تشارك كلمة المرور مع أي شخص آخر
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            غير كلمة المرور بانتظام لضمان الأمان
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success"></i>
                            تأكد من تسجيل الخروج عند الانتهاء من العمل
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Toggle password visibility
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const eyeIcon = document.getElementById(fieldId + '-eye');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // Auto-hide success alerts (VB.NET-like behavior)
        setTimeout(function() {
            $('.alert-success').fadeOut('slow');
        }, 5000);
        
        // Show success message prominently (like VB.NET MsgBox)
        if ($('.alert-success').length > 0) {
            // Add a gentle shake animation to draw attention
            $('.alert-success').addClass('animate-success');
            // Focus on old password field after success (like VB.NET)
            setTimeout(function() {
                document.getElementById('OldPassword').focus();
            }, 100);
        }

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // Real-time password confirmation validation
        document.getElementById('ConfirmPassword').addEventListener('input', function() {
            const newPassword = document.getElementById('NewPassword').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && newPassword !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });

        // Focus management (VB.NET behavior)
        document.addEventListener('DOMContentLoaded', function() {
            // Check if controller specified a focus field
            var focusField = '@ViewBag.FocusField';
            if (focusField && focusField !== '') {
                var element = document.getElementById(focusField);
                if (element) {
                    element.focus();
                    element.select(); // Select all text for easy replacement
                }
            } else {
                // Default focus on old password field
                document.getElementById('OldPassword').focus();
            }
        });
    </script>

    <style>
        .card {
            border-radius: 15px;
            border: none;
        }

        .card-header {
            border-radius: 15px 15px 0 0 !important;
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .input-group-text {
            background-color: #f8f9fa;
            border-color: #ced4da;
        }

        .btn {
            border-radius: 8px;
        }

        .alert {
            border-radius: 10px;
        }

        .form-text {
            font-size: 0.875em;
            color: #6c757d;
        }

        .list-unstyled li {
            padding: 0.25rem 0;
        }

        .shadow-lg {
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
        }

        /* VB.NET-like success animation */
        .animate-success {
            animation: successPulse 0.6s ease-in-out;
        }

        @@keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* VB.NET-like error animation */
        .animate-error {
            animation: errorShake 0.5s ease-in-out;
        }

        @@keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        /* Enhanced focus styles */
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
            animation: focusPulse 0.3s ease-in-out;
        }

        @@keyframes focusPulse {
            0% { box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25); }
            50% { box-shadow: 0 0 0 0.4rem rgba(13, 110, 253, 0.15); }
            100% { box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25); }
        }
    </style>
}
