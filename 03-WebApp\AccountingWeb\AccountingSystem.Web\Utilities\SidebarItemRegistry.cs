using System.Collections.Generic;

namespace AccountingSystem.Web.Utilities
{
    public class SidebarItem
    {
        public string Title { get; set; } = string.Empty;
        public string Route { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Section { get; set; } = string.Empty;
    }

    public static class SidebarItemRegistry
    {
        public static List<SidebarItem> AllItems { get; } = new List<SidebarItem>
        {
            // Dashboard
            new SidebarItem { Title = "الصفحة الرئيسية", Route = "/SimpleDashboard/Index", Icon = "fas fa-home", Section = "Dashboard" },

            // Sales
            new SidebarItem { Title = "فواتير المبيعات", Route = "/Sales/Invoice", Icon = "fas fa-file-invoice", Section = "Sales" },
            new SidebarItem { Title = "نقاط البيع", Route = "/POS/Index", Icon = "fas fa-cash-register", Section = "Sales" },
            new SidebarItem { Title = "نقطة البيع الجديدة", Route = "/posNew", Icon = "fas fa-cash-register", Section = "Sales" },
            new SidebarItem { Title = "جلسات نقاط البيع", Route = "/POSSessions/Index", Icon = "fas fa-clock", Section = "Sales" },
            new SidebarItem { Title = "مرتجع المبيعات", Route = "/Sales/Return", Icon = "fas fa-undo", Section = "Sales" },
            new SidebarItem { Title = "بحث المبيعات", Route = "/Sales/Search", Icon = "fas fa-search", Section = "Sales" },

            // Purchases
            new SidebarItem { Title = "إنشاء فاتورة مشتريات", Route = "/Purchase/Create", Icon = "fas fa-plus", Section = "Purchases" },
            new SidebarItem { Title = "عرض فواتير المشتريات", Route = "/Purchase/Index", Icon = "fas fa-list", Section = "Purchases" },
            new SidebarItem { Title = "مرتجع المشتريات", Route = "/Purchase/Return", Icon = "fas fa-undo", Section = "Purchases" },

            // Cash Movements
            new SidebarItem { Title = "سندات القبض", Route = "/Cash/Receipt", Icon = "fas fa-hand-holding-usd", Section = "Cash" },
            new SidebarItem { Title = "سندات الصرف", Route = "/Cash/Payment", Icon = "fas fa-money-bill-wave", Section = "Cash" },
            new SidebarItem { Title = "القيود اليومية", Route = "/Journal/Entry", Icon = "fas fa-book", Section = "Cash" },

            // Master Data
            new SidebarItem { Title = "العملاء", Route = "/Customers/Index", Icon = "fas fa-users", Section = "MasterData" },
            new SidebarItem { Title = "الموردين", Route = "/Vendors/Index", Icon = "fas fa-truck", Section = "MasterData" },
            new SidebarItem { Title = "الأصناف", Route = "/Items/Index", Icon = "fas fa-boxes", Section = "MasterData" },
            new SidebarItem { Title = "تصنيفات الأصناف", Route = "/ItemsCategories", Icon = "fas fa-sitemap", Section = "MasterData" },
            new SidebarItem { Title = "الموظفين", Route = "/Employees/Index", Icon = "fas fa-users", Section = "MasterData" },
            new SidebarItem { Title = "دليل الحسابات", Route = "/ChartOfAccounts/Index", Icon = "fas fa-sitemap", Section = "MasterData" },
            new SidebarItem { Title = "إدارة المتاجر", Route = "/StoresV2/Index", Icon = "fas fa-store", Section = "MasterData" },
            new SidebarItem { Title = "إدارة المستودعات", Route = "/Warehouses/Index", Icon = "fas fa-warehouse", Section = "MasterData" },

            // Reports
            new SidebarItem { Title = "التقارير المالية", Route = "/Reports/Financial", Icon = "fas fa-chart-line", Section = "Reports" },
            new SidebarItem { Title = "تقارير المخزون", Route = "/Reports/Inventory", Icon = "fas fa-warehouse", Section = "Reports" },

            // Settings
            new SidebarItem { Title = "إدارة المستخدمين", Route = "/UserManagement/Index", Icon = "fas fa-user-cog", Section = "Settings" },
            new SidebarItem { Title = "صلاحيات المجموعات", Route = "/GroupPermissions/Index", Icon = "fas fa-shield-alt", Section = "Settings" },
            new SidebarItem { Title = "ربط الحسابات", Route = "/GLConfig/Index", Icon = "fas fa-link", Section = "Settings" },
            new SidebarItem { Title = "إعدادات الفواتير", Route = "/InvoiceToolSettings", Icon = "fas fa-tools", Section = "Settings" },
            new SidebarItem { Title = "إعدادات النظام", Route = "/SystemSetup/Index", Icon = "fas fa-cogs", Section = "Settings" },
            new SidebarItem { Title = "إعدادات الباركود", Route = "/BarcodeSettings/Index", Icon = "fas fa-barcode", Section = "Settings" },

            // Utilities
            new SidebarItem { Title = "اختبار قاعدة البيانات", Route = "/Test/Index", Icon = "fas fa-database", Section = "Utilities" },
            new SidebarItem { Title = "النسخ الاحتياطي", Route = "/Backup/Index", Icon = "fas fa-database", Section = "Utilities" }
        };
    }
} 