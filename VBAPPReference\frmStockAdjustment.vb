﻿Imports System.ComponentModel
Imports System.Data.DataTable
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Drawing
Imports System.IO
Imports Microsoft.Office.Interop
Imports OfficeOpenXml
Imports OfficeOpenXml.Style


Public Class frmStockAdjustment
    Dim AdjustmentNo As Int64 = 0
    Dim LineSN As Int64 = 1
    Dim ForClose As Integer = 0
    Dim ForItemChange As Integer = 0
    Dim ImportedData As DataTable
    Dim PrintCopies, MaterialAccount, DiscountAccount As Int64
    Dim PrintOption, ReferenceMandatory, DefaultPrinter, MandatoryCustomerVATReg, PriceIncludeVATDef, NonVATInvoiceDef, PaymentType As String

    Function ReadExcelFileOleDb(filePath As String) As System.Data.DataTable
        Dim dt As New System.Data.DataTable()
        Dim connectionString As String = ""

        Try
            ' تحديد نوع الاتصال حسب امتداد الملف
            If Path.GetExtension(filePath).ToLower() = ".xlsx" Then
                connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" & filePath & ";Extended Properties=""Excel 12.0 Xml;HDR=Yes;IMEX=1"""
            Else
                connectionString = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" & filePath & ";Extended Properties=""Excel 8.0;HDR=Yes;IMEX=1"""
            End If

            Using connection As New OleDbConnection(connectionString)
                connection.Open()

                ' الحصول على اسم الورقة الأولى
                Dim schemaTable As DataTable = connection.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, Nothing)
                Dim sheetName As String = schemaTable.Rows(0)("TABLE_NAME").ToString()

                ' استعلام لقراءة البيانات
                Dim query As String = "SELECT * FROM [" & sheetName & "]"

                Using adapter As New OleDbDataAdapter(query, connection)
                    adapter.Fill(dt)
                End Using
            End Using

            ' إعادة تسمية الأعمدة وتنظيف البيانات
            If dt.Columns.Count >= 4 Then
                dt.Columns(0).ColumnName = "ItemNo"
                dt.Columns(1).ColumnName = "ActualQty"
                dt.Columns(2).ColumnName = "UofM"
                dt.Columns(3).ColumnName = "Notes"

                ' إزالة الصفوف الفارغة
                For i As Integer = dt.Rows.Count - 1 To 0 Step -1
                    If String.IsNullOrWhiteSpace(dt.Rows(i)("ItemNo").ToString()) Then
                        dt.Rows.RemoveAt(i)
                    End If
                Next
            End If

            Return dt

        Catch ex As Exception
            MessageBox.Show("OleDb: خطأ في استيراد ملف Excel: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return Nothing
        End Try
    End Function
    Private Sub frmStockAdjustment_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        AdjustmentNo = 0
        StoresLoad()
        LoadSettings()
        GetSerial()
        dtpAdjustmentDate.Value = DateTime.Now
        ForceGregorianForAllPickers(Me)
        LoadUserAuthorization(UserName)
    End Sub
    Sub LoadSettings()
        Dim CMD As New SqlCommand("Select * from tblToolsInvoice where InvoiceType = 'تسوية مخزون'", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        If reader.Read Then
            'ckbxVATIncluded.Enabled = Trim(reader.Item(1).ToString)
            'ckbxNonVatInvoice.Enabled = Trim(reader.Item(2).ToString)



            Store = Trim(reader.Item(12).ToString)
            PriceIncludeVATDef = Trim(reader.Item(13).ToString)
            NonVATInvoiceDef = Trim(reader.Item(14).ToString)
            MaterialAccount = Val(reader.Item(4).ToString)
            DiscountAccount = Val(reader.Item(5).ToString)
            MandatoryCustomerVATReg = Trim(reader.Item(8).ToString)
            DefaultPrinter = Trim(reader.Item(6).ToString)
            PrintOption = Trim(reader.Item(7).ToString)
            ReferenceMandatory = Trim(reader.Item(3).ToString)

        End If
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If


    End Sub
    Sub LoadUserAuthorization(ByVal username As String)
        Try
            Dim query As String = "SELECT GroupID, DefaultStore, StoreChange, DefaultCustomer, CustomerChange, DefaultCashier, CashierChange, ChangeInvoicePrice, MaxDiscountPercent 
                               FROM tblUsers WHERE Username = @Username"

            Dim cmd As New SqlCommand(query, Con)
            cmd.Parameters.AddWithValue("@Username", username)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            Dim reader As SqlDataReader = cmd.ExecuteReader()

            If reader.Read() Then
                Dim isAdmin As Boolean = (Not IsDBNull(reader("GroupID")) AndAlso Convert.ToInt32(reader("GroupID")) = 1)

                ' If the user is in GroupID = 1 (Admin), grant full access
                If isAdmin Then
                    cmbxStore.Enabled = True
                    'cmbxPartnerNo.Enabled = True
                    'cmbxCashier.Enabled = True

                Else
                    ' Apply Default Store
                    If Not IsDBNull(reader("DefaultStore")) Then
                        cmbxStore.SelectedItem = reader("DefaultStore").ToString()
                    End If
                    cmbxStore.Enabled = Convert.ToBoolean(reader("StoreChange")) ' Enable if allowed

                    ' Apply Default Customer
                    'If Not IsDBNull(reader("DefaultCustomer")) Then
                    '    cmbxPartnerNo.SelectedValue = Convert.ToInt32(reader("DefaultCustomer"))
                    'End If
                    'cmbxPartnerNo.Enabled = Convert.ToBoolean(reader("CustomerChange")) ' Enable if allowed

                    ' Apply Default Cashier
                    'If Not IsDBNull(reader("DefaultCashier")) Then
                    '    cmbxCashier.SelectedValue = Convert.ToInt32(reader("DefaultCashier"))
                    'End If
                    'cmbxCashier.Enabled = Convert.ToBoolean(reader("CashierChange")) ' Enable if allowed

                    ' Apply Price Change Authorization
                    'txtUnitPrice.Enabled = Convert.ToBoolean(reader("ChangeInvoicePrice")) ' Enable if allowed

                    If Not IsDBNull(reader("MaxDiscountPercent")) Then
                        MaxDiscountPercent = Convert.ToDecimal(reader("MaxDiscountPercent"))
                    Else
                        MaxDiscountPercent = 0
                    End If


                End If
            End If

            reader.Close()

            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If

        Catch ex As Exception
            MessageBox.Show("Error loading user authorization: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Sub StoresLoad()
        Try
            Dim CMD As New SqlCommand("SELECT SN, Store FROM tblStores ORDER BY Store", Con)
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim reader As SqlDataReader = CMD.ExecuteReader()
            cmbxStore.Items.Clear()
            While reader.Read()
                cmbxStore.Items.Add(reader("Store").ToString())
            End While
            reader.Close()
            If Con.State <> ConnectionState.Closed Then Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل المخازن: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Sub GetSerial()
        Try
            Dim PreUsedCMD As New SqlCommand("SELECT MIN(TrxNo) FROM tblStockMovHeader WHERE ReadyForUse = 'True' AND TrxType = 'تسوية مخزون'", Con)
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim reader As SqlDataReader = PreUsedCMD.ExecuteReader()
            If reader.Read() Then
                AdjustmentNo = Val(reader.Item(0).ToString())
            Else
                AdjustmentNo = 0
            End If
            reader.Close()

            If AdjustmentNo = 0 Then
                Dim SelectCMD As New SqlCommand("SELECT ISNULL(MAX(TrxNo), 0) + 1 FROM tblStockMovHeader WHERE TrxType = 'تسوية مخزون'", Con)
                Dim reader2 As SqlDataReader = SelectCMD.ExecuteReader()
                If reader2.Read() Then
                    AdjustmentNo = Val(reader2.Item(0).ToString())
                End If
                reader2.Close()

                Dim InsertCMD As New SqlCommand("INSERT INTO tblStockMovHeader (TrxType, TrxNo, ReadyForUse, CreatedBy, CreatedOn) VALUES ('تسوية مخزون', @TrxNo, 'False', @User, GETDATE())", Con)
                InsertCMD.Parameters.AddWithValue("@TrxNo", AdjustmentNo)
                InsertCMD.Parameters.AddWithValue("@User", UserName)
                InsertCMD.ExecuteNonQuery()
            Else
                Dim UpdateCMD As New SqlCommand("UPDATE tblStockMovHeader SET ReadyForUse = 'False' WHERE TrxNo = @TrxNo AND TrxType = 'تسوية مخزون'", Con)
                UpdateCMD.Parameters.AddWithValue("@TrxNo", AdjustmentNo)
                UpdateCMD.ExecuteNonQuery()
            End If

            lblStatus.Text = "رقم التسوية: " & AdjustmentNo
            If Con.State <> ConnectionState.Closed Then Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في الحصول على رقم التسوية: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnImportExcel_Click(sender As Object, e As EventArgs) Handles btnImportExcel.Click
        ImportFromExcel()
    End Sub
    Private Sub btnLoadItems_Click(sender As Object, e As EventArgs) Handles btnLoadItems.Click
        LoadItemsFromSystem()
        RemoveZeroValueItems()
    End Sub

    ' Add this event handler for Export Report button

    Sub ImportFromExcel()
        Try
            Dim openFileDialog As New OpenFileDialog()
            openFileDialog.Filter = "Excel Files|*.xlsx;*.xls"
            openFileDialog.Title = "اختر ملف Excel للاستيراد"
            If openFileDialog.ShowDialog() = DialogResult.OK Then
                progressBar.Visible = True
                progressBar.Style = ProgressBarStyle.Marquee
                lblStatus.Text = "جاري قراءة ملف Excel..."
                ImportedData = ReadExcelFileOleDb(openFileDialog.FileName)
                If ImportedData IsNot Nothing AndAlso ImportedData.Rows.Count > 0 Then
                    ProcessImportedData()
                    lblStatus.Text = "تم استيراد " & ImportedData.Rows.Count & " صنف من Excel"
                Else
                    MessageBox.Show("لم يتم العثور على بيانات في الملف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                End If
                progressBar.Visible = False
            End If
        Catch ex As Exception
            progressBar.Visible = False
            MessageBox.Show("خطأ في استيراد ملف Excel: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Sub RemoveZeroValueItems()
        Try
            For i As Integer = DGVItems.Rows.Count - 1 To 0 Step -1
                Dim row As DataGridViewRow = DGVItems.Rows(i)

                Dim qtySystem As Decimal = Convert.ToDecimal(row.Cells("كمية النظام").Value)
                Dim qtyActual As Decimal = Convert.ToDecimal(row.Cells("الكمية الفعلية").Value)
                Dim diff As Decimal = Convert.ToDecimal(row.Cells("الفرق").Value)

                If qtySystem = 0 AndAlso qtyActual = 0 AndAlso diff = 0 Then
                    DGVItems.Rows.RemoveAt(i)
                End If
            Next

            lblStatus.Text = "تم إزالة الأصناف ذات القيم الصفرية"
        Catch ex As Exception
            MessageBox.Show("خطأ أثناء إزالة الأصناف الصفرية: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub


    Sub LoadItemsFromSystem()
        Try
            progressBar.Visible = True
            progressBar.Style = ProgressBarStyle.Marquee
            lblStatus.Text = "جاري تحميل الأصناف..."
            Dim query As String = "SELECT i.ItemNo AS [رقم الصنف], i.ItemDescription AS [اسم الصنف], i.UofM AS الوحدة, ROUND(ISNULL(sb.StockBalance, 0), 3) AS [كمية النظام], 0.000 AS [الكمية الفعلية], 0.000 AS الفرق, '' AS ملاحظات, '' AS [نوع الفرق], ISNULL(sb.StockValue, 0) AS [قيمة المخزون] FROM tblItems AS i LEFT OUTER JOIN StockBalanceView AS sb ON sb.ItemNo = i.ItemNo AND sb.Store = @Store ORDER BY i.ItemDescription"
            '"SELECT 
            '    sb.ItemNo AS [رقم الصنف],
            '    i.ItemDescription AS [اسم الصنف],
            '    i.UofM AS [الوحدة],
            '    ROUND(sb.StockBalance, 3) AS [كمية النظام],
            '    0.000 AS [الكمية الفعلية],
            '    0.000 AS [الفرق],
            '    '' AS [ملاحظات],
            '    '' AS [نوع الفرق],
            '    sb.StockValue AS [قيمة المخزون]
            'FROM StockBalanceView sb
            'INNER JOIN tblItems i ON sb.ItemNo = i.ItemNo
            'WHERE sb.Store = @Store AND sb.StockBalance > 0
            'ORDER BY i.ItemDescription"

            Dim cmd As New SqlCommand(query, Con)
            cmd.Parameters.AddWithValue("@Store", cmbxStore.Text.Trim())

            Dim adapter As New SqlDataAdapter(cmd)
            Dim dt As New System.Data.DataTable()
            adapter.Fill(dt)

            DGVItems.DataSource = dt
            FormatDataGridView()

            progressBar.Visible = False
            lblStatus.Text = "تم تحميل " & dt.Rows.Count & " صنف من النظام"
        Catch ex As Exception
            progressBar.Visible = False
            MessageBox.Show("خطأ في تحميل الأصناف من النظام: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Sub FormatDataGridView()
        With DGVItems
            .AutoGenerateColumns = False
            .AllowUserToAddRows = False
            .AllowUserToDeleteRows = False
            .ReadOnly = False

            ' السماح بتعديل الكمية الفعلية والملاحظات فقط
            For Each col As DataGridViewColumn In .Columns
                If col.HeaderText = "الكمية الفعلية" Or col.HeaderText = "ملاحظات" Then
                    col.ReadOnly = False
                Else
                    col.ReadOnly = True
                End If

                If col.HeaderText.Contains("كمية") Or col.HeaderText.Contains("فرق") Then
                    col.DefaultCellStyle.Format = "N3"
                    col.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter
                End If
            Next
        End With
    End Sub

    Sub ProcessImportedData()
        Try
            ' تحميل بيانات النظام إن لم تكن محمّلة
            If DGVItems.DataSource Is Nothing Then
                LoadItemsFromSystem()
            End If

            Dim dt As System.Data.DataTable = CType(DGVItems.DataSource, System.Data.DataTable)
            Dim missingItems As New List(Of Integer)

            For Each importRow As DataRow In ImportedData.Rows
                Dim itemNo As Integer = Convert.ToInt32(importRow("ItemNo"))
                Dim actualQty As Decimal = Convert.ToDecimal(importRow("ActualQty"))
                Dim uom As String = importRow("UofM").ToString().Trim()
                Dim notes As String = importRow("Notes").ToString().Trim()

                ' البحث عن الصف المطابق في DataTable
                Dim foundRows() As DataRow = dt.Select("[رقم الصنف] = " & itemNo.ToString())

                If foundRows.Length > 0 Then
                    Dim systemUofM As String = foundRows(0)("الوحدة").ToString()

                    ' إذا كانت الوحدة مختلفة يتم تحويل الكمية
                    If Not String.IsNullOrWhiteSpace(uom) AndAlso uom <> systemUofM Then
                        Dim convCmd As New SqlCommand("
                        SELECT TOP 1 Coversion_to_Base 
                        FROM UnitsPricesView 
                        WHERE ItemNo = @ItemNo AND Sales_Unit = @UofM", Con)

                        convCmd.Parameters.AddWithValue("@ItemNo", itemNo)
                        convCmd.Parameters.AddWithValue("@UofM", uom)

                        If Con.State <> ConnectionState.Open Then Con.Open()
                        Dim factor = convCmd.ExecuteScalar()
                        If Con.State <> ConnectionState.Closed Then Con.Close()

                        If factor IsNot Nothing AndAlso IsNumeric(factor) Then
                            actualQty *= Convert.ToDecimal(factor)
                        End If
                    End If

                    ' تحديث القيم في الصف الموجود
                    foundRows(0)("الكمية الفعلية") = actualQty
                    foundRows(0)("ملاحظات") = notes

                    Dim systemQty As Decimal = Convert.ToDecimal(foundRows(0)("كمية النظام"))
                    Dim difference As Decimal = actualQty - systemQty
                    foundRows(0)("الفرق") = difference
                    foundRows(0)("نوع الفرق") = If(difference > 0, "زيادة", If(difference < 0, "نقص", ""))

                Else
                    ' صنف غير موجود
                    missingItems.Add(itemNo)

                    Dim newRow As DataRow = dt.NewRow()
                    newRow("رقم الصنف") = itemNo
                    newRow("اسم الصنف") = "غير موجود"
                    newRow("الوحدة") = uom
                    newRow("كمية النظام") = 0
                    newRow("الكمية الفعلية") = actualQty
                    newRow("الفرق") = actualQty
                    newRow("ملاحظات") = notes
                    newRow("نوع الفرق") = "غير معرف"
                    dt.Rows.Add(newRow)
                End If
            Next
            ' إزالة الصفوف التي تحتوي على قيم صفرية أو أصناف غير موجودة
            For i As Integer = dt.Rows.Count - 1 To 0 Step -1
                Dim row As DataRow = dt.Rows(i)
                Dim itemName As String = row("اسم الصنف").ToString().Trim()
                Dim qtySystem As Decimal = Convert.ToDecimal(row("كمية النظام"))
                Dim qtyActual As Decimal = Convert.ToDecimal(row("الكمية الفعلية"))
                Dim diff As Decimal = Convert.ToDecimal(row("الفرق"))

                If (qtySystem = 0 AndAlso qtyActual = 0 AndAlso diff = 0) OrElse itemName = "غير موجود" Then
                    dt.Rows.RemoveAt(i)
                End If
            Next

            DGVItems.DataSource = dt
            DGVItems.Refresh()

            ' تلوين الصفوف غير المعروفة
            For Each row As DataGridViewRow In DGVItems.Rows
                If row.Cells("اسم الصنف").Value.ToString() = "غير موجود" Then
                    row.DefaultCellStyle.BackColor = Color.LightYellow
                    row.DefaultCellStyle.ForeColor = Color.DarkRed
                End If
            Next
            ' تنبيه إذا كانت هناك أصناف مفقودة
            If missingItems.Count > 0 Then
                MessageBox.Show("لم يتم العثور على الأصناف التالية في النظام: " & String.Join(", ", missingItems), "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If

        Catch ex As Exception
            MessageBox.Show("خطأ في معالجة البيانات المستوردة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' First, install EPPlus NuGet package:
    ' Install-Package EPPlus

    ' Add this to your frmStockAdjustment class

#Region "Export Report Methods"


    Private Sub btnExportReport_Click(sender As Object, e As EventArgs) Handles btnExportReport.Click
    ExportReport()
End Sub

Sub ExportReport()
    Try
        If DGVItems.DataSource Is Nothing OrElse DGVItems.Rows.Count = 0 Then
            MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim saveFileDialog As New SaveFileDialog()
        saveFileDialog.Filter = "Excel Files|*.xlsx"
        saveFileDialog.Title = "حفظ تقرير التسوية"
        saveFileDialog.FileName = "تقرير_التسوية_" & cmbxStore.Text & "_" & DateTime.Now.ToString("yyyy-MM-dd")

        If saveFileDialog.ShowDialog() = DialogResult.OK Then
            progressBar.Visible = True
            progressBar.Style = ProgressBarStyle.Marquee
            lblStatus.Text = "جاري تصدير التقرير..."

            ExportToExcelWithEPPlus(saveFileDialog.FileName)
            
            progressBar.Visible = False
            lblStatus.Text = "تم تصدير التقرير بنجاح"
            
            If MessageBox.Show("تم تصدير التقرير بنجاح. هل تريد فتح الملف؟", "نجح", MessageBoxButtons.YesNo, MessageBoxIcon.Information) = DialogResult.Yes Then
                Process.Start(saveFileDialog.FileName)
            End If
        End If

    Catch ex As Exception
        progressBar.Visible = False
        MessageBox.Show("خطأ في تصدير التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Try
End Sub

Private Sub ExportToExcelWithEPPlus(filePath As String)
    ' EPPlus 4.x doesn't require LicenseContext - remove this line

    Using package As New ExcelPackage()
        Dim worksheet As ExcelWorksheet = package.Workbook.Worksheets.Add("تقرير التسوية")
        
        ' Set RTL for Arabic text
        worksheet.View.RightToLeft = True
        
        ' Add header information
        AddReportHeader(worksheet)
        
        ' Add data table
        AddDataTable(worksheet)
        
        ' Format the worksheet
        FormatWorksheet(worksheet)
        
        ' Save the file
        Dim fileInfo As New FileInfo(filePath)
        package.SaveAs(fileInfo)
    End Using
End Sub

Private Sub AddReportHeader(worksheet As ExcelWorksheet)
    ' Title
    worksheet.Cells("A1").Value = "تقرير تسوية الجرد المخزني"
    worksheet.Cells("A1:I1").Merge = True
    
    ' Report information
    worksheet.Cells("A3").Value = "رقم التسوية:"
    worksheet.Cells("B3").Value = AdjustmentNo
    
    worksheet.Cells("A4").Value = "المخزن:"
    worksheet.Cells("B4").Value = cmbxStore.Text
    
    worksheet.Cells("A5").Value = "تاريخ التسوية:"
    worksheet.Cells("B5").Value = dtpAdjustmentDate.Value.ToString("dd/MM/yyyy")
    
    worksheet.Cells("A6").Value = "تاريخ الطباعة:"
    worksheet.Cells("B6").Value = DateTime.Now.ToString("dd/MM/yyyy HH:mm")
    
    worksheet.Cells("A7").Value = "المستخدم:"
    worksheet.Cells("B7").Value = UserName
    
    ' Summary information
    Dim dt As DataTable = CType(DGVItems.DataSource, DataTable)
    Dim totalItems As Integer = dt.Rows.Count
    Dim itemsWithDifference As Integer = dt.Select("[الفرق] <> 0").Length
    Dim increaseItems As Integer = dt.Select("[نوع الفرق] = 'زيادة'").Length
    Dim decreaseItems As Integer = dt.Select("[نوع الفرق] = 'نقص'").Length
    
    worksheet.Cells("E3").Value = "إجمالي الأصناف:"
    worksheet.Cells("F3").Value = totalItems
    
    worksheet.Cells("E4").Value = "أصناف بها فروقات:"
    worksheet.Cells("F4").Value = itemsWithDifference
    
    worksheet.Cells("E5").Value = "أصناف زيادة:"
    worksheet.Cells("F5").Value = increaseItems
    
    worksheet.Cells("E6").Value = "أصناف نقص:"
    worksheet.Cells("F6").Value = decreaseItems
End Sub

Private Sub AddDataTable(worksheet As ExcelWorksheet)
    Dim dt As DataTable = CType(DGVItems.DataSource, DataTable)
    Dim startRow As Integer = 10
    
    ' Add column headers
    For col As Integer = 0 To dt.Columns.Count - 1
        worksheet.Cells(startRow, col + 1).Value = dt.Columns(col).ColumnName
    Next
    
    ' Add data rows
    For row As Integer = 0 To dt.Rows.Count - 1
        For col As Integer = 0 To dt.Columns.Count - 1
            Dim cellValue = dt.Rows(row)(col)
            
            ' Handle different data types
            If IsNumeric(cellValue) Then
                worksheet.Cells(startRow + row + 1, col + 1).Value = Convert.ToDecimal(cellValue)
            Else
                worksheet.Cells(startRow + row + 1, col + 1).Value = cellValue.ToString()
            End If
        Next
        
        ' Color rows based on difference type
        Dim differenceType As String = dt.Rows(row)("نوع الفرق").ToString()
        Select Case differenceType
            Case "زيادة"
                worksheet.Cells(startRow + row + 1, 1, startRow + row + 1, dt.Columns.Count).Style.Fill.PatternType = ExcelFillStyle.Solid
                worksheet.Cells(startRow + row + 1, 1, startRow + row + 1, dt.Columns.Count).Style.Fill.BackgroundColor.SetColor(Color.LightGreen)
            Case "نقص"
                worksheet.Cells(startRow + row + 1, 1, startRow + row + 1, dt.Columns.Count).Style.Fill.PatternType = ExcelFillStyle.Solid
                worksheet.Cells(startRow + row + 1, 1, startRow + row + 1, dt.Columns.Count).Style.Fill.BackgroundColor.SetColor(Color.LightCoral)
            Case "غير معرف"
                worksheet.Cells(startRow + row + 1, 1, startRow + row + 1, dt.Columns.Count).Style.Fill.PatternType = ExcelFillStyle.Solid
                worksheet.Cells(startRow + row + 1, 1, startRow + row + 1, dt.Columns.Count).Style.Fill.BackgroundColor.SetColor(Color.LightYellow)
        End Select
    Next
End Sub

Private Sub FormatWorksheet(worksheet As ExcelWorksheet)
    ' Title formatting
    With worksheet.Cells("A1")
        .Style.Font.Size = 16
        .Style.Font.Bold = True
        .Style.HorizontalAlignment = ExcelHorizontalAlignment.Center
        .Style.Fill.PatternType = ExcelFillStyle.Solid
        .Style.Fill.BackgroundColor.SetColor(Color.FromArgb(41, 53, 65))
        .Style.Font.Color.SetColor(Color.White)
    End With
    
    ' Header information formatting
    worksheet.Cells("A3:B7").Style.Font.Bold = True
    worksheet.Cells("E3:F6").Style.Font.Bold = True
    
    ' Data table header formatting
    Dim dt As DataTable = CType(DGVItems.DataSource, DataTable)
    Dim headerRange = worksheet.Cells(10, 1, 10, dt.Columns.Count)
    
    With headerRange.Style
        .Font.Bold = True
        .Fill.PatternType = ExcelFillStyle.Solid
        .Fill.BackgroundColor.SetColor(Color.FromArgb(52, 73, 94))
        .Font.Color.SetColor(Color.White)
        .HorizontalAlignment = ExcelHorizontalAlignment.Center
        .Border.BorderAround(ExcelBorderStyle.Thin)
    End With
    
    ' Data formatting
    Dim dataRange = worksheet.Cells(11, 1, 10 + dt.Rows.Count, dt.Columns.Count)
    With dataRange.Style
        .Border.BorderAround(ExcelBorderStyle.Thin)
        .HorizontalAlignment = ExcelHorizontalAlignment.Center
    End With
    
    ' Format numeric columns
    For col As Integer = 1 To dt.Columns.Count
        Dim columnName As String = dt.Columns(col - 1).ColumnName
        If columnName.Contains("كمية") Or columnName.Contains("فرق") Then
            worksheet.Cells(11, col, 10 + dt.Rows.Count, col).Style.Numberformat.Format = "#,##0.000"
        End If
    Next
    
    ' Auto-fit columns
    worksheet.Cells.AutoFitColumns()
    
    ' Set print settings (EPPlus 4.x syntax)
    worksheet.PrinterSettings.Orientation = eOrientation.Landscape
    worksheet.PrinterSettings.FitToPage = True
    worksheet.PrinterSettings.FitToWidth = 1
    worksheet.PrinterSettings.FitToHeight = 0
    
    ' Add borders to all data
    Dim allDataRange = worksheet.Cells(10, 1, 10 + dt.Rows.Count, dt.Columns.Count)
    allDataRange.Style.Border.Top.Style = ExcelBorderStyle.Thin
    allDataRange.Style.Border.Left.Style = ExcelBorderStyle.Thin
    allDataRange.Style.Border.Right.Style = ExcelBorderStyle.Thin
    allDataRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thin
End Sub

#End Region

    ' Add this to your frmStockAdjustment class

#Region "Process Adjustment Methods"

    Private Sub btnProcessAdjustment_Click(sender As Object, e As EventArgs) Handles btnProcessAdjustment.Click
        ProcessStockAdjustment()
    End Sub

    Sub ProcessStockAdjustment()
        Try
            ' Validate before processing
            If Not ValidateAdjustmentData() Then
                Return
            End If

            ' Confirm processing
            Dim result As DialogResult = MessageBox.Show(
            "هل أنت متأكد من تسوية الفروقات؟" & vbCrLf &
            "سيتم تحديث أرصدة المخزون وإنشاء حركات مخزنية." & vbCrLf &
            "لا يمكن التراجع عن هذه العملية!",
            "تأكيد التسوية",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question)

            If result = DialogResult.No Then
                Return
            End If

            progressBar.Visible = True
            progressBar.Style = ProgressBarStyle.Marquee
            lblStatus.Text = "جاري معالجة التسوية..."

            ' Start transaction
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim transaction As SqlTransaction = Con.BeginTransaction()

            Try
                ' Process the adjustment
                ProcessAdjustmentWithTransaction(transaction)

                ' Commit transaction
                transaction.Commit()

                ' Update UI
                progressBar.Visible = False
                lblStatus.Text = "تم إنجاز التسوية بنجاح - رقم التسوية: " & AdjustmentNo
                InsertJournal()
                MessageBox.Show("تم إنجاز التسوية بنجاح!" & vbCrLf & "رقم التسوية: " & AdjustmentNo,
                          "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ' Disable buttons after successful processing
                btnProcessAdjustment.Enabled = False
                btnImportExcel.Enabled = False
                btnLoadItems.Enabled = False
                DGVItems.ReadOnly = True

            Catch ex As Exception
                ' Rollback on error
                transaction.Rollback()
                Throw ex
            Finally
                If Con.State <> ConnectionState.Closed Then Con.Close()
            End Try

        Catch ex As Exception
            progressBar.Visible = False
            lblStatus.Text = "خطأ في معالجة التسوية"
            MessageBox.Show("خطأ في معالجة التسوية: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function ValidateAdjustmentData() As Boolean
        Try
            ' Check if store is selected
            If String.IsNullOrWhiteSpace(cmbxStore.Text) Then
                MessageBox.Show("يرجى اختيار المخزن", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                cmbxStore.Focus()
                Return False
            End If

            ' Check if data exists
            If DGVItems.DataSource Is Nothing OrElse DGVItems.Rows.Count = 0 Then
                MessageBox.Show("لا توجد بيانات للتسوية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return False
            End If

            ' Check if there are any differences to process
            Dim dt As DataTable = CType(DGVItems.DataSource, DataTable)
            Dim itemsWithDifference As Integer = dt.Select("[الفرق] <> 0").Length

            If itemsWithDifference = 0 Then
                MessageBox.Show("لا توجد فروقات للتسوية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return False
            End If

            ' Check if adjustment was already processed
            Dim checkCmd As New SqlCommand("SELECT ReadyForUse FROM tblStockMovHeader WHERE TrxNo = @TrxNo AND TrxType = 'تسوية مخزون'", Con)
            checkCmd.Parameters.AddWithValue("@TrxNo", AdjustmentNo)

            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim isReady As Object = checkCmd.ExecuteScalar()
            If Con.State <> ConnectionState.Closed Then Con.Close()

            If isReady IsNot Nothing AndAlso isReady.ToString() = "True" Then
                MessageBox.Show("تم معالجة هذه التسوية مسبقاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return False
            End If

            Return True

        Catch ex As Exception
            MessageBox.Show("خطأ في التحقق من البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    Private Sub ProcessAdjustmentWithTransaction(transaction As SqlTransaction)
        Dim dt As DataTable = CType(DGVItems.DataSource, DataTable)
        Dim processedItems As Integer = 0

        ' Update header record
        UpdateAdjustmentHeader(transaction)

        ' Process each item with differences
        For Each row As DataRow In dt.Rows
            Dim difference As Decimal = Convert.ToDecimal(row("الفرق"))

            If difference <> 0 Then
                Dim itemNo As Integer = Convert.ToInt32(row("رقم الصنف"))
                Dim systemQty As Decimal = Convert.ToDecimal(row("كمية النظام"))
                Dim actualQty As Decimal = Convert.ToDecimal(row("الكمية الفعلية"))
                Dim notes As String = row("ملاحظات").ToString()

                ' Insert stock movement detail
                InsertStockMovementDetail(transaction, itemNo, systemQty, actualQty, difference, notes)

                ' Update stock balance
                'UpdateStockBalance(transaction, itemNo, difference)

                processedItems += 1
            End If
        Next

        lblStatus.Text = $"تم معالجة {processedItems} صنف"

    End Sub

    Private Sub UpdateAdjustmentHeader(transaction As SqlTransaction)
        Dim updateHeaderCmd As New SqlCommand("
        UPDATE tblStockMovHeader 
        SET Store = @Store, 
            TrxDate = @TrxDate, 
            TrxNote = @Notes, 
            ModifiedBy = @ModifiedBy, 
            ModifiedOn = GETDATE(), 
            ReadyForUse = 'False'
        WHERE TrxNo = @TrxNo AND TrxType = 'تسوية مخزون'", Con, transaction)

        updateHeaderCmd.Parameters.AddWithValue("@Store", cmbxStore.Text.Trim())
        updateHeaderCmd.Parameters.AddWithValue("@TrxDate", dtpAdjustmentDate.Value.ToShortDateString)
        updateHeaderCmd.Parameters.AddWithValue("@Notes", "تسوية جرد مخزني - " & cmbxStore.Text)
        updateHeaderCmd.Parameters.AddWithValue("@ModifiedBy", UserName)
        updateHeaderCmd.Parameters.AddWithValue("@TrxNo", AdjustmentNo)

        updateHeaderCmd.ExecuteNonQuery()
    End Sub

    Private Sub InsertStockMovementDetail(transaction As SqlTransaction, itemNo As Integer,
                                systemQty As Decimal, actualQty As Decimal,
                                difference As Decimal, notes As String)

        ' First, get the UnitPrice and Conversion from StockBalanceView


        ' Get the uploaded Excel unit (UofM) for this item from ImportedData
        Dim uploadedUnit As String = ""
        If ImportedData IsNot Nothing Then
            Dim importedRows() As DataRow = ImportedData.Select($"ItemNo = {itemNo}")
            If importedRows.Length > 0 Then
                uploadedUnit = importedRows(0)("UofM").ToString()
            End If
        End If
        Dim getStockInfoCmd As New SqlCommand("
        SELECT UnitPrice, Coversion_to_Base 
        FROM UnitsPricesView 
        WHERE ItemNo = @ItemNo AND Sales_Unit = @UofM", Con, transaction)

        getStockInfoCmd.Parameters.AddWithValue("@ItemNo", itemNo)
        getStockInfoCmd.Parameters.AddWithValue("@UofM", uploadedUnit)

        Dim unitPrice As Decimal = 0
        Dim conversionToBase As Decimal = 1

        Dim reader As SqlDataReader = getStockInfoCmd.ExecuteReader()
        If reader.Read() Then
            unitPrice = If(IsDBNull(reader("UnitPrice")), 0, Convert.ToDecimal(reader("UnitPrice")))
            conversionToBase = If(IsDBNull(reader("Coversion_to_Base")), 1, Convert.ToDecimal(reader("Coversion_to_Base")))
        End If
        reader.Close()
        ' Calculate values
        Dim trxQty As Decimal = Math.Abs(difference) ' Always positive
        Dim lineAmount As Decimal = trxQty * unitPrice
        Dim trxType As String = If(difference > 0, "تسوية مخزون زيادة", "تسوية مخزون نقص")

        ' Insert the stock movement record
        Dim insertDetailCmd As New SqlCommand("
        INSERT INTO tblStockMovement 
        (DocNo, LineSN, TrxDate, ItemNo, Store, TrxQty, TrxType, UnitPrice, 
         LineAmount, UofMConversion, UofM, CreatedBy, CreatedOn)
        VALUES 
        (@DocNo, @LineSN, @TrxDate, @ItemNo, @Store, @TrxQty, @TrxType, @UnitPrice,
         @LineAmount, @UofMConversion, @UofM, @CreatedBy, GETDATE())", Con, transaction)

        insertDetailCmd.Parameters.AddWithValue("@DocNo", AdjustmentNo)
        insertDetailCmd.Parameters.AddWithValue("@LineSN", LineSN)
        insertDetailCmd.Parameters.AddWithValue("@TrxDate", dtpAdjustmentDate.Value.ToShortDateString)
        insertDetailCmd.Parameters.AddWithValue("@ItemNo", itemNo)
        insertDetailCmd.Parameters.AddWithValue("@Store", cmbxStore.Text.Trim())
        insertDetailCmd.Parameters.AddWithValue("@TrxQty", trxQty) ' Always positive
        insertDetailCmd.Parameters.AddWithValue("@TrxType", trxType) ' Indicates if increase or decrease
        insertDetailCmd.Parameters.AddWithValue("@UnitPrice", unitPrice)
        insertDetailCmd.Parameters.AddWithValue("@LineAmount", lineAmount)
        insertDetailCmd.Parameters.AddWithValue("@UofMConversion", conversionToBase)
        insertDetailCmd.Parameters.AddWithValue("@UofM", uploadedUnit)
        insertDetailCmd.Parameters.AddWithValue("@CreatedBy", UserName)

        insertDetailCmd.ExecuteNonQuery()
        LineSN += 1
    End Sub

    Private Sub InsertJournal()
        Try
            Using cmd As New SqlCommand("sp_CreateStockAdjustmentEntry", Con)
                cmd.CommandType = CommandType.StoredProcedure
                cmd.Parameters.AddWithValue("@DocNo", AdjustmentNo)
                cmd.Parameters.AddWithValue("@EntryDate", dtpAdjustmentDate.Value.Date) ' Add EntryDate parameter
                cmd.Parameters.AddWithValue("@CreatedBy", UserName) ' Add CreatedBy parameter

                cmd.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ أثناء ترحيل القيد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Throw ex ' Re-throw to trigger transaction rollback
        End Try
    End Sub

#End Region


End Class