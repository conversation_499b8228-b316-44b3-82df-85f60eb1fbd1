@model AccountingSystem.Services.ViewModels.POSSessionCreateViewModel
@{
    ViewData["Title"] = "فتح جلسة جديدة";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus me-2"></i>
                        فتح جلسة جديدة
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post" id="createSessionForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="ShopID" class="form-label"></label>
                                    <select asp-for="ShopID" class="form-select" required>
                                        <option value="">-- اختر المتجر --</option>
                                        @foreach (var shop in ViewBag.Shops)
                                        {
                                            <option value="@shop.SN">@shop.StoreName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="ShopID" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="DeviceID" class="form-label"></label>
                                    <select asp-for="DeviceID" class="form-select" required>
                                        <option value="">-- اختر الجهاز --</option>
                                        @foreach (var device in ViewBag.Devices)
                                        {
                                            <option value="@device.DeviceID">@device.DeviceName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="DeviceID" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="ShiftID" class="form-label"></label>
                                    <select asp-for="ShiftID" class="form-select" required>
                                        <option value="">-- اختر الوردية --</option>
                                        @foreach (var shift in ViewBag.Shifts)
                                        {
                                            <option value="@shift.ShiftID">@shift.ShiftName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="ShiftID" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="OpeningCash" class="form-label"></label>
                                    <input asp-for="OpeningCash" class="form-control" type="number" step="0.01" min="0" />
                                    <span asp-validation-for="OpeningCash" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                فتح الجلسة
                            </button>
                            <a href="@Url.Action("Index")" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i>
                                رجوع
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Form validation
            $('#createSessionForm').submit(function (e) {
                var shopId = $('#ShopID').val();
                var deviceId = $('#DeviceID').val();
                var shiftId = $('#ShiftID').val();

                if (!shopId || !deviceId || !shiftId) {
                    e.preventDefault();
                    alert('يرجى اختيار المتجر والجهاز والوردية');
                    return false;
                }
            });

            // Auto-format currency input
            $('#OpeningCash').on('input', function() {
                var value = $(this).val();
                if (value && !isNaN(value)) {
                    $(this).val(parseFloat(value).toFixed(2));
                }
            });
        });
    </script>
} 