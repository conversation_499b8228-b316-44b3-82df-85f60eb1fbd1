﻿Imports System.Data.SqlClient
Public Class frmJETrx
    Public Recipient As String = ""
    Public TrxType As String = ""


    Private Sub frmJETrx_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        DebitAccountsLoad()
        CreditAccountsLoad()
        ForceGregorianForAllPickers(Me)

    End Sub

    Sub DebitAccountsLoad()
        Dim CMD As New SqlCommand("Select AccountNo,AccountDescription from AccountChart order by ParentID,AccountNo", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountDescription + ' - ' + CONVERT(AccountNo, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxDebitAccount.DataSource = dt
        cmbxDebitAccount.DisplayMember = "DisplayText" ' Show Name
        cmbxDebitAccount.ValueMember = "AccountNo" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Sub CreditAccountsLoad()
        Dim CMD As New SqlCommand("Select AccountNo,AccountDescription from AccountChart order by ParentID,AccountNo", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountDescription + ' - ' + CONVERT(AccountNo, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxCreditAccount.DataSource = dt
        cmbxCreditAccount.DisplayMember = "DisplayText" ' Show Name
        cmbxCreditAccount.ValueMember = "AccountNo" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    'Sub GLEntry()
    '    'Dim CreditString As String = ""
    '    TrxCredit = Val(cmbxCreditAccount.SelectedValue)
    '    TrxDebit = Val(cmbxDebitAccount.SelectedValue)
    '    '===========================================
    '    JEHeader()
    '    '===========================================
    '    ' Insert Trx
    '    If TrxCredit And TrxDebit <> 0 Then
    '        Dim DeleteCMD As New SQLCommand("Delete tblGLTrx where JESN = " & Val(JESN) & " ", Con)
    '        If Con.State <> ConnectionState.Open Then
    '            Con.Open()
    '        End If
    '        DeleteCMD.ExecuteNonQuery()
    '        If Con.State <> ConnectionState.Closed Then
    '            Con.Close()
    '        End If
    '        Dim InsertDebitCMD As New SqlCommand("Insert Into tblGLTrx (JESN,AccountNo,Amount,DRCR) Values (" & Val(JESN) & "," & Val(TrxDebit) & "," & Val(txtAmount.Text) & ",'DR')", Con)
    '        Dim InsertCreditCMD As New SqlCommand("Insert Into tblGLTrx (JESN,AccountNo,Amount,DRCR) Values (" & Val(JESN) & "," & Val(TrxCredit) & "," & Val(txtAmount.Text) & ",'CR')", Con)
    '        If Con.State <> ConnectionState.Open Then
    '            Con.Open()
    '        End If
    '        InsertDebitCMD.ExecuteNonQuery()
    '        InsertCreditCMD.ExecuteNonQuery()
    '        If Con.State <> ConnectionState.Closed Then
    '            Con.Close()
    '        End If
    '        MsgBox("تم حفظ السند بنجاح", MsgBoxStyle.Information, "نظام السلطان")
    '        'If CashPayPrint = "True" Then
    '        '    PrintType = "GLTrx"
    '        '    frmCashPrintPreview.MdiParent = frmMain
    '        '    frmCashPrintPreview.Show()
    '        '    JESN = 0
    '        'End If
    '        ClearFields()
    '    Else
    '        MsgBox("خطأ في اعدادات الدليل المحاسبي ... لن يتم انشاء القيد", MsgBoxStyle.Critical, "نظام السلطان")
    '    End If
    'End Sub
    Sub ClearFields()
        txtAmount.Clear()
        txtDescription.Clear()
        DebitAccountsLoad()
        CreditAccountsLoad()
        txtAmount.Focus()
    End Sub
    'Sub JEHeader()

    '    Dim InsertHeaderCMD As New SQLCommand("Insert Into tblJEHeader (TrxDate,Amount,Reference,ReferenceTrx,ReferenceType,TrxNote,PostStatus,CreatedBy,CreatedOn) values ('" & Format(dtp1.Value.Date, "yyyy/MM/dd") & "'," & Val(txtAmount.Text) & ",'بدون',0,'سند قيد','" & Trim(txtDescription.Text) & "','Open','" & UserName & "',getdate()) SELECT SCOPE_IDENTITY()", Con)
    '    If Con.State <> ConnectionState.Open Then
    '        Con.Open()
    '    End If
    '    JESN = InsertHeaderCMD.ExecuteScalar()
    '    If Con.State <> ConnectionState.Closed Then
    '        Con.Close()
    '    End If

    'End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Me.Close()
    End Sub

    Private Sub Button1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Button1.Click
        If Val(cmbxCreditAccount.SelectedValue) <> 0 And Val(cmbxDebitAccount.SelectedValue) <> 0 And Val(txtAmount.Text) <> 0 And txtDescription.Text.Trim <> "" Then
            'GLEntry()
            Try
                Using Con As New SqlConnection(Constr)
                    Using cmd As New SqlCommand("sp_CreateManualJEEntry", Con)
                        cmd.CommandType = CommandType.StoredProcedure
                        cmd.Parameters.AddWithValue("@TrxDate", dtp1.Value.Date)
                        cmd.Parameters.AddWithValue("@FromAccountCode", cmbxCreditAccount.SelectedValue)
                        cmd.Parameters.AddWithValue("@ToAccountCode", cmbxDebitAccount.SelectedValue)
                        cmd.Parameters.AddWithValue("@Amount", Val(txtAmount.Text))
                        cmd.Parameters.AddWithValue("@Notes", Trim(txtDescription.Text))
                        cmd.Parameters.AddWithValue("@CreatedBy", UserName)

                        If Con.State <> ConnectionState.Open Then
                            Con.Open()
                        End If
                        cmd.ExecuteNonQuery()
                        If Con.State <> ConnectionState.Closed Then
                            Con.Close()
                        End If
                    End Using
                End Using
            Catch ex As Exception
                MessageBox.Show("خطأ أثناء ترحيل القيد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Exit Sub
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End Try
            MsgBox("تم حفظ القيد بنجاح", MsgBoxStyle.Information, "نظام السلطان")
            ClearFields()
        Else
            MsgBox("البيانات غير مكتملة، لايمكن حفظ المستند", MsgBoxStyle.Critical, "نظام السلطان")
        End If

    End Sub

    Private Sub btnCreditSearch_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnCreditSearch.Click
        AccountSearchForm = "frmJETrx_Credit"
        frmAccountSearch.ShowDialog()
    End Sub

    Private Sub btnDebitSearch_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnDebitSearch.Click
        AccountSearchForm = "frmJETrx_Debit"
        frmAccountSearch.ShowDialog()
    End Sub

End Class
