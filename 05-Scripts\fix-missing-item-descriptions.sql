-- Fix missing item descriptions in tblItems table
-- This script will help identify and fix items with missing descriptions

-- First, let's see which items have missing descriptions
SELECT 
    'Items with Missing Descriptions' as Status,
    ItemNo,
    ItemDescription,
    ItemDescription2,
    CASE 
        WHEN (ItemDescription IS NULL OR LTRIM(RTRIM(ItemDescription)) = '') 
         AND (ItemDescription2 IS NULL OR LTRIM(RTRIM(ItemDescription2)) = '')
        THEN 'BOTH DESCRIPTIONS MISSING'
        WHEN ItemDescription IS NULL OR LTRIM(RTRIM(ItemDescription)) = ''
        THEN 'PRIMARY DESCRIPTION MISSING'
        WHEN ItemDescription2 IS NULL OR LTRIM(RTRIM(ItemDescription2)) = ''
        THEN 'SECONDARY DESCRIPTION MISSING'
        ELSE 'DESCRIPTIONS OK'
    END as DescriptionStatus,
    Barcode,
    Status
FROM tblItems 
WHERE (ItemDescription IS NULL OR LTRIM(RTRIM(ItemDescription)) = '') 
   OR (ItemDescription2 IS NULL OR LTRIM(RTRIM(ItemDescription2)) = '')
ORDER BY ItemNo;

-- Check specifically items used in invoice 89
SELECT 
    'Invoice 89 Items Status' as Status,
    i.ItemNo,
    i.ItemDescription,
    i.ItemDescription2,
    CASE 
        WHEN (i.ItemDescription IS NULL OR LTRIM(RTRIM(i.ItemDescription)) = '') 
         AND (i.ItemDescription2 IS NULL OR LTRIM(RTRIM(i.ItemDescription2)) = '')
        THEN 'BOTH DESCRIPTIONS MISSING'
        WHEN i.ItemDescription IS NULL OR LTRIM(RTRIM(i.ItemDescription)) = ''
        THEN 'PRIMARY DESCRIPTION MISSING'
        WHEN i.ItemDescription2 IS NULL OR LTRIM(RTRIM(i.ItemDescription2)) = ''
        THEN 'SECONDARY DESCRIPTION MISSING'
        ELSE 'DESCRIPTIONS OK'
    END as DescriptionStatus,
    sm.TrxQTY,
    sm.UnitPrice,
    sm.LineAmount
FROM tblItems i
INNER JOIN tblStockMovement sm ON i.ItemNo = sm.ItemNo
WHERE sm.DocNo = 89 AND sm.TrxType = 'مبيعات'
ORDER BY sm.LineSN;

-- Optional: Update items with missing descriptions to have a fallback description
-- Uncomment the following lines if you want to automatically fix missing descriptions

/*
UPDATE tblItems 
SET ItemDescription = 'صنف رقم ' + CAST(ItemNo AS VARCHAR(20))
WHERE (ItemDescription IS NULL OR LTRIM(RTRIM(ItemDescription)) = '') 
  AND (ItemDescription2 IS NULL OR LTRIM(RTRIM(ItemDescription2)) = '')
  AND ItemNo IN (
    SELECT DISTINCT ItemNo 
    FROM tblStockMovement 
    WHERE DocNo = 89 AND TrxType = 'مبيعات'
  );

-- Verify the update
SELECT 
    'After Update - Invoice 89 Items' as Status,
    i.ItemNo,
    i.ItemDescription,
    i.ItemDescription2
FROM tblItems i
WHERE i.ItemNo IN (
    SELECT DISTINCT ItemNo 
    FROM tblStockMovement 
    WHERE DocNo = 89 AND TrxType = 'مبيعات'
)
ORDER BY i.ItemNo;
*/
