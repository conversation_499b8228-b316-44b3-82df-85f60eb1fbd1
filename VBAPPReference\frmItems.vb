﻿Imports System.Data.SqlClient
Imports System.IO
Imports Microsoft.Win32
Imports Zen
Public Class frmItems
    Private Sub frmItems_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        ClearFields()
    End Sub

    ' Resizing Controls Dynamically
    Private Sub Form1_Resize(sender As Object, e As EventArgs)
        For Each ctrl As Control In Me.Controls
            ctrl.Font = New Font(ctrl.Font.FontFamily, Me.Width / 100)
        Next
    End Sub
    Private Sub FillComboBoxWithShops()
        Dim query As String = "select Shop_Text from tblShops order by Shop_Text"
        Dim Shop_Text As New List(Of String)()


        Using Con As New SqlConnection(ConStr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Shop_Text.Add(category)
                        End While
                        If reader.IsClosed <> True Then
                            reader.Close()
                        End If
                    End Using

                    cmbxShop.DataSource = Shop_Text
                    cmbxShop.SelectedIndex = -1

                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub
    Private Sub FillComboBoxWithUnits()
        Dim query As String = "SELECT UofM FROM tblUofM ORDER BY UofM"
        Dim units As New List(Of String)()

        Using Con As New SqlConnection(ConStr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim unit As String = reader("UofM").ToString()
                            units.Add(unit)
                        End While
                    End Using

                    ' Assign separate copies of the list to each ComboBox
                    cmbxUofM.DataSource = New List(Of String)(units)
                    cmbxAUofM.DataSource = New List(Of String)(units)
                    cmbxAUofM2.DataSource = New List(Of String)(units)
                    cmbxAUofM3.DataSource = New List(Of String)(units)
                    cmbxSalesUofM.DataSource = New List(Of String)(units)

                Catch ex As Exception
                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub




    Private Sub FillComboBoxWithItemTypes()
        Dim query As String = "SELECT RootID,RootName FROM tblItemsCategory where ParentID = 0 ORDER BY RootName"
        Dim categories As New List(Of String)()


        Using Con As New SqlConnection(ConStr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader("RootName").ToString()
                            categories.Add(category)
                        End While
                    End Using

                    cmbxItemType.DataSource = categories


                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub
    Private Sub FillComboBoxWithCategories()
        'Dim query As String = "SELECT RootName FROM tblItemsCategory where ParentID = 1 ORDER BY RootName"
        'Dim categories As New List(Of String)()


        'Using Con As New SqlConnection(ConStr)
        '    Using command As New SqlCommand(query, Con)
        '        Try
        '            Con.Open()
        '            Using reader As SqlDataReader = command.ExecuteReader()
        '                While reader.Read()
        '                    Dim category As String = reader("RootName").ToString()
        '                    categories.Add(category)
        '                End While
        '            End Using

        '            cmbxCategory.DataSource = categories


        '        Catch ex As Exception

        '            MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
        '        End Try
        '    End Using
        'End Using
        Dim dtable As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT RootID,RootName FROM tblItemsCategory where ParentID = 1 ORDER BY RootName", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dtable)
        End Using
        cmbxCategory.DataSource = dtable
        cmbxCategory.DisplayMember = "RootName"
        cmbxCategory.ValueMember = "RootID"
    End Sub


    Private Sub FillComboBoxWithTaxes()
        Dim query As String = "select Tax_Percent from tblTax order by Tax_Percent desc"
        Dim RequiredList As New List(Of String)()


        Using Con As New SqlConnection(ConStr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader("Tax_Percent").ToString()
                            RequiredList.Add(category)
                        End While
                    End Using

                    cmbxTax.DataSource = RequiredList


                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub

    Private Sub FillComboBoxWithBrands()
        Dim query As String = "select Brand_Text from tblbrands order by Brand_Text"
        Dim RequiredList As New List(Of String)()


        Using Con As New SqlConnection(ConStr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader("Brand_Text").ToString()
                            RequiredList.Add(category)
                        End While
                    End Using

                    cmbxBrand.DataSource = RequiredList


                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub

    ' Call this method in your form load or initialization



    Sub ItemsLoad()
        Dim ds As DataSet = New DataSet
        Dim SearchString As String = ""
        SearchString = "Select ItemNo as [رقم الصنف],ItemDescription as [وصف الصنف],UofM as [الوحدة],UnitSalesPrice as [سعر بيع الوحدة],Barcode as [باركود] from tblItems order by ItemNo"
        Dim SearchCMD As New SqlCommand()
        SearchCMD.Connection = Con
        SearchCMD.CommandText = SearchString
        Dim da As New SqlDataAdapter(SearchCMD)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub
    Sub FillComboBox()
        FillComboBoxWithUnits()
        FillComboBoxWithCategories()
        FillComboBoxWithItemTypes()
        FillComboBoxWithTaxes()
        FillComboBoxWithBrands()
        FillComboBoxWithShops()
    End Sub

    Sub CheckItemAvailable()
        If Val(txtItemNo.Text) = 0 Then
            Dim CheckCMD As New SqlCommand("Select * from tblItems where ItemNo = '" & txtItemNo.Text.Trim & "'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CheckCMD.ExecuteReader
            If reader.Read Then
                MsgBox("هذا الصنف موجود مسبقا، لا يمكن الحفظ", MsgBoxStyle.Critical, "برنامج السلطان")
                reader.Close()
            End If
        End If

    End Sub


    Sub ClearFields()
        ItemsLoad()
        FillComboBox()
        txtItemNo.Text = ""
        txtItemDescription.Clear()
        txtItemDescription2.Clear()
        cmbxUofM.Text = ""
        cmbxAUofM.Text = ""
        cmbxAUofM2.Text = ""
        cmbxAUofM3.Text = ""
        txtUnitSalesPrice.Clear()
        txtUnitPurchasePrice.Clear()
        txtBarcode.Clear()
        ckbxEnableSN.Checked = False
        ckbxNegativeEnable.Checked = False
        ckbxInactive.Checked = False
        cmbxCategory.Text = ""
        cmbxItemType.Text = ""
        txtAUofMX.Clear()
        txtAUofMX2.Clear()
        txtAUofMX3.Clear()
        txtNotes.Clear()
        cmbxBrand.Text = ""
        cmbxSalesUofM.Text = ""
        cmbxTax.Text = ""
        txtPhoto.Clear()
        PictureBox1.Image = Nothing
        cmbxShop.Text = ""
        txtSalesPriceAUofM.Clear()
        txtSalesPriceAUofM2.Clear()
        txtSalesPriceAUofM3.Clear()
    End Sub
    Sub GetSerial()
        Dim query As String = "SELECT COALESCE(MAX(ItemNo), 0) + 1 AS ItemNo FROM tblItems"
        Using SelectCMD As New SqlCommand(query, Con)
            Try
                Con.Open()
                Dim result As Object = SelectCMD.ExecuteScalar()
                NewSerialItemNo = Convert.ToInt32(result)
            Catch ex As Exception
            End Try
        End Using

    End Sub




    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Integer.TryParse(txtItemNo.Text.Trim, ItemNo)
        Using Con As New SqlConnection(ConStr)
            Dim query As String
            If ItemNo <> 0 Then
                'query = "UPDATE tblItems SET ItemDescription = @ItemDescription,ItemDescription2=@ItemDescription2,Barcode=@Barcode,ItemCategory=@ItemCategory,SNEnable=@SNEnable,NegativeStock=@NegativeStock,UnitPurchasePrice=@UnitPurchasePrice,AUofM=@AUofM,AUofMX=@AUofMX,AUofM2=@AUofM2,AUofMX2=@AUofMX2,AUofM3=@AUofM3,AUofMX3=@AUofMX3,ItemType=@ItemType,UnitSalesPrice = @UnitSalesPrice, UofM = @UofM,Tax_Percent=@Tax_Percent,Brand=@Brand,Status=@Status,Item_Photo=@Item_Photo,Notes=@Notes,SalesUofM=@SalesUofM, ModifiedBy = @ModifiedBy, ModifiedOn = @ModifiedOn WHERE ItemNo = @ItemNo"
                query = "UPDATE tblItems SET ItemDescription = @ItemDescription, ItemDescription2=@ItemDescription2,Barcode=@Barcode,ItemCategory=@ItemCategory,SNEnable=@SNEnable,NegativeStock=@NegativeStock,UnitPurchasePrice=@UnitPurchasePrice,AUofM=@AUofM,AUofMX=@AUofMX,AUofM2=@AUofM2,AUofMX2=@AUofMX2,AUofM3=@AUofM3,AUofMX3=@AUofMX3,ItemType=@ItemType,UnitSalesPrice = @UnitSalesPrice, UofM = @UofM, Tax_Percent=@Tax_Percent, Brand=@Brand,Status=@Status,Notes=@Notes,SalesUofM=@SalesUofM,ModifiedBy=@ModifiedBy,ModifiedOn=@ModifiedOn,Shop=@Shop, AUofM_Price=@AUofM_Price, AUofM2_Price=@AUofM2_Price, AUofM3_Price=@AUofM3_Price"

                ' Only update the photo if a new one is selected
                If Not String.IsNullOrWhiteSpace(txtPhoto.Text) Then
                    query &= ", Item_Photo=@Item_Photo"
                End If

                query &= " WHERE ItemNo = @ItemNo"
            Else
                GetSerial()
                ItemNo = NewSerialItemNo
                query = "INSERT INTO tblItems (ItemNo, ItemDescription, ItemDescription2, Barcode, ItemCategory, SNEnable, NegativeStock, UnitSalesPrice, UnitPurchasePrice, ItemType, AUofM, AUofMX, AUofM2, AUofMX2, AUofM3, AUofMX3, UofM, Tax_Percent, Brand, Status, Item_Photo, Notes, SalesUofM, CreatedBy, CreatedOn, Shop, AUofM_Price, AUofM2_Price, AUofM3_Price) VALUES (@ItemNo, @ItemDescription, @ItemDescription2, @Barcode, @ItemCategory, @SNEnable, @NegativeStock, @UnitSalesPrice, @UnitPurchasePrice, @ItemType, @AUofM, @AUofMX, @AUofM2, @AUofMX2, @AUofM3, @AUofMX3, @UofM, @Tax_Percent, @Brand, @Status, @Item_Photo, @Notes, @SalesUofM, @CreatedBy, @CreatedOn, @Shop, @AUofM_Price, @AUofM2_Price, @AUofM3_Price)"
            End If
            Using command As New SqlCommand(query, Con)
                command.Parameters.AddWithValue("@ItemNo", ItemNo)
                command.Parameters.AddWithValue("@ItemDescription", txtItemDescription.Text.Trim())
                command.Parameters.AddWithValue("@ItemDescription2", txtItemDescription2.Text.Trim())

                command.Parameters.AddWithValue("@Barcode", If(String.IsNullOrWhiteSpace(txtBarcode.Text), DBNull.Value, txtBarcode.Text.Trim()))
                command.Parameters.AddWithValue("@ItemCategory", cmbxCategory.SelectedValue())

                ' Convert Boolean (Checked) to Integer (0 or 1)
                command.Parameters.AddWithValue("@SNEnable", Convert.ToInt32(ckbxEnableSN.Checked))
                command.Parameters.AddWithValue("@NegativeStock", Convert.ToInt32(ckbxNegativeEnable.Checked))
                command.Parameters.AddWithValue("@Status", Convert.ToInt32(ckbxInactive.Checked))

                ' Convert Text to Decimal Safely
                command.Parameters.AddWithValue("@UnitSalesPrice", If(String.IsNullOrWhiteSpace(txtUnitSalesPrice.Text), DBNull.Value, Convert.ToDecimal(txtUnitSalesPrice.Text)))
                command.Parameters.AddWithValue("@UnitPurchasePrice", If(String.IsNullOrWhiteSpace(txtUnitPurchasePrice.Text), DBNull.Value, Convert.ToDecimal(txtUnitPurchasePrice.Text)))

                command.Parameters.AddWithValue("@AUofM_Price", If(String.IsNullOrWhiteSpace(txtSalesPriceAUofM.Text), DBNull.Value, Convert.ToDecimal(txtSalesPriceAUofM.Text)))
                command.Parameters.AddWithValue("@AUofM2_Price", If(String.IsNullOrWhiteSpace(txtSalesPriceAUofM2.Text), DBNull.Value, Convert.ToDecimal(txtSalesPriceAUofM2.Text)))
                command.Parameters.AddWithValue("@AUofM3_Price", If(String.IsNullOrWhiteSpace(txtSalesPriceAUofM3.Text), DBNull.Value, Convert.ToDecimal(txtSalesPriceAUofM3.Text)))





                command.Parameters.AddWithValue("@ItemType", cmbxItemType.Text.Trim())

                command.Parameters.AddWithValue("@AUofM", cmbxAUofM.Text.Trim())
                command.Parameters.AddWithValue("@AUofMX", If(String.IsNullOrWhiteSpace(txtAUofMX.Text), DBNull.Value, Convert.ToDecimal(txtAUofMX.Text)))

                command.Parameters.AddWithValue("@AUofM2", cmbxAUofM2.Text.Trim())
                command.Parameters.AddWithValue("@AUofMX2", If(String.IsNullOrWhiteSpace(txtAUofMX2.Text), DBNull.Value, Convert.ToDecimal(txtAUofMX2.Text)))

                command.Parameters.AddWithValue("@AUofM3", cmbxAUofM3.Text.Trim())
                command.Parameters.AddWithValue("@AUofMX3", If(String.IsNullOrWhiteSpace(txtAUofMX3.Text), DBNull.Value, Convert.ToDecimal(txtAUofMX3.Text)))

                command.Parameters.AddWithValue("@UofM", cmbxUofM.Text.Trim())
                command.Parameters.AddWithValue("@Brand", cmbxBrand.Text.Trim())
                command.Parameters.AddWithValue("@Tax_Percent", If(String.IsNullOrWhiteSpace(cmbxTax.Text), DBNull.Value, Convert.ToDecimal(cmbxTax.Text)))

                command.Parameters.AddWithValue("@SalesUofM", cmbxSalesUofM.Text.Trim())
                command.Parameters.AddWithValue("@Notes", txtNotes.Text.Trim())
                command.Parameters.AddWithValue("@Shop", cmbxShop.Text.Trim())

                ' User and DateTime Fields
                command.Parameters.AddWithValue("@CreatedBy", UserName)
                command.Parameters.AddWithValue("@ModifiedBy", UserName)
                command.Parameters.AddWithValue("@CreatedOn", DateTime.Now)
                command.Parameters.AddWithValue("@ModifiedOn", DateTime.Now)

                Dim ImageToSave As String = txtPhoto.Text.Trim
                If String.IsNullOrWhiteSpace(ImageToSave) Then
                    command.Parameters.Add("@Item_Photo", SqlDbType.VarBinary).Value = DBNull.Value
                Else
                    Dim fs As FileStream = New FileStream(ImageToSave, FileMode.Open, FileAccess.Read)
                    Dim r As BinaryReader = New BinaryReader(fs)
                    Dim FileByteArray(fs.Length - 1) As Byte
                    r.Read(FileByteArray, 0, CInt(fs.Length))
                    fs.Close()
                    command.Parameters.Add("@Item_Photo", SqlDbType.VarBinary).Value = FileByteArray
                End If
                ' OpenConnection()
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                command.ExecuteNonQuery()
            End Using
            MessageBox.Show("تم حفظ بيانات الصنف بنجاح", "برنامج السلطان")
            CloseConnection()
        End Using
        ClearFields()

    End Sub

    Private Sub btnDelete_Click_1(sender As Object, e As EventArgs) Handles btnDelete.Click
        If txtItemNo.Text.Trim <> "" Then
            Dim detectCMD As New SqlCommand("Select * from tblStockMovement where ItemNo = " & Val(txtItemNo.Text) & "", Con)
            OpenConnection()
            Dim reader As SqlDataReader = detectCMD.ExecuteReader
            If reader.Read Then
                MsgBox("لايمكن حذف الصنف لوجود فواتير مدخلة", MsgBoxStyle.Critical, "برنامج السلطان")
                CloseConnection()
                'CloseReader()
                reader.Close()
                Return
            Else
                If MsgBox("هل تريد بالتأكيد حذف الصنف ؟", MsgBoxStyle.YesNo, "برنامج السلطان") = MsgBoxResult.Yes Then
                    Dim DeleteCMD As New SqlCommand("Delete from tblItems where ItemNo = " & Val(txtItemNo.Text) & " ", Con)
                    OpenConnection()
                    DeleteCMD.ExecuteNonQuery()
                    MessageBox.Show("تم حذف الصنف بنجاح", "برنامج السلطان")
                    CloseConnection()
                    ClearFields()

                End If
            End If
            CloseConnection()
        End If
    End Sub

    Private Sub DataGridView1_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If DataGridView1.SelectedRows.Count = 1 Then
            If Val(DataGridView1.SelectedRows(0).Cells(0).Value) <> 0 Then
                txtItemNo.Text = DataGridView1.SelectedRows(0).Cells(0).Value
            End If
        End If
    End Sub

    Private Sub txtItemNo_TextChanged(sender As Object, e As EventArgs) Handles txtItemNo.TextChanged
        Dim sn As Integer
        If Not Integer.TryParse(txtItemNo.Text, sn) Then

            txtItemDescription.Text = ""
            txtUnitSalesPrice.Text = ""
            cmbxUofM.Text = ""
            Return
        End If

        ' Use parameterized queries to prevent SQL injection
        Dim query As String = "SELECT ItemDescription,ItemDescription2,Barcode,ItemCategory,SNEnable,NegativeStock,UnitSalesPrice,UnitPurchasePrice,ItemType,AUofM,AUofMX,AUofM2,AUofMX2,AUofM3,AUofMX3, UofM,Tax_Percent,Brand,Status,Item_Photo,Notes,SalesUofM,Shop,AUofM_Price,AUofM2_Price,AUofM3_Price FROM tblItems WHERE ItemNo = @ItemNo"


        ' Utilize Using blocks for resource management
        Using Con As New SqlConnection(ConStr),
              SearchCMD As New SqlCommand(query, Con)

            ' Add the parameter to the command
            SearchCMD.Parameters.AddWithValue("@ItemNo", sn)

            Try
                Con.Open()
                Using reader As SqlDataReader = SearchCMD.ExecuteReader()
                    If reader.Read() Then
                        txtItemDescription.Text = Convert.ToString(reader.Item(0)).Trim()
                        txtItemDescription2.Text = Convert.ToString(reader.Item(1)).Trim()
                        txtBarcode.Text = Convert.ToString(reader.Item(2)).Trim()

                        ' ✅ FIXED: Category is a string, not a Boolean
                        cmbxCategory.SelectedValue = If(IsDBNull(reader.Item(3)), False, Convert.ToInt32(reader.Item(3)))

                        ' ✅ FIXED: Handle TINYINT to Boolean conversion safely
                        ckbxEnableSN.Checked = If(IsDBNull(reader.Item(4)), False, Convert.ToInt32(reader.Item(4)) = 1)
                        ckbxNegativeEnable.Checked = If(IsDBNull(reader.Item(5)), False, Convert.ToInt32(reader.Item(5)) = 1)

                        ' ✅ FIXED: Decimal conversion with proper formatting
                        txtUnitSalesPrice.Text = If(IsDBNull(reader.Item(6)), "0.00", Convert.ToDecimal(reader.Item(6)).ToString("F2"))
                        txtUnitPurchasePrice.Text = If(IsDBNull(reader.Item(7)), "0.00", Convert.ToDecimal(reader.Item(7)).ToString("F2"))

                        cmbxItemType.Text = Convert.ToString(reader.Item(8)).Trim()



                        cmbxAUofM.Text = Convert.ToString(reader.Item(9)).Trim()
                        txtAUofMX.Text = Convert.ToString(reader.Item(10)).Trim()

                        cmbxAUofM2.Text = Convert.ToString(reader.Item(11)).Trim()
                        txtAUofMX2.Text = Convert.ToString(reader.Item(12)).Trim()

                        cmbxAUofM3.Text = Convert.ToString(reader.Item(13)).Trim()
                        txtAUofMX3.Text = Convert.ToString(reader.Item(14)).Trim()

                        cmbxUofM.Text = Convert.ToString(reader.Item(15)).Trim()
                        cmbxTax.Text = If(IsDBNull(reader.Item(16)), "0.00", Convert.ToDecimal(reader.Item(16)).ToString("F2"))
                        cmbxBrand.Text = Convert.ToString(reader.Item(17)).Trim()
                        ckbxInactive.Checked = If(IsDBNull(reader.Item(18)), False, Convert.ToInt32(reader.Item(18)) = 1)

                        Try
                            Dim pictureData As Byte() = DirectCast(reader.Item(19), Byte())
                            Dim stream As New IO.MemoryStream(pictureData)
                            Me.PictureBox1.Image = Image.FromStream(stream)
                            stream.Dispose()
                        Catch ex As Exception
                            Me.PictureBox1.Image = Nothing
                        End Try

                        txtNotes.Text = Convert.ToString(reader.Item(20)).Trim()
                        cmbxSalesUofM.Text = Convert.ToString(reader.Item(21)).Trim()
                        cmbxShop.Text = Convert.ToString(reader.Item(22)).Trim()

                        txtSalesPriceAUofM.Text = If(IsDBNull(reader.Item(23)), "0.00", Convert.ToDecimal(reader.Item(23)).ToString("F2"))
                        txtSalesPriceAUofM2.Text = If(IsDBNull(reader.Item(24)), "0.00", Convert.ToDecimal(reader.Item(24)).ToString("F2"))
                        txtSalesPriceAUofM3.Text = If(IsDBNull(reader.Item(25)), "0.00", Convert.ToDecimal(reader.Item(25)).ToString("F2"))


                    Else
                        ' Clear the fields if no data is found
                        txtItemDescription.Clear()
                        txtItemDescription2.Clear()
                        txtBarcode.Clear()
                        cmbxCategory.SelectedIndex = -1
                        ckbxEnableSN.Checked = False
                        ckbxNegativeEnable.Checked = False
                        txtUnitSalesPrice.Text = "0.00"
                        txtSalesPriceAUofM.Text = "0.00"
                        txtSalesPriceAUofM2.Text = "0.00"
                        txtSalesPriceAUofM3.Text = "0.00"
                        txtUnitPurchasePrice.Text = "0.00"
                        cmbxAUofM.SelectedIndex = -1
                        txtAUofMX.Clear()
                        cmbxAUofM2.SelectedIndex = -1
                        txtAUofMX2.Clear()
                        cmbxItemType.SelectedIndex = -1
                        cmbxAUofM3.SelectedIndex = -1
                        txtAUofMX3.Clear()
                        cmbxUofM.SelectedIndex = -1
                        Me.PictureBox1.Image = Nothing
                        cmbxTax.SelectedIndex = -1
                        cmbxBrand.SelectedIndex = -1
                        ckbxInactive.Checked = False
                        txtNotes.Clear()
                        cmbxSalesUofM.SelectedIndex = -1
                        cmbxShop.SelectedIndex = -1
                    End If

                End Using
            Catch ex As Exception

                MessageBox.Show("An error occurred: " & ex.Message)

            End Try
        End Using
    End Sub

    Private Sub btnPhotoDelete_Click(sender As Object, e As EventArgs) Handles btnPhotoDelete.Click
        If MsgBox("هل أنت متأكد من رغبتك حذف الصورة ؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
            Me.PictureBox1.Image = Nothing
            txtPhoto.Clear()
        End If
    End Sub

    Private Sub btnPhotoSelect_Click(sender As Object, e As EventArgs) Handles btnPhotoSelect.Click
        Dim ofdRestore As New OpenFileDialog
        Try
            OpenFileDialog1.InitialDirectory = "c:\"
            ofdRestore.Filter = "JPEG files (*.jpg)|*.jpg|GIF files (*.gif)|*.gif|All files (*.*)|*.*"
            ofdRestore.ShowDialog()
            If ofdRestore.FileName <> "" Then
                txtPhoto.Text = ofdRestore.FileName
                Dim pictureData As Byte() = File.ReadAllBytes(txtPhoto.Text)
                Dim stream As New IO.MemoryStream(pictureData)
                Me.PictureBox1.Image = Image.FromStream(stream)
                stream.Dispose()
            End If

        Catch ex As Exception
            PictureBox1.Image = Nothing
            MsgBox("لم يتم تحميل الصورة ", MsgBoxStyle.Critical, "النظام")
        End Try
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        SearchBox()
    End Sub

    Sub SearchBox()
        Dim keyword As String = txtSearch.Text.Trim()
        If keyword = "" Then
            ItemsLoad()
            Return
        End If

        Dim ds As DataSet = New DataSet
        Dim cmd As New SqlClient.SqlCommand("
        SELECT ItemNo as [رقم الصنف],ItemDescription as [وصف الصنف],UofM as [الوحدة],UnitSalesPrice as [سعر بيع الوحدة],Barcode as [باركود]
        FROM tblItems 
        WHERE ItemDescription LIKE @kw OR CAST(ItemNo AS NVARCHAR) LIKE @kw or Barcode LIKE @kw", New SqlClient.SqlConnection(ConStr))
        cmd.Parameters.AddWithValue("@kw", "%" & keyword & "%")
        Dim da As New SqlClient.SqlDataAdapter(cmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub
    Private Sub txtSearch_KeyDown(sender As Object, e As KeyEventArgs) Handles txtSearch.KeyDown
        If e.KeyCode = Keys.Enter Then
            SearchBox()
            e.Handled = True
        End If
    End Sub


End Class