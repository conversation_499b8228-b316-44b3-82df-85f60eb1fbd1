﻿Imports System.Data.SqlClient
Imports System.Globalization
Imports System.Text
Imports QRCoder

Public Class frmSalesInvoiceTrx
    Dim EnteredBefore As Int64 = 0
    Dim Cr As Int64
    Dim LableCode As Int64
    Dim txtPrice As Int64
    Dim QTY As Int64
    'Dim TrxNo As Int64
    Dim ForClose As Integer = 0
    Dim ForItemChange As Integer = 0
    Dim flag_cell_edited As Boolean
    Dim currentRow As Integer
    Dim currentColumn As Integer
    Dim PaymentStatus As String = ""
    Dim ItemFailed As String = ""
    Dim x As Int64 = 0
    Dim PrintCopies, MaterialAccount, DiscountAccount As Int64
    Dim PrintOption, ReferenceMandatory, DefaultPrinter, MandatoryCustomerVATReg, PriceIncludeVATDef, NonVATInvoiceDef, PaymentType As String


    Private Sub frmSalesInvoiceTrx_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        InvNo = Val(txtInvoiceNo.Text)
        If InvNo <> 0 Then
            Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set ReadyForUse = 'True',TrxVAT=0, TrxTotal=0, TrxDiscount=0, TrxDiscountValue=0 where TrxNo = " & Val(InvNo) & " and  TrxType ='مبيعات'", Con)

            Con.Close()
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            Dim DeleteCMD As New SqlCommand("Delete tblStockMovement where TrxType = 'مبيعات' and DocNo = " & Val(InvNo) & "", Con)
            Dim Delete2CMD As New SqlCommand("Update tblStockTrx set OutInvoiceType= NULL, OutInvoiceNo= NULL,OutInvoiceItemSN = NULL, OutCreatedBy = NULL,OutCreatedOn = NULL,UnitPrice=0,InStock = 1 where OutInvoiceType= 'مبيعات' and OutInvoiceNo = " & Val(InvNo) & " and InStock = 0", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            DeleteCMD.ExecuteNonQuery()
            Delete2CMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End If
    End Sub
    Private Sub frmTrxInvIn_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        InvNo = 0
        CustomerLoad()
        StoresLoad()
        CashierLoad()
        FillComboBoxWithEmployees()
        LoadSettings()
        GetSerial()
        GetLineSerial()
        LoadUserAuthorization(UserName) ' Pass the logged-in username
        '   ForceGregorianCalendar(dtp1)
        ForceGregorianForAllPickers(Me)

    End Sub
    Private Sub FillComboBoxWithEmployees()
        Dim CMD As New SqlCommand("Select EmployeeNo,Emp_Name from tblEmployees order by EmployeeNo", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "Emp_Name + ' - ' + CONVERT(EmployeeNo, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxEmployee.DataSource = dt
        cmbxEmployee.DisplayMember = "DisplayText" ' Show Name
        cmbxEmployee.ValueMember = "EmployeeNo" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        cmbxEmployee.Text = ""
    End Sub
    Sub GetSerial()
        Try
            Dim PreUsedCMD As New SqlCommand("Select (Min(TrxNo)) as SN from tblStockMovHeader where ReadyForUse = 'True' and TrxType = 'مبيعات'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = PreUsedCMD.ExecuteReader()
            If reader.Read Then
                InvNo = Val(reader.Item(0).ToString)
            Else
                InvNo = 0
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If InvNo = 0 Then
                Dim SelectCMD As New SqlCommand("Select (Max(TrxNo)) + 1 as SN from tblStockMovHeader where TrxType = 'مبيعات'", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                Dim reader2 As SqlDataReader = SelectCMD.ExecuteReader()
                If reader2.Read Then
                    If reader2.Item("SN").ToString <> "" Then
                        InvNo = Val(reader2.Item("SN").ToString)
                    Else
                        InvNo = 1
                    End If
                End If
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                Dim InsertCMD As New SqlCommand("Insert into tblStockMovHeader (TrxType,TrxNo,ReadyForUse,CreatedBy,CreatedOn) values ('مبيعات'," & Val(InvNo) & ",'False','" & UserName & "',GetDate())", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                InsertCMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            Else
                Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set ReadyForUse = 'False' where TrxNo = " & Val(InvNo) & " and TrxType = 'مبيعات' ", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                UpdateCMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If
            txtInvoiceNo.Text = InvNo
        Catch ex As Exception

        End Try
    End Sub
    Sub GetLineSerial()
        Try
            Dim SelectCMD As New SqlCommand("Select Max(LineSN) + 1 as SN from tblStockMovement where TrxType = 'مبيعات' and DocNo = " & Val(txtInvoiceNo.Text) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                If reader.Item("SN").ToString <> "" Then
                    LineSN = Val(reader.Item("SN").ToString)
                Else
                    LineSN = 1
                End If
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            txtItemSN.Text = LineSN
        Catch ex As Exception

        End Try
    End Sub

    Sub LoadUserAuthorization(ByVal username As String)
        Try
            Dim query As String = "SELECT GroupID, DefaultStore, StoreChange, DefaultCustomer, CustomerChange, DefaultCashier, CashierChange, ChangeInvoicePrice, MaxDiscountPercent 
                               FROM tblUsers WHERE Username = @Username"

            Dim cmd As New SqlCommand(query, Con)
            cmd.Parameters.AddWithValue("@Username", username)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            Dim reader As SqlDataReader = cmd.ExecuteReader()

            If reader.Read() Then
                Dim isAdmin As Boolean = (Not IsDBNull(reader("GroupID")) AndAlso Convert.ToInt32(reader("GroupID")) = 1)

                ' If the user is in GroupID = 1 (Admin), grant full access
                If isAdmin Then
                    cmbxStore.Enabled = True
                    cmbxPartnerNo.Enabled = True
                    cmbxCashier.Enabled = True
                    txtUnitPrice.Enabled = True
                    MaxDiscountPercent = 99
                Else
                    ' Apply Default Store
                    If Not IsDBNull(reader("DefaultStore")) Then
                        cmbxStore.SelectedItem = reader("DefaultStore").ToString()
                    End If
                    cmbxStore.Enabled = Convert.ToBoolean(reader("StoreChange")) ' Enable if allowed

                    ' Apply Default Customer
                    If Not IsDBNull(reader("DefaultCustomer")) Then
                        cmbxPartnerNo.SelectedValue = Convert.ToInt32(reader("DefaultCustomer"))
                    End If
                    cmbxPartnerNo.Enabled = Convert.ToBoolean(reader("CustomerChange")) ' Enable if allowed

                    ' Apply Default Cashier
                    If Not IsDBNull(reader("DefaultCashier")) Then
                        cmbxCashier.SelectedValue = Convert.ToInt32(reader("DefaultCashier"))
                    End If
                    cmbxCashier.Enabled = Convert.ToBoolean(reader("CashierChange")) ' Enable if allowed

                    ' Apply Price Change Authorization
                    txtUnitPrice.Enabled = Convert.ToBoolean(reader("ChangeInvoicePrice")) ' Enable if allowed

                    If Not IsDBNull(reader("MaxDiscountPercent")) Then
                        MaxDiscountPercent = Convert.ToDecimal(reader("MaxDiscountPercent"))
                    Else
                        MaxDiscountPercent = 0
                    End If


                End If
            End If

            reader.Close()

            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If

        Catch ex As Exception
            MessageBox.Show("Error loading user authorization: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub


    Sub LoadSettings()
        Dim CMD As New SqlCommand("Select * from tblToolsInvoice where InvoiceType = 'مبيعات'", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        If reader.Read Then
            ckbxVATIncluded.Enabled = Trim(reader.Item(1).ToString)
            'ckbxNonVatInvoice.Enabled = Trim(reader.Item(2).ToString)
            PaymentType = Trim(reader.Item(10).ToString)
            Cashier = Val(reader.Item(11).ToString)
            AccountNo = Val(reader.Item(9).ToString)
            Store = Trim(reader.Item(12).ToString)
            PriceIncludeVATDef = Trim(reader.Item(13).ToString)
            NonVATInvoiceDef = Trim(reader.Item(14).ToString)
            MaterialAccount = Val(reader.Item(4).ToString)
            DiscountAccount = Val(reader.Item(5).ToString)
            MandatoryCustomerVATReg = Trim(reader.Item(8).ToString)
            DefaultPrinter = Trim(reader.Item(6).ToString)
            PrintOption = Trim(reader.Item(7).ToString)
            ReferenceMandatory = Trim(reader.Item(3).ToString)

        End If
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        cmbxPartnerNo.Text = AccountNo
        'cmbxStores.Text = Store
        'If Cashier <> 0 Then
        '    DisplayCashier()
        'End If
        cmbxPaymentType.Text = PaymentType
        'ckbxNonVatInvoice.Checked = NonVATInvoiceDef
        ckbxVATIncluded.Checked = PriceIncludeVATDef

    End Sub
    'Sub DisplayCashier()
    '    Dim CMD As New SQLCommand("Select RootName from tblRoots where RootID = " & Val(Cashier) & "", Con)

    '    If Con.State <> ConnectionState.Open Then
    '        Con.Open()
    '    End If
    '    dim reader As SqlDataReader cmd.ExecuteReader
    '    If reader.Read Then
    '        cmbxCashier.Text = Trim(reader.Item(0).ToString)
    '    End If
    '    If reader.IsClosed Then
    '    Else
    '        reader.Close()
    '    End If
    '    If Con.State <> ConnectionState.Closed Then
    '        Con.Close()
    '    End If
    'End Sub
    Private OriginalCustomerTable As DataTable
    Sub CustomerLoad()
        Dim CMD As New SqlCommand("  Select AccountCode,AccountName from tbl_Acc_Accounts where ParentAccountCode = (Select AccountNo from tblGLConfig where EntryReferenceModule = 'عملاء') order by AccountCode", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountName + ' - ' + CONVERT(AccountCode, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxPartnerNo.DataSource = dt
        cmbxPartnerNo.DisplayMember = "DisplayText" ' Show Name
        cmbxPartnerNo.ValueMember = "AccountCode" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub


    Private Sub cmbxPartnerNo_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbxPartnerNo.SelectedIndexChanged
        Try
            If cmbxPartnerNo.SelectedItem IsNot Nothing Then
                Dim drv As DataRowView = CType(cmbxPartnerNo.SelectedItem, DataRowView)
                txtPartnerName.Text = drv("AccountName").ToString()
                txtCustomerPhone.Clear()
            Else
                txtPartnerName.Clear()
            End If
        Catch ex As Exception
            txtPartnerName.Clear()
        End Try
    End Sub
    Sub CashierLoad()
        Dim CMD As New SqlCommand("SELECT AccountCode,AccountName from tbl_Acc_Accounts WHERE ParentAccountCode = (SELECT AccountNo FROM tblGLConfig WHERE EntryReferenceModule = 'نقدية') ORDER BY AccountName", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()

        dt.Load(reader)
        reader.Close()

        ' Bind the ComboBox to DataTable
        cmbxCashier.DataSource = dt
        cmbxCashier.DisplayMember = "AccountName" ' Show Name
        cmbxCashier.ValueMember = "AccountCode" ' Store ID

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If

    End Sub

    Sub CheckParentID()
        Try
            Dim SelectCMD As New SqlCommand("Select AccountNo from tblGLConfig where EntryReferenceModule = 'نقدية'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                ParentID = Val(reader.Item(0).ToString)
            Else
                ParentID = 0
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub

    Sub StoresLoad()
        Dim CMD As New SqlCommand("Select SN,Store from tblStores Order by Store", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxStore.Items.Clear()
        Do While reader.Read
            cmbxStore.Items.Add(reader.Item(1).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Sub UnitsLoad()
        Dim CMD As New SqlCommand("SELECT UofM, 1 AS BaseFactor, AUofM, AUofMX, AUofM2, AUofMX2, AUofM3, AUofMX3 
                               FROM tblItems WHERE ItemNo = @ItemNo", Con)

        CMD.Parameters.AddWithValue("@ItemNo", Val(txtItemNo.Text))

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim reader As SqlDataReader = CMD.ExecuteReader()

        ' Reset ComboBox before binding new data
        cmbxUnit.DataSource = Nothing
        cmbxUnit.Items.Clear()

        Dim units As New Dictionary(Of String, Decimal)

        If reader.Read() Then
            ' Add only non-empty values
            If Not IsDBNull(reader("UofM")) AndAlso reader("UofM").ToString().Trim() <> "" Then
                units.Add(reader("UofM").ToString(), 1) ' Base unit
            End If
            If Not IsDBNull(reader("AUofM")) AndAlso reader("AUofM").ToString().Trim() <> "" Then
                units.Add(reader("AUofM").ToString(), If(IsDBNull(reader("AUofMX")), 1, Convert.ToDecimal(reader("AUofMX"))))
            End If
            If Not IsDBNull(reader("AUofM2")) AndAlso reader("AUofM2").ToString().Trim() <> "" Then
                units.Add(reader("AUofM2").ToString(), If(IsDBNull(reader("AUofMX2")), 1, Convert.ToDecimal(reader("AUofMX2"))))
            End If
            If Not IsDBNull(reader("AUofM3")) AndAlso reader("AUofM3").ToString().Trim() <> "" Then
                units.Add(reader("AUofM3").ToString(), If(IsDBNull(reader("AUofMX3")), 1, Convert.ToDecimal(reader("AUofMX3"))))
            End If

            ' Bind to ComboBox
            Try
                cmbxUnit.DataSource = New BindingSource(units, Nothing)
                cmbxUnit.DisplayMember = "Key"  ' Show UofM
                cmbxUnit.ValueMember = "Value"  ' Store Conversion Factor
            Catch ex As Exception
                reader.Close()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End Try
        End If

        reader.Close()

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub



    Sub SaveTrx()
        If Val(txtInvoiceNo.Text) <> 0 And Val(lblInvoiceAmount.Text) <> 0 And Val(lblItemCount.Text) <> 0 Then
            If cmbxStore.Text.Trim = "" Then
                MsgBox("يجب إدخال المخزن للاستمرار", MsgBoxStyle.Critical, "نظام السلطان")
                Exit Sub
            End If
            InvNoForPrint = InvNo
            'If ForItemChange <> 1 Then
            '    CheckSNEntered()
            'End If
            'If SNMissed = "True" Then Exit Sub
            'If cmbxPaymentType.Text.Trim = "آجل" Then
            '    CashierLoad()
            'End If
            'Dim imageData As Byte() = ImageToByteArray(qrCodeImage)

            ' Create VAT QR code image

            sellername = StoreName
            vatregistration = VATReg
            timestamp = Format(DateAndTime.Now, "yyyy-MM-dd HH:mm:ss")
            invoiceamount = Val(lblInvoiceAmount.Text)
            vatAmount = Val(lblVATAmount.Text)


            Dim saudiConvertion = New SaudiConvertion()
            Dim QRString = saudiConvertion.GetData()
            Dim gen As New QRCodeGenerator
            Dim qrCodeData = gen.CreateQrCode(QRString, QRCodeGenerator.ECCLevel.Q)
            Dim code As New QRCode(qrCodeData)
            Dim qrCodeImage As Bitmap = New Bitmap(code.GetGraphic(20))
            Dim imageData As Byte() = ImageToByteArray(qrCodeImage)
            '=========================================================================



            Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set ReferenceInvoice='" & txtReferenceNo.Text.Trim & "',TrxDate='" & Format(dtp1.Value.Date, "yyyy/MM/dd hh:mm:ss tt") & "',PartnerNo=" & cmbxPartnerNo.SelectedValue & ",PartnerName='" & Trim(txtPartnerName.Text) & "',PartnerPhoneNo='" & Trim(txtCustomerPhone.Text) & "',PaymentMethod='" & Trim(cmbxPaymentType.Text) & "',PartnerReference='" & Trim(txtVendorInvNo.Text) & "',TrxNote = '" & Trim(txtNotes.Text) & "',TrxVAT = " & Val(lblVATAmount.Text) & ",TrxTotal=" & Val(lblTotal.Text) & ",TrxDiscount =" & Val(txtDiscount.Text) & " ,TrxDiscountValue = " & Val(lblDiscountAmount.Text) & ",TrxNetAmount = " & Val(lblInvoiceAmount.Text) & ", EmployeeName='" & cmbxEmployee.Text.Trim & "' ,ModifiedBy='" & UserName & "',ModifiedOn='" & Format(DateAndTime.Now, "yyyy/MM/dd hh:mm:ss tt") & "',VOIDSTTS=0,ReadyForUse='False',QRCodeImage = @QRCodeImage,Store= '" & cmbxStore.Text.Trim & "',Cashier = " & cmbxCashier.SelectedValue & ",PaymentStatus = '" & PaymentStatus & "' where TrxNo = " & Val(txtInvoiceNo.Text) & " and TrxType='مبيعات'", Con)
            UpdateCMD.Parameters.AddWithValue("@QRCodeImage", imageData)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If ForItemChange = 0 Then
                'CreateJEHeaderAndEntry(Val(txtInvoiceNo.Text), Val(lblTotal.Text))


                Dim netAmount As Decimal = Convert.ToDecimal(lblInvoiceAmount.Text)
                Dim discountAmount As Decimal = Convert.ToDecimal(lblDiscountAmount.Text)

                Call UpdateTotals()
                Dim grossSalesBeforeDiscount As Decimal = GetGrossAmountBeforeDiscount(DGV1, PriceIncludeVATDef)


            End If
            'InvNo = Val(txtInvoiceNo.Text)
            If ForClose = 1 Then
                Me.Close()
            ElseIf ForItemChange <> 1 Then
                Try
                    Using Con As New SqlConnection(Constr)
                        Using cmd As New SqlCommand("sp_CreateSalesInvoiceEntryFromView", Con)
                            cmd.CommandType = CommandType.StoredProcedure
                            cmd.Parameters.AddWithValue("@InvoiceNo", InvNo)

                            If Con.State <> ConnectionState.Open Then
                                Con.Open()
                            End If
                            cmd.ExecuteNonQuery()
                            If Con.State <> ConnectionState.Closed Then
                                Con.Close()
                            End If
                        End Using
                    End Using
                Catch ex As Exception
                    MessageBox.Show("خطأ أثناء ترحيل الفاتورة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                End Try
                Try
                    Using Con As New SqlConnection(Constr)
                        Using cmd As New SqlCommand("sp_CreatePaymentEntryFromView", Con)
                            cmd.CommandType = CommandType.StoredProcedure
                            cmd.Parameters.AddWithValue("@InvoiceNo", InvNo)

                            If Con.State <> ConnectionState.Open Then
                                Con.Open()
                            End If
                            cmd.ExecuteNonQuery()
                            If Con.State <> ConnectionState.Closed Then
                                Con.Close()
                            End If
                        End Using
                    End Using
                Catch ex As Exception
                    MessageBox.Show("خطأ أثناء ترحيل دفعة الفاتورة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                End Try

                Try
                    Using Con As New SqlConnection(Constr)
                        Using cmd As New SqlCommand("sp_CreateCOGSEntryFromView", Con)
                            cmd.CommandType = CommandType.StoredProcedure
                            cmd.Parameters.AddWithValue("@InvoiceNo", InvNo)

                            If Con.State <> ConnectionState.Open Then
                                Con.Open()
                            End If
                            cmd.ExecuteNonQuery()
                            If Con.State <> ConnectionState.Closed Then
                                Con.Close()
                            End If
                        End Using
                    End Using
                Catch ex As Exception
                    MessageBox.Show("خطأ أثناء ترحيل تكلفة البيع: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                End Try
                '


                MsgBox("تم حفظ الفاتورة بنجاح", MsgBoxStyle.Information, "نظام السلطان")
                PrintType = "SalesInvoice"
                InvoiceType = "مبيعات"
                LoadSettings()
                ClearFields()
                If PrintOption = 0 Then
                    'frmPrintPreview.MdiParent = frmMain
                    'frmPrintPreview.Show()
                    frmPrintPreview.ShowDialog()
                ElseIf PrintOption = 1 Then
                    If MsgBox("هل ترغب في طباعة الفاتورة؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
                        'frmPrintPreview.MdiParent = frmMain
                        'frmPrintPreview.Show()
                        frmPrintPreview.ShowDialog()
                    End If
                End If
            End If

        End If
    End Sub
    Private Sub txtCustomerPhone_KeyDown(sender As Object, e As KeyEventArgs) Handles txtCustomerPhone.KeyDown
        If e.KeyCode = Keys.Enter Then
            If txtCustomerPhone.TextLength = 10 Then
                Try
                    Dim cmd As New SqlCommand("Select TOP 1 PartnerName from tblStockMovHeader where PartnerPhoneNo = @PartnerPhoneNo and TrxType = 'مبيعات' order by TrxNo desc", Con)
                    cmd.Parameters.AddWithValue("@PartnerPhoneNo", txtCustomerPhone.Text)
                    OpenConnection()
                    Dim rdr = cmd.ExecuteReader()
                    If rdr.Read() Then
                        txtPartnerName.Text = rdr("PartnerName").ToString()
                    Else
                        txtPartnerName.Clear()
                    End If
                    rdr.Close()
                    CloseConnection()
                Catch ex As Exception

                End Try

            Else
                MessageBox.Show("تحقق من رقم الجوال", "خطأ في رقم الجوال", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        End If
    End Sub

    'Private Function LoadGLAccounts() As Dictionary(Of String, String)
    '    Dim map As New Dictionary(Of String, String)
    '    Dim cmd As New SqlCommand("SELECT EntryReferenceModule, AccountNo FROM tblGLConfig", Con)
    '    If Con.State <> ConnectionState.Open Then
    '        Con.Open()
    '    End If
    '    Dim rdr As SqlDataReader = cmd.ExecuteReader()
    '    While rdr.Read()
    '        map(rdr("EntryReferenceModule").ToString.Trim) = rdr("AccountNo").ToString.Trim
    '    End While
    '    rdr.Close()
    '    Return map
    'End Function

    'Private Sub InsertJEEntry(JESN As Long, accountNo As String, amount As Decimal, drcr As String)
    '    If amount <= 0 Then Exit Sub
    '    Dim cmd As New SqlCommand("INSERT INTO tblGLTrx (JESN, AccountNo, Amount, DRCR) VALUES (@JESN, @Acc, @Amt, @DRCR)", Con)
    '    cmd.Parameters.AddWithValue("@JESN", JESN)
    '    cmd.Parameters.AddWithValue("@Acc", accountNo)
    '    cmd.Parameters.AddWithValue("@Amt", amount)
    '    cmd.Parameters.AddWithValue("@DRCR", drcr)
    '    If Con.State <> ConnectionState.Open Then
    '        Con.Open()
    '    End If
    '    cmd.ExecuteNonQuery()
    'End Sub


    'Private Structure JournalEntry
    '    Public AccountNo As String
    '    Public Amount As Decimal
    '    Public DRCR As String
    'End Structure

    'Private Sub CreateJEHeaderAndEntry(trxNo As Long, totalAmount As Decimal)
    '    Dim JESN As Long
    '    Dim paymentType As String = cmbxPaymentType.Text.Trim
    '    Dim isCash As Boolean = (paymentType = "نقدي")
    '    Dim accountMap As Dictionary(Of String, String) = LoadGLAccounts()

    '    Dim customerAccount As String = If(isCash, accountMap("نقدية"), accountMap("عملاء"))

    '    ' التحقق من وجود القيد
    '    Dim checkCmd As New SqlCommand("SELECT JESN FROM tblJEHeader WHERE ReferenceTrx = @Trx AND ReferenceType = 'مبيعات'", Con)
    '    checkCmd.Parameters.AddWithValue("@Trx", trxNo)
    '    JESN = Val(ExecuteScalar(checkCmd))

    '    If JESN = 0 Then
    '        Dim insertCmd As New SqlCommand("
    '        INSERT INTO tblJEHeader (TrxDate, Amount, Reference, ReferenceTrx, ReferenceType, PostStatus, CreatedBy, CreatedOn)
    '        VALUES (GETDATE(), @Amt, @Ref, @Trx, 'مبيعات', 'Open', @User, GETDATE());
    '        SELECT SCOPE_IDENTITY()", Con)
    '        insertCmd.Parameters.AddWithValue("@Amt", totalAmount)
    '        insertCmd.Parameters.AddWithValue("@Ref", "فاتورة مبيعات - " & trxNo)
    '        insertCmd.Parameters.AddWithValue("@Trx", trxNo)
    '        insertCmd.Parameters.AddWithValue("@User", UserName)
    '        JESN = Val(ExecuteScalar(insertCmd))
    '    End If

    '    ' تحميل بيانات الأصناف
    '    Dim itemsCmd As New SqlCommand("
    '    SELECT I.DocNo,I.ItemNo, (I.LineAmount+I.VATAmount)*-1 as LineAmount, I.VATAmount, T.ItemType, T.UnitPurchasePrice, -I.TrxQTY as TrxQTY, (T.UnitPurchasePrice * -I.TrxQTY) as PurchasePrice ,I.UofMConversion 
    '    FROM tblStockMovement I 
    '    INNER JOIN tblItems T ON I.ItemNo = T.ItemNo 
    '    WHERE I.DocNo = @Trx", Con)
    '    itemsCmd.Parameters.AddWithValue("@Trx", trxNo)

    '    Dim journalEntries As New List(Of JournalEntry)
    '    Dim totalSales As Decimal = 0
    '    Dim totalVAT As Decimal = 0
    '    Dim totalCOGS As Decimal = 0
    '    If Con.State <> ConnectionState.Open Then
    '        Con.Open()
    '    End If
    '    Using rdr As SqlDataReader = itemsCmd.ExecuteReader()
    '        While rdr.Read
    '            Dim itemNo As String = rdr("ItemNo").ToString
    '            Dim amount As Decimal = rdr("LineAmount")
    '            Dim vatAmount As Decimal = rdr("VATAmount")
    '            Dim itemType As String = rdr("ItemType").ToString
    '            Dim itemCost As Decimal = Val(rdr("PurchasePrice"))

    '            ' إيراد
    '            journalEntries.Add(New JournalEntry With {
    '            .AccountNo = accountMap("مبيعات"),
    '            .Amount = amount,
    '            .DRCR = "CR"
    '        })

    '            totalSales += amount
    '            totalVAT += vatAmount

    '            If itemType = "مخزني" Then
    '                journalEntries.Add(New JournalEntry With {
    '                .AccountNo = accountMap("تكلفة المبيعات"),
    '                .Amount = itemCost,
    '                .DRCR = "DR"
    '            })
    '                journalEntries.Add(New JournalEntry With {
    '                .AccountNo = accountMap("المخزون"), ' أو حساب المخزون الجديد إذا عدلت اسمه
    '                .Amount = itemCost,
    '                .DRCR = "CR"
    '            })
    '                totalCOGS += itemCost
    '            End If
    '        End While
    '    End Using

    '    ' تنفيذ كل القيود بعد إغلاق الـ Reader
    '    For Each entry In journalEntries
    '        InsertJEEntry(JESN, entry.AccountNo, entry.Amount, entry.DRCR)
    '    Next

    '    ' العميل أو الصندوق
    '    InsertJEEntry(JESN, customerAccount, totalSales + totalVAT, "DR")

    '    ' الضريبة
    '    InsertJEEntry(JESN, accountMap("الضريبة"), totalVAT, "CR")
    'End Sub


    Sub ClearFieldsWOGetSN()
        DGV1.DataSource = ""
        txtNotes.Clear()
        'lblAccountName.Text = ""
        cmbxPaymentType.Text = ""
        txtVendorInvNo.Clear()
        txtReferenceNo.Clear()
        ckbxVATIncluded.Checked = False
        lblTotal.Text = ""
        lblItemCount.Text = ""
        lblQTY.Text = ""
        txtDiscount.Text = ""
        lblDiscountAmount.Text = ""
        lblInvoiceAmount.Text = ""
        LoadSettings()
        txtItemSN.Text = 1
        lblVATAmount.Text = 0
    End Sub
    Sub ClearFields()
        cmbxPartnerNo.Enabled = True
        cmbxPaymentType.Enabled = True
        CustomerLoad()
        DGV1.DataSource = ""
        txtNotes.Clear()
        'cmbxStores.Text = ""
        cmbxPartnerNo.Text = ""
        'lblAccountName.Text = ""
        cmbxPaymentType.Text = ""
        'cmbxCashier.Text = ""
        txtInvoiceNo.Clear()
        txtVendorInvNo.Clear()
        txtReferenceNo.Clear()
        'ckbxNonVatInvoice.Checked = False
        ckbxVATIncluded.Checked = False
        lblTotal.Text = ""
        lblItemCount.Text = ""
        lblQTY.Text = ""
        txtDiscount.Text = ""
        lblDiscountAmount.Text = ""
        lblInvoiceAmount.Text = ""
        LoadSettings()
        GetSerial()
        txtItemSN.Text = 1
        lblVATAmount.Text = 0
        'cmbxCashier.Enabled = True
        'cmbxStores.Enabled = True

    End Sub

    Sub UpdateTotals()
        Dim grossTotal As Decimal = 0
        Dim qty As Decimal = 0

        Dim FillCMD As New SqlCommand("SELECT tblStockMovement.LineSN AS م, tblStockMovement.ItemNo AS [رقم الصنف], tblItems.ItemDescription AS [وصف الصنف], tblStockMovement.TrxQTY AS الكمية,tblItems.UofM AS [الوحدة], tblStockMovement.UnitPrice AS [سعر الوحدة],  FORMAT(tblStockMovement.TrxQTY * tblStockMovement.UnitPrice, 'N2') AS [السعر الإجمالي] FROM tblStockMovement INNER JOIN tblItems ON tblStockMovement.ItemNo = tblItems.ItemNo WHERE        (tblStockMovement.DocNo = " & Val(txtInvoiceNo.Text) & ") AND (tblStockMovement.TrxType = 'مبيعات') ORDER BY م", Con)

        ' حساب إجمالي السعر الإجمالي (قبل الخصم)
        For Each r As DataGridViewRow In DGV1.Rows
            If Not r.IsNewRow Then
                grossTotal += r.Cells("السعر الإجمالي").Value
                qty += r.Cells("الكمية").Value
            End If
        Next
        lblInvoiceAmount.Text = grossTotal
        ' التحقق من الخصم
        Dim discountPercent As Decimal = 0
        If Decimal.TryParse(txtDiscount.Text, discountPercent) Then
            If discountPercent > MaxDiscountPercent Then
                MessageBox.Show("الخصم يتجاوز الحد المسموح به (" & MaxDiscountPercent & "%)", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                discountPercent = MaxDiscountPercent
                txtDiscount.Text = MaxDiscountPercent.ToString("0.##")
            End If
        Else
            discountPercent = 0
            txtDiscount.Text = "0"
        End If

        ' حساب مبلغ الخصم
        Dim discountAmount As Decimal = Math.Round((discountPercent / 100) * grossTotal, 2)
        Dim totalAfterDiscount As Decimal = grossTotal - discountAmount

        ' حساب ضريبة القيمة المضافة


        Dim vatAmount As Decimal
        If PriceIncludeVATDef = "True" Then
            vatAmount = Math.Round((15 / 115) * totalAfterDiscount, 2, MidpointRounding.AwayFromZero)
            lblInvoiceAmount.Text = totalAfterDiscount.ToString("0.00")
        Else
            vatAmount = Math.Round(totalAfterDiscount * 0.15D, 2, MidpointRounding.AwayFromZero)

            'vatAmount = Math.Round((totalAfterDiscount * 15) / 100, 2, MidpointRounding.AwayFromZero)
            lblInvoiceAmount.Text = totalAfterDiscount + vatAmount
        End If

        ' عرض النتائج
        lblDiscountAmount.Text = discountAmount.ToString("0.00")

        lblVATAmount.Text = vatAmount.ToString("0.00")
        lblQTY.Text = qty
        lblItemCount.Text = DGV1.Rows.Count



        Dim Lines As Integer = 0

        lblQTY.Text = qty
        'lblItemCount.Text = Lines
        'lblInvoiceAmount.Text = Total1
        txtVAT.Text = "15"
        lblTotal.Text = lblInvoiceAmount.Text - vatAmount

        If Lines > 0 Then

            cmbxPartnerNo.Enabled = False
            cmbxPaymentType.Enabled = False
        ElseIf Lines = 0 Then

            cmbxPartnerNo.Enabled = True
            cmbxPaymentType.Enabled = True
        End If
        ' لحساب إجمالي المبيعات قبل الخصم لاستخدامه في القيد المحاسبي
        grossAmountBeforeDiscount = GetGrossAmountBeforeDiscount(DGV1, PriceIncludeVATDef)

    End Sub
    Private Sub cmbxpaymentType_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbxPaymentType.SelectedIndexChanged
        If cmbxPaymentType.Text.Trim = "آجل" Then
            cmbxCashier.Enabled = False
            PaymentStatus = "Open"
        ElseIf cmbxPaymentType.Text.Trim = "نقدي" Then
            cmbxCashier.Enabled = True
            PaymentStatus = "Paid"
        End If
    End Sub
    Function GetGrossAmountBeforeDiscount(dgv As DataGridView, isPriceIncludesVAT As Boolean) As Decimal
        Dim total As Decimal = 0

        For Each row As DataGridViewRow In dgv.Rows
            If Not row.IsNewRow Then
                Dim unitPrice As Decimal = Val(row.Cells("سعر الوحدة").Value)
                Dim qty As Decimal = Val(row.Cells("الكمية").Value)
                Dim lineAmount As Decimal = unitPrice * qty

                total += lineAmount
            End If
        Next

        ' إذا كانت الأسعار تشمل الضريبة، نستخرج الأساس قبل الضريبة
        'If isPriceIncludesVAT Then
        '    total = Math.Round(total / 1.15D, 2, MidpointRounding.AwayFromZero)
        'End If

        Return total
    End Function

    'Private Sub Totalcount()
    '    Dim Total1 As Double
    '    Dim QTY As Integer = 0
    '    Dim Lines As Integer = 0
    '    Dim VATAmount As Double = 0
    '    Dim CountCMD As New SqlCommand("Select Sum(-LineAmount) as Total,sum(-TrxQTY) as QTY,sum(VATAmount) as TotalVAT,Max(LineSN) as Lines from tblStockMovement where TrxType = 'مبيعات' and DocNo = " & Val(txtInvoiceNo.Text) & "", Con)
    '    If Con.State <> ConnectionState.Open Then
    '        Con.Open()
    '    End If

    '    Dim reader As SqlDataReader = CountCMD.ExecuteReader
    '    If reader.Read Then
    '        If reader.Item("Total").ToString <> "" Then
    '            Total1 = Val(reader.Item("Total").ToString)
    '            QTY = Val(reader.Item("QTY").ToString)
    '            Lines = Val(reader.Item("Lines").ToString)
    '            VATAmount = Val(reader.Item("TotalVAT").ToString)
    '        Else
    '            Total1 = 0
    '            QTY = 0
    '            Lines = 0
    '            VATAmount = 0
    '        End If
    '    End If
    '    If Con.State <> ConnectionState.Closed Then
    '        Con.Close()
    '    End If

    '    lblVATAmount.Text = VATAmount
    '    lblInvoiceAmount.Text = Total1
    '    txtVAT.Text = "15"
    '    lblTotal.Text = Total1 - VATAmount



    '    lblQTY.Text = QTY
    '    lblItemCount.Text = Lines
    '    If Lines > 0 Then

    '        cmbxPartnerNo.Enabled = False
    '        cmbxPaymentType.Enabled = False
    '    ElseIf Lines = 0 Then

    '        cmbxPartnerNo.Enabled = True
    '        cmbxPaymentType.Enabled = True
    '    End If
    'End Sub

    Private Sub ckbxVATIncluded_CheckedChanged(sender As Object, e As EventArgs) Handles ckbxVATIncluded.CheckedChanged
        UpdateTotals()
    End Sub

    Private Sub txtDiscount_TextChanged(sender As Object, e As EventArgs) Handles txtDiscount.TextChanged
        UpdateTotals()
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        If Val(lblItemCount.Text) = 0 And Val(lblInvoiceAmount.Text) = 0 Then
            ClearFieldsWOGetSN()
        End If
    End Sub


    Private Sub txtItemNo_KeyDown(sender As Object, e As KeyEventArgs) Handles txtItemNo.KeyDown
        If e.KeyCode = Keys.Enter Then
            If txtItemNo.Text <> "" Then
                Dim SearchCMD As New SqlCommand("Select ItemDescription,SalesUofM,UnitSalesPrice from tblItems where ItemNo = " & Val(txtItemNo.Text) & "", Con)
                OpenConnection()
                Dim reader As SqlDataReader = SearchCMD.ExecuteReader
                If reader.Read Then
                    ItemDescription = Trim(reader.Item(0).ToString)
                    ItemUnit = Trim(reader.Item(1).ToString)
                    UnitPrice = Val(reader.Item(2).ToString)
                    txtItemDescription.Text = ItemDescription
                    cmbxUnit.Text = ItemUnit
                    If Val(txtQTY.Text) = 0 Then
                        txtQTY.Text = 1
                    End If
                    If Val(txtUnitPrice.Text) = 0 Then
                        txtUnitPrice.Text = UnitPrice
                    End If
                    If reader.IsClosed = False Then
                        reader.Close()
                    End If
                    CloseConnection()
                    UnitsLoad()
                    txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
                    txtQTY.Focus()
                    Try
                        Dim selectedConversion As Decimal = Convert.ToDecimal(cmbxUnit.SelectedValue)
                    Catch ex As Exception
                        If reader.IsClosed = False Then
                            reader.Close()
                        End If
                        txtQTY.Clear()
                        txtUnitPrice.Clear()
                        txtTotal.Clear()
                        txtItemDescription.Clear()
                    End Try
                Else
                    If reader.IsClosed = False Then
                        reader.Close()
                    End If
                    txtQTY.Clear()
                    txtUnitPrice.Clear()
                    txtTotal.Clear()
                    txtItemDescription.Clear()
                End If
            End If
        End If
    End Sub

    Private Sub DGV1_KeyDown(sender As Object, e As KeyEventArgs) Handles DGV1.KeyDown
        Try
            Dim iCol = DGV1.CurrentCell.ColumnIndex
            Dim iRow = DGV1.CurrentCell.RowIndex

            If e.KeyCode = Keys.Enter Then
                e.SuppressKeyPress = True


                If e.KeyCode = Keys.Enter Then
                    e.SuppressKeyPress = True
                    If iCol = DGV1.Columns.Count - 1 Then
                        If iRow < DGV1.Rows.Count - 1 Then
                            DGV1.CurrentCell = DGV1(0, iRow + 1)
                        End If
                    Else

                        If iCol = 1 Then
                            DGV1.CurrentCell = DGV1(iCol + 3, iRow)
                        Else
                            DGV1.CurrentCell = DGV1(iCol + 1, iRow)
                        End If
                    End If
                End If




            ElseIf e.KeyCode = Keys.Delete Then
                If DGV1.CurrentRow.Cells(0).Value <> 0 Then
                    If DGV1.Rows.Count <> 0 Then

                        x = DGV1.CurrentRow.Index
                        DGV1.Rows.Remove(DGV1.Rows(x))
                        x += 1
                        UpdateLineSN()

                    End If
                End If

            End If
        Catch ex As Exception

        End Try







    End Sub
    Sub UpdateLineSN()
        Dim DeleteCMD As New SqlCommand("Delete from tblStockMovement where DocNo =  " & Val(txtInvoiceNo.Text) & " and LineSN = " & Val(x) & " and TrxType = 'مبيعات'", Con)
        Dim Delete2CMD As New SqlCommand("Delete from tblStockTrx where OutInvoiceNo =  " & Val(txtInvoiceNo.Text) & " and OutInvoiceItemSN = " & Val(x) & " and OutInvoiceType = 'مبيعات'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        DeleteCMD.ExecuteNonQuery()
        Delete2CMD.ExecuteNonQuery()
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        '====================================================
        Dim UpdateCMD As New SqlCommand("Update tblStockMovement set LineSN = LineSN - 1 where DocNo = " & Val(txtInvoiceNo.Text) & " and LineSN > " & Val(x) & " and TrxType = 'مبيعات'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        UpdateCMD.ExecuteNonQuery()

        'If reader.isclosed Then
        'Else
        '    reader.close()
        'End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        '====================================================
        Dim Update2CMD As New SqlCommand("Update tblStockTrx set OutInvoiceItemSN = OutInvoiceItemSN -1 where OutInvoiceNo = " & Val(txtInvoiceNo.Text) & " and OutInvoiceItemSN > " & Val(x) & " and OutInvoiceType = 'مبيعات'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Update2CMD.ExecuteNonQuery()

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Private Sub DGV1_DoubleClick(sender As Object, e As EventArgs) Handles DGV1.DoubleClick
        If DGV1.SelectedRows.Count = 1 And Val(DGV1.CurrentRow.Cells(0).Value) <> 0 Then
            txtItemSN.Text = Val(DGV1.CurrentRow.Cells(0).Value)
            txtItemNo.Text = DGV1.CurrentRow.Cells(1).Value
            txtQTY.Text = Val(DGV1.CurrentRow.Cells(3).Value)
            cmbxUnit.Text = Trim(DGV1.CurrentRow.Cells(4).Value)
            txtUnitPrice.Text = Val(DGV1.CurrentRow.Cells(5).Value)
            txtItemDescription.Text = Trim(DGV1.CurrentRow.Cells(2).Value)
            txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
            EnteredItemBySN = 0
        End If
    End Sub



    Sub CheckItem()
        If txtItemNo.Text <> "" Then
            Dim SearchCMD As New SqlCommand("Select ItemDescription,SalesUofM,UnitSalesPrice from tblItems where ItemNo = " & Val(txtItemNo.Text) & "", Con)
            OpenConnection()
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                ItemDescription = Trim(reader.Item(0).ToString)
                ItemUnit = Val(reader.Item(1).ToString)
                UnitPrice = Trim(reader.Item(2).ToString)
                txtItemDescription.Text = ItemDescription
                cmbxUnit.Text = ItemUnit
                If Val(txtQTY.Text) = 0 Then
                    txtQTY.Text = 1
                End If
                If Val(txtUnitPrice.Text) = 0 Then
                    txtUnitPrice.Text = UnitPrice
                End If
                If reader.IsClosed = False Then
                    reader.Close()
                End If
                CloseConnection()
                UnitsLoad()
                txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
                txtQTY.Focus()
            Else
                txtQTY.Clear()
                txtUnitPrice.Clear()
                txtTotal.Clear()
                SearchItemFor = "Sales"
                frmItemSearch.ShowDialog()
            End If
        Else
            txtItemDescription.Clear()
            txtQTY.Clear()
            txtUnitPrice.Clear()
            txtTotal.Clear()
            SearchItemFor = "Sales"
            frmItemSearch.ShowDialog()
        End If

    End Sub


    Private Sub txtItemNo_LostFocus(sender As Object, e As EventArgs) Handles txtItemNo.LostFocus
        If txtItemNo.Text <> "" Then
            Dim SearchCMD As New SqlCommand("Select ItemDescription,SalesUofM,UnitSalesPrice from tblItems where ItemNo = " & Val(txtItemNo.Text) & "", Con)
            OpenConnection()
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                ItemDescription = Trim(reader.Item(0).ToString)
                ItemUnit = Trim(reader.Item(1).ToString)
                UnitPrice = Val(reader.Item(2).ToString)
                txtItemDescription.Text = ItemDescription
                cmbxUnit.Text = ItemUnit
                If Val(txtQTY.Text) = 0 Then
                    txtQTY.Text = 1
                End If
                If Val(txtUnitPrice.Text) = 0 Then
                    txtUnitPrice.Text = UnitPrice
                End If
                If reader.IsClosed = False Then
                    reader.Close()
                End If
                CloseConnection()
                UnitsLoad()
                txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
                txtQTY.Focus()
                Try
                    Dim selectedConversion As Decimal = Convert.ToDecimal(cmbxUnit.SelectedValue)
                Catch ex As Exception
                    If reader.IsClosed = False Then
                        reader.Close()
                    End If
                    txtQTY.Clear()
                    txtUnitPrice.Clear()
                    txtTotal.Clear()
                    txtItemDescription.Clear()
                End Try
            Else
                If reader.IsClosed = False Then
                    reader.Close()
                End If
                txtQTY.Clear()
                txtUnitPrice.Clear()
                txtTotal.Clear()
                txtItemDescription.Clear()
            End If
        End If
    End Sub

    Private Sub txtQTY_Leave(sender As Object, e As EventArgs) Handles txtQTY.Leave, cmbxUnit.Leave
        txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
    End Sub

    Private Sub txtUnitPrice_Leave(sender As Object, e As EventArgs) Handles txtUnitPrice.Leave
        txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
    End Sub
    Sub RefreshGrid()
        Dim ds As DataSet = New DataSet
        Dim FillCMD As New SqlCommand("SELECT tblStockMovement.LineSN AS م, tblStockMovement.ItemNo AS [رقم الصنف], tblItems.ItemDescription AS [وصف الصنف], tblStockMovement.TrxQTY AS الكمية,tblItems.UofM AS [الوحدة], tblStockMovement.UnitPrice AS [سعر الوحدة],  FORMAT(tblStockMovement.TrxQTY * tblStockMovement.UnitPrice, 'N2') AS [السعر الإجمالي] FROM tblStockMovement INNER JOIN tblItems ON tblStockMovement.ItemNo = tblItems.ItemNo WHERE        (tblStockMovement.DocNo = " & Val(txtInvoiceNo.Text) & ") AND (tblStockMovement.TrxType = 'مبيعات') ORDER BY م", Con)
        Dim da As New SqlDataAdapter(FillCMD)
        ds.Clear()
        da.Fill(ds)
        DGV1.DataSource = ds.Tables(0)
    End Sub
    Sub AddItems()
        If ReferenceMandatory = "True" And txtReferenceNo.Text.Trim <> "" Then
            MsgBox("يجب إدخال رقم المرجع للاستمرار", MsgBoxStyle.Critical, "نظام السلطان")
            Exit Sub
        End If
        If cmbxStore.Text.Trim = "" Then
            MsgBox("يجب إدخال المخزن للاستمرار", MsgBoxStyle.Critical, "نظام السلطان")
            Exit Sub
        End If

        If Val(txtItemSN.Text) <> 0 And Val(txtItemNo.Text) <> 0 And Trim(txtItemDescription.Text) <> "" And Val(txtQTY.Text) <> 0 And Val(txtUnitPrice.Text) <> 0 And Val(txtTotal.Text) <> 0 Then
            Dim itemNo As Long = Trim(txtItemNo.Text)
            Dim store As String = cmbxStore.Text.Trim
            Dim requiredQty As Decimal = Convert.ToDecimal(txtQTY.Text)
            Dim inputUofM As String = cmbxUnit.Text.Trim
            Dim vatPercentage As Decimal = 0
            Dim vatAmount As Decimal = 0
            Dim lineTotal As Decimal = 0
            Dim isVatIncluded As Boolean = ckbxVATIncluded.Checked ' checkbox to determine inclusion
            Dim unitPrice As Decimal = Val(txtUnitPrice.Text)
            Dim qty As Decimal = Val(txtQTY.Text)

            ' === Get VAT Percentage from tblItems ===
            Dim vatCmd As New SqlCommand("SELECT ISNULL(Tax_Percent, 0) FROM tblItems WHERE ItemNo = @ItemNo", Con)
            vatCmd.Parameters.AddWithValue("@ItemNo", itemNo)
            If Con.State <> ConnectionState.Open Then Con.Open()
            vatPercentage = Convert.ToDecimal(vatCmd.ExecuteScalar())
            If Con.State <> ConnectionState.Closed Then Con.Close()

            ' === Calculate VAT Amount ===
            'BaseAmount = Math.Round(requiredQty * Val(txtUnitPrice.Text), 2)
            'If ckbxVATIncluded.Checked Then
            '    BaseAmount = Math.Round(requiredQty * Val(txtUnitPrice.Text) / ((1 + vatPercentage / 100)), 2)
            '    vatAmount = Math.Round((requiredQty * Val(txtUnitPrice.Text)) - BaseAmount, 2)
            'Else
            '    vatAmount = Math.Round(requiredQty * Val(txtUnitPrice.Text) * vatPercentage / 100, 2)
            'End If

            If isVatIncluded Then
                ' If VAT is already included in the price
                vatAmount = Math.Round((unitPrice * qty * vatPercentage) / (100 + vatPercentage), 2)
                lineTotal = unitPrice * qty ' Already includes VAT
            Else
                ' If VAT should be added on top
                vatAmount = Math.Round((unitPrice * qty * vatPercentage) / 100, 2)
                lineTotal = (unitPrice * qty) + vatAmount
            End If


            ' === Check if line exists ===
            Dim CheckCMD As New SqlCommand("SELECT * FROM tblStockMovement WHERE DocNo = @DocNo AND TrxType = 'مبيعات' AND LineSN = @LineSN", Con)
            CheckCMD.Parameters.AddWithValue("@DocNo", Val(txtInvoiceNo.Text))
            CheckCMD.Parameters.AddWithValue("@LineSN", Val(txtItemSN.Text))
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim reader As SqlDataReader = CheckCMD.ExecuteReader()

            If reader.Read() Then
                reader.Close()

                ' === Check Stock Before Update ===
                If CheckStockAvailabilityByUnit(itemNo, store, requiredQty, inputUofM) = 0 Then
                    MessageBox.Show("لايوجد رصيد متاح", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Exit Sub
                End If

                ' === Update Line
                Dim UpdateCMD As New SqlCommand("
                UPDATE tblStockMovement 
                SET UofMConversion = @Conv, UofM = @UofM, TrxQTY = @Qty, UnitPrice = @Price,
                    ItemNo = @ItemNo, ModifiedOn = GetDate(), ModifiedBy = @User, 
                    Store = @Store, VATAmount = @VAT,LineAmount= @LineAmount
                WHERE DocNo = @DocNo AND TrxType = 'مبيعات' AND LineSN = @LineSN", Con)

                With UpdateCMD.Parameters
                    .AddWithValue("@Conv", Convert.ToDecimal(cmbxUnit.SelectedValue))
                    .AddWithValue("@UofM", inputUofM)
                    .AddWithValue("@Qty", requiredQty)
                    .AddWithValue("@Price", Val(txtUnitPrice.Text))
                    .AddWithValue("@ItemNo", itemNo)
                    .AddWithValue("@User", UserName)
                    .AddWithValue("@Store", store)
                    .AddWithValue("@VAT", vatAmount)
                    .AddWithValue("@LineAmount", lineTotal)
                    .AddWithValue("@DocNo", Val(txtInvoiceNo.Text))
                    .AddWithValue("@LineSN", Val(txtItemSN.Text))
                End With

                UpdateCMD.ExecuteNonQuery()

            Else
                reader.Close()

                ' === Check Stock Before Insert ===
                If CheckStockAvailabilityByUnit(itemNo, store, requiredQty, inputUofM) = 0 Then
                    MessageBox.Show("لايوجد رصيد متاح", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Exit Sub
                End If

                ' === Insert New Line
                Dim InsertCMD As New SqlCommand("
                INSERT INTO tblStockMovement 
                (DocNo, ItemNo, LineSN, TrxType, TrxDate, TrxQTY, UnitPrice, CreatedBy, CreatedOn, Store, UofM, UofMConversion, VATAmount,LineAmount) 
                VALUES 
                (@DocNo, @ItemNo, @LineSN, 'مبيعات', @TrxDate, @Qty, @Price, @User, GetDate(), @Store, @UofM, @Conv, @VAT,@LineAmount)", Con)

                With InsertCMD.Parameters
                    .AddWithValue("@DocNo", Val(txtInvoiceNo.Text))
                    .AddWithValue("@ItemNo", itemNo)
                    .AddWithValue("@LineSN", Val(txtItemSN.Text))
                    .AddWithValue("@TrxDate", Format(dtp1.Value.Date, "yyyy/MM/dd"))
                    .AddWithValue("@Qty", requiredQty)
                    .AddWithValue("@Price", Val(txtUnitPrice.Text))
                    .AddWithValue("@User", UserName)
                    .AddWithValue("@Store", store)
                    .AddWithValue("@UofM", inputUofM)
                    .AddWithValue("@Conv", Convert.ToDecimal(cmbxUnit.SelectedValue))
                    .AddWithValue("@VAT", vatAmount)
                    .AddWithValue("@LineAmount", lineTotal)
                End With

                InsertCMD.ExecuteNonQuery()
            End If

            If Con.State <> ConnectionState.Closed Then Con.Close()

            ' === Finalizing
            RefreshGrid()
            ForItemChange = 1
            SaveTrx()
            ForItemChange = 0
            UpdateTotals()

            txtItemNo.Clear()
            txtItemSN.Clear()
            txtItemDescription.Clear()
            txtQTY.Clear()
            txtUnitPrice.Clear()
            txtTotal.Clear()
            cmbxUnit.Text = ""
            GetLineSerial()
        End If
    End Sub


    Private Sub btnLineSave_Click(sender As Object, e As EventArgs) Handles btnLineSave.Click
        AddItems()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If MsgBox("هل ترغب بالتأكيد في حفظ الفاتورة", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
            EnteredItemBySN = 0
            SaveTrx()
        End If
    End Sub

    Private Sub btnLineDelete_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnLineDelete.Click
        If Val(txtItemSN.Text) <> 0 And Val(txtInvoiceNo.Text) <> 0 Then
            InvNo = Val(txtInvoiceNo.Text)
            If MsgBox("هل ترغب بحذف السطر بالتأكيد ؟", MsgBoxStyle.YesNoCancel, "نظام السلطان") = MsgBoxResult.Yes Then
                Dim DeleteCMD As New SqlCommand("Delete tblStockMovement where TrxType = 'مبيعات' and DocNo = " & Val(InvNo) & " and LineSN = " & Val(txtItemSN.Text) & " ", Con)
                'Dim Delete2CMD As New SqlCommand("Update tblStockTrx set OutInvoiceType= NULL, OutInvoiceNo= NULL,OutInvoiceItemSN = NULL, OutCreatedBy = NULL,OutCreatedOn = NULL,UnitPrice=0,InStock = 1 where OutInvoiceItemSN = " & Val(txtItemSN.Text) & " and OutInvoiceType= 'مبيعات' and OutInvoiceNo = " & Val(InvNo) & "and DeviceSN = '" & txtSN.Text.Trim & "' and InStock = 0", Con)
                Dim Delete3CMD As New SqlCommand("Update tblStockMovement set LineSN = LineSN - 1 where LineSN > " & Val(txtItemSN.Text) & " and TrxType = 'مبيعات' and DocNo = " & Val(InvNo) & "", Con)
                'Dim Delete4CMD As New SqlCommand("Update tblStockTrx set OutInvoiceItemSN = OutInvoiceItemSN - 1 where OutInvoiceItemSN > " & Val(txtItemSN.Text) & " and OutInvoiceType = 'مبيعات' and OutInvoiceNo = " & Val(InvNo) & "", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If

                DeleteCMD.ExecuteNonQuery()
                'Delete2CMD.ExecuteNonQuery()
                Delete3CMD.ExecuteNonQuery()
                'Delete4CMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                RefreshGrid()
                ForItemChange = 1
                SaveTrx()
                UpdateTotals()
                ForItemChange = 0
                txtItemNo.Clear()
                txtItemDescription.Clear()
                txtQTY.Clear()
                txtUnitPrice.Clear()
                txtTotal.Clear()
            End If
        End If
    End Sub

    Private Sub ckbxNonVatInvoice_CheckedChanged(sender As Object, e As EventArgs)
        UpdateTotals()
    End Sub


    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        SearchForReference = "مبيعات"
        frmInvoiceReference.ShowDialog()
    End Sub



    Private Sub btnItemLookup_Click(sender As Object, e As EventArgs) Handles btnItemLookup.Click
        SearchItemFor = "Sales"
        frmItemSearch.ShowDialog()
    End Sub




    Private Sub cmbxUnit_TextChanged(sender As Object, e As EventArgs) Handles cmbxUnit.TextChanged
        If Val(txtItemNo.Text) <> 0 And cmbxUnit.Text.Trim <> "" Then
            Try
                Dim SearchCMD As New SqlCommand("Select sales_price from UnitsPricesView where ItemNo = " & Val(txtItemNo.Text) & " And Sales_Unit = '" & cmbxUnit.Text.Trim & "'", Con)
                OpenConnection()
                Dim SqlDataReader As SqlDataReader = SearchCMD.ExecuteReader
                If SqlDataReader.Read Then
                    UnitPrice = Val(SqlDataReader.Item(0).ToString)
                    If Val(txtQTY.Text) = 0 Then
                        txtQTY.Text = 1
                    End If
                    If Val(UnitPrice) <> 0 Then
                        txtUnitPrice.Text = UnitPrice
                    End If

                    CloseConnection()

                    txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
                    txtQTY.Focus()

                    Dim selectedConversion As Decimal = Convert.ToDecimal(cmbxUnit.SelectedValue)

                Else
                    If SqlDataReader.IsClosed = False Then
                        SqlDataReader.Close()
                    End If

                    txtUnitPrice.Clear()
                    txtTotal.Clear()

                End If
            Catch ex As Exception

            End Try

        End If
    End Sub


    Private Sub txtBarcode_KeyDown(sender As Object, e As KeyEventArgs) Handles txtBarcode.KeyDown
        If e.KeyCode = Keys.Enter Then
            If txtBarcode.Text.Trim() <> "" Then
                ProcessScannedBarcode(txtBarcode.Text.Trim())
                e.SuppressKeyPress = True
            End If
        End If
    End Sub

    Private Sub ProcessScannedBarcode(scannedCode As String)
        Try
            ' الإعدادات
            Dim format As String = ""
            Dim weightDivisor As Integer = 1000

            ' قراءة إعدادات الباركود من قاعدة البيانات
            Dim settingQuery As String = "SELECT * FROM tblBarcodeSettings WHERE Shop = @Shop"
            Using conn As New SqlConnection(ConStr)
                Using cmd As New SqlCommand(settingQuery, conn)
                    cmd.Parameters.AddWithValue("@Shop", cmbxStore.Text)
                    conn.Open()
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then
                            format = reader("EmbeddedFormat").ToString()
                            weightDivisor = Convert.ToInt32(reader("WeightDivisor"))
                        Else
                            MessageBox.Show("لم يتم العثور على إعدادات الباركود لهذا الفرع.")
                            Exit Sub
                        End If
                    End Using
                End Using
            End Using

            ' تحليل الصيغة
            Dim itemLength As Integer = format.Count(Function(c) c = "x"c)
            Dim weightLength As Integer = format.Count(Function(c) c = "w"c)

            If scannedCode.Length < itemLength + weightLength Then
                MessageBox.Show("الباركود الممسوح لا يتوافق مع الطول المتوقع.")
                Exit Sub
            End If

            ' تخطي أول خانة (مثلاً 2 في EAN13)
            Dim itemCode As String = scannedCode.Substring(0, itemLength)
            Dim weightStr As String = scannedCode.Substring(0 + itemLength, weightLength)


            'MessageBox.Show("الباركود: " & scannedCode & vbCrLf &
            '    "الطول الكلي: " & scannedCode.Length & vbCrLf &
            '    "ItemLength: " & itemLength & vbCrLf &
            '    "WeightLength: " & weightLength)



            Dim weightQty As Decimal
            If Not Decimal.TryParse(weightStr, weightQty) Then
                MessageBox.Show("القيمة المستخرجة للوزن غير صحيحة: " & weightStr)
                Exit Sub
            End If

            weightQty = weightQty / weightDivisor

            ' جلب بيانات الصنف من قاعدة البيانات
            Dim itemQuery As String = "SELECT ItemNo, UnitSalesPrice, SalesUofM,ItemDescription FROM tblItems WHERE ItemNo = @ItemNo OR Barcode = @ItemNo"
            Using conn As New SqlConnection(ConStr)
                Using cmd As New SqlCommand(itemQuery, conn)
                    cmd.Parameters.AddWithValue("@ItemNo", itemCode)
                    conn.Open()
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then


                            ItemNo = reader("ItemNo").ToString()
                            ItemDescription = Trim(reader.Item("ItemDescription").ToString)
                            UnitPrice = reader("UnitSalesPrice").ToString()
                            txtQTY.Text = weightQty.ToString("0.###")
                            UnitsLoad()
                            Dim selectedConversion As Decimal = Convert.ToDecimal(cmbxUnit.SelectedValue)
                            ItemUnit = reader("SalesUofM").ToString()


                            txtItemNo.Text = ItemNo
                            txtUnitPrice.Text = UnitPrice
                            txtItemDescription.Text = ItemDescription
                            cmbxUnit.Text = ItemUnit




                            ' ✅ استدعاء AddItems لإضافة الصنف إلى الفاتورة
                            AddItems()

                            ' مسح حقل الباركود لتجهيز المسح التالي
                            txtBarcode.Clear()
                            txtBarcode.Focus()
                        Else
                            MessageBox.Show("لم يتم العثور على الصنف المرتبط بالباركود.")
                        End If
                    End Using
                End Using
            End Using

        Catch ex As Exception
            MessageBox.Show("خطأ أثناء معالجة الباركود: " & ex.Message)
        End Try
    End Sub

    Private Sub btnCustomerSearch_Click(sender As Object, e As EventArgs) Handles btnCustomerSearch.Click
        CustomerSearchForm = "frmSalesInvoiceTrx"
        frmCustomerSearch.ShowDialog()
    End Sub
    Private Sub btnNewCustomer_Click(sender As Object, e As EventArgs) Handles btnNewCustomer.Click
        IsNewCustomer = "frmSalesInvoiceTrx"
        frmCustomers.ShowDialog()
    End Sub



End Class