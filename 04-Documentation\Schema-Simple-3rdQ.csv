﻿STORED PROCEDURE,dbo,sp_AccountStatement,@AccountCode,bigint,8,19,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_AccountStatement,@FromDate,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_AccountStatement,@ToDate,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_AllocateOpenPaymentToInvoice,@InvoiceNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_AllocateOpenPaymentToInvoice,@PaymentJEID,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_AllocateOpenPaymentToInvoice,@ModifiedBy,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_AllocateOpenVendorPaymentToInvoice,@InvoiceNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_AllocateOpenVendorPaymentToInvoice,@PaymentJEID,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_AllocateOpenVendorPaymentToInvoice,@ModifiedBy,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CheckDuplicateVendorInvoice,@VendorNo,bigint,8,19,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CheckDuplicateVendorInvoice,@VendorInvoiceNo,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CheckDuplicateVendorInvoice,@CurrentInvoiceID,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CheckDuplicateVendorInvoice,@IsDuplicate,bit,1,1,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_CheckDuplicateVendorInvoice,@ExistingInvoiceID,int,4,10,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_CheckDuplicateVendorInvoice,@ExistingInvoiceDate,date,3,10,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_CheckDuplicateVendorInvoice,@ResultMessage,nvarchar,1000,0,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateCOGSEntryFromView,@InvoiceNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateCustomerPaymentForInvoice,@InvoiceNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateCustomerPaymentForInvoice,@Amount,decimal,9,18,2,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateCustomerPaymentForInvoice,@EntryDate,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateCustomerPaymentForInvoice,@CashAccountCode,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateCustomerPaymentForInvoice,@Description,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateExpenseInvoiceJournalEntry,@InvoiceID,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateExpenseInvoiceJournalEntry,@ResultMessage,nvarchar,1000,0,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashPaymentEntry,@TrxDate,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashPaymentEntry,@CashAccountCode,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashPaymentEntry,@ReceiverAccountCode,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashPaymentEntry,@Amount,decimal,9,18,2,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashPaymentEntry,@Notes,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashPaymentEntry,@CreatedBy,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashReceiptEntry,@TrxDate,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashReceiptEntry,@CashAccountCode,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashReceiptEntry,@PayerAccountCode,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashReceiptEntry,@Amount,decimal,9,18,2,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashReceiptEntry,@Notes,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualCashReceiptEntry,@CreatedBy,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualJEEntry,@TrxDate,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualJEEntry,@FromAccountCode,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualJEEntry,@ToAccountCode,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualJEEntry,@Amount,decimal,9,18,2,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualJEEntry,@Notes,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateManualJEEntry,@CreatedBy,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateOpeningStockEntry,@DocNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreatePaymentEntryFromView,@InvoiceNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreatePurchaseInvoiceEntryFromView,@InvoiceNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreatePurchaseReturnInvoiceEntryFromView,@InvoiceNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateReturnPaymentEntryFromView,@InvoiceNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateSalesInvoiceEntryFromView,@InvoiceNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateSalesReturnEntryFromView,@InvoiceNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateStockAdjustmentEntry,@DocNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateStockAdjustmentEntry,@EntryDate,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateStockAdjustmentEntry,@CreatedBy,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateStockReturnEntryFromView,@InvoiceNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateVendorPaymentEntry,@PaymentNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateVendorPaymentForInvoice,@InvoiceNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateVendorPaymentForInvoice,@Amount,decimal,9,18,2,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateVendorPaymentForInvoice,@EntryDate,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateVendorPaymentForInvoice,@CashAccountCode,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateVendorPaymentForInvoice,@Description,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_CreateVendorReturnPaymentEntry,@PaymentNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_DeleteJEEntry,@JEHeaderID,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_DeleteJEEntry,@DeleteReason,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_DeleteJEEntry,@DeletedBy,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_EndSession,@SessionToken,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_GetCustomerBalances,@AsOfDate,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_GetCustomerBalances,@CustomerNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_GetCustomerBalances,@SalesEmployee,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_GetVendorBalances,@AsOfDate,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_GetVendorBalances,@VendorNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_GrantPermissionWithInheritance,@GroupID,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_GrantPermissionWithInheritance,@FormID,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@Invoice_Date,date,3,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@CreditCashier,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@DebitExpense,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@Invoice_amount,decimal,9,18,2,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@Tax_amount,decimal,9,18,2,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@VendorNo,bigint,8,19,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@VendorInvoiceNo,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@VendorName,nvarchar,400,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@VendorVATRN,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@EmployeeBuyer,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@Notes,nvarchar,-1,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@Attached_photo,varbinary,-1,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@CreatedBy,nvarchar,-1,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@IgnoreDuplicate,bit,1,1,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@CreateJournalEntry,bit,1,1,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@NewInvoiceID,int,4,10,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@ResultMessage,nvarchar,1000,0,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_InsertExpenseInvoice,@DuplicateWarning,nvarchar,1000,0,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_LogSessionAttempt,@Username,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_LogSessionAttempt,@LoginStatus,nvarchar,40,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_LogSessionAttempt,@MachineName,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_LogSessionAttempt,@MachineUser,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_LogSessionAttempt,@SystemUsername,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_LogSessionAttempt,@IPAddress,nvarchar,90,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_LogSessionAttempt,@UserAgent,nvarchar,1000,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_LogSessionAttempt,@FailureReason,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_LogSessionAttempt,@SessionToken,nvarchar,510,0,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_ModifyJEEntry,@JEHeaderID,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_ModifyJEEntry,@NewEntryReference,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_ModifyJEEntry,@NewAmount,decimal,9,18,2,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_ModifyJEEntry,@ModifiedBy,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UpdateItemPricesByDocNo,@DocNo,int,4,10,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@CustomerNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@CustomerName,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@FirstName,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@LastName,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@Mobile,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@Phone,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@Email,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@StreetAddress1,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@StreetAddress2,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@City,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@Region,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@PostalCode,nvarchar,40,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@BuildingNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@AdditionalNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@District,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@PaymentMethod,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@CreditLimit,decimal,9,18,2,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@PaymentTerm,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@Contacts,nvarchar,-1,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@CR,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@VATReg,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@Shop,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@Status,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@Local,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@Employee,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@Notes,nvarchar,-1,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@CreatedBy,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@NewCustomerNo,int,4,10,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@ResultCode,int,4,10,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadCustomerMaster,@ResultMessage,nvarchar,510,0,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@VendorNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@VendorName,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@FirstName,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@LastName,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@Mobile,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@Phone,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@Email,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@StreetAddress1,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@StreetAddress2,nvarchar,510,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@City,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@Region,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@PostalCode,nvarchar,40,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@PaymentMethod,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@CreditLimit,decimal,9,18,2,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@PaymentTerm,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@ContactPerson,nvarchar,-1,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@CR,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@VATRegNo,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@Shop,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@Status,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@LocalVendor,nvarchar,100,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@Notes,nvarchar,-1,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@CreatedBy,nvarchar,200,0,0,YES,INPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@NewVendorNo,bigint,8,19,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@ResultCode,int,4,10,0,YES,OUTPUT,NULL,
STORED PROCEDURE,dbo,sp_UploadVendorMaster,@ResultMessage,nvarchar,510,0,0,YES,OUTPUT,NULL,
