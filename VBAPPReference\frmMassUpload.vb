﻿
Imports System.Data.OleDb
    Imports System.Data.SqlClient
    Imports System.IO
    Imports System.Drawing
    Imports System.ComponentModel

Public Class frmMassUpload

#Region "Variables and Constants"
    Private dtFileData As DataTable
    Private dtMappedData As DataTable
    Private dtValidatedData As DataTable
    ' Private connString As String = "" ' Your connection string here
    Private fileColumns As List(Of String)
    Private systemFields As Dictionary(Of String, FieldInfo)
    Private lookupTables As Dictionary(Of String, DataTable)
    Private uploadResults As List(Of UploadResult)

    ' Public username variable for audit fields
    Public Username As String = Environment.UserName

    ' System field definitions
    Private Structure FieldInfo
        Public DisplayName As String
        Public DatabaseColumn As String
        Public IsRequired As Boolean
        Public DataType As Type
        Public LookupTable As String
        Public LookupDisplayColumn As String
        Public LookupValueColumn As String
    End Structure

    Private Structure UploadResult
        Public RowIndex As Integer
        Public Status As String
        Public ItemCode As String
        Public ErrorMessage As String
        Public ProcessedAt As DateTime
    End Structure
#End Region

#Region "Form Events"
    Private Sub frmMassUpload_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeForm()
        LoadSystemFields()
        ' Don't load lookup tables immediately to avoid connection errors
        ' LoadLookupTables()

        ' Don't maximize - keep normal size
        Application.DoEvents()
        RefreshLayout()
    End Sub

    Private Sub frmMassUpload_Shown(sender As Object, e As EventArgs) Handles MyBase.Shown
        ' Additional layout refresh when form is shown
        RefreshLayout()
    End Sub

    Private Sub frmMassUpload_Resize(sender As Object, e As EventArgs) Handles MyBase.Resize
        ' Handle form resizing to prevent cut-off issues
        If Me.WindowState <> FormWindowState.Minimized Then
            RefreshLayout()
        End If
    End Sub

    Private Sub btnBrowseFile_Click(sender As Object, e As EventArgs) Handles btnBrowseFile.Click
        If ofdSelectFile.ShowDialog() = DialogResult.OK Then
            txtFilePath.Text = ofdSelectFile.FileName
            LoadFileData()
        End If
    End Sub

    Private Sub btnNext_Click(sender As Object, e As EventArgs) Handles btnNext.Click
        Select Case tcMain.SelectedIndex
            Case 0 ' File Selection
                If ValidateFileSelection() Then
                    ' Load lookup tables when moving to field mapping
                    If lookupTables.Count = 0 Then
                        LoadLookupTables()
                    End If
                    tcMain.SelectedIndex = 1
                    UpdateNavigationButtons()
                End If
            Case 1 ' Field Mapping
                If ValidateFieldMapping() Then
                    PrepareDataPreview()
                    tcMain.SelectedIndex = 2
                    UpdateNavigationButtons()
                End If
            Case 2 ' Data Preview
                If ValidateDataPreview() Then
                    tcMain.SelectedIndex = 3
                    UpdateNavigationButtons()
                End If
        End Select
    End Sub

    Private Sub btnPrevious_Click(sender As Object, e As EventArgs) Handles btnPrevious.Click
        If tcMain.SelectedIndex > 0 Then
            tcMain.SelectedIndex -= 1
            UpdateNavigationButtons()
        End If
    End Sub

    Private Sub btnUpload_Click(sender As Object, e As EventArgs) Handles btnUpload.Click
        If MessageBox.Show("هل أنت متأكد من رفع البيانات؟", "تأكيد الرفع",
                          MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            StartUploadProcess()
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub
#End Region

#Region "Initialization"
    Private Sub InitializeForm()
        ' Initialize form controls
        tcMain.SelectedIndex = 0
        UpdateNavigationButtons()

        ' Configure DataGridView properties
        ConfigureDataGridViews()

        ' Initialize data tables
        dtFileData = New DataTable()
        dtMappedData = New DataTable()
        dtValidatedData = New DataTable()

        ' Initialize collections
        fileColumns = New List(Of String)()
        systemFields = New Dictionary(Of String, FieldInfo)()
        lookupTables = New Dictionary(Of String, DataTable)()
        uploadResults = New List(Of UploadResult)()
    End Sub

    Private Sub RefreshLayout()
        Try
            ' Force refresh of the entire tab page without docking issues
            Me.SuspendLayout()
            tpFileSelection.SuspendLayout()

            ' Ensure proper positioning with anchoring
            gbFileSelection.Location = New Point(6, 6)
            gbFileSelection.Size = New Size(tpFileSelection.Width - 12, 95)

            gbFilePreview.Location = New Point(6, 107)
            gbFilePreview.Size = New Size(tpFileSelection.Width - 12, tpFileSelection.Height - 113)

            ' Update DataGridView size
            dgvFilePreview.Location = New Point(8, 25)
            dgvFilePreview.Size = New Size(gbFilePreview.Width - 16, gbFilePreview.Height - 33)

            ' Refresh the DataGridView
            dgvFilePreview.Invalidate()
            dgvFilePreview.Update()
            dgvFilePreview.Refresh()

            ' Resume layout
            tpFileSelection.ResumeLayout(True)
            Me.ResumeLayout(True)

            ' Force repaint
            Me.Invalidate()
            Me.Update()

        Catch ex As Exception
            ' Ignore layout errors
        End Try
    End Sub

    Private Sub ConfigureDataGridViews()
        ' Configure file preview grid
        With dgvFilePreview
            .AllowUserToAddRows = False
            .AllowUserToDeleteRows = False
            .ReadOnly = True
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect
            .MultiSelect = False
            .RowHeadersVisible = True
            .RowHeadersWidth = 120
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells
            .ScrollBars = ScrollBars.Both
            .ClipboardCopyMode = DataGridViewClipboardCopyMode.EnableWithoutHeaderText
        End With

        ' Configure field mapping grid
        With dgvFieldMapping
            .AllowUserToAddRows = False
            .AllowUserToDeleteRows = False
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect
            .MultiSelect = False
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        End With

        ' Configure data preview grid
        With dgvDataPreview
            .AllowUserToAddRows = False
            .AllowUserToDeleteRows = False
            .ReadOnly = True
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells
        End With

        ' Configure upload results grid
        With dgvUploadResults
            .AllowUserToAddRows = False
            .AllowUserToDeleteRows = False
            .ReadOnly = True
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells
        End With
    End Sub

    Private Sub LoadSystemFields()
        ' Define system fields based on the original form
        systemFields.Clear()

        ' Add system fields
        systemFields.Add("ItemNo", New FieldInfo With {
            .DisplayName = "رقم الصنف",
            .DatabaseColumn = "ItemNo",
            .IsRequired = True,
            .DataType = GetType(String)
        })

        systemFields.Add("ItemDescription", New FieldInfo With {
            .DisplayName = "الوصف العربي",
            .DatabaseColumn = "ItemDescription",
            .IsRequired = True,
            .DataType = GetType(String)
        })

        systemFields.Add("ItemDescription2", New FieldInfo With {
            .DisplayName = "الوصف الإنجليزي",
            .DatabaseColumn = "ItemDescription2",
            .IsRequired = False,
            .DataType = GetType(String)
        })

        systemFields.Add("UnitSalesPrice", New FieldInfo With {
            .DisplayName = "سعر البيع",
            .DatabaseColumn = "UnitSalesPrice",
            .IsRequired = False,
            .DataType = GetType(Decimal)
        })

        systemFields.Add("UnitPurchasePrice", New FieldInfo With {
            .DisplayName = "سعر الشراء",
            .DatabaseColumn = "UnitPurchasePrice",
            .IsRequired = False,
            .DataType = GetType(Decimal)
        })

        systemFields.Add("Barcode", New FieldInfo With {
            .DisplayName = "باركود الصنف",
            .DatabaseColumn = "Barcode",
            .IsRequired = False,
            .DataType = GetType(String)
        })

        systemFields.Add("Category", New FieldInfo With {
            .DisplayName = "الفئة",
            .DatabaseColumn = "CategoryID",
            .IsRequired = False,
            .DataType = GetType(Integer),
            .LookupTable = "Categories",
            .LookupDisplayColumn = "RootName",
            .LookupValueColumn = "RootID"
        })

        systemFields.Add("Brand", New FieldInfo With {
            .DisplayName = "الماركة",
            .DatabaseColumn = "Brand",
            .IsRequired = False,
            .DataType = GetType(String),
            .LookupTable = "Brands",
            .LookupDisplayColumn = "Brand_Text",
            .LookupValueColumn = "Brand_Text"
        })

        systemFields.Add("ItemType", New FieldInfo With {
            .DisplayName = "نوع الصنف",
            .DatabaseColumn = "ItemTypeID",
            .IsRequired = False,
            .DataType = GetType(Integer),
            .LookupTable = "ItemTypes",
            .LookupDisplayColumn = "RootName",
            .LookupValueColumn = "RootID"
        })

        systemFields.Add("UofM", New FieldInfo With {
            .DisplayName = "الوحدة الأساسية",
            .DatabaseColumn = "UofM",
            .IsRequired = False,
            .DataType = GetType(String),
            .LookupTable = "UnitsOfMeasure",
            .LookupDisplayColumn = "UofM",
            .LookupValueColumn = "UofM"
        })

        systemFields.Add("SalesUofM", New FieldInfo With {
            .DisplayName = "وحدة البيع",
            .DatabaseColumn = "SalesUofM",
            .IsRequired = False,
            .DataType = GetType(String),
            .LookupTable = "UnitsOfMeasure",
            .LookupDisplayColumn = "UofM",
            .LookupValueColumn = "UofM"
        })

        systemFields.Add("Tax", New FieldInfo With {
            .DisplayName = "الضريبة",
            .DatabaseColumn = "Tax",
            .IsRequired = False,
            .DataType = GetType(String),
            .LookupTable = "Taxes",
            .LookupDisplayColumn = "Tax_Percent",
            .LookupValueColumn = "Tax_Percent"
        })

        systemFields.Add("Shop", New FieldInfo With {
            .DisplayName = "المتجر",
            .DatabaseColumn = "Shop",
            .IsRequired = False,
            .DataType = GetType(String),
            .LookupTable = "Shops",
            .LookupDisplayColumn = "Shop_Text",
            .LookupValueColumn = "Shop_Text"
        })

        systemFields.Add("Notes", New FieldInfo With {
            .DisplayName = "ملاحظات",
            .DatabaseColumn = "Notes",
            .IsRequired = False,
            .DataType = GetType(String)
        })

        systemFields.Add("Photo", New FieldInfo With {
            .DisplayName = "الصورة",
            .DatabaseColumn = "PhotoPath",
            .IsRequired = False,
            .DataType = GetType(String)
        })

        systemFields.Add("EnableSN", New FieldInfo With {
            .DisplayName = "دعم رقم تسلسلي",
            .DatabaseColumn = "EnableSN",
            .IsRequired = False,
            .DataType = GetType(Boolean)
        })

        systemFields.Add("NegativeEnable", New FieldInfo With {
            .DisplayName = "سماح البيع بالسالب",
            .DatabaseColumn = "NegativeEnable",
            .IsRequired = False,
            .DataType = GetType(Boolean)
        })

        systemFields.Add("Inactive", New FieldInfo With {
            .DisplayName = "غير نشط",
            .DatabaseColumn = "Inactive",
            .IsRequired = False,
            .DataType = GetType(Boolean)
        })

        ' Alternative Units
        systemFields.Add("AUofM", New FieldInfo With {
            .DisplayName = "وحدة اضافية",
            .DatabaseColumn = "AUofM",
            .IsRequired = False,
            .DataType = GetType(String),
            .LookupTable = "UnitsOfMeasure",
            .LookupDisplayColumn = "UofM",
            .LookupValueColumn = "UofM"
        })

        systemFields.Add("AUofMX", New FieldInfo With {
            .DisplayName = "معامل التحويل 1",
            .DatabaseColumn = "AUofMX",
            .IsRequired = False,
            .DataType = GetType(Decimal)
        })

        systemFields.Add("SalesPriceAUofM", New FieldInfo With {
            .DisplayName = "سعر البيع للوحدة الاضافية",
            .DatabaseColumn = "SalesPriceAUofM",
            .IsRequired = False,
            .DataType = GetType(Decimal)
        })

        systemFields.Add("AUofM2", New FieldInfo With {
            .DisplayName = "وحدة اضافية 2",
            .DatabaseColumn = "AUofM2",
            .IsRequired = False,
            .DataType = GetType(String),
            .LookupTable = "UnitsOfMeasure",
            .LookupDisplayColumn = "UofM",
            .LookupValueColumn = "UofM"
        })

        systemFields.Add("AUofMX2", New FieldInfo With {
            .DisplayName = "معامل التحويل 2",
            .DatabaseColumn = "AUofMX2",
            .IsRequired = False,
            .DataType = GetType(Decimal)
        })

        systemFields.Add("SalesPriceAUofM2", New FieldInfo With {
            .DisplayName = "سعر البيع للوحدة الاضافية 2",
            .DatabaseColumn = "SalesPriceAUofM2",
            .IsRequired = False,
            .DataType = GetType(Decimal)
        })

        systemFields.Add("AUofM3", New FieldInfo With {
            .DisplayName = "وحدة اضافية 3",
            .DatabaseColumn = "AUofM3",
            .IsRequired = False,
            .DataType = GetType(String),
            .LookupTable = "UnitsOfMeasure",
            .LookupDisplayColumn = "UofM",
            .LookupValueColumn = "UofM"
        })

        systemFields.Add("AUofMX3", New FieldInfo With {
            .DisplayName = "معامل التحويل 3",
            .DatabaseColumn = "AUofMX3",
            .IsRequired = False,
            .DataType = GetType(Decimal)
        })

        systemFields.Add("SalesPriceAUofM3", New FieldInfo With {
            .DisplayName = "سعر البيع للوحدة الاضافية 3",
            .DatabaseColumn = "SalesPriceAUofM3",
            .IsRequired = False,
            .DataType = GetType(Decimal)
        })

        PopulateFieldMappingGrid()
    End Sub

    Private Sub LoadLookupTables()
        ' Only load lookup tables if connection string is configured
        If String.IsNullOrEmpty(Constr) Then
            ' Create dummy lookup tables for demo purposes
            CreateDummyLookupTables()
            Return
        End If

        Try
            Using conn As New SqlConnection(Constr)
                conn.Open()
                ' Load Categories
                LoadLookupTable(conn, "Categories", "SELECT RootID,RootName FROM tblItemsCategory where ParentID = 1 ORDER BY RootName")
                ' Load Brands
                LoadLookupTable(conn, "Brands", "select Brand_Text from tblbrands order by Brand_Text")
                ' Load Item Types
                LoadLookupTable(conn, "ItemTypes", "SELECT RootID,RootName FROM tblItemsCategory where ParentID = 0 ORDER BY RootName")
                ' Load Units of Measure
                LoadLookupTable(conn, "UnitsOfMeasure", "SELECT UofM FROM tblUofM ORDER BY UofM")
                ' Load Taxes
                LoadLookupTable(conn, "Taxes", "select Tax_Percent from tblTax order by Tax_Percent desc")
                ' Load Shops
                LoadLookupTable(conn, "Shops", "select Shop_Text from tblShops order by Shop_Text")
            End Using
        Catch ex As Exception
            ' If database connection fails, create dummy data for testing
            CreateDummyLookupTables()
            Console.WriteLine(String.Format("خطأ في تحميل الجداول المرجعية: {0}", ex.Message))
        End Try
    End Sub

    Private Sub CreateDummyLookupTables()
        ' Create dummy lookup tables for testing without database

        ' Categories
        Dim categoriesTable As New DataTable("Categories")
        categoriesTable.Columns.Add("CategoryID", GetType(Integer))
        categoriesTable.Columns.Add("CategoryName", GetType(String))
        categoriesTable.Rows.Add(1, "إلكترونيات")
        categoriesTable.Rows.Add(2, "ملابس")
        categoriesTable.Rows.Add(3, "طعام")
        lookupTables("Categories") = categoriesTable

        ' Brands
        Dim brandsTable As New DataTable("Brands")
        brandsTable.Columns.Add("BrandID", GetType(Integer))
        brandsTable.Columns.Add("BrandName", GetType(String))
        brandsTable.Rows.Add(1, "سامسونج")
        brandsTable.Rows.Add(2, "آبل")
        brandsTable.Rows.Add(3, "هواوي")
        lookupTables("Brands") = brandsTable

        ' Item Types
        Dim itemTypesTable As New DataTable("ItemTypes")
        itemTypesTable.Columns.Add("ItemTypeID", GetType(Integer))
        itemTypesTable.Columns.Add("ItemTypeName", GetType(String))
        itemTypesTable.Rows.Add(1, "منتج")
        itemTypesTable.Rows.Add(2, "خدمة")
        lookupTables("ItemTypes") = itemTypesTable

        ' Units of Measure
        Dim unitsTable As New DataTable("UnitsOfMeasure")
        unitsTable.Columns.Add("UofM", GetType(String))
        unitsTable.Rows.Add("عدد")
        unitsTable.Rows.Add("كيلو")
        unitsTable.Rows.Add("متر")
        unitsTable.Rows.Add("لتر")
        unitsTable.Rows.Add("صندوق")
        unitsTable.Rows.Add("حبة")
        lookupTables("UnitsOfMeasure") = unitsTable

        ' Taxes
        Dim taxesTable As New DataTable("Taxes")
        taxesTable.Columns.Add("TaxID", GetType(Integer))
        taxesTable.Columns.Add("TaxName", GetType(String))
        taxesTable.Rows.Add(1, "ضريبة القيمة المضافة 15%")
        taxesTable.Rows.Add(2, "معفى من الضريبة")
        lookupTables("Taxes") = taxesTable

        ' Shops
        Dim shopsTable As New DataTable("Shops")
        shopsTable.Columns.Add("ShopID", GetType(Integer))
        shopsTable.Columns.Add("ShopName", GetType(String))
        shopsTable.Rows.Add(1, "الفرع الرئيسي")
        shopsTable.Rows.Add(2, "فرع النسيم")
        lookupTables("Shops") = shopsTable
    End Sub

    Private Sub LoadLookupTable(conn As SqlConnection, tableName As String, query As String)
        Try
            Dim dt As New DataTable()
            Using cmd As New SqlCommand(query, conn)
                Using adapter As New SqlDataAdapter(cmd)
                    adapter.Fill(dt)
                End Using
            End Using
            lookupTables(tableName) = dt
        Catch ex As Exception
            MessageBox.Show(String.Format("خطأ في تحميل جدول {0}: {1}", tableName, ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
#End Region

#Region "File Operations"
    Private Sub LoadFileData()
        Try
            dtFileData.Clear()
            fileColumns.Clear()

            Dim extension As String = Path.GetExtension(txtFilePath.Text).ToLower()

            Select Case extension
                Case ".csv"
                    LoadCSVFile()
                Case ".xls", ".xlsx"
                    LoadExcelFile()
                Case Else
                    MessageBox.Show("نوع ملف غير مدعوم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Return
            End Select

            ' Update file info
            lblFileInfo.Text = String.Format("تم تحميل {0} سجل من الملف", dtFileData.Rows.Count)
            lblFileInfo.ForeColor = Color.Green

            ' Create preview table with headers as first row
            CreateFilePreviewTable()

            ' Update file column dropdown in mapping
            PopulateFileColumnDropdown()

            ' Enable next button
            btnNext.Enabled = dtFileData.Rows.Count > 0

            ' Force layout refresh after a short delay
            System.Threading.Tasks.Task.Delay(100).ContinueWith(Sub() Me.Invoke(Sub() RefreshLayout()))

        Catch ex As Exception
            MessageBox.Show(String.Format("خطأ في قراءة الملف: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            lblFileInfo.Text = "خطأ في قراءة الملف"
            lblFileInfo.ForeColor = Color.Red
        End Try
    End Sub

    Private Sub CreateFilePreviewTable()
        ' Create a new table for preview that includes headers as first row
        Dim previewTable As New DataTable()

        ' Add generic column names for preview
        For i As Integer = 0 To fileColumns.Count - 1
            previewTable.Columns.Add(String.Format("Column{0}", i + 1), GetType(String))
        Next

        ' Add header row as first data row
        If fileColumns.Count > 0 Then
            Dim headerRow As DataRow = previewTable.NewRow()
            For i As Integer = 0 To fileColumns.Count - 1
                headerRow(i) = fileColumns(i)
            Next
            previewTable.Rows.Add(headerRow)
        End If

        ' Add data rows (limit to first 100 for performance)
        Dim rowLimit As Integer = Math.Min(dtFileData.Rows.Count, 100)
        For i As Integer = 0 To rowLimit - 1
            Dim previewRow As DataRow = previewTable.NewRow()
            For j As Integer = 0 To Math.Min(dtFileData.Columns.Count - 1, previewTable.Columns.Count - 1)
                previewRow(j) = If(dtFileData.Rows(i)(j) IsNot DBNull.Value, dtFileData.Rows(i)(j).ToString(), "")
            Next
            previewTable.Rows.Add(previewRow)
        Next

        ' Bind to preview grid
        dgvFilePreview.DataSource = Nothing
        dgvFilePreview.DataSource = previewTable

        ' Force refresh and layout
        dgvFilePreview.SuspendLayout()
        dgvFilePreview.Invalidate()
        dgvFilePreview.Update()
        dgvFilePreview.Refresh()
        dgvFilePreview.ResumeLayout(True)

        ' Clear any selection
        dgvFilePreview.ClearSelection()

        ' Update column headers to show the actual file column names
        For i As Integer = 0 To Math.Min(fileColumns.Count - 1, dgvFilePreview.Columns.Count - 1)
            dgvFilePreview.Columns(i).HeaderText = String.Format("عمود {0}: {1}", i + 1, fileColumns(i))
            dgvFilePreview.Columns(i).ToolTipText = fileColumns(i)
            dgvFilePreview.Columns(i).AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells
        Next

        ' Color the header row differently and make it more prominent
        If dgvFilePreview.Rows.Count > 0 Then
            With dgvFilePreview.Rows(0)
                .DefaultCellStyle.BackColor = Color.Navy
                .DefaultCellStyle.ForeColor = Color.White
                .DefaultCellStyle.Font = New Font(dgvFilePreview.DefaultCellStyle.Font, FontStyle.Bold)
                .Height = 25
                .HeaderCell.Value = "رؤوس الأعمدة"
                .HeaderCell.Style.BackColor = Color.DarkBlue
                .HeaderCell.Style.ForeColor = Color.White
            End With
        End If

        ' Add row numbers for data rows
        For i As Integer = 1 To dgvFilePreview.Rows.Count - 1
            If i < dgvFilePreview.Rows.Count Then
                dgvFilePreview.Rows(i).HeaderCell.Value = String.Format("سجل {0}", i)
            End If
        Next

        ' Ensure first row is visible and scroll to top
        If dgvFilePreview.Rows.Count > 0 Then
            dgvFilePreview.FirstDisplayedScrollingRowIndex = 0
            dgvFilePreview.CurrentCell = Nothing
        End If

        ' Force the parent form to refresh
        Me.Refresh()
        Application.DoEvents()
    End Sub

    Private Sub LoadCSVFile()
        Using reader As New StreamReader(txtFilePath.Text, System.Text.Encoding.UTF8)
            Dim lines As New List(Of String)()
            While Not reader.EndOfStream
                lines.Add(reader.ReadLine())
            End While

            If lines.Count = 0 Then Return

            ' Parse header
            Dim headers As String() = ParseCSVLine(lines(0))
            For Each header In headers
                dtFileData.Columns.Add(header.Trim())
                fileColumns.Add(header.Trim())
            Next

            ' Parse data rows
            For i As Integer = 1 To lines.Count - 1
                If Not String.IsNullOrWhiteSpace(lines(i)) Then
                    Dim values As String() = ParseCSVLine(lines(i))
                    Dim row As DataRow = dtFileData.NewRow()

                    For j As Integer = 0 To Math.Min(values.Length - 1, dtFileData.Columns.Count - 1)
                        row(j) = values(j).Trim()
                    Next

                    dtFileData.Rows.Add(row)
                End If
            Next
        End Using
    End Sub

    Private Function ParseCSVLine(line As String) As String()
        Dim result As New List(Of String)()
        Dim current As String = ""
        Dim inQuotes As Boolean = False

        For i As Integer = 0 To line.Length - 1
            Dim c As Char = line(i)

            If c = """"c Then
                inQuotes = Not inQuotes
            ElseIf c = ","c And Not inQuotes Then
                result.Add(current)
                current = ""
            Else
                current += c
            End If
        Next

        result.Add(current)
        Return result.ToArray()
    End Function

    Private Sub LoadExcelFile()
        Dim connectionString As String = ""

        If Path.GetExtension(txtFilePath.Text).ToLower() = ".xlsx" Then
            connectionString = String.Format("Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties=""Excel 12.0 Xml;HDR=Yes;IMEX=1""", txtFilePath.Text)
        Else
            connectionString = String.Format("Provider=Microsoft.Jet.OLEDB.4.0;Data Source={0};Extended Properties=""Excel 8.0;HDR=Yes;IMEX=1""", txtFilePath.Text)
        End If

        Using conn As New OleDbConnection(connectionString)
            conn.Open()
            Dim schemaTable As DataTable = conn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, Nothing)

            If schemaTable.Rows.Count > 0 Then
                Dim sheetName As String = schemaTable.Rows(0)("TABLE_NAME").ToString()
                Dim query As String = String.Format("SELECT * FROM [{0}]", sheetName)

                Using adapter As New OleDbDataAdapter(query, conn)
                    adapter.Fill(dtFileData)
                End Using

                ' Populate file columns
                For Each column As DataColumn In dtFileData.Columns
                    fileColumns.Add(column.ColumnName)
                Next
            End If
        End Using
    End Sub
#End Region

#Region "Field Mapping"
    Private Sub PopulateFieldMappingGrid()
        dgvFieldMapping.Rows.Clear()

        For Each kvp In systemFields
            Dim row As Integer = dgvFieldMapping.Rows.Add()
            With dgvFieldMapping.Rows(row)
                .Cells("colSystemField").Value = kvp.Value.DisplayName
                .Cells("colSystemField").Tag = kvp.Key
                .Cells("colMappingType").Value = "تجاهل"
                .Cells("colRequired").Value = kvp.Value.IsRequired

                ' Set status icon based on required field
                If kvp.Value.IsRequired Then
                    .Cells("colStatus").Value = GetStatusIcon("Required")
                Else
                    .Cells("colStatus").Value = GetStatusIcon("Optional")
                End If
            End With
        Next
    End Sub

    Private Sub PopulateFileColumnDropdown()
        Dim fileColumnCombo As DataGridViewComboBoxColumn = CType(dgvFieldMapping.Columns("colFileColumn"), DataGridViewComboBoxColumn)
        fileColumnCombo.Items.Clear()
        fileColumnCombo.Items.Add("") ' Empty option

        ' Add file columns with their index for clarity
        For i As Integer = 0 To fileColumns.Count - 1
            Dim displayText As String = String.Format("{0} ({1})", fileColumns(i), i + 1)
            fileColumnCombo.Items.Add(displayText)
        Next

        ' Also update the display text to show original column names
        For Each item As String In fileColumns
            If Not fileColumnCombo.Items.Contains(item) Then
                fileColumnCombo.Items.Add(item)
            End If
        Next
    End Sub

    Private Sub btnAutoMap_Click(sender As Object, e As EventArgs) Handles btnAutoMap.Click
        For Each row As DataGridViewRow In dgvFieldMapping.Rows
            If row.IsNewRow Then Continue For

            Dim systemFieldKey As String = row.Cells("colSystemField").Tag.ToString()
            Dim systemField As FieldInfo = systemFields(systemFieldKey)

            ' Try to find matching column in file
            Dim matchingColumn As String = FindMatchingColumn(systemFieldKey, systemField.DisplayName)

            If Not String.IsNullOrEmpty(matchingColumn) Then
                row.Cells("colFileColumn").Value = matchingColumn
                row.Cells("colMappingType").Value = "من الملف"
                row.Cells("colStatus").Value = GetStatusIcon("Mapped")
            End If
        Next

        MessageBox.Show("تم تطبيق الربط التلقائي", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Function FindMatchingColumn(systemKey As String, displayName As String) As String
        ' Define common mapping patterns
        Dim patterns As New Dictionary(Of String, String()) From {
            {"ItemNo", {"ProductCode", "ItemCode", "Code", "رقم", "كود"}},
            {"ItemDescription", {"Name", "Description", "اسم", "وصف", "الاسم"}},
            {"ItemDescription2", {"EnglishName", "EnglishDescription", "Name_EN"}},
            {"UnitSalesPrice", {"Price", "SalesPrice", "UnitPrice", "سعر"}},
            {"UnitPurchasePrice", {"BuyPrice", "PurchasePrice", "CostPrice", "تكلفة"}},
            {"Barcode", {"Barcode", "باركود"}},
            {"Category", {"Category", "فئة", "تصنيف"}},
            {"Brand", {"Brand", "ماركة", "علامة"}},
            {"Notes", {"Notes", "ملاحظات", "تعليق"}}
        }

        If patterns.ContainsKey(systemKey) Then
            For Each pattern In patterns(systemKey)
                For Each fileColumn In fileColumns
                    If fileColumn.ToLower().Contains(pattern.ToLower()) Then
                        Return fileColumn
                    End If
                Next
            Next
        End If

        Return ""
    End Function

    Private Sub btnClearMapping_Click(sender As Object, e As EventArgs) Handles btnClearMapping.Click
        For Each row As DataGridViewRow In dgvFieldMapping.Rows
            If row.IsNewRow Then Continue For

            row.Cells("colFileColumn").Value = ""
            row.Cells("colMappingType").Value = "تجاهل"
            row.Cells("colFixedValue").Value = ""

            Dim isRequired As Boolean = CBool(row.Cells("colRequired").Value)
            row.Cells("colStatus").Value = If(isRequired, GetStatusIcon("Required"), GetStatusIcon("Optional"))
        Next
    End Sub

    Private Function GetStatusIcon(status As String) As Image
        ' Create simple status icons (you can replace with actual icons)
        Dim icon As New Bitmap(16, 16)
        Using g As Graphics = Graphics.FromImage(icon)
            Select Case status
                Case "Required"
                    g.FillEllipse(Brushes.Red, 2, 2, 12, 12)
                Case "Optional"
                    g.FillEllipse(Brushes.Gray, 2, 2, 12, 12)
                Case "Mapped"
                    g.FillEllipse(Brushes.Green, 2, 2, 12, 12)
                Case "Error"
                    g.FillEllipse(Brushes.Orange, 2, 2, 12, 12)
            End Select
        End Using
        Return icon
    End Function
#End Region

#Region "Data Processing"
    Private Sub PrepareDataPreview()
        Try
            dtMappedData = CreateMappedDataTable()
            ProcessFileData()
            dgvDataPreview.DataSource = dtMappedData
            UpdateDataStatistics()
        Catch ex As Exception
            MessageBox.Show(String.Format("خطأ في تحضير البيانات: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function CreateMappedDataTable() As DataTable
        Dim dt As New DataTable()

        ' Add system columns
        For Each kvp In systemFields
            Dim column As New DataColumn(kvp.Key, kvp.Value.DataType)
            dt.Columns.Add(column)
        Next

        ' Add metadata columns
        dt.Columns.Add("_RowIndex", GetType(Integer))
        dt.Columns.Add("_Status", GetType(String))
        dt.Columns.Add("_Errors", GetType(String))
        dt.Columns.Add("_IsUpdate", GetType(Boolean))

        Return dt
    End Function

    Private Sub ProcessFileData()
        For rowIndex As Integer = 0 To dtFileData.Rows.Count - 1
            Dim sourceRow As DataRow = dtFileData.Rows(rowIndex)
            Dim mappedRow As DataRow = dtMappedData.NewRow()
            Dim errors As New List(Of String)()

            ' Set metadata
            mappedRow("_RowIndex") = rowIndex + 1
            mappedRow("_Status") = "جاري المعالجة"
            mappedRow("_IsUpdate") = False

            ' Process each mapped field
            For Each mappingRow As DataGridViewRow In dgvFieldMapping.Rows
                If mappingRow.IsNewRow Then Continue For

                Dim systemFieldKey As String = If(mappingRow.Cells("colSystemField").Tag IsNot Nothing, mappingRow.Cells("colSystemField").Tag.ToString(), "")
                Dim mappingType As String = If(mappingRow.Cells("colMappingType").Value IsNot Nothing, mappingRow.Cells("colMappingType").Value.ToString(), "تجاهل")
                Dim fileColumn As String = If(mappingRow.Cells("colFileColumn").Value IsNot Nothing, mappingRow.Cells("colFileColumn").Value.ToString(), "")
                Dim fixedValue As String = If(mappingRow.Cells("colFixedValue").Value IsNot Nothing, mappingRow.Cells("colFixedValue").Value.ToString(), "")

                If String.IsNullOrEmpty(systemFieldKey) OrElse Not systemFields.ContainsKey(systemFieldKey) Then
                    Continue For
                End If

                Dim fieldInfo As FieldInfo = systemFields(systemFieldKey)

                Try
                    Select Case mappingType
                        Case "من الملف"
                            If Not String.IsNullOrEmpty(fileColumn) AndAlso dtFileData.Columns.Contains(fileColumn) Then
                                Dim value = sourceRow(fileColumn)
                                mappedRow(systemFieldKey) = ProcessFieldValue(value, fieldInfo, errors)
                            End If

                        Case "قيمة ثابتة"
                            If Not String.IsNullOrEmpty(fixedValue) Then
                                mappedRow(systemFieldKey) = ProcessFieldValue(fixedValue, fieldInfo, errors)
                            End If
                    End Select
                Catch ex As Exception
                    errors.Add($"خطأ في حقل {fieldInfo.DisplayName}: {ex.Message}")
                End Try
            Next

            ' Apply default values and additional processing
            ApplyDefaultValues(mappedRow)

            ' Check if this is an update or insert
            CheckIfUpdate(mappedRow)

            ' Set final status
            mappedRow("_Errors") = String.Join("; ", errors)
            mappedRow("_Status") = If(errors.Count > 0, "خطأ", "جاهز")

            dtMappedData.Rows.Add(mappedRow)
        Next
    End Sub

    Private Function ProcessFieldValue(value As Object, fieldInfo As FieldInfo, errors As List(Of String)) As Object
        If value Is Nothing OrElse value Is DBNull.Value OrElse String.IsNullOrWhiteSpace(value.ToString()) Then
            If fieldInfo.IsRequired Then
                errors.Add(String.Format("الحقل {0} مطلوب", fieldInfo.DisplayName))
            End If
            Return DBNull.Value
        End If

        Dim stringValue As String = value.ToString().Trim()

        Try
            ' Handle lookup fields
            If Not String.IsNullOrEmpty(fieldInfo.LookupTable) Then
                Return ProcessLookupValue(stringValue, fieldInfo, errors)
            End If

            ' Convert to appropriate data type
            Select Case fieldInfo.DataType
                Case GetType(String)
                    Return stringValue
                Case GetType(Integer)
                    Dim intValue As Integer
                    If Integer.TryParse(stringValue, intValue) Then
                        Return intValue
                    Else
                        errors.Add(String.Format("قيمة غير صالحة للحقل {0}: {1}", fieldInfo.DisplayName, stringValue))
                        Return DBNull.Value
                    End If
                Case GetType(Decimal)
                    Dim decValue As Decimal
                    If Decimal.TryParse(stringValue, decValue) Then
                        Return decValue
                    Else
                        errors.Add(String.Format("قيمة رقمية غير صالحة للحقل {0}: {1}", fieldInfo.DisplayName, stringValue))
                        Return DBNull.Value
                    End If
                Case GetType(Boolean)
                    Select Case stringValue.ToLower()
                        Case "true", "1", "yes", "نعم", "صحيح"
                            Return True
                        Case "false", "0", "no", "لا", "خطأ"
                            Return False
                        Case Else
                            errors.Add(String.Format("قيمة منطقية غير صالحة للحقل {0}: {1}", fieldInfo.DisplayName, stringValue))
                            Return DBNull.Value
                    End Select
                Case Else
                    Return stringValue
            End Select

        Catch ex As Exception
            errors.Add(String.Format("خطأ في معالجة {0}: {1}", fieldInfo.DisplayName, ex.Message))
            Return DBNull.Value
        End Try
    End Function

    Private Function ProcessLookupValue(value As String, fieldInfo As FieldInfo, errors As List(Of String)) As Object
        If Not lookupTables.ContainsKey(fieldInfo.LookupTable) Then
            errors.Add(String.Format("جدول مرجعي غير موجود: {0}", fieldInfo.LookupTable))
            Return DBNull.Value
        End If

        Dim lookupTable As DataTable = lookupTables(fieldInfo.LookupTable)
        Dim rows As DataRow() = {}

        Select Case fieldInfo.LookupTable
            Case "Categories", "ItemTypes"
                ' These tables have ID columns - need to return ID
                ' Try to find by display name first
                rows = lookupTable.Select(String.Format("{0} = '{1}'", fieldInfo.LookupDisplayColumn, value.Replace("'", "''")))

                If rows.Length = 0 Then
                    ' Try to find by ID if it's numeric
                    Dim idValue As Integer
                    If Integer.TryParse(value, idValue) Then
                        rows = lookupTable.Select(String.Format("{0} = {1}", fieldInfo.LookupValueColumn, idValue))
                    End If
                End If

                If rows.Length > 0 Then
                    Return rows(0)(fieldInfo.LookupValueColumn) ' Return the ID
                End If

            Case "Brands", "UnitsOfMeasure", "Taxes", "Shops"
                ' These tables store text directly - return text as-is
                rows = lookupTable.Select(String.Format("{0} = '{1}'", fieldInfo.LookupDisplayColumn, value.Replace("'", "''")))

                If rows.Length > 0 Then
                    Return rows(0)(fieldInfo.LookupValueColumn) ' Return the text value
                Else
                    ' For text fields, if exact match not found, check if the value itself exists in the table
                    For Each row As DataRow In lookupTable.Rows
                        If row(fieldInfo.LookupDisplayColumn).ToString().Trim().Equals(value.Trim(), StringComparison.OrdinalIgnoreCase) Then
                            Return value.Trim() ' Return the original value if close match found
                        End If
                    Next
                End If
        End Select

        ' If no match found, add error
        errors.Add(String.Format("قيمة غير موجودة في {0}: {1}", fieldInfo.DisplayName, value))
        Return DBNull.Value
    End Function

    Private Sub ApplyDefaultValues(row As DataRow)
        ' Apply default unit if not specified
        If row("UofM") Is DBNull.Value Then
            Dim defaultUnit = GetDefaultUnit()
            If defaultUnit IsNot Nothing Then
                row("UofM") = defaultUnit
            End If
        End If

        ' Set sales unit same as base unit if not specified
        If row("SalesUofM") Is DBNull.Value AndAlso row("UofM") IsNot DBNull.Value Then
            row("SalesUofM") = row("UofM")
        End If

        ' Set default boolean values
        If row("EnableSN") Is DBNull.Value Then row("EnableSN") = False
        If row("NegativeEnable") Is DBNull.Value Then row("NegativeEnable") = False
        If row("Inactive") Is DBNull.Value Then row("Inactive") = False
    End Sub

    Private Function GetDefaultUnit() As Object
        If lookupTables.ContainsKey("UnitsOfMeasure") Then
            Dim unitTable As DataTable = lookupTables("UnitsOfMeasure")
            Dim rows As DataRow() = unitTable.Select("UofM = 'عدد'")
            If rows.Length > 0 Then
                Return rows(0)("UofM")
            ElseIf unitTable.Rows.Count > 0 Then
                Return unitTable.Rows(0)("UofM")
            End If
        End If
        Return "عدد" ' Default fallback
    End Function

    Private Sub CheckIfUpdate(row As DataRow)
        If row("ItemNo") Is DBNull.Value OrElse String.IsNullOrEmpty(row("ItemNo").ToString()) Then
            Return
        End If

        Try
            Using conn As New SqlConnection(Constr)
                conn.Open()
                Dim query As String = "SELECT COUNT(*) FROM Items WHERE ItemNo = @ItemNo"
                Using cmd As New SqlCommand(query, conn)
                    cmd.Parameters.AddWithValue("@ItemNo", row("ItemNo"))
                    Dim count As Integer = CInt(cmd.ExecuteScalar())
                    row("_IsUpdate") = (count > 0)
                End Using
            End Using
        Catch ex As Exception
            ' If can't check, assume insert
            row("_IsUpdate") = False
        End Try
    End Sub
#End Region

#Region "Data Validation"
    Private Sub btnValidateData_Click(sender As Object, e As EventArgs) Handles btnValidateData.Click
        ValidateAllData()
    End Sub

    Private Sub ValidateAllData()
        Try
            For Each row As DataRow In dtMappedData.Rows
                Dim errors As New List(Of String)()

                ' Validate required fields
                If chkValidateRequired.Checked Then
                    ValidateRequiredFields(row, errors)
                End If

                ' Validate lookup values
                If chkValidateLookups.Checked Then
                    ValidateLookupFields(row, errors)
                End If

                ' Validate photos
                If chkValidatePhotos.Checked Then
                    ValidatePhotoField(row, errors)
                End If

                ' Update row status
                row("_Errors") = String.Join("; ", errors)
                row("_Status") = If(errors.Count > 0, "خطأ", "جاهز")
            Next

            UpdateDataStatistics()
            MessageBox.Show("تم التحقق من البيانات", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show(String.Format("خطأ في التحقق من البيانات: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ValidateRequiredFields(row As DataRow, errors As List(Of String))
        For Each kvp In systemFields
            If kvp.Value.IsRequired Then
                If row(kvp.Key) Is DBNull.Value OrElse String.IsNullOrWhiteSpace(row(kvp.Key).ToString()) Then
                    errors.Add(String.Format("الحقل المطلوب {0} فارغ", kvp.Value.DisplayName))
                End If
            End If
        Next
    End Sub

    Private Sub ValidateLookupFields(row As DataRow, errors As List(Of String))
        For Each kvp In systemFields
            If Not String.IsNullOrEmpty(kvp.Value.LookupTable) Then
                If row(kvp.Key) IsNot DBNull.Value AndAlso Not String.IsNullOrEmpty(row(kvp.Key).ToString()) Then
                    If Not ValidateLookupValue(row(kvp.Key), kvp.Value) Then
                        errors.Add(String.Format("قيمة غير صالحة في {0}", kvp.Value.DisplayName))
                    End If
                End If
            End If
        Next
    End Sub

    Private Function ValidateLookupValue(value As Object, fieldInfo As FieldInfo) As Boolean
        If Not lookupTables.ContainsKey(fieldInfo.LookupTable) Then
            Return False
        End If

        Dim lookupTable As DataTable = lookupTables(fieldInfo.LookupTable)

        ' For units table, check the UofM column directly
        If fieldInfo.LookupTable = "UnitsOfMeasure" Then
            Dim rows As DataRow() = lookupTable.Select(String.Format("UofM = '{0}'", value.ToString().Replace("'", "''")))
            Return rows.Length > 0
        Else
            ' For other lookup tables with ID columns
            Dim rows As DataRow() = lookupTable.Select(String.Format("{0} = {1}", fieldInfo.LookupValueColumn, value))
            Return rows.Length > 0
        End If
    End Function

    Private Sub ValidatePhotoField(row As DataRow, errors As List(Of String))
        If row("Photo") Is DBNull.Value OrElse String.IsNullOrEmpty(row("Photo").ToString()) Then
            Return
        End If

        Dim photoPath As String = row("Photo").ToString()

        ' Check if it's a URL
        If photoPath.StartsWith("http://") OrElse photoPath.StartsWith("https://") Then
            ' For URLs, we could validate accessibility (optional)
            Return
        End If

        ' Check if it's a local file path
        If Not File.Exists(photoPath) Then
            errors.Add("مسار الصورة غير موجود")
        End If
    End Sub

    Private Sub UpdateDataStatistics()
        Dim totalRecords As Integer = dtMappedData.Rows.Count
        Dim validRecords As Integer = dtMappedData.Select("_Status = 'جاهز'").Length
        Dim invalidRecords As Integer = totalRecords - validRecords
        Dim updateRecords As Integer = dtMappedData.Select("_IsUpdate = True AND _Status = 'جاهز'").Length
        Dim insertRecords As Integer = validRecords - updateRecords

        lblTotalRecords.Text = String.Format("إجمالي السجلات: {0}", totalRecords)
        lblValidRecords.Text = String.Format("السجلات الصحيحة: {0}", validRecords)
        lblInvalidRecords.Text = String.Format("السجلات الخاطئة: {0}", invalidRecords)
        lblUpdateRecords.Text = String.Format("سجلات للتحديث: {0}", updateRecords)
        lblInsertRecords.Text = String.Format("سجلات للإضافة: {0}", insertRecords)

        ' Enable upload button if there are valid records
        btnUpload.Enabled = validRecords > 0
    End Sub
#End Region

#Region "Upload Process"
    Private Sub StartUploadProcess()
        ' Prepare upload
        uploadResults.Clear()

        ' Configure background worker
        bgwUpload.WorkerReportsProgress = True
        bgwUpload.WorkerSupportsCancellation = True

        ' Disable controls during upload
        SetControlsEnabled(False)

        ' Start upload
        bgwUpload.RunWorkerAsync()
    End Sub

    Private Sub SetControlsEnabled(enabled As Boolean)
        tcMain.Enabled = enabled
        btnNext.Enabled = enabled
        btnPrevious.Enabled = enabled
        btnUpload.Enabled = enabled And dtMappedData.Select("_Status = 'جاهز'").Length > 0
    End Sub

    Private Sub bgwUpload_DoWork(sender As Object, e As DoWorkEventArgs) Handles bgwUpload.DoWork
        Dim worker As BackgroundWorker = CType(sender, BackgroundWorker)
        Dim validRows As DataRow() = dtMappedData.Select("_Status = 'جاهز'")
        Dim totalRows As Integer = validRows.Length
        Dim batchSize As Integer = CInt(nudBatchSize.Value)
        Dim processedCount As Integer = 0

        Try
            ' Create backup if requested
            If chkBackupBeforeUpload.Checked Then
                worker.ReportProgress(0, "إنشاء نسخة احتياطية...")
                CreateBackup()
            End If

            ' Process in batches
            For i As Integer = 0 To validRows.Length - 1 Step batchSize
                If worker.CancellationPending Then
                    e.Cancel = True
                    Return
                End If

                Dim batchEnd As Integer = Math.Min(i + batchSize - 1, validRows.Length - 1)
                Dim currentBatch As DataRow() = validRows.Skip(i).Take(batchEnd - i + 1).ToArray()

                ' Process batch
                ProcessBatch(currentBatch, worker)

                processedCount += currentBatch.Length
                Dim progressPercent As Integer = CInt((processedCount / totalRows) * 100)
                worker.ReportProgress(progressPercent, String.Format("تم معالجة {0} من {1} سجل", processedCount, totalRows))

                ' Stop on error if requested
                If chkStopOnError.Checked Then
                    Dim hasErrors As Boolean = uploadResults.Any(Function(r) r.Status = "خطأ")
                    If hasErrors Then
                        Exit For
                    End If
                End If
            Next

        Catch ex As Exception
            worker.ReportProgress(100, String.Format("خطأ في عملية الرفع: {0}", ex.Message))
        End Try
    End Sub

    Private Sub ProcessBatch(batch As DataRow(), worker As BackgroundWorker)
        Using conn As New SqlConnection(Constr)
            conn.Open()
            Using transaction As SqlTransaction = conn.BeginTransaction()
                Try
                    For Each row As DataRow In batch
                        If worker.CancellationPending Then Return

                        ProcessSingleRow(row, conn, transaction)
                    Next

                    transaction.Commit()

                Catch ex As Exception
                    transaction.Rollback()

                    ' Mark all batch items as failed
                    For Each row As DataRow In batch
                        uploadResults.Add(New UploadResult With {
                            .RowIndex = CInt(row("_RowIndex")),
                            .Status = "خطأ",
                            .ItemCode = If(row("ItemNo") IsNot Nothing, row("ItemNo").ToString(), ""),
                            .ErrorMessage = ex.Message,
                            .ProcessedAt = DateTime.Now
                        })
                    Next
                End Try
            End Using
        End Using
    End Sub

    Private Sub ProcessSingleRow(row As DataRow, conn As SqlConnection, transaction As SqlTransaction)
        Try
            Dim isUpdate As Boolean = CBool(row("_IsUpdate"))
            Dim itemCode As String = If(row("ItemNo") IsNot Nothing, row("ItemNo").ToString(), "")

            If isUpdate Then
                UpdateItem(row, conn, transaction)
            Else
                InsertItem(row, conn, transaction)
            End If

            ' Process photo if provided
            ProcessItemPhoto(row, conn, transaction)

            uploadResults.Add(New UploadResult With {
                .RowIndex = CInt(row("_RowIndex")),
                .Status = If(isUpdate, "تم التحديث", "تم الإضافة"),
                .ItemCode = itemCode,
                .ErrorMessage = "",
                .ProcessedAt = DateTime.Now
            })

        Catch ex As Exception
            uploadResults.Add(New UploadResult With {
                .RowIndex = CInt(row("_RowIndex")),
                .Status = "خطأ",
                .ItemCode = If(row("ItemNo") IsNot Nothing, row("ItemNo").ToString(), ""),
                .ErrorMessage = ex.Message,
                .ProcessedAt = DateTime.Now
            })
        End Try
    End Sub

    Private Sub InsertItem(row As DataRow, conn As SqlConnection, transaction As SqlTransaction)
        Dim sql As String = "INSERT INTO tblItems (ItemNo, ItemDescription, ItemDescription2, UnitSalesPrice, UnitPurchasePrice, " &
                           "Barcode, ItemCategory, Brand, ItemType, UofM, SalesUofM, Tax_Percent, Shop, Notes, " &
                           "SNEnable, NegativeStock, Status, AUofM, AUofMX, AUofM_Price, " &
                           "AUofM2, AUofMX2, AUofM2_Price, AUofM3, AUofMX3, AUofM3_Price, " &
                           "CreatedBy, CreatedOn) " &
                           "VALUES (@ItemNo, @ItemDescription, @ItemDescription2, @UnitSalesPrice, @UnitPurchasePrice, " &
                           "@Barcode, @CategoryID, @Brand, @ItemTypeID, @UofM, @SalesUofM, @Tax, @Shop, @Notes, " &
                           "@EnableSN, @NegativeEnable, @Inactive, @AUofM, @AUofMX, @SalesPriceAUofM, " &
                           "@AUofM2, @AUofMX2, @SalesPriceAUofM2, @AUofM3, @AUofMX3, @SalesPriceAUofM3, " &
                           "@CreatedBy, GETDATE())"

        Using cmd As New SqlCommand(sql, conn, transaction)
            AddParameters(cmd, row)
            cmd.Parameters.AddWithValue("@CreatedBy", Username)
            cmd.ExecuteNonQuery()
        End Using
    End Sub

    Private Sub UpdateItem(row As DataRow, conn As SqlConnection, transaction As SqlTransaction)
        Dim sql As String = "UPDATE tblItems SET ItemDescription = @ItemDescription, ItemDescription2 = @ItemDescription2, " &
                           "UnitSalesPrice = @UnitSalesPrice, UnitPurchasePrice = @UnitPurchasePrice, " &
                           "Barcode = @Barcode, ItemCategory = @CategoryID, Brand = @Brand, ItemType = @ItemTypeID, " &
                           "UofM = @UofM, SalesUofM = @SalesUofM, Tax_Percent = @Tax, Shop = @Shop, " &
                           "Notes = @Notes, SNEnable = @EnableSN, NegativeStock = @NegativeEnable, Status = @Inactive, " &
                           "AUofM = @AUofM, AUofMX = @AUofMX, AUofM_Price = @SalesPriceAUofM, " &
                           "AUofM2 = @AUofM2, AUofMX2 = @AUofMX2, AUofM2_Price = @SalesPriceAUofM2, " &
                           "AUofM3 = @AUofM3, AUofMX3 = @AUofMX3, AUofM3_Price = @SalesPriceAUofM3, " &
                           "ModifiedBy = @ModifiedBy, ModifiedOn = GETDATE() " &
                           "WHERE ItemNo = @ItemNo"

        Using cmd As New SqlCommand(sql, conn, transaction)
            AddParameters(cmd, row)
            cmd.Parameters.AddWithValue("@ModifiedBy", Username)
            cmd.ExecuteNonQuery()
        End Using
    End Sub

    Private Sub AddParameters(cmd As SqlCommand, row As DataRow)
        cmd.Parameters.AddWithValue("@ItemNo", GetValueOrDBNull(row, "ItemNo"))
        cmd.Parameters.AddWithValue("@ItemDescription", GetValueOrDBNull(row, "ItemDescription"))
        cmd.Parameters.AddWithValue("@ItemDescription2", GetValueOrDBNull(row, "ItemDescription2"))
        cmd.Parameters.AddWithValue("@UnitSalesPrice", GetValueOrDBNull(row, "UnitSalesPrice"))
        cmd.Parameters.AddWithValue("@UnitPurchasePrice", GetValueOrDBNull(row, "UnitPurchasePrice"))
        cmd.Parameters.AddWithValue("@Barcode", GetValueOrDBNull(row, "Barcode"))
        cmd.Parameters.AddWithValue("@CategoryID", GetValueOrDBNull(row, "Category"))      ' Stores ID
        cmd.Parameters.AddWithValue("@Brand", GetValueOrDBNull(row, "Brand"))             ' Stores Text
        cmd.Parameters.AddWithValue("@ItemTypeID", GetValueOrDBNull(row, "ItemType"))     ' Stores ID
        cmd.Parameters.AddWithValue("@UofM", GetValueOrDBNull(row, "UofM"))               ' Stores Text
        cmd.Parameters.AddWithValue("@SalesUofM", GetValueOrDBNull(row, "SalesUofM"))     ' Stores Text
        cmd.Parameters.AddWithValue("@Tax", GetValueOrDBNull(row, "Tax"))                 ' Stores Text
        cmd.Parameters.AddWithValue("@Shop", GetValueOrDBNull(row, "Shop"))               ' Stores Text
        cmd.Parameters.AddWithValue("@Notes", GetValueOrDBNull(row, "Notes"))
        cmd.Parameters.AddWithValue("@EnableSN", GetValueOrDBNull(row, "EnableSN"))
        cmd.Parameters.AddWithValue("@NegativeEnable", GetValueOrDBNull(row, "NegativeEnable"))
        cmd.Parameters.AddWithValue("@Inactive", GetValueOrDBNull(row, "Inactive"))
        cmd.Parameters.AddWithValue("@AUofM", GetValueOrDBNull(row, "AUofM"))             ' Stores Text
        cmd.Parameters.AddWithValue("@AUofMX", GetValueOrDBNull(row, "AUofMX"))
        cmd.Parameters.AddWithValue("@SalesPriceAUofM", GetValueOrDBNull(row, "SalesPriceAUofM"))
        cmd.Parameters.AddWithValue("@AUofM2", GetValueOrDBNull(row, "AUofM2"))           ' Stores Text
        cmd.Parameters.AddWithValue("@AUofMX2", GetValueOrDBNull(row, "AUofMX2"))
        cmd.Parameters.AddWithValue("@SalesPriceAUofM2", GetValueOrDBNull(row, "SalesPriceAUofM2"))
        cmd.Parameters.AddWithValue("@AUofM3", GetValueOrDBNull(row, "AUofM3"))           ' Stores Text
        cmd.Parameters.AddWithValue("@AUofMX3", GetValueOrDBNull(row, "AUofMX3"))
        cmd.Parameters.AddWithValue("@SalesPriceAUofM3", GetValueOrDBNull(row, "SalesPriceAUofM3"))
        'cmd.Parameters.AddWithValue("@PhotoPath", GetValueOrDBNull(row, "Photo"))
    End Sub

    Private Function GetValueOrDBNull(row As DataRow, columnName As String) As Object
        Try
            If row IsNot Nothing AndAlso row.Table.Columns.Contains(columnName) AndAlso
               row(columnName) IsNot Nothing AndAlso row(columnName) IsNot DBNull.Value Then
                Dim value = row(columnName)
                If TypeOf value Is String AndAlso String.IsNullOrWhiteSpace(value.ToString()) Then
                    Return DBNull.Value
                End If
                Return value
            End If
        Catch ex As Exception
            ' Log error but return DBNull to continue processing
            Console.WriteLine($"Error getting value for column {columnName}: {ex.Message}")
        End Try
        Return DBNull.Value
    End Function

    Private Sub ProcessItemPhoto(row As DataRow, conn As SqlConnection, transaction As SqlTransaction)
        Try
            If row Is Nothing OrElse Not row.Table.Columns.Contains("Photo") OrElse
               row("Photo") Is DBNull.Value OrElse String.IsNullOrEmpty(row("Photo").ToString()) Then
                Return
            End If

            Dim photoPath As String = row("Photo").ToString().Trim()
            If String.IsNullOrEmpty(photoPath) Then Return

            Dim itemNo As String = If(row("ItemNo") IsNot Nothing, row("ItemNo").ToString(), "")
            If String.IsNullOrEmpty(itemNo) Then Return

            ' If it's a URL, download the image
            If photoPath.StartsWith("http://") OrElse photoPath.StartsWith("https://") Then
                photoPath = DownloadPhoto(photoPath, itemNo)
            End If

            ' Update the photo path in database
            If Not String.IsNullOrEmpty(photoPath) Then
                Dim sql As String = "UPDATE Items SET PhotoPath = @PhotoPath WHERE ItemNo = @ItemNo"
                Using cmd As New SqlCommand(sql, conn, transaction)
                    cmd.Parameters.AddWithValue("@PhotoPath", photoPath)
                    cmd.Parameters.AddWithValue("@ItemNo", itemNo)
                    cmd.ExecuteNonQuery()
                End Using
            End If

        Catch ex As Exception
            ' Log photo error but don't fail the entire item
            Console.WriteLine($"خطأ في معالجة صورة الصنف {row("ItemNo")}: {ex.Message}")
        End Try
    End Sub

    Private Function DownloadPhoto(url As String, itemNo As String) As String
        Try
            Dim fileName As String = String.Format("item_{0}_{1}.jpg", itemNo, DateTime.Now.ToString("yyyyMMddHHmmss"))
            Dim photosFolder As String = Path.Combine(Application.StartupPath, "Photos")

            If Not Directory.Exists(photosFolder) Then
                Directory.CreateDirectory(photosFolder)
            End If

            Dim localPath As String = Path.Combine(photosFolder, fileName)

            Using client As New System.Net.WebClient()
                client.DownloadFile(url, localPath)
            End Using

            Return localPath

        Catch ex As Exception
            Return ""
        End Try
    End Function

    Private Sub CreateBackup()
        Try
            Dim backupPath As String = Path.Combine(Application.StartupPath, "Backups")
            If Not Directory.Exists(backupPath) Then
                Directory.CreateDirectory(backupPath)
            End If

            Dim backupFile As String = Path.Combine(backupPath, String.Format("Items_Backup_{0}.sql", DateTime.Now.ToString("yyyyMMdd_HHmmss")))

            Using conn As New SqlConnection(Constr)
                conn.Open()
                Dim sql As String = "SELECT * FROM Items"
                Using cmd As New SqlCommand(sql, conn)
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        Using writer As New StreamWriter(backupFile, False, System.Text.Encoding.UTF8)
                            writer.WriteLine("-- Items Backup Created: " & DateTime.Now.ToString())
                            writer.WriteLine("-- Total Records: " & dtMappedData.Rows.Count.ToString())
                            writer.WriteLine()

                            While reader.Read()
                                Dim insertSql As String = GenerateInsertSQL(reader)
                                writer.WriteLine(insertSql)
                            End While
                        End Using
                    End Using
                End Using
            End Using

        Catch ex As Exception
            ' Log backup error but continue
            Console.WriteLine(String.Format("خطأ في إنشاء النسخة الاحتياطية: {0}", ex.Message))
        End Try
    End Sub

    Private Function GenerateInsertSQL(reader As SqlDataReader) As String
        Dim sql As String = "INSERT INTO Items ("
        Dim values As String = "VALUES ("

        For i As Integer = 0 To reader.FieldCount - 1
            If i > 0 Then
                sql += ", "
                values += ", "
            End If

            sql += reader.GetName(i)

            If reader.IsDBNull(i) Then
                values += "NULL"
            Else
                Dim value = reader.GetValue(i)
                If TypeOf value Is String Then
                    values += String.Format("'{0}'", value.ToString().Replace("'", "''"))
                ElseIf TypeOf value Is DateTime Then
                    values += String.Format("'{0}'", CType(value, DateTime).ToString("yyyy-MM-dd HH:mm:ss"))
                Else
                    values += value.ToString()
                End If
            End If
        Next

        sql += ") " & values & ");"
        Return sql
    End Function

    Private Sub bgwUpload_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles bgwUpload.ProgressChanged
        pbUploadProgress.Value = e.ProgressPercentage
        lblProgressPercent.Text = String.Format("{0}%", e.ProgressPercentage)
        lblProgressStatus.Text = If(e.UserState IsNot Nothing, e.UserState.ToString(), "")
    End Sub

    Private Sub bgwUpload_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles bgwUpload.RunWorkerCompleted
        ' Re-enable controls
        SetControlsEnabled(True)

        ' Update progress
        pbUploadProgress.Value = 100
        lblProgressPercent.Text = "100%"

        If e.Cancelled Then
            lblProgressStatus.Text = "تم إلغاء العملية"
            MessageBox.Show("تم إلغاء عملية الرفع", "إلغاء", MessageBoxButtons.OK, MessageBoxIcon.Information)
        ElseIf e.Error IsNot Nothing Then
            lblProgressStatus.Text = "خطأ في العملية"
            MessageBox.Show(String.Format("خطأ في عملية الرفع: {0}", e.Error.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            lblProgressStatus.Text = "تمت العملية بنجاح"
            MessageBox.Show("تمت عملية الرفع بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        End If

        ' Display upload results
        DisplayUploadResults()
    End Sub

    Private Sub DisplayUploadResults()
        Try
            Dim resultsTable As New DataTable()
            resultsTable.Columns.Add("رقم السجل", GetType(Integer))
            resultsTable.Columns.Add("كود الصنف", GetType(String))
            resultsTable.Columns.Add("الحالة", GetType(String))
            resultsTable.Columns.Add("رسالة الخطأ", GetType(String))
            resultsTable.Columns.Add("وقت المعالجة", GetType(DateTime))

            For Each result In uploadResults
                Dim row As DataRow = resultsTable.NewRow()
                row("رقم السجل") = result.RowIndex
                row("كود الصنف") = result.ItemCode
                row("الحالة") = result.Status
                row("رسالة الخطأ") = result.ErrorMessage
                row("وقت المعالجة") = result.ProcessedAt
                resultsTable.Rows.Add(row)
            Next

            dgvUploadResults.DataSource = resultsTable

            ' Color code the results
            For Each row As DataGridViewRow In dgvUploadResults.Rows
                If row.Cells("الحالة").Value IsNot Nothing Then
                    Dim status As String = row.Cells("الحالة").Value.ToString()
                    Select Case status
                        Case "تم الإضافة"
                            row.DefaultCellStyle.BackColor = Color.LightGreen
                        Case "تم التحديث"
                            row.DefaultCellStyle.BackColor = Color.LightBlue
                        Case "خطأ"
                            row.DefaultCellStyle.BackColor = Color.LightPink
                    End Select
                End If
            Next

        Catch ex As Exception
            MessageBox.Show(String.Format("خطأ في عرض النتائج: {0}", ex.Message), "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
#End Region

#Region "Navigation and Validation"
    Private Sub UpdateNavigationButtons()
        Select Case tcMain.SelectedIndex
            Case 0 ' File Selection
                btnPrevious.Enabled = False
                btnNext.Enabled = dtFileData IsNot Nothing AndAlso dtFileData.Rows.Count > 0
                btnUpload.Enabled = False

            Case 1 ' Field Mapping
                btnPrevious.Enabled = True
                btnNext.Enabled = ValidateFieldMapping()
                btnUpload.Enabled = False

            Case 2 ' Data Preview
                btnPrevious.Enabled = True
                btnNext.Enabled = ValidateDataPreview()
                btnUpload.Enabled = False

            Case 3 ' Upload
                btnPrevious.Enabled = True
                btnNext.Enabled = False
                btnUpload.Enabled = dtMappedData IsNot Nothing AndAlso dtMappedData.Select("_Status = 'جاهز'").Length > 0
        End Select
    End Sub

    Private Function ValidateFileSelection() As Boolean
        If dtFileData Is Nothing OrElse dtFileData.Rows.Count = 0 Then
            MessageBox.Show("يرجى اختيار ملف صالح يحتوي على بيانات", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If
        Return True
    End Function

    Private Function ValidateFieldMapping() As Boolean
        Dim hasRequiredMappings As Boolean = True
        Dim missingFields As New List(Of String)()

        For Each row As DataGridViewRow In dgvFieldMapping.Rows
            If row.IsNewRow Then Continue For

            Dim isRequired As Boolean = If(row.Cells("colRequired").Value IsNot Nothing, CBool(row.Cells("colRequired").Value), False)
            Dim mappingType As String = If(row.Cells("colMappingType").Value IsNot Nothing, row.Cells("colMappingType").Value.ToString(), "تجاهل")
            Dim fieldName As String = If(row.Cells("colSystemField").Value IsNot Nothing, row.Cells("colSystemField").Value.ToString(), "")

            If isRequired AndAlso mappingType = "تجاهل" Then
                hasRequiredMappings = False
                missingFields.Add(fieldName)
            End If
        Next

        If Not hasRequiredMappings Then
            MessageBox.Show(String.Format("يرجى ربط الحقول المطلوبة التالية: {0}", String.Join(", ", missingFields)),
                          "حقول مطلوبة", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        Return True
    End Function

    Private Function ValidateDataPreview() As Boolean
        If dtMappedData Is Nothing OrElse dtMappedData.Rows.Count = 0 Then
            MessageBox.Show("لا توجد بيانات للمعاينة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        Dim validRows As Integer = dtMappedData.Select("_Status = 'جاهز'").Length
        If validRows = 0 Then
            MessageBox.Show("لا توجد سجلات صالحة للرفع. يرجى مراجعة الأخطاء وإصلاحها", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        Return True
    End Function

    Private Sub tcMain_SelectedIndexChanged(sender As Object, e As EventArgs) Handles tcMain.SelectedIndexChanged
        UpdateNavigationButtons()
    End Sub
#End Region

#Region "Event Handlers"
    Private Sub dgvFieldMapping_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles dgvFieldMapping.CellValueChanged
        If e.RowIndex >= 0 AndAlso e.ColumnIndex >= 0 AndAlso Not dgvFieldMapping.Rows(e.RowIndex).IsNewRow Then
            Dim row As DataGridViewRow = dgvFieldMapping.Rows(e.RowIndex)

            ' Update status based on mapping
            If e.ColumnIndex = dgvFieldMapping.Columns("colMappingType").Index OrElse
               e.ColumnIndex = dgvFieldMapping.Columns("colFileColumn").Index Then

                Dim mappingType As String = If(row.Cells("colMappingType").Value IsNot Nothing, row.Cells("colMappingType").Value.ToString(), "تجاهل")
                Dim fileColumn As String = If(row.Cells("colFileColumn").Value IsNot Nothing, row.Cells("colFileColumn").Value.ToString(), "")
                Dim fixedValue As String = If(row.Cells("colFixedValue").Value IsNot Nothing, row.Cells("colFixedValue").Value.ToString(), "")
                Dim isRequired As Boolean = If(row.Cells("colRequired").Value IsNot Nothing, CBool(row.Cells("colRequired").Value), False)

                Dim status As String = "Optional"

                If mappingType = "من الملف" AndAlso Not String.IsNullOrEmpty(fileColumn) Then
                    status = "Mapped"
                ElseIf mappingType = "قيمة ثابتة" AndAlso Not String.IsNullOrEmpty(fixedValue) Then
                    status = "Mapped"
                ElseIf isRequired Then
                    status = "Required"
                End If

                row.Cells("colStatus").Value = GetStatusIcon(status)
            End If

            UpdateNavigationButtons()
        End If
    End Sub

    Private Sub dgvFieldMapping_CurrentCellDirtyStateChanged(sender As Object, e As EventArgs) Handles dgvFieldMapping.CurrentCellDirtyStateChanged
        If dgvFieldMapping.IsCurrentCellDirty Then
            dgvFieldMapping.CommitEdit(DataGridViewDataErrorContexts.Commit)
        End If
    End Sub
#End Region

End Class