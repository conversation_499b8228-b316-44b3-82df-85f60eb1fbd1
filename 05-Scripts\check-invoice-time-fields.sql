-- Check the actual time fields available in tblStockMovHeader
-- This will help us understand which field contains the actual transaction time

-- Check invoice 89 with all time-related fields
SELECT 
    'Invoice 89 Time Analysis' as Section,
    TrxNo,
    TrxDate,
    CAST(TrxDate AS TIME) as TrxDate_TimeOnly,
    DATEPART(HOUR, TrxDate) as TrxDate_Hour,
    DATEPART(MINUTE, TrxDate) as TrxDate_Minute,
    CreatedOn,
    CAST(CreatedOn AS TIME) as CreatedOn_TimeOnly,
    DATEPART(HOUR, CreatedOn) as CreatedOn_Hour,
    DATEPART(MINUTE, CreatedOn) as CreatedOn_Minute,
    ModifiedOn,
    CAST(ModifiedOn AS TIME) as ModifiedOn_TimeOnly,
    DATEPART(HOUR, ModifiedOn) as ModifiedOn_Hour,
    DATEPART(MINUTE, ModifiedOn) as ModifiedOn_Minute,
    Cashier,
    PartnerName
FROM tblStockMovHeader 
WHERE TrxNo = 89 AND TrxType = 'مبيعات';

-- Check a few more recent invoices to see the pattern
SELECT TOP 5
    'Recent Invoices Time Analysis' as Section,
    TrxNo,
    TrxDate,
    CAST(TrxDate AS TIME) as TrxDate_TimeOnly,
    CreatedOn,
    CAST(CreatedOn AS TIME) as CreatedOn_TimeOnly,
    ModifiedOn,
    CAST(ModifiedOn AS TIME) as ModifiedOn_TimeOnly,
    Cashier
FROM tblStockMovHeader 
WHERE TrxType = 'مبيعات'
ORDER BY TrxNo DESC;

-- Check if QRCodeImage contains timestamp information
-- (This would require examining the QR code data structure)
SELECT 
    'QR Code Analysis' as Section,
    TrxNo,
    TrxDate,
    CreatedOn,
    ModifiedOn,
    CASE 
        WHEN QRCodeImage IS NOT NULL THEN 'QR Code Present'
        ELSE 'No QR Code'
    END as QRCodeStatus,
    LEN(QRCodeImage) as QRCodeLength
FROM tblStockMovHeader 
WHERE TrxNo = 89 AND TrxType = 'مبيعات';
