@model IEnumerable<AccountingSystem.Models.POSInvoice>
@{
    ViewData["Title"] = "بحث المبيعات";
    var totalSales = ViewBag.TotalSales ?? 0M;
}

<div class="container mt-4">
    <h2 class="mb-4"><i class="fas fa-search"></i> بحث المبيعات</h2>
    <form id="salesSearchForm" method="get" class="row g-3 mb-4">
        <input type="hidden" id="exportFormatHidden" name="format" />
        <div class="col-md-2">
            <label class="form-label">رقم الفاتورة</label>
            <input type="text" name="invoiceNo" class="form-control" value="@ViewBag.InvoiceNo" />
        </div>
        <div class="col-md-3">
            <label class="form-label">العميل (رقم)</label>
            <select name="accountNo" class="form-select">
                <option value="">الكل</option>
                @if (ViewBag.CustomerOptions != null)
                {
                    long? selectedAcc = ViewBag.AccountNo != null ? (long)ViewBag.AccountNo : (long?)null;
                    foreach (var c in (IEnumerable<AccountingSystem.Models.Customer>)ViewBag.CustomerOptions)
                    {
                        if (selectedAcc.HasValue && selectedAcc.Value == c.CustomerNo)
                        {
                            <option value="@c.CustomerNo" selected>@c.CustomerName (@c.CustomerNo)</option>
                        }
                        else
                        {
                            <option value="@c.CustomerNo">@c.CustomerName (@c.CustomerNo)</option>
                        }
                    }
                }
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">المستخدم</label>
            <select name="user" class="form-select">
                <option value="">الكل</option>
                @if (ViewBag.UserOptions != null)
                {
                    string selectedUser = ViewBag.User as string;
                    foreach (var u in (IEnumerable<string>)ViewBag.UserOptions)
                    {
                        if (!string.IsNullOrEmpty(selectedUser) && selectedUser == u)
                        {
                            <option value="@u" selected>@u</option>
                        }
                        else
                        {
                            <option value="@u">@u</option>
                        }
                    }
                }
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">المخزن</label>
            <select name="store" class="form-select">
                <option value="">الكل</option>
                @if (ViewBag.StoreOptions != null)
                {
                    string selectedStore = ViewBag.Store as string;
                    foreach (var s in (IEnumerable<string>)ViewBag.StoreOptions)
                    {
                        if (!string.IsNullOrEmpty(selectedStore) && selectedStore == s)
                        {
                            <option value="@s" selected>@s</option>
                        }
                        else
                        {
                            <option value="@s">@s</option>
                        }
                    }
                }
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">من تاريخ</label>
            <input type="date" name="dateFrom" class="form-control" value="@((ViewBag.DateFrom != null ? ((DateTime)ViewBag.DateFrom).ToString("yyyy-MM-dd") : ""))" />
        </div>
        <div class="col-md-2">
            <label class="form-label">إلى تاريخ</label>
            <input type="date" name="dateTo" class="form-control" value="@((ViewBag.DateTo != null ? ((DateTime)ViewBag.DateTo).ToString("yyyy-MM-dd") : ""))" />
        </div>
        <div class="col-md-2">
            <label class="form-label">نوع الفاتورة</label>
            <select name="invoiceType" class="form-select">
                @{
                    var it = ViewBag.InvoiceType as string;
                }
                <option value="">الكل</option>
                <option value="مبيعات">مبيعات</option>
                <option value="مرتجع مبيعات">مرتجع مبيعات</option>
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label d-block">نمط البحث</label>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="mode" id="modeHeaders" value="headers" @(string.IsNullOrWhiteSpace(ViewBag.Mode as string) || (ViewBag.Mode as string) != "lines" ? "checked" : "") />
                <label class="form-check-label" for="modeHeaders">فواتير</label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="mode" id="modeLines" value="lines" @((ViewBag.Mode as string) == "lines" ? "checked" : "") />
                <label class="form-check-label" for="modeLines">بنود الفاتورة</label>
            </div>
        </div>
        <div class="col-md-2">
            <label class="form-label">رقم الصنف (لبنود الفاتورة)</label>
            <input type="text" name="itemCode" class="form-control" value="@ViewBag.ItemCode" />
        </div>

        <div class="col-md-2">
            <label class="form-label">طريقة الدفع</label>
            <select name="paymentMethod" class="form-select">
                <option value="">الكل</option>
                @if (ViewBag.PaymentOptions != null)
                {
                    string selectedPayment = ViewBag.PaymentMethod as string;
                    foreach (var p in (IEnumerable<string>)ViewBag.PaymentOptions)
                    {
                        if (!string.IsNullOrEmpty(selectedPayment) && selectedPayment == p)
                        {
                            <option value="@p" selected>@p</option>
                        }
                        else
                        {
                            <option value="@p">@p</option>
                        }
                    }
                }
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">رقم مرجع</label>
            <input type="text" name="reference" class="form-control" value="@ViewBag.Reference" />
        </div>

        <div class="col-md-2">
            <label class="form-label">ملاحظات</label>
            <input type="text" name="notes" class="form-control" value="@ViewBag.Notes" />
        </div>
        @if ((ViewBag.Mode as string) == "lines")
        {
            <div class="col-md-2 d-flex align-items-end">
                <select id="exportFormatLines" class="form-select">
                    <option value="csv">CSV</option>
                    <option value="xlsx">XLSX</option>
                    <option value="pdf">PDF</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-outline-success w-100" onclick="exportLines()"><i class="fas fa-file-export"></i> تصدير (بنود)</button>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-outline-secondary w-100" onclick="showLinesReport()"><i class="fas fa-print"></i> طباعة (بنود)</button>
            </div>
        }
        else
        {
            <div class="col-md-2 d-flex align-items-end">
                <select id="exportFormatHeaders" class="form-select">
                    <option value="csv">CSV</option>
                    <option value="xlsx">XLSX</option>
                    <option value="pdf">PDF</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-outline-success w-100" onclick="exportHeaders()"><i class="fas fa-file-export"></i> تصدير (فواتير)</button>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-outline-secondary w-100" onclick="showHeadersReport()"><i class="fas fa-print"></i> طباعة (فواتير)</button>
            </div>
        }

        <div class="col-md-2 d-flex align-items-end">
            <button type="submit" class="btn btn-primary w-100"><i class="fas fa-search"></i> بحث</button>
        </div>
    </form>

    @if ((ViewBag.Mode as string) != "lines")
    {
        <div class="mb-3">
            <span class="badge bg-success fs-5">إجمالي المبيعات: @totalSales.ToString("N2") ريال</span>
        </div>
    }

    <div id="searchResults">
        @if ((ViewBag.Mode as string) == "lines" && ViewBag.LinesTable != null)
        {
            var dt = (System.Data.DataTable)ViewBag.LinesTable;
            if (dt.Rows.Count > 0)
            {
                <table class="table table-bordered table-striped">
                    <thead class="table-dark">
                        <tr>
                            @foreach (System.Data.DataColumn col in dt.Columns)
                            {
                                <th>@col.ColumnName</th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (System.Data.DataRow row in dt.Rows)
                        {
                            <tr>
                                @foreach (System.Data.DataColumn col in dt.Columns)
                                {
                                    <td>@(row[col] ?? "")</td>
                                }
                            </tr>
                        }
                    </tbody>
                </table>
            }
            else
            {
                <div class="alert alert-info">لا توجد نتائج للبحث الحالي.</div>
            }
        }
        else if (Model != null && Model.Any())
        {
            <table class="table table-bordered table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>المستخدم</th>
                        <th>المستودع</th>
                        <th>الإجمالي النهائي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                @foreach (var invoice in Model)
                {
                    <tr>
                        <td>@invoice.TrxNo</td>
                        <td>@(invoice.TrxDate?.ToString("yyyy/MM/dd") ?? "غير محدد")</td>
                        <td>
                            @{
                                string customerName = invoice.PartnerName ?? "";
                                if (string.IsNullOrWhiteSpace(customerName))
                                {
                                    customerName = "عميل غير محدد";
                                }
                            }
                            @customerName
                        </td>
                        <td>
                            @{ var userMap = ViewBag.UserMap as IDictionary<int, string>; string userVal = (userMap != null && userMap.ContainsKey(invoice.TrxNo)) ? (string)userMap[invoice.TrxNo] : (invoice.Cashier ?? ""); }
                            @userVal
                        </td>
                        <td>@invoice.Store</td>
                        <td>@((invoice.TrxNetAmount ?? 0).ToString("N2"))</td>
                        <td>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="showPrintout(@invoice.TrxNo)">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </td>
                    </tr>
                }
                </tbody>
            </table>
        }
        else
        {
            <div class="alert alert-info">لا توجد نتائج للبحث الحالي.</div>
        }
    </div>

    <!-- Printout Modal -->
    <div class="modal fade" id="printoutModal" tabindex="-1" aria-labelledby="printoutModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="printoutModalLabel">معاينة الفاتورة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <iframe id="printoutFrame" src="" width="100%" height="600" style="border:none;"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function showPrintout(invoiceNo) {
            document.getElementById('printoutFrame').src = 'about:blank';
            var modal = new bootstrap.Modal(document.getElementById('printoutModal'));
            modal.show();
            setTimeout(function() {
                document.getElementById('printoutFrame').src = '/POS/ThermalReceipt/' + invoiceNo;
            }, 100);
        }

        function buildQuery() {
            const form = document.getElementById('salesSearchForm');
            const fd = new FormData(form);
            // Normalize date range explicitly from inputs
            const dfInput = form.querySelector('input[name="dateFrom"]');
            const dtInput = form.querySelector('input[name="dateTo"]');
            let df = dfInput?.value || '';
            let dt = dtInput?.value || '';
            if (df && !dt) dt = df;
            if (dt && !df) df = dt;
            const params = new URLSearchParams(fd);
            if (df) params.set('dateFrom', df);
            if (dt) params.set('dateTo', dt);
            return params.toString();
        }

        function showLinesReport() {
            const mode = document.querySelector('input[name="mode"]:checked')?.value;
            if (mode !== 'lines') { alert('غيِّر نمط البحث إلى "بنود الفاتورة" أولاً'); return; }
            const qs = buildQuery();
            document.getElementById('printoutFrame').src = 'about:blank';
            var modal = new bootstrap.Modal(document.getElementById('printoutModal'));
            modal.show();
            setTimeout(function() {
                document.getElementById('printoutFrame').src = '/Sales/PrintSalesLines?' + qs;
            }, 100);
        }

        function exportLines() {
            const mode = document.querySelector('input[name="mode"]:checked')?.value;
            if (mode !== 'lines') { alert('غيِّر نمط البحث إلى "بنود الفاتورة" أولاً'); return; }
            const qs = buildQuery();
            const format = document.getElementById('exportFormatLines')?.value || 'csv';
            window.location.href = '/Sales/ExportLines?format=' + encodeURIComponent(format) + '&' + qs;
        }

        function exportHeaders() {
            if ((document.querySelector('input[name="mode"]:checked')?.value || 'headers') === 'lines') { alert('اختر نمط "فواتير" للتصدير'); return; }
            const qs = buildQuery();
            const format = document.getElementById('exportFormatHeaders')?.value || 'csv';
            window.location.href = '/Sales/ExportInvoices?format=' + encodeURIComponent(format) + '&' + qs;
        }

        function showHeadersReport() {
            if ((document.querySelector('input[name="mode"]:checked')?.value || 'headers') === 'lines') { alert('اختر نمط "فواتير" للطباعة'); return; }
            const qs = buildQuery();
            document.getElementById('printoutFrame').src = 'about:blank';
            var modal = new bootstrap.Modal(document.getElementById('printoutModal'));
            modal.show();
            setTimeout(function(){
                document.getElementById('printoutFrame').src = '/Sales/PrintSalesHeaders?' + qs;
            }, 100);
        }
    </script>
}
