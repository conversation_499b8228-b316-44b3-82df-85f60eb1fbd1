﻿Imports System.Data.SqlClient
Imports System.IO
Imports System.Data.OleDb
Imports Microsoft.VisualBasic.FileIO
Imports Excel = Microsoft.Office.Interop.Excel

Public Class frmVendorsUpload
    Dim dtImported As New DataTable()
    Dim dtMapped As New DataTable()
    Dim FixedLists As New Dictionary(Of String, List(Of String))


    Private Function GetFieldStructure() As List(Of String)
        Return New List(Of String) From {
            "VendorNo", "VendorName", "FirstName", "LastName", "Mobile", "Phone", "Email",
            "StreetAddress1", "StreetAddress2", "City", "Region", "PostalCode", "BuildingNo",
            "AdditionalNo", "District", "PaymentMethod", "CreditLimit", "PaymentTerm",
            "Contacts", "CR", "VATReg", "Shop", "Status", "Local", "Employee", "Notes"
        }
    End Function

    Private Function GetValuesFromQuery(query As String) As List(Of String)
        Dim values As New List(Of String)()
        Using con As New SqlConnection(Constr)
            Using cmd As New SqlCommand(query, con)
                con.Open()
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    While reader.Read()
                        values.Add(reader(0).ToString())
                    End While
                End Using
                con.Close()
            End Using
        End Using
        Return values
    End Function

    Private Function GetEmployees() As List(Of String)
        Dim values As New List(Of String)()
        Using cmd As New SqlCommand("Select EmployeeNo,Emp_Name from tblEmployees order by EmployeeNo", New SqlConnection(Constr))
            cmd.Connection.Open()
            Dim reader As SqlDataReader = cmd.ExecuteReader()
            While reader.Read()
                values.Add(reader("Emp_Name") & " - " & reader("EmployeeNo"))
            End While
            reader.Close()
            cmd.Connection.Close()
        End Using
        Return values
    End Function

    Private Sub ActionChanged(sender As Object, e As EventArgs)
        Dim actionCombo = CType(sender, ComboBox)
        Dim field = actionCombo.Name.Replace("cmbxAction", "")
        Dim mapCombo = CType(Me.Controls("cmbx" & field), ComboBox)

        mapCombo.Items.Clear()
        If actionCombo.Text = "Fixed" Then
            If FixedLists.ContainsKey(field) Then
                mapCombo.Items.AddRange(FixedLists(field).ToArray())
            Else
                MessageBox.Show($"لا توجد بيانات ثابتة للحقل '{field}' لذلك تم إخفاء خيار Fixed")
                actionCombo.Items.Remove("Fixed")
                actionCombo.SelectedItem = "Manual"
            End If
        ElseIf actionCombo.Text = "Mapped" Then
            For Each col As DataColumn In dtImported.Columns
                mapCombo.Items.Add(col.ColumnName)
            Next
        End If
    End Sub

    Private Sub FillActionCombos()
        For Each c As ComboBox In Me.Controls.OfType(Of ComboBox)().Where(Function(x) x.Name.StartsWith("cmbxAction"))
            c.Items.Clear()
            c.Items.Add("Mapped")
            c.Items.Add("Manual")
            If FixedLists.ContainsKey(c.Name.Replace("cmbxAction", "")) Then
                c.Items.Add("Fixed")
            End If
            c.SelectedIndex = 0
        Next
    End Sub

    Private Sub btnBrowse_Click(sender As Object, e As EventArgs) Handles btnBrowse.Click
        Dim ofd As New OpenFileDialog With {.Filter = "Excel Files|*.xlsx;*.xls|CSV Files|*.csv"}
        If ofd.ShowDialog = DialogResult.OK Then
            txtFilePath.Text = ofd.FileName
            If Path.GetExtension(ofd.FileName).ToLower() = ".csv" Then
                dtImported = LoadCSV(ofd.FileName)
            Else
                dtImported = LoadExcel(ofd.FileName)
            End If

            ' Populate FixedLists here
            FixedLists("Status") = GetValuesFromQuery("SELECT Vendor_Status FROM tblVendorStatus")
            FixedLists("PaymentMethod") = GetValuesFromQuery("SELECT Payment_Method FROM tblPaymentMethod")
            FixedLists("Shop") = GetValuesFromQuery("SELECT Shop_Text FROM tblShops")
            FixedLists("Local") = GetValuesFromQuery("SELECT Local_KSA FROM tblLocalKSA")
            FixedLists("Employee") = GetEmployees()

            FillActionCombos()
            AutoMapFields()
            lblTotalRecords.Text = dtImported.Rows.Count.ToString()
        End If
    End Sub

    Private Function LoadCSV(filePath As String) As DataTable
        Dim dt As New DataTable()
        Using parser As New TextFieldParser(filePath)
            parser.TextFieldType = FieldType.Delimited
            parser.SetDelimiters(",")
            parser.HasFieldsEnclosedInQuotes = True
            If Not parser.EndOfData Then
                Dim headers = parser.ReadFields()
                For Each header In headers
                    dt.Columns.Add(header.Trim())
                Next
            End If
            While Not parser.EndOfData
                dt.Rows.Add(parser.ReadFields())
            End While
        End Using
        Return dt
    End Function

    Private Function LoadExcel(filePath As String) As DataTable
        Dim dt As New DataTable()
        Using conn As New OleDbConnection($"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={filePath};Extended Properties='Excel 12.0 Xml;HDR=YES'")
            conn.Open()
            Dim schema = conn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, Nothing)
            Dim sheetName As String = schema.Rows(0)("TABLE_NAME").ToString()
            Dim adapter As New OleDbDataAdapter("SELECT * FROM [" & sheetName & "]", conn)
            adapter.Fill(dt)
        End Using
        Return dt
    End Function

    Private Sub AutoMapFields()
        For Each field In GetFieldStructure()
            Dim mapCombo = CType(Me.Controls.Find("cmbx" & field, True).FirstOrDefault(), ComboBox)
            Dim actionCombo = CType(Me.Controls.Find("cmbxAction" & field, True).FirstOrDefault(), ComboBox)

            If mapCombo Is Nothing OrElse actionCombo Is Nothing Then Continue For

            mapCombo.Items.Clear()
            For Each col As DataColumn In dtImported.Columns
                mapCombo.Items.Add(col.ColumnName)
            Next

            Dim matchedCol = dtImported.Columns.Cast(Of DataColumn)().FirstOrDefault(Function(c) c.ColumnName.ToLower().Replace("_", "") = field.ToLower())
            If matchedCol IsNot Nothing Then
                mapCombo.SelectedItem = matchedCol.ColumnName
                actionCombo.SelectedItem = "Mapped"
            Else
                mapCombo.Text = ""
                actionCombo.SelectedItem = "Manual"
            End If
        Next
    End Sub

    Private Sub btnGridView_Click(sender As Object, e As EventArgs) Handles btnGridView.Click
        dtMapped = BuildMappedTable()
        DataGridView1.DataSource = dtMapped
        lblSucceed.Text = dtMapped.AsEnumerable().Count(Function(r) r("Error").ToString() = "").ToString()
        lblErrors.Text = dtMapped.AsEnumerable().Count(Function(r) r("Error").ToString() <> "").ToString()

        ' Color code failed rows
        For Each dgRow As DataGridViewRow In DataGridView1.Rows
            If dgRow.Cells("Error").Value?.ToString() <> "" Then
                dgRow.DefaultCellStyle.BackColor = Color.LightPink
            End If
        Next
    End Sub

    Private Function BuildMappedTable() As DataTable
        Dim result As New DataTable()
        result.Columns.Add("Error")
        For Each field In GetFieldStructure()
            result.Columns.Add(field)
        Next

        For Each row As DataRow In dtImported.Rows
            Dim newRow = result.NewRow()
            Dim errorMsg As String = ""

            For Each field In GetFieldStructure()
                Dim mapCombo = CType(Me.Controls("cmbx" & field), ComboBox)
                Dim actionCombo = CType(Me.Controls("cmbxAction" & field), ComboBox)
                Dim value As String = ""

                Select Case actionCombo.Text.ToLower()
                    Case "mapped"
                        If mapCombo.SelectedItem IsNot Nothing AndAlso row.Table.Columns.Contains(mapCombo.SelectedItem.ToString()) Then
                            value = row(mapCombo.SelectedItem.ToString()).ToString()
                        Else
                            errorMsg &= $"[{field}: حقل غير موجود] "
                        End If
                    Case "fixed"
                        If Not String.IsNullOrWhiteSpace(mapCombo.Text) Then
                            value = mapCombo.Text.Trim()
                        Else
                            errorMsg &= $"[{field}: قيمة ثابتة مفقودة] "
                        End If
                    Case Else ' manual
                        value = ""
                End Select

                ' Additional validation example
                If field = "CreditLimit" AndAlso Not String.IsNullOrWhiteSpace(value) Then
                    Dim testDecimal As Decimal
                    If Not Decimal.TryParse(value, testDecimal) Then
                        errorMsg &= $"[{field}: صيغة غير صحيحة] "
                    End If
                End If

                newRow(field) = value
            Next

            newRow("Error") = errorMsg.Trim()
            result.Rows.Add(newRow)
        Next

        Return result
    End Function

    Private Sub btnUpload_Click(sender As Object, e As EventArgs) Handles btnUpload.Click
        If dtMapped Is Nothing OrElse dtMapped.Rows.Count = 0 Then Exit Sub
        Dim validCount = dtMapped.AsEnumerable().Count(Function(r) r("Error").ToString() = "")
        If validCount = 0 Then
            MessageBox.Show("لا توجد سجلات صالحة للرفع.")
            Exit Sub
        End If
        If MessageBox.Show($"هل أنت متأكد من رفع عدد {validCount} سجل؟", "تأكيد الرفع", MessageBoxButtons.YesNo) <> DialogResult.Yes Then Exit Sub

        Dim successCount As Integer = 0
        Dim errorCount As Integer = 0
        For Each row As DataGridViewRow In DataGridView1.Rows
            If row.Cells("Error").Value.ToString() = "" Then
                Using con As New SqlConnection(Constr)
                    Using cmd As New SqlCommand("sp_UploadVendorMaster", con)
                        cmd.CommandType = CommandType.StoredProcedure
                        cmd.Parameters.AddWithValue("@VendorNo", If(String.IsNullOrWhiteSpace(row.Cells("VendorNo").Value?.ToString()), "", row.Cells("VendorNo").Value.ToString()))

                        For Each field In GetFieldStructure().Where(Function(f) f <> "VendorNo")
                            Dim value As Object = row.Cells(field).Value
                            If String.IsNullOrWhiteSpace(value?.ToString()) Then
                                cmd.Parameters.AddWithValue("@" & field, DBNull.Value)
                            ElseIf field = "CreditLimit" Then
                                Dim decimalValue As Decimal
                                If Decimal.TryParse(value.ToString(), decimalValue) Then
                                    cmd.Parameters.AddWithValue("@" & field, decimalValue)
                                Else
                                    cmd.Parameters.AddWithValue("@" & field, DBNull.Value)
                                End If
                            Else
                                cmd.Parameters.AddWithValue("@" & field, value.ToString())
                            End If
                        Next
                        cmd.Parameters.AddWithValue("@CreatedBy", UserName)
                        cmd.Parameters.Add("@NewVendorNo", SqlDbType.BigInt).Direction = ParameterDirection.Output
                        cmd.Parameters.Add("@ResultCode", SqlDbType.BigInt).Direction = ParameterDirection.Output
                        cmd.Parameters.Add("@ResultMessage", SqlDbType.NVarChar, 200).Direction = ParameterDirection.Output


                        Try
                            con.Open()
                            cmd.ExecuteNonQuery()
                            Dim resultCode As Integer = CInt(cmd.Parameters("@ResultCode").Value)
                            If resultCode = 0 Then
                                row.Cells("VendorNo").Value = cmd.Parameters("@NewVendorNo").Value.ToString()
                            Else
                                row.Cells("Error").Value = cmd.Parameters("@ResultMessage").Value.ToString()
                            End If

                        Catch ex As Exception
                            row.Cells("Error").Value = ex.Message
                            row.DefaultCellStyle.BackColor = Color.LightPink
                            errorCount += 1
                        End Try
                    End Using
                End Using
            End If
        Next
        MessageBox.Show($"تم رفع عدد {successCount} من الموردين بنجاح. عدد الأخطاء: {errorCount}", "نتيجة الرفع", MessageBoxButtons.OK)
    End Sub
    Private Sub btnExcel_Click(sender As Object, e As EventArgs) Handles btnExcel.Click
        If DataGridView1.Rows.Count = 0 Then
            MessageBox.Show("لا يوجد بيانات لتصديرها.", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim xlApp As New Excel.Application
        Dim xlWorkBook = xlApp.Workbooks.Add()
        Dim xlWorkSheet = CType(xlWorkBook.Sheets(1), Excel.Worksheet)

        ' Headers
        For i As Integer = 0 To DataGridView1.Columns.Count - 1
            xlWorkSheet.Cells(1, i + 1) = DataGridView1.Columns(i).HeaderText
        Next

        ' Data
        For r As Integer = 0 To DataGridView1.Rows.Count - 1
            For c As Integer = 0 To DataGridView1.Columns.Count - 1
                xlWorkSheet.Cells(r + 2, c + 1) = DataGridView1.Rows(r).Cells(c).Value?.ToString()
            Next
        Next

        xlApp.Visible = True
    End Sub


End Class
