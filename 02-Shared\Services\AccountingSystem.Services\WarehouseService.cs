using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AccountingSystem.Data;
using AccountingSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;

namespace AccountingSystem.Services
{
    public class WarehouseService : IWarehouseService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<WarehouseService> _logger;

        public WarehouseService(AccountingDbContext context, ILogger<WarehouseService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<Warehouse>> GetWarehousesAsync()
        {
            try
            {
                // Get all warehouses from tblStores using raw SQL (like frmStoresMaster)
                var warehouses = new List<Warehouse>();
                var query = "SELECT SN, Store, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn FROM tblStores ORDER BY SN";

                using var connection = new SqlConnection(_context.Database.GetConnectionString());
                using var command = new SqlCommand(query, connection);

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    warehouses.Add(new Warehouse
                    {
                        SN = reader.GetInt32(0),
                        WarehouseName = reader.IsDBNull(1) ? "" : reader.GetString(1),
                        CreatedBy = reader.IsDBNull(2) ? null : reader.GetString(2),
                        CreatedOn = reader.IsDBNull(3) ? null : reader.GetDateTime(3),
                        ModifiedBy = reader.IsDBNull(4) ? null : reader.GetString(4),
                        ModifiedOn = reader.IsDBNull(5) ? null : reader.GetDateTime(5)
                    });
                }

                return warehouses;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting warehouses");
                return new List<Warehouse>();
            }
        }

        public async Task<Warehouse?> GetWarehouseByIdAsync(int id)
        {
            try
            {
                var query = "SELECT SN, Store, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn FROM tblStores WHERE SN = @id";

                using var connection = new SqlConnection(_context.Database.GetConnectionString());
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@id", id);

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    return new Warehouse
                    {
                        SN = reader.GetInt32(0),
                        WarehouseName = reader.IsDBNull(1) ? "" : reader.GetString(1),
                        CreatedBy = reader.IsDBNull(2) ? null : reader.GetString(2),
                        CreatedOn = reader.IsDBNull(3) ? null : reader.GetDateTime(3),
                        ModifiedBy = reader.IsDBNull(4) ? null : reader.GetString(4),
                        ModifiedOn = reader.IsDBNull(5) ? null : reader.GetDateTime(5)
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting warehouse by id: {Id}", id);
                return null;
            }
        }

        public async Task<(bool success, Warehouse? createdWarehouse)> CreateWarehouseAsync(Warehouse warehouse, string currentUser)
        {
            if (string.IsNullOrWhiteSpace(warehouse.WarehouseName))
            {
                return (false, null);
            }

            try
            {
                // Get next SN from tblStores (like frmStoresMaster)
                var getMaxSnQuery = "SELECT ISNULL(MAX(SN), 0) + 1 FROM tblStores";
                int nextSn;

                using var connection = new SqlConnection(_context.Database.GetConnectionString());
                using var getMaxCommand = new SqlCommand(getMaxSnQuery, connection);

                await connection.OpenAsync();
                nextSn = (int)await getMaxCommand.ExecuteScalarAsync();

                // Insert new warehouse
                var insertQuery = "INSERT INTO tblStores (SN, Store, CreatedBy, CreatedOn) VALUES (@sn, @store, @createdBy, @createdOn)";
                using var insertCommand = new SqlCommand(insertQuery, connection);
                insertCommand.Parameters.AddWithValue("@sn", nextSn);
                insertCommand.Parameters.AddWithValue("@store", warehouse.WarehouseName);
                insertCommand.Parameters.AddWithValue("@createdBy", currentUser);
                insertCommand.Parameters.AddWithValue("@createdOn", DateTime.Now);

                await insertCommand.ExecuteNonQueryAsync();

                var newWarehouse = new Warehouse
                {
                    SN = nextSn,
                    WarehouseName = warehouse.WarehouseName,
                    CreatedBy = currentUser,
                    CreatedOn = DateTime.Now
                };

                _logger.LogInformation($"Warehouse created successfully with SN: {nextSn}");
                return (true, newWarehouse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating warehouse");
                return (false, null);
            }
        }

        public async Task<bool> UpdateWarehouseAsync(Warehouse warehouse, string currentUser)
        {
            try
            {
                var updateQuery = "UPDATE tblStores SET Store = @store, ModifiedBy = @modifiedBy, ModifiedOn = @modifiedOn WHERE SN = @sn";

                using var connection = new SqlConnection(_context.Database.GetConnectionString());
                using var command = new SqlCommand(updateQuery, connection);
                command.Parameters.AddWithValue("@sn", warehouse.SN);
                command.Parameters.AddWithValue("@store", warehouse.WarehouseName);
                command.Parameters.AddWithValue("@modifiedBy", currentUser);
                command.Parameters.AddWithValue("@modifiedOn", DateTime.Now);

                await connection.OpenAsync();
                var rowsAffected = await command.ExecuteNonQueryAsync();

                if (rowsAffected > 0)
                {
                    _logger.LogInformation($"Warehouse updated successfully with SN: {warehouse.SN}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating warehouse with SN: {SN}", warehouse.SN);
                return false;
            }
        }

        public async Task<(bool success, string? errorMessage)> DeleteWarehouseAsync(int id)
        {
            try
            {
                var deleteQuery = "DELETE FROM tblStores WHERE SN = @sn";

                using var connection = new SqlConnection(_context.Database.GetConnectionString());
                using var command = new SqlCommand(deleteQuery, connection);
                command.Parameters.AddWithValue("@sn", id);

                await connection.OpenAsync();
                var rowsAffected = await command.ExecuteNonQueryAsync();

                if (rowsAffected > 0)
                {
                    _logger.LogInformation($"Warehouse deleted successfully with SN: {id}");
                    return (true, null);
                }

                return (false, "المستودع غير موجود");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting warehouse with SN: {SN}", id);
                return (false, "حدث خطأ أثناء حذف المستودع");
            }
        }

        public async Task<bool> WarehouseExistsAsync(int id)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM tblStores WHERE SN = @sn";

                using var connection = new SqlConnection(_context.Database.GetConnectionString());
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@sn", id);

                await connection.OpenAsync();
                var count = (int)await command.ExecuteScalarAsync();

                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if warehouse exists with SN: {SN}", id);
                return false;
            }
        }
    }
}
