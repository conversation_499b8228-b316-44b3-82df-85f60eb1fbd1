@model List<AccountingSystem.Models.PurchaseInvoice>
@{
    Layout = "_Layout";
    ViewData["Title"] = ViewBag.PageTitle ?? "فواتير المشتريات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-list me-2"></i>@ViewData["Title"]</h4>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <a class="btn btn-success" href="@Url.Action("Create", "Purchase")"><i class="fas fa-plus"></i> إنشاء فاتورة جديدة</a>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover text-end" dir="rtl">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>المورد</th>
                                    <th>المخزن</th>
                                    <th>الصافي</th>
                                    <th>الحالة</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model != null && Model.Any())
                                {
                                    foreach (var inv in Model)
                                    {
                                        <tr>
                                            <td>@inv.TrxNo</td>
                                            <td>@(inv.TrxDate?.ToString("yyyy-MM-dd") ?? "-")</td>
                                            <td>@inv.PartnerName</td>
                                            <td>@inv.Store</td>
                                            <td>@((inv.TrxNetAmount ?? 0).ToString("N2"))</td>
                                            <td>@inv.ReadyForUse</td>
                                            <td>
                                                <a class="btn btn-sm btn-secondary" href="@Url.Action("Edit", "Purchase", new { id = inv.TrxNo })">تعديل</a>
                                                <a class="btn btn-sm btn-info" href="@Url.Action("Details", "Purchase", new { id = inv.TrxNo })">عرض</a>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="7" class="text-center">لا توجد فواتير مكتملة</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
