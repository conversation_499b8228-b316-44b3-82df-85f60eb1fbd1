@model AccountingSystem.Models.Customer

@{
    ViewData["Title"] = "حذف العميل";
}

<h1>@ViewData["Title"]</h1>

<div class="alert alert-danger">
    <h4>هل أنت متأكد من حذف هذا العميل؟</h4>
    <p>هذا الإجراء لا يمكن التراجع عنه.</p>
</div>

<div>
    <h4>بيانات العميل</h4>
    <hr />
    
    <div class="row">
        <!-- العمود الأول - معلومات العميل الأساسية -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معلومات العميل الأساسية</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.CustomerNo)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.CustomerNo)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.CustomerName)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.CustomerName)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.FirstName)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.FirstName)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.LastName)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.LastName)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.Mobile)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.Mobile)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.Phone)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.Phone)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.Email)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.Email)</dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- العمود الثاني - معلومات الدفع والأعمال -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الدفع والأعمال</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.PaymentMethod)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.PaymentMethod)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.CreditLimit)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.CreditLimit)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.PaymentTerm)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.PaymentTerm)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.ContactPerson)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.ContactPerson)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.CR)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.CR)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.VATRegNo)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.VATRegNo)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.Shop)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.Shop)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.Status)</dt>
                        <dd class="col-sm-6">
                            @if (Model.Status == "نشط")
                            {
                                <span class="badge bg-success">@Model.Status</span>
                            }
                            else if (Model.Status == "غير نشط")
                            {
                                <span class="badge bg-danger">@Model.Status</span>
                            }
                            else
                            {
                                <span class="badge bg-warning">@Model.Status</span>
                            }
                        </dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.LocalCustomer)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.LocalCustomer)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.EmployeeNo)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.EmployeeNo)</dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- العمود الثالث - العنوان والملاحظات -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">العنوان والملاحظات</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.StreetAddress1)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.StreetAddress1)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.StreetAddress2)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.StreetAddress2)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.City)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.City)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.Region)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.Region)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.PostalCode)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.PostalCode)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.BuildingNo)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.BuildingNo)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.AdditionalNo)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.AdditionalNo)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.District)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.District)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.Notes)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.Notes)</dd>

                        <dt class="col-sm-6">@Html.DisplayNameFor(model => model.OldCode)</dt>
                        <dd class="col-sm-6">@Html.DisplayFor(model => model.OldCode)</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<form asp-action="Delete" class="mt-3">
    <input type="hidden" asp-for="CustomerNo" />
    <button type="submit" class="btn btn-danger">
        <i class="fas fa-trash"></i>
        حذف
    </button>
    <a asp-action="Index" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i>
        العودة إلى القائمة
    </a>
</form> 