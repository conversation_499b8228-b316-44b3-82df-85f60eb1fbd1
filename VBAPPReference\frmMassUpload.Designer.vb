﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmMassUpload
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.pnlMain = New System.Windows.Forms.Panel()
        Me.tcMain = New System.Windows.Forms.TabControl()
        Me.tpFileSelection = New System.Windows.Forms.TabPage()
        Me.gbFileSelection = New System.Windows.Forms.GroupBox()
        Me.lblFileInfo = New System.Windows.Forms.Label()
        Me.btnBrowseFile = New System.Windows.Forms.Button()
        Me.txtFilePath = New System.Windows.Forms.TextBox()
        Me.lblFilePath = New System.Windows.Forms.Label()
        Me.gbFilePreview = New System.Windows.Forms.GroupBox()
        Me.dgvFilePreview = New System.Windows.Forms.DataGridView()
        Me.tpFieldMapping = New System.Windows.Forms.TabPage()
        Me.gbFieldMapping = New System.Windows.Forms.GroupBox()
        Me.dgvFieldMapping = New System.Windows.Forms.DataGridView()
        Me.colSystemField = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.colFileColumn = New System.Windows.Forms.DataGridViewComboBoxColumn()
        Me.colMappingType = New System.Windows.Forms.DataGridViewComboBoxColumn()
        Me.colFixedValue = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.colRequired = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.colStatus = New System.Windows.Forms.DataGridViewImageColumn()
        Me.gbMappingOptions = New System.Windows.Forms.GroupBox()
        Me.btnAutoMap = New System.Windows.Forms.Button()
        Me.btnClearMapping = New System.Windows.Forms.Button()
        Me.tpDataPreview = New System.Windows.Forms.TabPage()
        Me.gbDataStats = New System.Windows.Forms.GroupBox()
        Me.lblValidRecords = New System.Windows.Forms.Label()
        Me.lblInvalidRecords = New System.Windows.Forms.Label()
        Me.lblTotalRecords = New System.Windows.Forms.Label()
        Me.lblUpdateRecords = New System.Windows.Forms.Label()
        Me.lblInsertRecords = New System.Windows.Forms.Label()
        Me.gbDataPreview = New System.Windows.Forms.GroupBox()
        Me.dgvDataPreview = New System.Windows.Forms.DataGridView()
        Me.gbValidation = New System.Windows.Forms.GroupBox()
        Me.btnValidateData = New System.Windows.Forms.Button()
        Me.chkValidatePhotos = New System.Windows.Forms.CheckBox()
        Me.chkValidateLookups = New System.Windows.Forms.CheckBox()
        Me.chkValidateRequired = New System.Windows.Forms.CheckBox()
        Me.tpUpload = New System.Windows.Forms.TabPage()
        Me.gbUploadProgress = New System.Windows.Forms.GroupBox()
        Me.lblProgressStatus = New System.Windows.Forms.Label()
        Me.lblProgressPercent = New System.Windows.Forms.Label()
        Me.pbUploadProgress = New System.Windows.Forms.ProgressBar()
        Me.gbUploadOptions = New System.Windows.Forms.GroupBox()
        Me.chkBackupBeforeUpload = New System.Windows.Forms.CheckBox()
        Me.chkStopOnError = New System.Windows.Forms.CheckBox()
        Me.nudBatchSize = New System.Windows.Forms.NumericUpDown()
        Me.lblBatchSize = New System.Windows.Forms.Label()
        Me.gbUploadResults = New System.Windows.Forms.GroupBox()
        Me.dgvUploadResults = New System.Windows.Forms.DataGridView()
        Me.pnlButtons = New System.Windows.Forms.Panel()
        Me.btnClose = New System.Windows.Forms.Button()
        Me.btnUpload = New System.Windows.Forms.Button()
        Me.btnNext = New System.Windows.Forms.Button()
        Me.btnPrevious = New System.Windows.Forms.Button()
        Me.ofdSelectFile = New System.Windows.Forms.OpenFileDialog()
        Me.bgwUpload = New System.ComponentModel.BackgroundWorker()
        Me.pnlMain.SuspendLayout()
        Me.tcMain.SuspendLayout()
        Me.tpFileSelection.SuspendLayout()
        Me.gbFileSelection.SuspendLayout()
        Me.gbFilePreview.SuspendLayout()
        CType(Me.dgvFilePreview, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tpFieldMapping.SuspendLayout()
        Me.gbFieldMapping.SuspendLayout()
        CType(Me.dgvFieldMapping, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.gbMappingOptions.SuspendLayout()
        Me.tpDataPreview.SuspendLayout()
        Me.gbDataStats.SuspendLayout()
        Me.gbDataPreview.SuspendLayout()
        CType(Me.dgvDataPreview, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.gbValidation.SuspendLayout()
        Me.tpUpload.SuspendLayout()
        Me.gbUploadProgress.SuspendLayout()
        Me.gbUploadOptions.SuspendLayout()
        CType(Me.nudBatchSize, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.gbUploadResults.SuspendLayout()
        CType(Me.dgvUploadResults, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlButtons.SuspendLayout()
        Me.SuspendLayout()
        '
        'pnlMain
        '
        Me.pnlMain.Controls.Add(Me.tcMain)
        Me.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlMain.Location = New System.Drawing.Point(0, 0)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(1200, 650)
        Me.pnlMain.TabIndex = 0
        '
        'tcMain
        '
        Me.tcMain.Controls.Add(Me.tpFileSelection)
        Me.tcMain.Controls.Add(Me.tpFieldMapping)
        Me.tcMain.Controls.Add(Me.tpDataPreview)
        Me.tcMain.Controls.Add(Me.tpUpload)
        Me.tcMain.Dock = System.Windows.Forms.DockStyle.Fill
        Me.tcMain.Font = New System.Drawing.Font("Arial", 10.2!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tcMain.Location = New System.Drawing.Point(0, 0)
        Me.tcMain.Name = "tcMain"
        Me.tcMain.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.tcMain.RightToLeftLayout = True
        Me.tcMain.SelectedIndex = 0
        Me.tcMain.Size = New System.Drawing.Size(1200, 650)
        Me.tcMain.TabIndex = 0
        '
        'tpFileSelection
        '
        Me.tpFileSelection.Controls.Add(Me.gbFileSelection)
        Me.tpFileSelection.Controls.Add(Me.gbFilePreview)
        Me.tpFileSelection.Location = New System.Drawing.Point(4, 25)
        Me.tpFileSelection.Name = "tpFileSelection"
        Me.tpFileSelection.Padding = New System.Windows.Forms.Padding(5)
        Me.tpFileSelection.Size = New System.Drawing.Size(1192, 621)
        Me.tpFileSelection.TabIndex = 0
        Me.tpFileSelection.Text = "اختيار الملف"
        Me.tpFileSelection.UseVisualStyleBackColor = True
        '
        'gbFileSelection
        '
        Me.gbFileSelection.Controls.Add(Me.lblFileInfo)
        Me.gbFileSelection.Controls.Add(Me.btnBrowseFile)
        Me.gbFileSelection.Controls.Add(Me.txtFilePath)
        Me.gbFileSelection.Controls.Add(Me.lblFilePath)
        Me.gbFileSelection.Dock = System.Windows.Forms.DockStyle.Top
        Me.gbFileSelection.Location = New System.Drawing.Point(5, 5)
        Me.gbFileSelection.Name = "gbFileSelection"
        Me.gbFileSelection.Size = New System.Drawing.Size(1182, 95)
        Me.gbFileSelection.TabIndex = 0
        Me.gbFileSelection.TabStop = False
        Me.gbFileSelection.Text = "اختيار ملف البيانات"
        '
        'lblFileInfo
        '
        Me.lblFileInfo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblFileInfo.ForeColor = System.Drawing.Color.Blue
        Me.lblFileInfo.Location = New System.Drawing.Point(6, 70)
        Me.lblFileInfo.Name = "lblFileInfo"
        Me.lblFileInfo.Size = New System.Drawing.Size(1170, 18)
        Me.lblFileInfo.TabIndex = 3
        Me.lblFileInfo.Text = "يدعم النظام ملفات: CSV, Excel (XLS, XLSX)"
        Me.lblFileInfo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnBrowseFile
        '
        Me.btnBrowseFile.Location = New System.Drawing.Point(6, 43)
        Me.btnBrowseFile.Name = "btnBrowseFile"
        Me.btnBrowseFile.Size = New System.Drawing.Size(80, 23)
        Me.btnBrowseFile.TabIndex = 2
        Me.btnBrowseFile.Text = "تصفح..."
        Me.btnBrowseFile.UseVisualStyleBackColor = True
        '
        'txtFilePath
        '
        Me.txtFilePath.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtFilePath.Location = New System.Drawing.Point(92, 43)
        Me.txtFilePath.Name = "txtFilePath"
        Me.txtFilePath.ReadOnly = True
        Me.txtFilePath.Size = New System.Drawing.Size(946, 23)
        Me.txtFilePath.TabIndex = 1
        '
        'lblFilePath
        '
        Me.lblFilePath.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblFilePath.AutoSize = True
        Me.lblFilePath.Location = New System.Drawing.Point(1044, 21)
        Me.lblFilePath.Name = "lblFilePath"
        Me.lblFilePath.Size = New System.Drawing.Size(64, 16)
        Me.lblFilePath.TabIndex = 0
        Me.lblFilePath.Text = "مسار الملف:"
        '
        'gbFilePreview
        '
        Me.gbFilePreview.Controls.Add(Me.dgvFilePreview)
        Me.gbFilePreview.Dock = System.Windows.Forms.DockStyle.Fill
        Me.gbFilePreview.Location = New System.Drawing.Point(5, 5)
        Me.gbFilePreview.Name = "gbFilePreview"
        Me.gbFilePreview.Padding = New System.Windows.Forms.Padding(10)
        Me.gbFilePreview.Size = New System.Drawing.Size(1182, 611)
        Me.gbFilePreview.TabIndex = 1
        Me.gbFilePreview.TabStop = False
        Me.gbFilePreview.Text = "معاينة محتوى الملف"
        '
        'dgvFilePreview
        '
        Me.dgvFilePreview.AllowUserToAddRows = False
        Me.dgvFilePreview.AllowUserToDeleteRows = False
        Me.dgvFilePreview.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells
        Me.dgvFilePreview.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgvFilePreview.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.dgvFilePreview.Location = New System.Drawing.Point(10, 98)
        Me.dgvFilePreview.Margin = New System.Windows.Forms.Padding(5)
        Me.dgvFilePreview.Name = "dgvFilePreview"
        Me.dgvFilePreview.ReadOnly = True
        Me.dgvFilePreview.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.dgvFilePreview.RowHeadersWidth = 120
        Me.dgvFilePreview.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgvFilePreview.Size = New System.Drawing.Size(1162, 503)
        Me.dgvFilePreview.TabIndex = 0
        '
        'tpFieldMapping
        '
        Me.tpFieldMapping.Controls.Add(Me.gbFieldMapping)
        Me.tpFieldMapping.Controls.Add(Me.gbMappingOptions)
        Me.tpFieldMapping.Location = New System.Drawing.Point(4, 25)
        Me.tpFieldMapping.Name = "tpFieldMapping"
        Me.tpFieldMapping.Padding = New System.Windows.Forms.Padding(3)
        Me.tpFieldMapping.Size = New System.Drawing.Size(1192, 621)
        Me.tpFieldMapping.TabIndex = 1
        Me.tpFieldMapping.Text = "ربط الحقول"
        Me.tpFieldMapping.UseVisualStyleBackColor = True
        '
        'gbFieldMapping
        '
        Me.gbFieldMapping.Controls.Add(Me.dgvFieldMapping)
        Me.gbFieldMapping.Dock = System.Windows.Forms.DockStyle.Fill
        Me.gbFieldMapping.Location = New System.Drawing.Point(3, 63)
        Me.gbFieldMapping.Name = "gbFieldMapping"
        Me.gbFieldMapping.Padding = New System.Windows.Forms.Padding(10)
        Me.gbFieldMapping.Size = New System.Drawing.Size(1186, 555)
        Me.gbFieldMapping.TabIndex = 1
        Me.gbFieldMapping.TabStop = False
        Me.gbFieldMapping.Text = "ربط حقول النظام مع أعمدة الملف"
        '
        'dgvFieldMapping
        '
        Me.dgvFieldMapping.AllowUserToAddRows = False
        Me.dgvFieldMapping.AllowUserToDeleteRows = False
        Me.dgvFieldMapping.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.dgvFieldMapping.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgvFieldMapping.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.colSystemField, Me.colFileColumn, Me.colMappingType, Me.colFixedValue, Me.colRequired, Me.colStatus})
        Me.dgvFieldMapping.Dock = System.Windows.Forms.DockStyle.Fill
        Me.dgvFieldMapping.Location = New System.Drawing.Point(10, 26)
        Me.dgvFieldMapping.Margin = New System.Windows.Forms.Padding(5)
        Me.dgvFieldMapping.Name = "dgvFieldMapping"
        Me.dgvFieldMapping.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.dgvFieldMapping.Size = New System.Drawing.Size(1166, 519)
        Me.dgvFieldMapping.TabIndex = 0
        '
        'colSystemField
        '
        Me.colSystemField.HeaderText = "حقل النظام"
        Me.colSystemField.Name = "colSystemField"
        Me.colSystemField.ReadOnly = True
        '
        'colFileColumn
        '
        Me.colFileColumn.HeaderText = "عمود الملف"
        Me.colFileColumn.Name = "colFileColumn"
        '
        'colMappingType
        '
        Me.colMappingType.HeaderText = "نوع الربط"
        Me.colMappingType.Items.AddRange(New Object() {"من الملف", "قيمة ثابتة", "تجاهل"})
        Me.colMappingType.Name = "colMappingType"
        '
        'colFixedValue
        '
        Me.colFixedValue.HeaderText = "القيمة الثابتة"
        Me.colFixedValue.Name = "colFixedValue"
        '
        'colRequired
        '
        Me.colRequired.HeaderText = "مطلوب"
        Me.colRequired.Name = "colRequired"
        '
        'colStatus
        '
        Me.colStatus.HeaderText = "الحالة"
        Me.colStatus.Name = "colStatus"
        '
        'gbMappingOptions
        '
        Me.gbMappingOptions.Controls.Add(Me.btnAutoMap)
        Me.gbMappingOptions.Controls.Add(Me.btnClearMapping)
        Me.gbMappingOptions.Dock = System.Windows.Forms.DockStyle.Top
        Me.gbMappingOptions.Location = New System.Drawing.Point(3, 3)
        Me.gbMappingOptions.Name = "gbMappingOptions"
        Me.gbMappingOptions.Size = New System.Drawing.Size(1186, 60)
        Me.gbMappingOptions.TabIndex = 0
        Me.gbMappingOptions.TabStop = False
        Me.gbMappingOptions.Text = "خيارات الربط"
        '
        'btnAutoMap
        '
        Me.btnAutoMap.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAutoMap.Location = New System.Drawing.Point(980, 25)
        Me.btnAutoMap.Name = "btnAutoMap"
        Me.btnAutoMap.Size = New System.Drawing.Size(100, 30)
        Me.btnAutoMap.TabIndex = 0
        Me.btnAutoMap.Text = "ربط تلقائي"
        Me.btnAutoMap.UseVisualStyleBackColor = True
        '
        'btnClearMapping
        '
        Me.btnClearMapping.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClearMapping.Location = New System.Drawing.Point(1086, 25)
        Me.btnClearMapping.Name = "btnClearMapping"
        Me.btnClearMapping.Size = New System.Drawing.Size(100, 30)
        Me.btnClearMapping.TabIndex = 1
        Me.btnClearMapping.Text = "مسح الربط"
        Me.btnClearMapping.UseVisualStyleBackColor = True
        '
        'tpDataPreview
        '
        Me.tpDataPreview.Controls.Add(Me.gbDataStats)
        Me.tpDataPreview.Controls.Add(Me.gbDataPreview)
        Me.tpDataPreview.Controls.Add(Me.gbValidation)
        Me.tpDataPreview.Location = New System.Drawing.Point(4, 25)
        Me.tpDataPreview.Name = "tpDataPreview"
        Me.tpDataPreview.Padding = New System.Windows.Forms.Padding(3)
        Me.tpDataPreview.Size = New System.Drawing.Size(1192, 621)
        Me.tpDataPreview.TabIndex = 2
        Me.tpDataPreview.Text = "معاينة البيانات"
        Me.tpDataPreview.UseVisualStyleBackColor = True
        '
        'gbDataStats
        '
        Me.gbDataStats.Controls.Add(Me.lblValidRecords)
        Me.gbDataStats.Controls.Add(Me.lblInvalidRecords)
        Me.gbDataStats.Controls.Add(Me.lblTotalRecords)
        Me.gbDataStats.Controls.Add(Me.lblUpdateRecords)
        Me.gbDataStats.Controls.Add(Me.lblInsertRecords)
        Me.gbDataStats.Dock = System.Windows.Forms.DockStyle.Right
        Me.gbDataStats.Location = New System.Drawing.Point(989, 63)
        Me.gbDataStats.Name = "gbDataStats"
        Me.gbDataStats.Size = New System.Drawing.Size(200, 555)
        Me.gbDataStats.TabIndex = 2
        Me.gbDataStats.TabStop = False
        Me.gbDataStats.Text = "إحصائيات البيانات"
        '
        'lblValidRecords
        '
        Me.lblValidRecords.AutoSize = True
        Me.lblValidRecords.ForeColor = System.Drawing.Color.Green
        Me.lblValidRecords.Location = New System.Drawing.Point(6, 100)
        Me.lblValidRecords.Name = "lblValidRecords"
        Me.lblValidRecords.Size = New System.Drawing.Size(104, 16)
        Me.lblValidRecords.TabIndex = 2
        Me.lblValidRecords.Text = "السجلات الصحيحة: 0"
        '
        'lblInvalidRecords
        '
        Me.lblInvalidRecords.AutoSize = True
        Me.lblInvalidRecords.ForeColor = System.Drawing.Color.Red
        Me.lblInvalidRecords.Location = New System.Drawing.Point(6, 130)
        Me.lblInvalidRecords.Name = "lblInvalidRecords"
        Me.lblInvalidRecords.Size = New System.Drawing.Size(96, 16)
        Me.lblInvalidRecords.TabIndex = 3
        Me.lblInvalidRecords.Text = "السجلات الخاطئة: 0"
        '
        'lblTotalRecords
        '
        Me.lblTotalRecords.AutoSize = True
        Me.lblTotalRecords.Font = New System.Drawing.Font("Arial", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblTotalRecords.Location = New System.Drawing.Point(6, 25)
        Me.lblTotalRecords.Name = "lblTotalRecords"
        Me.lblTotalRecords.Size = New System.Drawing.Size(96, 16)
        Me.lblTotalRecords.TabIndex = 0
        Me.lblTotalRecords.Text = "إجمالي السجلات: 0"
        '
        'lblUpdateRecords
        '
        Me.lblUpdateRecords.AutoSize = True
        Me.lblUpdateRecords.ForeColor = System.Drawing.Color.Orange
        Me.lblUpdateRecords.Location = New System.Drawing.Point(6, 70)
        Me.lblUpdateRecords.Name = "lblUpdateRecords"
        Me.lblUpdateRecords.Size = New System.Drawing.Size(92, 16)
        Me.lblUpdateRecords.TabIndex = 1
        Me.lblUpdateRecords.Text = "سجلات للتحديث: 0"
        '
        'lblInsertRecords
        '
        Me.lblInsertRecords.AutoSize = True
        Me.lblInsertRecords.ForeColor = System.Drawing.Color.Blue
        Me.lblInsertRecords.Location = New System.Drawing.Point(6, 50)
        Me.lblInsertRecords.Name = "lblInsertRecords"
        Me.lblInsertRecords.Size = New System.Drawing.Size(93, 16)
        Me.lblInsertRecords.TabIndex = 1
        Me.lblInsertRecords.Text = "سجلات للإضافة: 0"
        '
        'gbDataPreview
        '
        Me.gbDataPreview.Controls.Add(Me.dgvDataPreview)
        Me.gbDataPreview.Dock = System.Windows.Forms.DockStyle.Fill
        Me.gbDataPreview.Location = New System.Drawing.Point(3, 63)
        Me.gbDataPreview.Name = "gbDataPreview"
        Me.gbDataPreview.Padding = New System.Windows.Forms.Padding(10)
        Me.gbDataPreview.Size = New System.Drawing.Size(1186, 555)
        Me.gbDataPreview.TabIndex = 1
        Me.gbDataPreview.TabStop = False
        Me.gbDataPreview.Text = "معاينة البيانات المحضرة للرفع"
        '
        'dgvDataPreview
        '
        Me.dgvDataPreview.AllowUserToAddRows = False
        Me.dgvDataPreview.AllowUserToDeleteRows = False
        Me.dgvDataPreview.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells
        Me.dgvDataPreview.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgvDataPreview.Dock = System.Windows.Forms.DockStyle.Left
        Me.dgvDataPreview.Location = New System.Drawing.Point(10, 26)
        Me.dgvDataPreview.Margin = New System.Windows.Forms.Padding(5)
        Me.dgvDataPreview.Name = "dgvDataPreview"
        Me.dgvDataPreview.ReadOnly = True
        Me.dgvDataPreview.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.dgvDataPreview.Size = New System.Drawing.Size(980, 519)
        Me.dgvDataPreview.TabIndex = 0
        '
        'gbValidation
        '
        Me.gbValidation.Controls.Add(Me.btnValidateData)
        Me.gbValidation.Controls.Add(Me.chkValidatePhotos)
        Me.gbValidation.Controls.Add(Me.chkValidateLookups)
        Me.gbValidation.Controls.Add(Me.chkValidateRequired)
        Me.gbValidation.Dock = System.Windows.Forms.DockStyle.Top
        Me.gbValidation.Location = New System.Drawing.Point(3, 3)
        Me.gbValidation.Name = "gbValidation"
        Me.gbValidation.Size = New System.Drawing.Size(1186, 60)
        Me.gbValidation.TabIndex = 0
        Me.gbValidation.TabStop = False
        Me.gbValidation.Text = "خيارات التحقق"
        '
        'btnValidateData
        '
        Me.btnValidateData.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnValidateData.Location = New System.Drawing.Point(1086, 25)
        Me.btnValidateData.Name = "btnValidateData"
        Me.btnValidateData.Size = New System.Drawing.Size(100, 30)
        Me.btnValidateData.TabIndex = 3
        Me.btnValidateData.Text = "تحقق من البيانات"
        Me.btnValidateData.UseVisualStyleBackColor = True
        '
        'chkValidatePhotos
        '
        Me.chkValidatePhotos.AutoSize = True
        Me.chkValidatePhotos.Checked = True
        Me.chkValidatePhotos.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkValidatePhotos.Location = New System.Drawing.Point(6, 30)
        Me.chkValidatePhotos.Name = "chkValidatePhotos"
        Me.chkValidatePhotos.Size = New System.Drawing.Size(106, 20)
        Me.chkValidatePhotos.TabIndex = 2
        Me.chkValidatePhotos.Text = "التحقق من الصور"
        Me.chkValidatePhotos.UseVisualStyleBackColor = True
        '
        'chkValidateLookups
        '
        Me.chkValidateLookups.AutoSize = True
        Me.chkValidateLookups.Checked = True
        Me.chkValidateLookups.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkValidateLookups.Location = New System.Drawing.Point(200, 30)
        Me.chkValidateLookups.Name = "chkValidateLookups"
        Me.chkValidateLookups.Size = New System.Drawing.Size(153, 20)
        Me.chkValidateLookups.TabIndex = 1
        Me.chkValidateLookups.Text = "التحقق من الجداول المرجعية"
        Me.chkValidateLookups.UseVisualStyleBackColor = True
        '
        'chkValidateRequired
        '
        Me.chkValidateRequired.AutoSize = True
        Me.chkValidateRequired.Checked = True
        Me.chkValidateRequired.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkValidateRequired.Location = New System.Drawing.Point(450, 30)
        Me.chkValidateRequired.Name = "chkValidateRequired"
        Me.chkValidateRequired.Size = New System.Drawing.Size(146, 20)
        Me.chkValidateRequired.TabIndex = 0
        Me.chkValidateRequired.Text = "التحقق من الحقول المطلوبة"
        Me.chkValidateRequired.UseVisualStyleBackColor = True
        '
        'tpUpload
        '
        Me.tpUpload.Controls.Add(Me.gbUploadProgress)
        Me.tpUpload.Controls.Add(Me.gbUploadOptions)
        Me.tpUpload.Controls.Add(Me.gbUploadResults)
        Me.tpUpload.Location = New System.Drawing.Point(4, 25)
        Me.tpUpload.Name = "tpUpload"
        Me.tpUpload.Padding = New System.Windows.Forms.Padding(3)
        Me.tpUpload.Size = New System.Drawing.Size(1192, 621)
        Me.tpUpload.TabIndex = 3
        Me.tpUpload.Text = "رفع البيانات"
        Me.tpUpload.UseVisualStyleBackColor = True
        '
        'gbUploadProgress
        '
        Me.gbUploadProgress.Controls.Add(Me.lblProgressStatus)
        Me.gbUploadProgress.Controls.Add(Me.lblProgressPercent)
        Me.gbUploadProgress.Controls.Add(Me.pbUploadProgress)
        Me.gbUploadProgress.Dock = System.Windows.Forms.DockStyle.Top
        Me.gbUploadProgress.Location = New System.Drawing.Point(3, 83)
        Me.gbUploadProgress.Name = "gbUploadProgress"
        Me.gbUploadProgress.Size = New System.Drawing.Size(1186, 100)
        Me.gbUploadProgress.TabIndex = 1
        Me.gbUploadProgress.TabStop = False
        Me.gbUploadProgress.Text = "تقدم الرفع"
        '
        'lblProgressStatus
        '
        Me.lblProgressStatus.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblProgressStatus.Location = New System.Drawing.Point(6, 70)
        Me.lblProgressStatus.Name = "lblProgressStatus"
        Me.lblProgressStatus.Size = New System.Drawing.Size(1174, 23)
        Me.lblProgressStatus.TabIndex = 2
        Me.lblProgressStatus.Text = "جاهز للبدء..."
        Me.lblProgressStatus.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblProgressPercent
        '
        Me.lblProgressPercent.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblProgressPercent.Location = New System.Drawing.Point(1086, 25)
        Me.lblProgressPercent.Name = "lblProgressPercent"
        Me.lblProgressPercent.Size = New System.Drawing.Size(100, 23)
        Me.lblProgressPercent.TabIndex = 1
        Me.lblProgressPercent.Text = "0%"
        Me.lblProgressPercent.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pbUploadProgress
        '
        Me.pbUploadProgress.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pbUploadProgress.Location = New System.Drawing.Point(6, 25)
        Me.pbUploadProgress.Name = "pbUploadProgress"
        Me.pbUploadProgress.Size = New System.Drawing.Size(1074, 30)
        Me.pbUploadProgress.TabIndex = 0
        '
        'gbUploadOptions
        '
        Me.gbUploadOptions.Controls.Add(Me.chkBackupBeforeUpload)
        Me.gbUploadOptions.Controls.Add(Me.chkStopOnError)
        Me.gbUploadOptions.Controls.Add(Me.nudBatchSize)
        Me.gbUploadOptions.Controls.Add(Me.lblBatchSize)
        Me.gbUploadOptions.Dock = System.Windows.Forms.DockStyle.Top
        Me.gbUploadOptions.Location = New System.Drawing.Point(3, 3)
        Me.gbUploadOptions.Name = "gbUploadOptions"
        Me.gbUploadOptions.Size = New System.Drawing.Size(1186, 80)
        Me.gbUploadOptions.TabIndex = 0
        Me.gbUploadOptions.TabStop = False
        Me.gbUploadOptions.Text = "خيارات الرفع"
        '
        'chkBackupBeforeUpload
        '
        Me.chkBackupBeforeUpload.AutoSize = True
        Me.chkBackupBeforeUpload.Checked = True
        Me.chkBackupBeforeUpload.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkBackupBeforeUpload.Location = New System.Drawing.Point(200, 50)
        Me.chkBackupBeforeUpload.Name = "chkBackupBeforeUpload"
        Me.chkBackupBeforeUpload.Size = New System.Drawing.Size(127, 20)
        Me.chkBackupBeforeUpload.TabIndex = 3
        Me.chkBackupBeforeUpload.Text = "نسخ احتياطي قبل الرفع"
        Me.chkBackupBeforeUpload.UseVisualStyleBackColor = True
        '
        'chkStopOnError
        '
        Me.chkStopOnError.AutoSize = True
        Me.chkStopOnError.Location = New System.Drawing.Point(450, 50)
        Me.chkStopOnError.Name = "chkStopOnError"
        Me.chkStopOnError.Size = New System.Drawing.Size(97, 20)
        Me.chkStopOnError.TabIndex = 2
        Me.chkStopOnError.Text = "توقف عند الخطأ"
        Me.chkStopOnError.UseVisualStyleBackColor = True
        '
        'nudBatchSize
        '
        Me.nudBatchSize.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.nudBatchSize.Location = New System.Drawing.Point(980, 25)
        Me.nudBatchSize.Maximum = New Decimal(New Integer() {1000, 0, 0, 0})
        Me.nudBatchSize.Minimum = New Decimal(New Integer() {1, 0, 0, 0})
        Me.nudBatchSize.Name = "nudBatchSize"
        Me.nudBatchSize.Size = New System.Drawing.Size(100, 23)
        Me.nudBatchSize.TabIndex = 1
        Me.nudBatchSize.Value = New Decimal(New Integer() {100, 0, 0, 0})
        '
        'lblBatchSize
        '
        Me.lblBatchSize.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblBatchSize.AutoSize = True
        Me.lblBatchSize.Location = New System.Drawing.Point(1086, 27)
        Me.lblBatchSize.Name = "lblBatchSize"
        Me.lblBatchSize.Size = New System.Drawing.Size(95, 16)
        Me.lblBatchSize.TabIndex = 0
        Me.lblBatchSize.Text = "حجم الدفعة الواحدة:"
        '
        'gbUploadResults
        '
        Me.gbUploadResults.Controls.Add(Me.dgvUploadResults)
        Me.gbUploadResults.Dock = System.Windows.Forms.DockStyle.Fill
        Me.gbUploadResults.Location = New System.Drawing.Point(3, 3)
        Me.gbUploadResults.Name = "gbUploadResults"
        Me.gbUploadResults.Padding = New System.Windows.Forms.Padding(10)
        Me.gbUploadResults.Size = New System.Drawing.Size(1186, 615)
        Me.gbUploadResults.TabIndex = 2
        Me.gbUploadResults.TabStop = False
        Me.gbUploadResults.Text = "نتائج الرفع"
        '
        'dgvUploadResults
        '
        Me.dgvUploadResults.AllowUserToAddRows = False
        Me.dgvUploadResults.AllowUserToDeleteRows = False
        Me.dgvUploadResults.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells
        Me.dgvUploadResults.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.dgvUploadResults.Dock = System.Windows.Forms.DockStyle.Fill
        Me.dgvUploadResults.Location = New System.Drawing.Point(10, 26)
        Me.dgvUploadResults.Margin = New System.Windows.Forms.Padding(5)
        Me.dgvUploadResults.Name = "dgvUploadResults"
        Me.dgvUploadResults.ReadOnly = True
        Me.dgvUploadResults.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.dgvUploadResults.Size = New System.Drawing.Size(1166, 579)
        Me.dgvUploadResults.TabIndex = 0
        '
        'pnlButtons
        '
        Me.pnlButtons.Controls.Add(Me.btnClose)
        Me.pnlButtons.Controls.Add(Me.btnUpload)
        Me.pnlButtons.Controls.Add(Me.btnNext)
        Me.pnlButtons.Controls.Add(Me.btnPrevious)
        Me.pnlButtons.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlButtons.Location = New System.Drawing.Point(0, 650)
        Me.pnlButtons.Name = "pnlButtons"
        Me.pnlButtons.Size = New System.Drawing.Size(1200, 50)
        Me.pnlButtons.TabIndex = 1
        '
        'btnClose
        '
        Me.btnClose.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnClose.Location = New System.Drawing.Point(12, 10)
        Me.btnClose.Name = "btnClose"
        Me.btnClose.Size = New System.Drawing.Size(100, 30)
        Me.btnClose.TabIndex = 3
        Me.btnClose.Text = "إغلاق"
        Me.btnClose.UseVisualStyleBackColor = True
        '
        'btnUpload
        '
        Me.btnUpload.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnUpload.Enabled = False
        Me.btnUpload.Font = New System.Drawing.Font("Arial", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnUpload.Location = New System.Drawing.Point(880, 10)
        Me.btnUpload.Name = "btnUpload"
        Me.btnUpload.Size = New System.Drawing.Size(100, 30)
        Me.btnUpload.TabIndex = 2
        Me.btnUpload.Text = "رفع البيانات"
        Me.btnUpload.UseVisualStyleBackColor = True
        '
        'btnNext
        '
        Me.btnNext.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnNext.Enabled = False
        Me.btnNext.Location = New System.Drawing.Point(986, 10)
        Me.btnNext.Name = "btnNext"
        Me.btnNext.Size = New System.Drawing.Size(100, 30)
        Me.btnNext.TabIndex = 1
        Me.btnNext.Text = "التالي"
        Me.btnNext.UseVisualStyleBackColor = True
        '
        'btnPrevious
        '
        Me.btnPrevious.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnPrevious.Enabled = False
        Me.btnPrevious.Location = New System.Drawing.Point(1092, 10)
        Me.btnPrevious.Name = "btnPrevious"
        Me.btnPrevious.Size = New System.Drawing.Size(100, 30)
        Me.btnPrevious.TabIndex = 0
        Me.btnPrevious.Text = "السابق"
        Me.btnPrevious.UseVisualStyleBackColor = True
        '
        'ofdSelectFile
        '
        Me.ofdSelectFile.Filter = "Excel Files|*.xlsx;*.xls|CSV Files|*.csv|All Supported|*.xlsx;*.xls;*.csv"
        Me.ofdSelectFile.Title = "اختيار ملف البيانات"
        '
        'bgwUpload
        '
        Me.bgwUpload.WorkerReportsProgress = True
        Me.bgwUpload.WorkerSupportsCancellation = True
        '
        'frmMassUpload
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(8.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.White
        Me.ClientSize = New System.Drawing.Size(1200, 700)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.pnlButtons)
        Me.Font = New System.Drawing.Font("Arial", 10.2!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.Name = "frmMassUpload"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "رفع الأصناف بالجملة"
        Me.pnlMain.ResumeLayout(False)
        Me.tcMain.ResumeLayout(False)
        Me.tpFileSelection.ResumeLayout(False)
        Me.gbFileSelection.ResumeLayout(False)
        Me.gbFileSelection.PerformLayout()
        Me.gbFilePreview.ResumeLayout(False)
        CType(Me.dgvFilePreview, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tpFieldMapping.ResumeLayout(False)
        Me.gbFieldMapping.ResumeLayout(False)
        CType(Me.dgvFieldMapping, System.ComponentModel.ISupportInitialize).EndInit()
        Me.gbMappingOptions.ResumeLayout(False)
        Me.tpDataPreview.ResumeLayout(False)
        Me.gbDataStats.ResumeLayout(False)
        Me.gbDataStats.PerformLayout()
        Me.gbDataPreview.ResumeLayout(False)
        CType(Me.dgvDataPreview, System.ComponentModel.ISupportInitialize).EndInit()
        Me.gbValidation.ResumeLayout(False)
        Me.gbValidation.PerformLayout()
        Me.tpUpload.ResumeLayout(False)
        Me.gbUploadProgress.ResumeLayout(False)
        Me.gbUploadOptions.ResumeLayout(False)
        Me.gbUploadOptions.PerformLayout()
        CType(Me.nudBatchSize, System.ComponentModel.ISupportInitialize).EndInit()
        Me.gbUploadResults.ResumeLayout(False)
        CType(Me.dgvUploadResults, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlButtons.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub

    ' Control declarations
    Friend WithEvents pnlMain As Panel
    Friend WithEvents tcMain As TabControl
    Friend WithEvents tpFileSelection As TabPage
    Friend WithEvents gbFileSelection As GroupBox
    Friend WithEvents lblFileInfo As Label
    Friend WithEvents btnBrowseFile As Button
    Friend WithEvents txtFilePath As TextBox
    Friend WithEvents lblFilePath As Label
    Friend WithEvents gbFilePreview As GroupBox
    Friend WithEvents dgvFilePreview As DataGridView
    Friend WithEvents tpFieldMapping As TabPage
    Friend WithEvents gbMappingOptions As GroupBox
    Friend WithEvents btnAutoMap As Button
    Friend WithEvents btnClearMapping As Button
    Friend WithEvents gbFieldMapping As GroupBox
    Friend WithEvents dgvFieldMapping As DataGridView
    Friend WithEvents colSystemField As DataGridViewTextBoxColumn
    Friend WithEvents colFileColumn As DataGridViewComboBoxColumn
    Friend WithEvents colMappingType As DataGridViewComboBoxColumn
    Friend WithEvents colFixedValue As DataGridViewTextBoxColumn
    Friend WithEvents colRequired As DataGridViewCheckBoxColumn
    Friend WithEvents colStatus As DataGridViewImageColumn
    Friend WithEvents tpDataPreview As TabPage
    Friend WithEvents gbDataStats As GroupBox
    Friend WithEvents lblValidRecords As Label
    Friend WithEvents lblInvalidRecords As Label
    Friend WithEvents lblTotalRecords As Label
    Friend WithEvents lblUpdateRecords As Label
    Friend WithEvents lblInsertRecords As Label
    Friend WithEvents gbDataPreview As GroupBox
    Friend WithEvents dgvDataPreview As DataGridView
    Friend WithEvents gbValidation As GroupBox
    Friend WithEvents btnValidateData As Button
    Friend WithEvents chkValidatePhotos As CheckBox
    Friend WithEvents chkValidateLookups As CheckBox
    Friend WithEvents chkValidateRequired As CheckBox
    Friend WithEvents tpUpload As TabPage
    Friend WithEvents gbUploadProgress As GroupBox
    Friend WithEvents lblProgressStatus As Label
    Friend WithEvents lblProgressPercent As Label
    Friend WithEvents pbUploadProgress As ProgressBar
    Friend WithEvents gbUploadOptions As GroupBox
    Friend WithEvents chkBackupBeforeUpload As CheckBox
    Friend WithEvents chkStopOnError As CheckBox
    Friend WithEvents nudBatchSize As NumericUpDown
    Friend WithEvents lblBatchSize As Label
    Friend WithEvents gbUploadResults As GroupBox
    Friend WithEvents dgvUploadResults As DataGridView
    Friend WithEvents pnlButtons As Panel
    Friend WithEvents btnClose As Button
    Friend WithEvents btnUpload As Button
    Friend WithEvents btnNext As Button
    Friend WithEvents btnPrevious As Button
    Friend WithEvents ofdSelectFile As OpenFileDialog
    Friend WithEvents bgwUpload As System.ComponentModel.BackgroundWorker

End Class