using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AccountingSystem.Services;
using Microsoft.AspNetCore.Localization;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    [Route("posNew")]
    public class POSNewController : Controller
    {
        private readonly IPOSService _posService;
        public POSNewController(IPOSService posService)
        {
            _posService = posService;
        }

        [HttpGet("")]
        public IActionResult Index()
        {
            // Data loading will be handled in the view via AJAX for responsiveness
            return View();
        }

        [HttpPost("setLanguage")]
        [ValidateAntiForgeryToken]
        public IActionResult SetLanguage(string lang)
        {
            if (lang != "ar" && lang != "en") lang = "ar";
            Response.Cookies.Append(
                CookieRequestCultureProvider.DefaultCookieName,
                CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(lang)),
                new CookieOptions { Expires = DateTimeOffset.UtcNow.AddYears(1) }
            );
            return RedirectToAction("Index");
        }

        // --- AJAX Endpoints for POSNew ---

        [HttpGet("init")]
        public async Task<JsonResult> GetInitData()
        {
            // Loads stores, customers, items, favorites, user settings
            var data = await _posService.GetPOSInitDataAsync();
            // Add user settings, favorites, etc. as needed
            return Json(data);
        }

        [HttpPost("cart/add")]
        public async Task<JsonResult> AddItemToCart(int invoiceNo, long itemNo, decimal quantity, decimal unitPrice, string username, string store)
        {
            var item = await _posService.AddItemToInvoiceAsync(invoiceNo, itemNo, quantity, unitPrice, username, store);
            return Json(item);
        }

        [HttpPost("cart/update")]
        public async Task<JsonResult> UpdateCartItem(int invoiceNo, int lineSN, decimal quantity, decimal unitPrice, string username)
        {
            var result = await _posService.UpdateInvoiceItemAsync(invoiceNo, lineSN, quantity, unitPrice, username);
            return Json(result);
        }

        [HttpPost("cart/delete")]
        public async Task<JsonResult> DeleteCartItem(int invoiceNo, int lineSN, string username)
        {
            var result = await _posService.DeleteInvoiceItemAsync(invoiceNo, lineSN, username);
            return Json(result);
        }

        [HttpPost("barcode/process")]
        public async Task<JsonResult> ProcessBarcode(string barcode, int invoiceNo, string store, string username)
        {
            var result = await _posService.ProcessBarcodeAsync(barcode, invoiceNo, store, username);
            return Json(result);
        }

        [HttpGet("items/search")]
        public async Task<JsonResult> SearchItems(string searchTerm)
        {
            var items = await _posService.SearchItemsAsync(searchTerm);
            return Json(items);
        }

        [HttpPost("payment/save")]
        public async Task<JsonResult> SaveInvoice(int invoiceNo, string username)
        {
            var invoice = await _posService.SaveInvoiceAsync(invoiceNo, username);
            return Json(invoice);
        }

        [HttpPost("payment/complete")]
        public async Task<JsonResult> CompleteInvoice(int invoiceNo, decimal cashAmount, decimal cardAmount, string username)
        {
            var invoice = await _posService.CompleteInvoiceAsync(invoiceNo, cashAmount, cardAmount, username);
            return Json(invoice);
        }

        [HttpPost("abort")]
        public async Task<JsonResult> AbortInvoice(int invoiceNo)
        {
            var result = await _posService.ClearInvoiceItemsAsync(invoiceNo);
            return Json(result);
        }

        [HttpGet("invoice/current")]
        public async Task<JsonResult> GetCurrentInvoice()
        {
            var invoice = await _posService.GetCurrentInvoiceAsync();
            return Json(invoice);
        }

        [HttpGet("invoice/items")]
        public async Task<JsonResult> GetInvoiceItems(int invoiceNo)
        {
            var items = await _posService.GetInvoiceItemsAsync(invoiceNo);
            return Json(items);
        }

        [HttpGet("invoice/payments")]
        public async Task<JsonResult> GetInvoicePayments(int invoiceNo)
        {
            var payments = await _posService.GetInvoicePaymentsAsync(invoiceNo);
            return Json(payments);
        }

        [HttpGet("user/settings")]
        public async Task<JsonResult> GetUserSettings(string username)
        {
            var settings = await _posService.GetUserAuthorizationAsync(username);
            return Json(settings);
        }
    }
} 