﻿Imports System.Data.SqlClient
Public Class frmPaySplit
    Public CallerForm As String = ""
    Private Sub frmPaySplit_Load(sender As Object, e As EventArgs) Handles MyBase.Load



        'Dim CMD As New SqlCommand("SELECT Pay_mthd, Pay_mthd_Text FROM tblPayMethod", Con)

        'If Con.State <> ConnectionState.Open Then
        '    Con.Open()
        'End If

        'Dim reader As SqlDataReader = CMD.ExecuteReader()
        'Dim dt As New DataTable()

        'dt.Load(reader)
        'reader.Close()

        '' Bind the ComboBox to DataTable
        'cmbPaymentMethod1.DataSource = dt
        'cmbPaymentMethod1.DisplayMember = "Pay_mthd_Text" ' Show Name
        'cmbPaymentMethod1.ValueMember = "Pay_mthd" ' Store ID

        'cmbPaymentMethod2.DataSource = dt
        'cmbPaymentMethod2.DisplayMember = "Pay_mthd_Text"
        'cmbPaymentMethod2.ValueMember = "Pay_mthd"

        'If Con.State <> ConnectionState.Closed Then
        '    Con.Close()
        'End If



        Dim paymentMethods As New DataTable()

        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT Pay_mthd, Pay_mthd_Text FROM tblPayMethod", con)
            Dim adapter As New SqlDataAdapter(cmd)
            adapter.Fill(paymentMethods)
        End Using

        cmbPaymentMethod1.DataSource = paymentMethods.Copy()
        cmbPaymentMethod1.DisplayMember = "Pay_mthd_Text"
        cmbPaymentMethod1.ValueMember = "Pay_mthd"

        cmbPaymentMethod2.DataSource = paymentMethods
        cmbPaymentMethod2.DisplayMember = "Pay_mthd_Text"
        cmbPaymentMethod2.ValueMember = "Pay_mthd"

        Dim defaultID = GetDefaultPaymentMethodID()

        cmbPaymentMethod1.SelectedValue = defaultID
        txtAmount1.Text = lblInvoiceTotal.Text
        txtAmount2.Text = "0"

        cmbPaymentMethod2.Visible = False
        txtAmount2.Visible = False
        lblMethod2.Visible = False
    End Sub

    Private _invoiceTotal As Decimal
    Public Property InvoiceTotal As Decimal
        Get
            Return _invoiceTotal
        End Get
        Set(value As Decimal)
            _invoiceTotal = value
            If lblInvoiceTotal IsNot Nothing Then
                lblInvoiceTotal.Text = value.ToString("0.00")
            Else
                ' Handle the case where lblInvoiceTotal is not initialized
                MessageBox.Show("lblInvoiceTotal is not initialized.")
            End If
        End Set
    End Property


    Private Sub txtAmount1_TextChanged(sender As Object, e As EventArgs) Handles txtAmount1.TextChanged
        Dim total As Decimal = Convert.ToDecimal(lblInvoiceTotal.Text)
        Dim amt1 As Decimal = 0
        Decimal.TryParse(txtAmount1.Text, amt1)

        If amt1 < total Then
            cmbPaymentMethod2.Visible = True
            cmbPaymentMethod2.SelectedValue = cmbPaymentMethod1.SelectedValue + 1
            txtAmount2.Visible = True
            lblMethod2.Visible = True
            txtAmount2.Text = (total - amt1).ToString("0.00")
        Else
            cmbPaymentMethod2.Visible = False
            txtAmount2.Visible = False
            lblMethod2.Visible = False
            txtAmount2.Text = "0"
        End If
    End Sub
    Private Sub btnConfirm_Click(sender As Object, e As EventArgs) Handles btnConfirm.Click
        Dim amt1 As Decimal = Convert.ToDecimal(txtAmount1.Text)
        Dim amt2 As Decimal = If(txtAmount2.Visible, Convert.ToDecimal(txtAmount2.Text), 0)
        Dim total As Decimal = Convert.ToDecimal(lblInvoiceTotal.Text)

        If amt1 + amt2 <> total Then
            MessageBox.Show("The total of both amounts must equal the invoice total.", "Mismatch", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        Me.DialogResult = DialogResult.OK
        Me.Close()
    End Sub
    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub

    Public ReadOnly Property PaymentMethod1ID As Integer
        Get
            Return Convert.ToInt32(cmbPaymentMethod1.SelectedValue)
        End Get
    End Property

    Public ReadOnly Property PaymentMethod2ID As Integer
        Get
            If cmbPaymentMethod2.Visible Then
                Return Convert.ToInt32(cmbPaymentMethod2.SelectedValue)
            Else
                Return 0
            End If
        End Get
    End Property

    Public ReadOnly Property Amount1 As Decimal
        Get
            Return Convert.ToDecimal(txtAmount1.Text)
        End Get
    End Property

    Public ReadOnly Property Amount2 As Decimal
        Get
            Return If(txtAmount2.Visible, Convert.ToDecimal(txtAmount2.Text), 0)
        End Get
    End Property
End Class