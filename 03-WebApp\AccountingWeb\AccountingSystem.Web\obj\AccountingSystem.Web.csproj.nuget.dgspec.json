{"format": 1, "restore": {"D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\AccountingSystem.Web.csproj": {}}, "projects": {"D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Core\\AccountingSystem.Core\\AccountingSystem.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Core\\AccountingSystem.Core\\AccountingSystem.Core.csproj", "projectName": "AccountingSystem.Core", "projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Core\\AccountingSystem.Core\\AccountingSystem.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Core\\AccountingSystem.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj", "projectName": "AccountingSystem.Data", "projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj": {"projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj", "projectName": "AccountingSystem.Models", "projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Services\\AccountingSystem.Services\\AccountingSystem.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Services\\AccountingSystem.Services\\AccountingSystem.Services.csproj", "projectName": "AccountingSystem.Services", "projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Services\\AccountingSystem.Services\\AccountingSystem.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Services\\AccountingSystem.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Core\\AccountingSystem.Core\\AccountingSystem.Core.csproj": {"projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Core\\AccountingSystem.Core\\AccountingSystem.Core.csproj"}, "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj": {"projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj"}, "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj": {"projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "QRCoder": {"target": "Package", "version": "[1.6.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\AccountingSystem.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\AccountingSystem.Web.csproj", "projectName": "AccountingSystem.Web", "projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\AccountingSystem.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\03-WebApp\\AccountingWeb\\AccountingSystem.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Core\\AccountingSystem.Core\\AccountingSystem.Core.csproj": {"projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Core\\AccountingSystem.Core\\AccountingSystem.Core.csproj"}, "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj": {"projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj"}, "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj": {"projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj"}, "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Services\\AccountingSystem.Services\\AccountingSystem.Services.csproj": {"projectPath": "D:\\OneDrive\\Documents\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Services\\AccountingSystem.Services\\AccountingSystem.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"ClosedXML": {"target": "Package", "version": "[0.105.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "QRCoder": {"target": "Package", "version": "[1.6.0, )"}, "QuestPDF": {"target": "Package", "version": "[2025.7.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}