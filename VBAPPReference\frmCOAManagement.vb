﻿Imports System.Data.SqlClient

Public Class frmCOAManagement

    Dim dt As New DataTable

    Private Sub frmCOAManagement_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        cmbAccountType.Items.AddRange(New String() {"Asset", "Liability", "Equity", "Revenue", "Expense"})
        cmbNature.Items.AddRange(New String() {"Debit", "Credit"})
        chkIsPosting.Checked = True
        LoadCOATree()
    End Sub

    Private Sub LoadCOATree()
        TreeView_COA.Nodes.Clear()
        Dim da As New SqlDataAdapter("SELECT * FROM tbl_Acc_Accounts ORDER BY AccountCode", Con)
        dt.Clear()
        da.Fill(dt)

        Dim nodes As New Dictionary(Of String, TreeNode)

        For Each row As DataRow In dt.Rows
            Dim code As String = row("AccountCode").ToString()
            Dim name As String = row("AccountName").ToString()
            Dim parentCode As String = If(IsDBNull(row("ParentAccountCode")), Nothing, row("ParentAccountCode").ToString())

            Dim node As New TreeNode(code & " - " & name) With {.Tag = code}
            nodes(code) = node

            If String.IsNullOrEmpty(parentCode) Then
                TreeView_COA.Nodes.Add(node)
            ElseIf nodes.ContainsKey(parentCode) Then
                nodes(parentCode).Nodes.Add(node)
            End If
        Next

        TreeView_COA.CollapseAll()
    End Sub

    Private Function GenerateNextSegmentCode(parentCode As String) As String
        Dim nextSegment As String = "0001"
        Try
            Dim parentLength As Integer = parentCode.Length
            Dim segmentLength As Integer = 7 - parentLength
            If segmentLength <= 0 Then segmentLength = 4 ' fallback

            Dim cmd As New SqlCommand("SELECT MAX(CAST(SUBSTRING(AccountCode, @StartIndex, @Length) AS INT)) FROM tbl_Acc_Accounts WHERE ParentAccountCode = @ParentCode", Con)
            cmd.Parameters.AddWithValue("@ParentCode", parentCode)
            cmd.Parameters.AddWithValue("@StartIndex", parentLength + 1)
            cmd.Parameters.AddWithValue("@Length", segmentLength)

            Con.Open()
            Dim result = cmd.ExecuteScalar()
            Con.Close()

            If result IsNot DBNull.Value AndAlso result IsNot Nothing Then
                nextSegment = (CInt(result) + 1).ToString("D" & segmentLength)
            Else
                nextSegment = "1".PadLeft(segmentLength, "0"c)
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في توليد كود الجزء: " & ex.Message)
            Con.Close()
        End Try
        Return nextSegment
    End Function

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        txtAccountName.Text = ""
        txtSegmentCode.Text = ""
        txtAccountCode.Text = ""
        txtParentCode.Text = ""
        cmbAccountType.SelectedIndex = -1
        cmbNature.SelectedIndex = -1
        chkIsPosting.Checked = True
        txtOpeningBalance.Text = "0.00"
        txtNotes.Text = ""

        If TreeView_COA.SelectedNode IsNot Nothing Then
            Dim parentCode = TreeView_COA.SelectedNode.Tag.ToString()
            Dim segmentCode = GenerateNextSegmentCode(parentCode)
            Dim fullCode = parentCode & segmentCode

            txtParentCode.Text = parentCode
            txtSegmentCode.Text = segmentCode
            txtAccountCode.Text = fullCode
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If String.IsNullOrWhiteSpace(txtAccountCode.Text) Then
            MessageBox.Show("كود الحساب غير مُحدَّد.")
            Exit Sub
        End If

        Dim exists As Boolean = False
        Dim chkCmd As New SqlCommand("SELECT 1 FROM tbl_Acc_Accounts WHERE AccountCode = @Code", Con)
        chkCmd.Parameters.AddWithValue("@Code", txtAccountCode.Text)

        Con.Open()
        exists = chkCmd.ExecuteScalar() IsNot Nothing
        Con.Close()

        Dim sql As String
        If exists Then
            sql = "UPDATE tbl_Acc_Accounts SET AccountName = @Name, AccountType = @Type, AccountNature = @Nature, IsPosting = @IsPost, OpeningBalance = @OB, Notes = @Notes WHERE AccountCode = @Code"
        Else
            sql = "INSERT INTO tbl_Acc_Accounts (AccountCode, SegmentCode, AccountName, ParentAccountCode, AccountLevel, IsPosting, AccountType, AccountNature, OpeningBalance, Notes, CreatedBy, CreatedOn) VALUES (@Code, @Segment, @Name, @Parent, @Level, @IsPost, @Type, @Nature, @OB, @Notes, @User, GETDATE())"
        End If

        Dim cmd As New SqlCommand(sql, Con)
        cmd.Parameters.AddWithValue("@Code", txtAccountCode.Text)
        cmd.Parameters.AddWithValue("@Segment", txtSegmentCode.Text)
        cmd.Parameters.AddWithValue("@Name", txtAccountName.Text)
        cmd.Parameters.AddWithValue("@Parent", If(txtParentCode.Text = "", DBNull.Value, txtParentCode.Text))
        cmd.Parameters.AddWithValue("@Level", txtAccountCode.Text.Length \ 4)
        cmd.Parameters.AddWithValue("@IsPost", chkIsPosting.Checked)
        cmd.Parameters.AddWithValue("@Type", cmbAccountType.Text)
        cmd.Parameters.AddWithValue("@Nature", cmbNature.Text)
        cmd.Parameters.AddWithValue("@OB", Val(txtOpeningBalance.Text))
        cmd.Parameters.AddWithValue("@Notes", txtNotes.Text)
        cmd.Parameters.AddWithValue("@User", UserName)

        Con.Open()
        cmd.ExecuteNonQuery()
        Con.Close()

        MessageBox.Show(If(exists, "تمّ تحديث الحساب بنجاح.", "تمّ إضافة الحساب بنجاح."))
        LoadCOATree()
    End Sub

    Private Sub TreeView_COA_AfterSelect(sender As Object, e As TreeViewEventArgs) Handles TreeView_COA.AfterSelect
        Dim accountCode As String = TreeView_COA.SelectedNode.Tag.ToString()

        Dim cmd As New SqlCommand("SELECT * FROM tbl_Acc_Accounts WHERE AccountCode = @Code", Con)
        cmd.Parameters.AddWithValue("@Code", accountCode)
        Dim da As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        da.Fill(dt)

        If dt.Rows.Count > 0 Then
            With dt.Rows(0)
                txtAccountName.Text = .Item("AccountName").ToString()
                txtSegmentCode.Text = .Item("SegmentCode").ToString()
                txtAccountCode.Text = .Item("AccountCode").ToString()
                txtParentCode.Text = If(IsDBNull(.Item("ParentAccountCode")), "", .Item("ParentAccountCode").ToString())
                cmbAccountType.Text = .Item("AccountType").ToString()
                cmbNature.Text = .Item("AccountNature").ToString()
                chkIsPosting.Checked = Convert.ToBoolean(.Item("IsPosting"))
                txtOpeningBalance.Text = .Item("OpeningBalance").ToString()
                txtNotes.Text = .Item("Notes").ToString()
            End With
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If MessageBox.Show("هل أنت متأكد من حذف هذا الحساب؟", "تأكيد الحذف", MessageBoxButtons.YesNo) = DialogResult.Yes Then
            Dim checkCmd As New SqlCommand("SELECT COUNT(*) FROM tbl_Acc_Accounts WHERE ParentAccountCode = @Code", Con)
            checkCmd.Parameters.AddWithValue("@Code", txtAccountCode.Text)
            Con.Open()
            Dim count As Integer = CInt(checkCmd.ExecuteScalar())
            Con.Close()

            If count > 0 Then
                MessageBox.Show("لا يمكن حذف الحساب لأنه يحتوي على حسابات فرعية.")
                Return
            End If

            Dim delCmd As New SqlCommand("DELETE FROM tbl_Acc_Accounts WHERE AccountCode = @Code", Con)
            delCmd.Parameters.AddWithValue("@Code", txtAccountCode.Text)
            Con.Open()
            delCmd.ExecuteNonQuery()
            Con.Close()

            MessageBox.Show("تم حذف الحساب بنجاح.")
            LoadCOATree()
        End If
    End Sub
End Class
