# Setup Menu System Script
# This script will ensure the database is updated and seed the menu data

Write-Host "Setting up Menu System..." -ForegroundColor Green

# Navigate to the web project directory
$webProjectPath = "03-WebApp\AccountingWeb\AccountingSystem.Web"
Set-Location $PSScriptRoot\..

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow

# Check if we're in the right location
if (!(Test-Path $webProjectPath)) {
    Write-Host "Error: Web project not found at $webProjectPath" -ForegroundColor Red
    exit 1
}

# Update database with latest migrations
Write-Host "Updating database with latest migrations..." -ForegroundColor Yellow
Set-Location $webProjectPath

try {
    dotnet ef database update
    Write-Host "Database updated successfully!" -ForegroundColor Green
} catch {
    Write-Host "Warning: Database update failed or no migrations needed" -ForegroundColor Yellow
}

# Go back to root
Set-Location $PSScriptRoot\..

# Run the SQL seed script using sqlcmd
Write-Host "Seeding menu data..." -ForegroundColor Yellow

$connectionString = "Server=***************;Database=SULTDB;User Id=AppUser;Password=StrongP@ss123;TrustServerCertificate=true;"
$sqlFile = "05-Scripts\seed-web-authorization-data.sql"

try {
    # Try using sqlcmd if available
    sqlcmd -S "***************" -U "AppUser" -P "StrongP@ss123" -d "SULTDB" -i $sqlFile -C
    Write-Host "Menu data seeded successfully!" -ForegroundColor Green
} catch {
    Write-Host "sqlcmd not available. Please run the SQL script manually:" -ForegroundColor Yellow
    Write-Host "File: $sqlFile" -ForegroundColor White
    Write-Host "Connection: $connectionString" -ForegroundColor White
}

Write-Host "Menu system setup complete!" -ForegroundColor Green
Write-Host "Please restart the web application to see the changes." -ForegroundColor Cyan
