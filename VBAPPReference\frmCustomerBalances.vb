﻿

Imports System.Data.SqlClient
Imports System.Runtime.InteropServices
Imports Excel = Microsoft.Office.Interop.Excel

Public Class frmCustomerBalances
    Private printRowIndex As Integer = 0
    Private totalBalance As Decimal = 0
    Private logoImage As Image = Nothing

    Private totalPages As Integer = 1
    Private currentPage As Integer = 1
    Private Sub frmCustomerBalances_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        CustomerLoad()
        ForceGregorianForAllPickers(Me)
        FillComboBoxWithEmployees
        cmbxPartnerNo.SelectedIndex = 0
        logoImage = LoadLogoFromDatabase()
    End Sub
    Private Sub FillComboBoxWithEmployees()
        Dim CMD As New SqlCommand("Select EmployeeNo,Emp_Name from tblEmployees order by EmployeeNo", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "Emp_Name + ' - ' + CONVERT(EmployeeNo, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxEmployee.DataSource = dt
        cmbxEmployee.DisplayMember = "DisplayText" ' Show Name
        cmbxEmployee.ValueMember = "EmployeeNo" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        cmbxEmployee.Text = ""
    End Sub

    Sub CustomerLoad()

        Dim cmd As New SqlCommand("
        SELECT AccountCode, AccountName 
        FROM tbl_Acc_Accounts 
        WHERE ParentAccountCode = (
            SELECT AccountNo FROM tblGLConfig WHERE EntryReferenceModule = 'عملاء'
        ) 
        ORDER BY AccountCode", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim reader As SqlDataReader = cmd.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()

        ' Add a computed display column (e.g., اسم العميل - كود العميل)
        dt.Columns.Add("DisplayText", GetType(String), "AccountName + ' - ' + CONVERT(AccountCode, 'System.String')")

        ' Bind to ComboBox
        cmbxPartnerNo.DataSource = dt
        cmbxPartnerNo.DisplayMember = "DisplayText" ' e.g., العميل - كود
        cmbxPartnerNo.ValueMember = "AccountCode"   ' store the customer number

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If

        cmbxPartnerNo.Text = ""
    End Sub



    Private Sub btnPreview_Click(sender As Object, e As EventArgs) Handles btnPreview.Click
        PrintType = "CustomerBalances"
        If cmbxPartnerNo.Text = "" Then
            CustNo = 0
        Else
            CustNo = cmbxPartnerNo.SelectedValue
        End If
        If chkSalesFilter.Checked Then
            EmpNo = cmbxEmployee.SelectedValue.ToString()
        Else
            EmpNo = 0
        End If
        DateTo = dtpAsOfDate.Value.Date

        frmPrintPreview.ShowDialog()
    End Sub

    Private Function LoadLogoFromDatabase() As Image
        Dim cmd As New SqlCommand("SELECT TOP 1 LogoFile FROM tblConfig", Con)
        If Con.State <> ConnectionState.Open Then Con.Open()
        Dim result As Object = cmd.ExecuteScalar()
        Con.Close()

        If result IsNot DBNull.Value Then
            Dim logoBytes As Byte() = CType(result, Byte())
            Using ms As New IO.MemoryStream(logoBytes)
                Return Image.FromStream(ms)
            End Using
        End If

        Return Nothing
    End Function


    Private Sub btnGenerate_Click(sender As Object, e As EventArgs) Handles btnGenerate.Click
        Dim cmd As New SqlCommand("sp_GetCustomerBalances", Con)
        cmd.CommandType = CommandType.StoredProcedure

        ' Add required parameter
        cmd.Parameters.AddWithValue("@AsOfDate", dtpAsOfDate.Value.Date)

        ' Handle optional @Customer filter
        If String.IsNullOrWhiteSpace(cmbxPartnerNo.Text) OrElse cmbxPartnerNo.Text = "All Customers" Then
            cmd.Parameters.AddWithValue("@CustomerNo", DBNull.Value)
        Else
            cmd.Parameters.AddWithValue("@CustomerNo", cmbxPartnerNo.SelectedValue.ToString)
        End If

        ' Handle optional @SalesEmployee filter
        If chkSalesFilter.Checked AndAlso cmbxEmployee.SelectedValue IsNot Nothing Then
            cmd.Parameters.AddWithValue("@SalesEmployee", cmbxEmployee.SelectedValue.ToString())
        Else
            cmd.Parameters.AddWithValue("@SalesEmployee", DBNull.Value)
        End If

        ' Fill the data table
        Dim dt As New DataTable()
        Dim da As New SqlDataAdapter(cmd)
        da.Fill(dt)

        ' Calculate total
        Dim totalBalance As Decimal = 0
        For Each row As DataRow In dt.Rows
            If Not IsDBNull(row("الرصيد")) Then
                totalBalance += Convert.ToDecimal(row("الرصيد"))
            End If
        Next

        ' Add summary row
        If dt.Rows.Count > 0 Then
            Dim totalRow As DataRow = dt.NewRow()
            totalRow("اسم العميل") = "الإجمالي"
            totalRow("الرصيد") = totalBalance
            totalRow("اسم الموظف") = dt.Rows(0)("اسم الموظف") ' Keep same employee name
            dt.Rows.Add(totalRow)
        End If

        dgvResult.DataSource = dt


    End Sub
    Private Sub dgvReport_CellFormatting(sender As Object, e As DataGridViewCellFormattingEventArgs) Handles dgvResult.CellFormatting
        If dgvResult.Rows(e.RowIndex).Cells("اسم العميل").Value.ToString() = "الإجمالي" Then
            e.CellStyle.Font = New Font(dgvResult.Font, FontStyle.Bold)
        End If
    End Sub

    Public Function ExecuteScalar(query As String) As String
        Dim cmd As New SqlCommand(query, Con)
        If Con.State <> ConnectionState.Open Then Con.Open()
        Dim result = cmd.ExecuteScalar()
        Con.Close()
        Return If(result IsNot Nothing, result.ToString(), "")
    End Function

    Public Function GetDataTable(query As String) As DataTable
        Dim dt As New DataTable()
        Dim adapter As New SqlDataAdapter(query, Con)
        adapter.Fill(dt)
        Return dt
    End Function

    Private Sub btnCustomerSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCustomerSearch.Click
        CustomerSearchForm = "frmCustomerBalances"
        frmCustomerSearch.ShowDialog()
    End Sub



    'Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
    '    printRowIndex = 0
    '    totalBalance = 0
    '    currentPage = 1

    '    ' Estimate total pages
    '    Dim rowsPerPage = Math.Floor((PrintDocument1.DefaultPageSettings.Bounds.Height - 200) / 25)
    '    totalPages = Math.Ceiling(dgvResult.Rows.Count / rowsPerPage)

    '    PrintDocument1.DefaultPageSettings.Landscape = False
    '    PrintDocument1.Print()
    'End Sub





    'Private Sub PrintDocument1_PrintPage(sender As Object, e As Printing.PrintPageEventArgs) Handles PrintDocument1.PrintPage
    '    Dim font = New Font("Arial", 10)
    '    Dim headerFont = New Font("Arial", 12, FontStyle.Bold)
    '    Dim rtlFormat As New StringFormat With {.Alignment = StringAlignment.Far}
    '    Dim centerFormat As New StringFormat With {.Alignment = StringAlignment.Center}
    '    Dim y As Integer = 50
    '    Dim rowHeight = 20
    '    Dim xStart = e.MarginBounds.Right - 600

    '    ' Draw Logo at top-left
    '    If currentPage = 1 AndAlso logoImage IsNot Nothing Then
    '        ' Adjust size ~6.5cm width, 2.3cm height => approx. 250x90 pixels
    '        e.Graphics.DrawImage(logoImage, e.MarginBounds.Left, y, 250, 90)
    '    End If

    '    ' Title (top center)
    '    e.Graphics.DrawString("تقرير أرصدة العملاء في تاريخ " & dtpAsOfDate.Value.ToShortDateString(),
    '                      headerFont, Brushes.Black, e.MarginBounds.Right, y + 10, rtlFormat)

    '    y += 110 ' leave space for logo

    '    ' Column headers: الرصيد | اسم العميل | كود العميل
    '    Dim colWidths() As Integer = {100, 300, 150}
    '    Dim headers() As String = {"الرصيد", "اسم العميل", "كود العميل"}
    '    Dim x = xStart

    '    For i = 0 To headers.Length - 1
    '        e.Graphics.DrawRectangle(Pens.Black, x, y, colWidths(i), rowHeight)
    '        e.Graphics.DrawString(headers(i), font, Brushes.Black, New RectangleF(x, y, colWidths(i), rowHeight), rtlFormat)
    '        x += colWidths(i)
    '    Next

    '    y += rowHeight

    '    ' Print data rows
    '    'Dim rowsPerPage = Math.Floor((e.MarginBounds.Height - y - 100) / rowHeight)
    '    Dim rowsPrinted = 0

    '    Dim footerHeight = 60 ' Enough space for page number
    '    Dim availableHeight = e.MarginBounds.Bottom - y - footerHeight
    '    Dim rowsPerPage = Math.Floor(availableHeight / rowHeight)

    '    While printRowIndex < dgvResult.Rows.Count
    '        If rowsPrinted >= rowsPerPage Then Exit While

    '        Dim row = dgvResult.Rows(printRowIndex)
    '        If row.IsNewRow Then
    '            printRowIndex += 1
    '            Continue While
    '        End If

    '        x = xStart
    '        Dim accCode = row.Cells("كود العميل").Value.ToString()
    '        Dim partner = row.Cells("اسم العميل").Value.ToString()
    '        Dim balance = Convert.ToDecimal(row.Cells("الرصيد").Value)
    '        totalBalance += balance

    '        Dim values() As String = {FormatNumber(balance, 2), partner, accCode}

    '        For i = 0 To values.Length - 1
    '            e.Graphics.DrawRectangle(Pens.Black, x, y, colWidths(i), rowHeight)
    '            e.Graphics.DrawString(values(i), font, Brushes.Black, New RectangleF(x, y, colWidths(i), rowHeight), rtlFormat)
    '            x += colWidths(i)
    '        Next

    '        y += rowHeight
    '        printRowIndex += 1
    '        rowsPrinted += 1
    '    End While

    '    ' Final row: total inside the "الرصيد" column
    '    'If printRowIndex >= dgvResult.Rows.Count Then
    '    '    x = xStart
    '    '    Dim values() As String = {FormatNumber(totalBalance, 2), "الإجمالي", ""}

    '    '    For i = 0 To values.Length - 1
    '    '        e.Graphics.FillRectangle(Brushes.LightGray, x, y, colWidths(i), rowHeight)
    '    '        e.Graphics.DrawRectangle(Pens.Black, x, y, colWidths(i), rowHeight)
    '    '        e.Graphics.DrawString(values(i), font, Brushes.Black, New RectangleF(x, y, colWidths(i), rowHeight), rtlFormat)
    '    '        x += colWidths(i)
    '    '    Next

    '    '    y += rowHeight
    '    'End If

    '    ' Page footer
    '    Dim footerText = $"صفحة {currentPage} من {totalPages}"
    '    e.Graphics.DrawString(footerText, font, Brushes.Black, e.MarginBounds.Right, e.MarginBounds.Bottom + 20, rtlFormat)

    '    ' Pagination
    '    If printRowIndex < dgvResult.Rows.Count Then
    '        currentPage += 1
    '        e.HasMorePages = True
    '    Else
    '        printRowIndex = 0
    '        totalBalance = 0
    '        currentPage = 1
    '        e.HasMorePages = False
    '    End If
    'End Sub

    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        ExportToExcelAndSave()
    End Sub


    Private Sub ExportToExcelAndSave()
        If dgvResult.Rows.Count = 0 Then
            MessageBox.Show("لا يوجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim saveFileDialog As New SaveFileDialog()
        saveFileDialog.Filter = "Excel Workbook|*.xlsx"
        saveFileDialog.Title = "حدد مكان حفظ الملف"
        saveFileDialog.FileName = "ارصدة العملاء.xlsx"

        If saveFileDialog.ShowDialog() = DialogResult.OK Then
            Dim xlApp As Excel.Application = Nothing
            Dim xlWorkbook As Excel.Workbook = Nothing
            Dim xlWorksheet As Excel.Worksheet = Nothing

            Try
                xlApp = New Excel.Application()
                xlWorkbook = xlApp.Workbooks.Add()
                xlWorksheet = CType(xlWorkbook.Sheets(1), Excel.Worksheet)

                ' Header row
                Dim colIndex As Integer = 1
                For Each col As DataGridViewColumn In dgvResult.Columns
                    If col.Visible Then
                        xlWorksheet.Cells(1, colIndex).Value = col.HeaderText
                        colIndex += 1
                    End If
                Next

                ' Data rows
                Dim rowIndex As Integer = 2
                For Each row As DataGridViewRow In dgvResult.Rows
                    If Not row.IsNewRow Then
                        colIndex = 1
                        For Each col As DataGridViewColumn In dgvResult.Columns
                            If col.Visible Then
                                xlWorksheet.Cells(rowIndex, colIndex).Value = row.Cells(col.Index).Value?.ToString()
                                colIndex += 1
                            End If
                        Next
                        rowIndex += 1
                    End If
                Next

                ' Save the file
                xlWorkbook.SaveAs(saveFileDialog.FileName)
                MessageBox.Show("تم حفظ الملف بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)

            Catch ex As Exception
                MessageBox.Show("خطأ أثناء التصدير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Finally
                ' Cleanup Excel COM objects
                If Not IsNothing(xlWorkbook) Then xlWorkbook.Close(False)
                If Not IsNothing(xlApp) Then xlApp.Quit()
                Marshal.ReleaseComObject(xlWorksheet)
                Marshal.ReleaseComObject(xlWorkbook)
                Marshal.ReleaseComObject(xlApp)
            End Try
        End If
    End Sub



    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Me.Close()
    End Sub

End Class