﻿Imports System.Data.SqlClient
Imports System.Net.WebRequestMethods
Public Class frmJEStatement

    Private Sub frmExpensesStatement_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        ' frmReports.Enabled = True
    End Sub

    Private Sub frmExpensesStatement_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        AccountsLoad()
        ForceGregorianCalendar(dtpFrom)
        ForceGregorianCalendar(dtpTo)
        Try
            cmbxPartnerNo.SelectedIndex = 0
        Catch ex As Exception

        End Try
    End Sub

    Private Sub btnPreview_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPreview.Click
        If cmbxPartnerNo.SelectedValue <> 0 Then
            AccNo = cmbxPartnerNo.SelectedValue
            'dtp.CustomFormat = "dd/MM/yyyy"
            DateFrom = dtpFrom.Value.Date
            DateTo = dtpTo.Value.Date
            DateStrFrom = dtpFrom.Value.ToString("yyyy-MM-dd")
            DateStrTo = dtpTo.Value.ToString("yyyy-MM-dd")
            PrintType = "JESN"
            frmPrintPreview.MdiParent = frmMain
            frmPrintPreview.Show()
        Else
            MsgBox("فضلا اختر الحساب", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        AccountSearchForm = "frmJEStatement"
        frmAccountSearch.ShowDialog()
    End Sub

    Sub AccountsLoad()
        Dim CMD As New SqlCommand("Select AccountNo,AccountDescription from AccountChart order by ParentID,AccountNo", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountDescription + ' - ' + CONVERT(AccountNo, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxPartnerNo.DataSource = dt
        cmbxPartnerNo.DisplayMember = "DisplayText" ' Show Name
        cmbxPartnerNo.ValueMember = "AccountNo" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Me.Close()
    End Sub
End Class