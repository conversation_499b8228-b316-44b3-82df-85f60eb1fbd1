@model IEnumerable<AccountingSystem.Models.Vendor>

@{
    ViewData["Title"] = "إدارة الموردين";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        إدارة الموردين
                    </h4>
                    <a asp-action="Create" class="btn btn-light">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مورد جديد
                    </a>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <!-- Search Form -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-10">
                                <input type="text" name="searchTerm" value="@ViewBag.SearchTerm" class="form-control" placeholder="البحث في الموردين (الاسم، رقم المورد، الجوال...)" />
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Vendors Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="vendorsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم المورد</th>
                                    <th>اسم المورد</th>
                                    <th>الاسم الأول</th>
                                    <th>الاسم الأخير</th>
                                    <th>الجوال</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>المدينة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var vendor in Model)
                                {
                                    <tr>
                                        <td>@vendor.VendorNo</td>
                                        <td>@vendor.VendorName</td>
                                        <td>@vendor.FirstName</td>
                                        <td>@vendor.LastName</td>
                                        <td>@vendor.Mobile</td>
                                        <td>@vendor.Email</td>
                                        <td>@vendor.City</td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(vendor.Status))
                                            {
                                                <span class="badge bg-@(vendor.Status == "نشط" ? "success" : "secondary")">
                                                    @vendor.Status
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Edit" asp-route-vendorNo="@vendor.VendorNo" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-vendorNo="@vendor.VendorNo" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد موردين</h5>
                            <p class="text-muted">لم يتم العثور على أي موردين. يمكنك إضافة مورد جديد.</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة مورد جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#vendorsTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
                },
                "order": [[ 0, "desc" ]],
                "pageLength": 25
            });
        });
    </script>
}
