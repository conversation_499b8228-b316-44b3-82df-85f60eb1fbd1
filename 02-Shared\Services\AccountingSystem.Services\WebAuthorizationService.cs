using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;

namespace AccountingSystem.Services
{
    /// <summary>
    /// Service for managing web authorization (menu and quick action permissions)
    /// </summary>
    public class WebAuthorizationService
    {
        private readonly AccountingDbContext _context;
        private readonly IMemoryCache _cache;
        private const string CACHE_KEY_PREFIX_MENU = "WebMenu_Group_";
        private const string CACHE_KEY_PREFIX_QUICKACTION = "WebQuickAction_Group_";
        private const int CACHE_DURATION_MINUTES = 30;

        public WebAuthorizationService(AccountingDbContext context, IMemoryCache cache)
        {
            _context = context;
            _cache = cache;
        }

        #region Menu Permission Methods

        /// <summary>
        /// Get all menu items with hierarchical structure
        /// </summary>
        public async Task<List<WebMenuItemDto>> GetAllMenuItemsAsync()
        {
            var allMenus = await _context.WebFormMenus
                .Where(m => m.IsActive)
                .OrderBy(m => m.DisplayOrder)
                .ToListAsync();

            return BuildMenuHierarchy(allMenus, null);
        }

        /// <summary>
        /// Get menu items accessible by a specific group
        /// </summary>
        public async Task<List<WebMenuItemDto>> GetMenuItemsByGroupAsync(int groupId)
        {
            var cacheKey = $"{CACHE_KEY_PREFIX_MENU}{groupId}";

            if (_cache.TryGetValue(cacheKey, out List<WebMenuItemDto>? cachedMenus) && cachedMenus != null)
            {
                return cachedMenus;
            }

            // Get all menus that the group has permission to view
            var accessibleMenuIds = await _context.WebGroupMenuPermissions
                .Where(p => p.GroupID == groupId && p.CanView)
                .Select(p => p.MenuId)
                .ToListAsync();

            var allMenus = await _context.WebFormMenus
                .Where(m => m.IsActive && accessibleMenuIds.Contains(m.Id))
                .OrderBy(m => m.DisplayOrder)
                .ToListAsync();

            var menus = BuildMenuHierarchy(allMenus, null);

            // Cache for 30 minutes
            _cache.Set(cacheKey, menus, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));

            return menus;
        }

        /// <summary>
        /// Check if a user has permission to access a specific menu/route
        /// </summary>
        public async Task<bool> CanAccessMenuAsync(int groupId, string route)
        {
            var menu = await _context.WebFormMenus
                .FirstOrDefaultAsync(m => m.Route == route && m.IsActive);

            if (menu == null)
                return false; // Menu doesn't exist or is inactive

            var permission = await _context.WebGroupMenuPermissions
                .FirstOrDefaultAsync(p => p.GroupID == groupId && p.MenuId == menu.Id);

            return permission?.CanView ?? false;
        }

        /// <summary>
        /// Get specific permissions for a menu item
        /// </summary>
        public async Task<MenuPermissionDto?> GetMenuPermissionsAsync(int groupId, int menuId)
        {
            var permission = await _context.WebGroupMenuPermissions
                .FirstOrDefaultAsync(p => p.GroupID == groupId && p.MenuId == menuId);

            if (permission == null)
                return null;

            return new MenuPermissionDto
            {
                MenuId = menuId,
                CanView = permission.CanView,
                CanAdd = permission.CanAdd,
                CanEdit = permission.CanEdit,
                CanDelete = permission.CanDelete,
                CanPrint = permission.CanPrint
            };
        }

        /// <summary>
        /// Save menu permissions for a group
        /// </summary>
        public async Task<bool> SaveMenuPermissionsAsync(int groupId, List<MenuPermissionDto> permissions, string modifiedBy)
        {
            try
            {
                // Remove existing permissions for this group
                var existingPermissions = await _context.WebGroupMenuPermissions
                    .Where(p => p.GroupID == groupId)
                    .ToListAsync();

                _context.WebGroupMenuPermissions.RemoveRange(existingPermissions);

                // Add new permissions
                foreach (var permission in permissions.Where(p => p.CanView))
                {
                    _context.WebGroupMenuPermissions.Add(new WebGroupMenuPermission
                    {
                        GroupID = groupId,
                        MenuId = permission.MenuId,
                        CanView = permission.CanView,
                        CanAdd = permission.CanAdd,
                        CanEdit = permission.CanEdit,
                        CanDelete = permission.CanDelete,
                        CanPrint = permission.CanPrint,
                        CreatedBy = modifiedBy,
                        ModifiedBy = modifiedBy
                    });
                }

                await _context.SaveChangesAsync();

                // Clear cache for this group
                _cache.Remove($"{CACHE_KEY_PREFIX_MENU}{groupId}");

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get all menu permissions for a group
        /// </summary>
        public async Task<GroupMenuPermissionsDto> GetGroupMenuPermissionsAsync(int groupId)
        {
            var permissions = await _context.WebGroupMenuPermissions
                .Where(p => p.GroupID == groupId)
                .Select(p => new MenuPermissionDto
                {
                    MenuId = p.MenuId,
                    CanView = p.CanView,
                    CanAdd = p.CanAdd,
                    CanEdit = p.CanEdit,
                    CanDelete = p.CanDelete,
                    CanPrint = p.CanPrint
                })
                .ToListAsync();

            return new GroupMenuPermissionsDto
            {
                GroupID = groupId,
                MenuPermissions = permissions
            };
        }

        #endregion

        #region Quick Action Permission Methods

        /// <summary>
        /// Get all quick actions
        /// </summary>
        public async Task<List<WebQuickActionDto>> GetAllQuickActionsAsync()
        {
            var quickActions = await _context.WebQuickActions
                .Where(qa => qa.IsActive)
                .OrderBy(qa => qa.DisplayOrder)
                .Select(qa => new WebQuickActionDto
                {
                    Id = qa.Id,
                    DisplayName = qa.DisplayName,
                    DisplayNameEn = qa.DisplayNameEn,
                    Route = qa.Route,
                    Icon = qa.Icon,
                    ColorClass = qa.ColorClass,
                    DisplayOrder = qa.DisplayOrder,
                    Category = qa.Category,
                    IsActive = qa.IsActive
                })
                .ToListAsync();

            return quickActions;
        }

        /// <summary>
        /// Get quick actions accessible by a specific group
        /// </summary>
        public async Task<List<WebQuickActionDto>> GetQuickActionsByGroupAsync(int groupId)
        {
            var cacheKey = $"{CACHE_KEY_PREFIX_QUICKACTION}{groupId}";

            if (_cache.TryGetValue(cacheKey, out List<WebQuickActionDto>? cachedQuickActions) && cachedQuickActions != null)
            {
                return cachedQuickActions;
            }

            var accessibleQuickActionIds = await _context.WebGroupQuickActionPermissions
                .Where(p => p.GroupID == groupId && p.IsVisible)
                .Select(p => p.QuickActionId)
                .ToListAsync();

            var quickActions = await _context.WebQuickActions
                .Where(qa => qa.IsActive && accessibleQuickActionIds.Contains(qa.Id))
                .OrderBy(qa => qa.DisplayOrder)
                .Select(qa => new WebQuickActionDto
                {
                    Id = qa.Id,
                    DisplayName = qa.DisplayName,
                    DisplayNameEn = qa.DisplayNameEn,
                    Route = qa.Route,
                    Icon = qa.Icon,
                    ColorClass = qa.ColorClass,
                    DisplayOrder = qa.DisplayOrder,
                    Category = qa.Category,
                    IsActive = qa.IsActive
                })
                .ToListAsync();

            // Cache for 30 minutes
            _cache.Set(cacheKey, quickActions, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));

            return quickActions;
        }

        /// <summary>
        /// Save quick action permissions for a group
        /// </summary>
        public async Task<bool> SaveQuickActionPermissionsAsync(int groupId, List<int> quickActionIds, string modifiedBy)
        {
            try
            {
                // Remove existing permissions for this group
                var existingPermissions = await _context.WebGroupQuickActionPermissions
                    .Where(p => p.GroupID == groupId)
                    .ToListAsync();

                _context.WebGroupQuickActionPermissions.RemoveRange(existingPermissions);

                // Add new permissions
                foreach (var quickActionId in quickActionIds)
                {
                    _context.WebGroupQuickActionPermissions.Add(new WebGroupQuickActionPermission
                    {
                        GroupID = groupId,
                        QuickActionId = quickActionId,
                        IsVisible = true,
                        CreatedBy = modifiedBy,
                        ModifiedBy = modifiedBy
                    });
                }

                await _context.SaveChangesAsync();

                // Clear cache for this group
                _cache.Remove($"{CACHE_KEY_PREFIX_QUICKACTION}{groupId}");

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get all quick action permissions for a group
        /// </summary>
        public async Task<GroupQuickActionPermissionsDto> GetGroupQuickActionPermissionsAsync(int groupId)
        {
            var quickActionIds = await _context.WebGroupQuickActionPermissions
                .Where(p => p.GroupID == groupId && p.IsVisible)
                .Select(p => p.QuickActionId)
                .ToListAsync();

            return new GroupQuickActionPermissionsDto
            {
                GroupID = groupId,
                QuickActionIds = quickActionIds
            };
        }

        #endregion

        #region User Group Methods

        /// <summary>
        /// Get all user groups with user count
        /// </summary>
        public async Task<List<UserGroupDto>> GetAllUserGroupsAsync()
        {
            var groups = await _context.UserGroups
                .Select(g => new UserGroupDto
                {
                    GroupID = g.GroupID,
                    GroupName = g.GroupName,
                    UserCount = g.Users.Count
                })
                .ToListAsync();

            return groups;
        }

        /// <summary>
        /// Clear all permission caches for a specific group
        /// </summary>
        public void ClearGroupCache(int groupId)
        {
            _cache.Remove($"{CACHE_KEY_PREFIX_MENU}{groupId}");
            _cache.Remove($"{CACHE_KEY_PREFIX_QUICKACTION}{groupId}");
        }

        /// <summary>
        /// Clear all permission caches
        /// </summary>
        public void ClearAllCaches()
        {
            // Note: This is a simple implementation. For production, you might want to maintain a list of cache keys
            // or use a cache invalidation strategy
        }

        #endregion

        #region Menu Management Methods

        /// <summary>
        /// Add a new menu item
        /// </summary>
        public async Task<WebFormMenu?> AddMenuItemAsync(WebFormMenu menu)
        {
            try
            {
                _context.WebFormMenus.Add(menu);
                await _context.SaveChangesAsync();
                return menu;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Update a menu item
        /// </summary>
        public async Task<bool> UpdateMenuItemAsync(WebFormMenu menu)
        {
            try
            {
                _context.WebFormMenus.Update(menu);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Delete a menu item
        /// </summary>
        public async Task<bool> DeleteMenuItemAsync(int menuId)
        {
            try
            {
                var menu = await _context.WebFormMenus.FindAsync(menuId);
                if (menu == null)
                    return false;

                _context.WebFormMenus.Remove(menu);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Quick Action Management Methods

        /// <summary>
        /// Add a new quick action
        /// </summary>
        public async Task<WebQuickAction?> AddQuickActionAsync(WebQuickAction quickAction)
        {
            try
            {
                _context.WebQuickActions.Add(quickAction);
                await _context.SaveChangesAsync();
                return quickAction;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Update a quick action
        /// </summary>
        public async Task<bool> UpdateQuickActionAsync(WebQuickAction quickAction)
        {
            try
            {
                _context.WebQuickActions.Update(quickAction);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Delete a quick action
        /// </summary>
        public async Task<bool> DeleteQuickActionAsync(int quickActionId)
        {
            try
            {
                var quickAction = await _context.WebQuickActions.FindAsync(quickActionId);
                if (quickAction == null)
                    return false;

                _context.WebQuickActions.Remove(quickAction);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Build hierarchical menu structure
        /// </summary>
        private List<WebMenuItemDto> BuildMenuHierarchy(List<WebFormMenu> allMenus, int? parentId)
        {
            return allMenus
                .Where(m => m.ParentMenuId == parentId)
                .Select(m => new WebMenuItemDto
                {
                    Id = m.Id,
                    DisplayName = m.DisplayName,
                    DisplayNameEn = m.DisplayNameEn,
                    Route = m.Route,
                    Icon = m.Icon,
                    ParentMenuId = m.ParentMenuId,
                    DisplayOrder = m.DisplayOrder,
                    ModuleName = m.ModuleName,
                    IsContainer = m.IsContainer,
                    IsActive = m.IsActive,
                    Children = BuildMenuHierarchy(allMenus, m.Id)
                })
                .OrderBy(m => m.DisplayOrder)
                .ToList();
        }

        #endregion
    }
}

