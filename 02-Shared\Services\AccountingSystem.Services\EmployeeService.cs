using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AccountingSystem.Data;
using AccountingSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public class EmployeeService : IEmployeeService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<EmployeeService> _logger;

        public EmployeeService(AccountingDbContext context, ILogger<EmployeeService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<Employee>> GetEmployeesAsync()
        {
            return await _context.Employees.OrderBy(e => e.Id).ToListAsync();
        }

        public async Task<Employee> GetEmployeeByIdAsync(int id)
        {
            return await _context.Employees.FindAsync(id);
        }

        public async Task<int> GetNextEmployeeNoAsync()
        {
            if (await _context.Employees.AnyAsync())
            {
                return await _context.Employees.MaxAsync(e => e.Id) + 1;
            }
            return 1;
        }

        public async Task<bool> CreateEmployeeAsync(Employee employee, string currentUser)
        {
            try
            {
                employee.Id = await GetNextEmployeeNoAsync();
                employee.CreatedBy = currentUser;
                employee.CreatedOn = DateTime.Now;

                _context.Employees.Add(employee);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating employee.");
                return false;
            }
        }

        public async Task<bool> UpdateEmployeeAsync(Employee employee, string currentUser)
        {
            var existingEmployee = await _context.Employees.FindAsync(employee.Id);
            if (existingEmployee == null) return false;

            existingEmployee.Name = employee.Name;
            existingEmployee.Salary = employee.Salary;
            existingEmployee.ModifiedBy = currentUser;
            existingEmployee.ModifiedOn = DateTime.Now;

            try
            {
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating employee.");
                return false;
            }
        }

        public async Task<bool> DeleteEmployeeAsync(int id)
        {
            var employee = await _context.Employees.FindAsync(id);
            if (employee == null) return false;
            
            // Add checks here if employee is used in other tables before deletion.

            try
            {
                _context.Employees.Remove(employee);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting employee.");
                return false;
            }
        }
    }
} 