using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    [Table("tblItemsCategory")]
    public class ItemsCategory
    {
        [Key]
        [Column("RootID")]
        public int Id { get; set; }

        [Column("RootName")]
        public string? Name { get; set; } // Made nullable

        [Column("ParentID")]
        public int? ParentId { get; set; }

        [Column("RootLevel")]
        public int? Level { get; set; } // Made nullable

        [Column("CreatedBy")]
        public string? CreatedBy { get; set; } // Made nullable

        [Column("CreatedOn")]
        public DateTime? CreatedOn { get; set; }

        [Column("ModifiedBy")]
        public string? ModifiedBy { get; set; } // Made nullable

        [Column("ModifiedOn")]
        public DateTime? ModifiedOn { get; set; }

        // Navigation properties for tree structure
        [ForeignKey("ParentId")]
        public virtual ItemsCategory? Parent { get; set; }
        public virtual ICollection<ItemsCategory> Children { get; set; }

        public ItemsCategory()
        {
            Children = new HashSet<ItemsCategory>();
        }
    }
} 