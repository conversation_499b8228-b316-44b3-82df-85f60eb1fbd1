# Development Environment Setup Script
# Run this script to set up the development environment

Write-Host "Setting up Accounting System Development Environment..." -ForegroundColor Green

# Navigate to solution root
Set-Location $PSScriptRoot\..

# Restore all packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore

# Add Entity Framework tools if not already installed
Write-Host "Installing Entity Framework tools..." -ForegroundColor Yellow
dotnet tool install --global dotnet-ef

# Add common packages to projects
Write-Host "Adding common packages..." -ForegroundColor Yellow

# Add Entity Framework to Data project
Set-Location "02-Shared\Data\AccountingSystem.Data"
dotnet add package Microsoft.EntityFrameworkCore.SqlServer
dotnet add package Microsoft.EntityFrameworkCore.Tools
dotnet add package Microsoft.EntityFrameworkCore.Design

# Add references between projects
Write-Host "Setting up project references..." -ForegroundColor Yellow
dotnet add reference "..\..\Models\AccountingSystem.Models\AccountingSystem.Models.csproj"

# Go to Services project
Set-Location "..\Services\AccountingSystem.Services"
dotnet add reference "..\..\Core\AccountingSystem.Core\AccountingSystem.Core.csproj"
dotnet add reference "..\..\Models\AccountingSystem.Models\AccountingSystem.Models.csproj"
dotnet add reference "..\..\Data\AccountingSystem.Data\AccountingSystem.Data.csproj"

# Go to Web project
Set-Location "..\..\..\03-WebApp\AccountingWeb\AccountingSystem.Web"
dotnet add package Microsoft.EntityFrameworkCore.SqlServer
dotnet add package Microsoft.EntityFrameworkCore.Tools
dotnet add package Microsoft.AspNetCore.Identity.EntityFrameworkCore

# Add references to shared libraries
dotnet add reference "..\..\..\02-Shared\Core\AccountingSystem.Core\AccountingSystem.Core.csproj"
dotnet add reference "..\..\..\02-Shared\Data\AccountingSystem.Data\AccountingSystem.Data.csproj"
dotnet add reference "..\..\..\02-Shared\Models\AccountingSystem.Models\AccountingSystem.Models.csproj"
dotnet add reference "..\..\..\02-Shared\Services\AccountingSystem.Services\AccountingSystem.Services.csproj"

# Go to Tests project
Set-Location "..\Tests\AccountingSystem.Tests"
dotnet add reference "..\..\..\02-Shared\Core\AccountingSystem.Core\AccountingSystem.Core.csproj"
dotnet add reference "..\..\..\02-Shared\Models\AccountingSystem.Models\AccountingSystem.Models.csproj"
dotnet add reference "..\..\..\02-Shared\Services\AccountingSystem.Services\AccountingSystem.Services.csproj"
dotnet add reference "..\AccountingWeb\AccountingSystem.Web\AccountingSystem.Web.csproj"

# Return to solution root
Set-Location ..\..\..\..

# Build the solution
Write-Host "Building solution..." -ForegroundColor Yellow
dotnet build

Write-Host "Development environment setup complete!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Copy your VB.NET source code to 01-Reference/VB.NET-Source/" -ForegroundColor White
Write-Host "2. Update connection string in AccountingSystem.Web/appsettings.json" -ForegroundColor White
Write-Host "3. Run 'dotnet ef dbcontext scaffold' to generate models from existing database" -ForegroundColor White
Write-Host "4. Start development with 'dotnet run' in the AccountingSystem.Web project" -ForegroundColor White
