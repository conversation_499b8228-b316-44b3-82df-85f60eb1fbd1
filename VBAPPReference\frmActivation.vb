﻿Imports System.Management
Imports System.Data.SqlClient

Public Class frmActivation
    Dim Idp As String
    Dim Idp1 As String
    Dim idp2 As String

    Private Sub frmActivation_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' Get Processor ID using your existing method
            Dim Searcher As ManagementObjectSearcher
            Searcher = New ManagementObjectSearcher("Select ProcessorId From Win32_Processor")
            For Each Device As ManagementObject In Searcher.Get()
                For Each Prop As PropertyData In Device.Properties
                    '%%%%%%%%%%%%%%%%%%%%%%%%%%%%  الرقم استخراج'
                    Idp = (Prop.Value.ToString)
                Next
            Next

            '%%%%%%%%%%%%%%%%%%%%%%%%%%%%  الرقم تشویش'
            Idp = Obfuscate(Idp)  'اسكي الى تحویله'
            Idp = Str2Int(Idp)  'التسجیل رقم'
            TextBoxReg.Text = Idp

            '%%%%%%%%%%%%%%%%%%%%%%%%%%%%  الرقم تشویش'
            Idp1 = Obfuscate(Idp)  'اسكي الى تحویله'  التفعیل رقم
            Idp1 = Str2Int(Idp1)
            Idp1 = (Idp1.Substring(0, 14))

        Catch ex As Exception
            MsgBox("خطأ في قراءة معلومات الجهاز: " & ex.Message, MsgBoxStyle.Critical, "نظام السلطان")
        End Try
    End Sub

    Private Sub ButtonOK_Click(sender As Object, e As EventArgs) Handles ButtonOK.Click
        idp2 = Idp1
        If TextBoxUser.Text.Trim = idp2 Then
            ' Activation code is correct
            Try
                ' Save basic settings
                My.Settings.nameuser = TextBoxUser.Text.Trim

                ' Create and save license with enhanced machine fingerprint
                Dim machineFingerprint As String = GenerateEnhancedMachineFingerprint()
                Dim expiryDate As DateTime = DateTime.MinValue ' Forever license

                ' Save to database
                If SaveLicenseToDatabase(TextBoxUser.Text.Trim, machineFingerprint, expiryDate, True) Then
                    ' Save to local settings
                    SaveLicenseToSettings(TextBoxUser.Text.Trim, expiryDate, machineFingerprint)
                    My.Settings.Save()

                    MsgBox("تم تفعيل النسخة بنجاح يرجى اعادة تشغيل البرنامج", MsgBoxStyle.Information, "نظام السلطان")
                    Me.Close()
                    frmMain.Show()
                Else
                    MsgBox("حدث خطأ في حفظ التفعيل، يرجى المحاولة مرة أخرى", MsgBoxStyle.Critical, "نظام السلطان")
                End If

            Catch ex As Exception
                MsgBox("خطأ في عملية التفعيل: " & ex.Message, MsgBoxStyle.Critical, "نظام السلطان")

                ' Clear settings on error
                My.Settings.nameuser = ""
                My.Settings.Save()
            End Try
        Else
            ' Activation code is incorrect
            My.Settings.nameuser = ""
            My.Settings.Save()

            ' Log failed attempt
            Try
                Dim machineFingerprint As String = GenerateEnhancedMachineFingerprint()
                SaveLicenseToDatabase(TextBoxUser.Text.Trim, machineFingerprint, DateTime.MinValue, False)
            Catch
                ' Silent fail for logging
            End Try

            MsgBox("عذرًا رمز التفعيل غير صحيح", MsgBoxStyle.Critical, "نظام السلطان")
        End If
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        End
    End Sub

    ' Enhanced machine fingerprint that includes your processor ID logic
    Private Function GenerateEnhancedMachineFingerprint() As String
        Try
            Dim fingerprint As String = ""

            ' Use your existing processor ID as primary identifier
            fingerprint += "PROC:" & Idp & "|"

            ' Add additional machine identifiers
            fingerprint += "MACHINE:" & Environment.MachineName & "|"
            fingerprint += "USER:" & Environment.UserName & "|"
            fingerprint += "PROCCOUNT:" & Environment.ProcessorCount.ToString() & "|"
            fingerprint += "OS:" & Environment.OSVersion.Version.ToString() & "|"
            fingerprint += "SYSDIR:" & Environment.SystemDirectory

            ' Try to get additional hardware info
            Try
                fingerprint += "|" & GetAdditionalHardwareInfo()
            Catch
                ' Continue without additional info if error
            End Try

            Return fingerprint

        Catch ex As Exception
            ' Fallback to basic info
            Return "PROC:" & Idp & "|MACHINE:" & Environment.MachineName & "|USER:" & Environment.UserName
        End Try
    End Function

    ' Get additional hardware information for stronger fingerprinting
    Private Function GetAdditionalHardwareInfo() As String
        Try
            Dim hwInfo As String = ""

            ' Get Motherboard Serial
            Try
                Dim searcher As New ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard")
                For Each obj As ManagementObject In searcher.Get()
                    hwInfo += "MB:" & obj("SerialNumber").ToString() & "|"
                    Exit For
                Next
            Catch
                ' Continue if motherboard info not available
            End Try

            ' Get Hard Drive Serial
            Try
                Dim searcher As New ManagementObjectSearcher("SELECT SerialNumber FROM Win32_PhysicalMedia")
                For Each obj As ManagementObject In searcher.Get()
                    If obj("SerialNumber") IsNot Nothing Then
                        hwInfo += "HDD:" & obj("SerialNumber").ToString().Trim() & "|"
                        Exit For
                    End If
                Next
            Catch
                ' Continue if HDD info not available
            End Try

            Return hwInfo

        Catch
            Return ""
        End Try
    End Function

    Private Sub SaveLicenseToSettings(activationCode As String, expiryDate As DateTime, fingerprint As String)
        My.Settings.MachineActivated = True
        My.Settings.ActivationCode = activationCode
        My.Settings.LicenseExpiryDate = expiryDate
        My.Settings.MachineFingerprint = fingerprint
        My.Settings.LicenseActivationDate = DateTime.Now
        My.Settings.Save()
    End Sub

    Private Function SaveLicenseToDatabase(activationCode As String, machineFingerprint As String, expiryDate As DateTime, isSuccessful As Boolean) As Boolean
        Try
            ' First check if this machine already has a license
            Dim checkSQL As String = "SELECT COUNT(*) FROM tblMachineLicenses WHERE MachineID = @MachineID AND IsActive = 1"

            Using checkCmd As New SqlCommand(checkSQL, Con)
                checkCmd.Parameters.AddWithValue("@MachineID", machineFingerprint)

                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If

                Dim existingCount As Integer = Convert.ToInt32(checkCmd.ExecuteScalar())

                If existingCount > 0 AndAlso isSuccessful Then
                    ' Update existing record
                    Dim updateSQL As String = "UPDATE tblMachineLicenses SET " &
                                            "ActivationCode = @ActivationCode, " &
                                            "ExpiryDate = @ExpiryDate, " &
                                            "LastAccessDate = @LastAccessDate, " &
                                            "Notes = @Notes " &
                                            "WHERE MachineID = @MachineID AND IsActive = 1"

                    Using updateCmd As New SqlCommand(updateSQL, Con)
                        updateCmd.Parameters.AddWithValue("@ActivationCode", activationCode)
                        updateCmd.Parameters.AddWithValue("@ExpiryDate", If(expiryDate = DateTime.MinValue, DBNull.Value, CObj(expiryDate)))
                        updateCmd.Parameters.AddWithValue("@LastAccessDate", DateTime.Now)
                        updateCmd.Parameters.AddWithValue("@Notes", "License re-activated with processor ID: " & Idp)
                        updateCmd.Parameters.AddWithValue("@MachineID", machineFingerprint)

                        updateCmd.ExecuteNonQuery()
                    End Using
                Else
                    ' Insert new record
                    Dim insertSQL As String = "INSERT INTO tblMachineLicenses " &
                                            "(Username, ActivationCode, MachineName, MachineID, WindowsUser, " &
                                            "ProcessorCount, OSVersion, IPAddress, ActivationDate, ExpiryDate, " &
                                            "IsActive, Notes, LastAccessDate) " &
                                            "VALUES (@Username, @ActivationCode, @MachineName, @MachineID, @WindowsUser, " &
                                            "@ProcessorCount, @OSVersion, @IPAddress, @ActivationDate, @ExpiryDate, " &
                                            "@IsActive, @Notes, @LastAccessDate)"

                    Using insertCmd As New SqlCommand(insertSQL, Con)
                        insertCmd.Parameters.AddWithValue("@Username", If(UserName, ""))
                        insertCmd.Parameters.AddWithValue("@ActivationCode", activationCode)
                        insertCmd.Parameters.AddWithValue("@MachineName", Environment.MachineName)
                        insertCmd.Parameters.AddWithValue("@MachineID", machineFingerprint)
                        insertCmd.Parameters.AddWithValue("@WindowsUser", Environment.UserName)
                        insertCmd.Parameters.AddWithValue("@ProcessorCount", Environment.ProcessorCount)
                        insertCmd.Parameters.AddWithValue("@OSVersion", Environment.OSVersion.ToString())
                        insertCmd.Parameters.AddWithValue("@IPAddress", GetLocalIPAddress())
                        insertCmd.Parameters.AddWithValue("@ActivationDate", DateTime.Now)
                        insertCmd.Parameters.AddWithValue("@ExpiryDate", If(expiryDate = DateTime.MinValue, DBNull.Value, CObj(expiryDate)))
                        insertCmd.Parameters.AddWithValue("@IsActive", isSuccessful)
                        insertCmd.Parameters.AddWithValue("@Notes",
                            If(isSuccessful,
                                "New activation - Processor ID: " & Idp & " | Registration: " & TextBoxReg.Text,
                                "Failed activation attempt - Processor ID: " & Idp))
                        insertCmd.Parameters.AddWithValue("@LastAccessDate", If(isSuccessful, CObj(DateTime.Now), DBNull.Value))

                        insertCmd.ExecuteNonQuery()
                    End Using
                End If
            End Using

            Return True

        Catch ex As Exception
            Console.WriteLine("Error saving license: " & ex.Message)
            Return False
        Finally
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End Try
    End Function

    Private Function GetLocalIPAddress() As String
        Try
            Dim host As System.Net.IPHostEntry = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName())
            For Each ip As System.Net.IPAddress In host.AddressList
                If ip.AddressFamily = System.Net.Sockets.AddressFamily.InterNetwork Then
                    Return ip.ToString()
                End If
            Next
            Return "127.0.0.1"
        Catch
            Return "Unknown"
        End Try
    End Function

    ' You need to include your existing Obfuscate and Str2Int functions here
    ' Add your existing functions:
    ' Private Function Obfuscate(input As String) As String
    ' Private Function Str2Int(input As String) As String

End Class