﻿Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports CrystalDecisions.Windows.Forms
Imports System.Data.SqlClient

Public Class frmPrintPreview

    Private Sub frmPrintPreview_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        'CheckPrinter()
        'Try
        '    If PrtRPTCheck = "True" Then
        '        Shell(String.Format("rundll32 printui.dll,PrintUIEntry /y /n ""{0}""", PrinterReports))
        '    End If
        'Catch ex As Exception

        'End Try
    End Sub
    Private Sub frmPrintPreview_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        Try
            If PrintType = "PurchaseInvoice" Then
                Dim RPT As New rptPurchaseInvoice
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.SetParameterValue(0, InvNoForPrint)
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "SalesReturnInvoice" Then

                'Dim RPT As New rptSalesReturnInvoice
                'UpdateCRDataSource(RPT)
                'RPT.Refresh()
                'RPT.SetParameterValue(0, InvNoForPrint)
                'CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "OpenStockInvoice" Then

                Dim RPT As New rptOpenStockInvoice
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.SetParameterValue(0, InvNoForPrint)
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "SalesInvoice" Then
                Dim RPT As CrystalDecisions.CrystalReports.Engine.ReportDocument

                If isTestDeploy = 1 Then
                    RPT = New rptSalesInvoice_NoQR
                Else
                    RPT = New rptSalesInvoice
                End If

                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.SetParameterValue(0, InvNoForPrint)
                RPT.SetParameterValue(1, InvoiceType)
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "ReturnPurchaseInvoice" Then

                Dim RPT As New rptPurchaseReturnInvoice
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.SetParameterValue(0, InvNoForPrint)
                CrystalReportViewer2.ReportSource = RPT


            ElseIf PrintType = "POSInvoice" Then

                Dim RPT As CrystalDecisions.CrystalReports.Engine.ReportDocument

                If isTestDeploy = 1 Then
                    RPT = New rptPOSInvoiceNoQR
                Else
                    RPT = New rptPOSInvoice
                End If

                'Dim RPT As New rptPOSInvoice
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.SetParameterValue(0, InvNoForPrint)
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "ItemStock" Then

                Dim RPT As New rptItemStock
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()
                RPT.ParameterFields(2).CurrentValues.Clear()
                RPT.SetParameterValue(0, StoreForReport)
                RPT.SetParameterValue(1, ItemNoForReport)
                RPT.SetParameterValue(2, ItemsCategory)
                CrystalReportViewer2.ReportSource = RPT
            ElseIf PrintType = "JESN" Then

                Dim RPT As New rptJEStatement
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()
                RPT.ParameterFields(2).CurrentValues.Clear()
                RPT.SetParameterValue(0, DateStrFrom)
                RPT.SetParameterValue(1, DateStrTo)
                RPT.SetParameterValue(2, AccNo)
                AccNo = 0
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "CustStatement" Then

                Dim RPT As New rptCustomerStatement
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()
                'RPT.ParameterFields(2).CurrentValues.Clear()
                RPT.SetParameterValue(1, AccNo)
                RPT.ParameterFields(0).CurrentValues.AddRange(DateStrFrom, DateStrTo, RangeBoundType.BoundInclusive, RangeBoundType.BoundInclusive)
                AccNo = 0
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "SalesHeader" Then

                Dim RPT As New rptSalesHeader
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()
                RPT.ParameterFields(2).CurrentValues.Clear()
                RPT.SetParameterValue(0, DateStrFrom)
                RPT.SetParameterValue(1, DateStrTo)
                RPT.SetParameterValue(2, InvoiceType)
                AccNo = 0
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "SalesLines" Then

                Dim RPT As New rptSalesLines
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()
                RPT.ParameterFields(2).CurrentValues.Clear()
                RPT.SetParameterValue(0, DateStrFrom)
                RPT.SetParameterValue(1, DateStrTo)
                RPT.SetParameterValue(2, InvoiceType)
                AccNo = 0
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "PurchaseHeader" Then

                Dim RPT As New rptPurchaseHeader
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()
                RPT.ParameterFields(2).CurrentValues.Clear()
                RPT.SetParameterValue(0, DateStrFrom)
                RPT.SetParameterValue(1, DateStrTo)
                RPT.SetParameterValue(2, InvoiceType)
                AccNo = 0
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "PurchaseLines" Then

                Dim RPT As New rptPurchaseLines
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()
                RPT.ParameterFields(2).CurrentValues.Clear()
                RPT.SetParameterValue(0, DateStrFrom)
                RPT.SetParameterValue(1, DateStrTo)
                RPT.SetParameterValue(2, InvoiceType)
                AccNo = 0
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "StockMovements" Then

                Dim RPT As New rptStockMovements
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()
                RPT.ParameterFields(2).CurrentValues.Clear()
                RPT.SetParameterValue(0, DateStrFrom)
                RPT.SetParameterValue(1, DateStrTo)
                RPT.SetParameterValue(2, StoreForReport)
                AccNo = 0
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "VendorStatement" Then

                Dim RPT As New rptVendorStatement
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()
                'RPT.ParameterFields(2).CurrentValues.Clear()
                RPT.SetParameterValue(1, AccNo)
                RPT.ParameterFields(0).CurrentValues.AddRange(DateStrFrom, DateStrTo, RangeBoundType.BoundInclusive, RangeBoundType.BoundInclusive)
                AccNo = 0
                CrystalReportViewer2.ReportSource = RPT


            ElseIf PrintType = "GLTrx" Then

                Dim RPT As New rptGLTrx
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.SetParameterValue(0, JESN)
                JESN = 0
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "CashRec" Then

                Dim RPT As New rptCashRec
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.SetParameterValue(0, JESN)
                JESN = 0
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "CashPay" Then

                Dim RPT As New rptCashPay
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.SetParameterValue(0, JESN)
                JESN = 0
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "CustomerBalances" Then

                Dim RPT As New rptCustomerBalances
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()
                RPT.ParameterFields(2).CurrentValues.Clear()
                RPT.SetParameterValue(0, DateTo)
                If CustNo = 0 Then
                    RPT.SetParameterValue(2, Nothing)
                Else
                    RPT.SetParameterValue(2, CustNo)
                End If

                ' Set @SalesEmployee
                If EmpNo = 0 Then
                    RPT.SetParameterValue(1, Nothing)
                Else
                    RPT.SetParameterValue(1, EmpNo)
                End If
                CrystalReportViewer2.ReportSource = RPT

            ElseIf PrintType = "VendorBalances" Then

                Dim RPT As New rptVendorBalances
                UpdateCRDataSource(RPT)
                RPT.Refresh()
                RPT.ParameterFields(0).CurrentValues.Clear()
                RPT.ParameterFields(1).CurrentValues.Clear()

                RPT.SetParameterValue(0, DateTo)
                If VendNo = 0 Then
                    RPT.SetParameterValue(1, Nothing)
                Else
                    RPT.SetParameterValue(1, VendNo)
                End If

                CrystalReportViewer2.ReportSource = RPT

            End If
        Catch ex As Exception
            MsgBox("قم بتثبيت برنامج Crystal Report على الجهاز", MsgBoxStyle.Critical, "خطأ في الطباعة")
        End Try
        CrystalReportViewer2.Zoom(80)
        Me.Size = New Size(frmWidth, frmHeight)
        'Me.Size = New Size(frmRPTWidth, frmRPTHeight)
        'Me.Size = New Size(995, 613)


    End Sub


End Class