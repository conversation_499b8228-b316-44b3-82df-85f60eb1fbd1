﻿@model ErrorViewModel
@{
    ViewData["Title"] = "خطأ";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">حدث خطأ أثناء معالجة الطلب</h4>
                </div>
                <div class="card-body">
                    @if (TempData["Error"] != null)
                    {
                        <div class="alert alert-danger" role="alert">
                            @TempData["Error"]
                        </div>
                    }
                    @if (TempData["ErrorDetail"] != null)
                    {
                        <details class="mb-3">
                            <summary>تفاصيل الخطأ</summary>
                            <pre class="mt-2">@TempData["ErrorDetail"]</pre>
                        </details>
                    }

                    @if (Model.ShowRequestId)
                    {
                        <p>
                            <strong>رقم الطلب:</strong> <code>@Model.RequestId</code>
                        </p>
                    }
                    <a href="/Purchase/Create" class="btn btn-primary">المحاولة مرة أخرى</a>
                    <a href="/SimpleDashboard/Index" class="btn btn-secondary">العودة للوحة التحكم</a>
                </div>
            </div>
        </div>
    </div>
</div>
