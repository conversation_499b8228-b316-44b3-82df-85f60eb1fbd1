﻿Imports System.Data.SqlClient
Public Class frmPOSShifts



    Private Sub frmShifts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
            LoadShifts()
        End Sub

        Private Sub LoadShifts()
            Dim dt As New DataTable()
            Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT ShiftID, ShiftName, StartTime, EndTime, IsActive FROM tblPOSShifts ORDER BY ShiftID DESC", con)
            Dim da As New SqlDataAdapter(cmd)
                da.Fill(dt)
            End Using
            dgvShifts.DataSource = dt
        End Sub

        Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
            If txtShiftName.Text.Trim = "" Then
                MessageBox.Show("يرجى إدخال اسم الوردية", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If

            Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("IF EXISTS (SELECT 1 FROM tblPOSShifts WHERE ShiftName = @ShiftName) UPDATE tblPOSShifts SET StartTime=@StartTime, EndTime=@EndTime, IsActive=@IsActive WHERE ShiftName=@ShiftName ELSE INSERT INTO tblPOSShifts (ShiftName, StartTime, EndTime, IsActive) VALUES (@ShiftName, @StartTime, @EndTime, @IsActive)", con)

            cmd.Parameters.AddWithValue("@ShiftName", txtShiftName.Text.Trim())
                cmd.Parameters.AddWithValue("@StartTime", dtpStartTime.Value.TimeOfDay)
                cmd.Parameters.AddWithValue("@EndTime", dtpEndTime.Value.TimeOfDay)
                cmd.Parameters.AddWithValue("@IsActive", chkActive.Checked)

                con.Open()
                cmd.ExecuteNonQuery()
            End Using

            LoadShifts()
            ClearFields()
            MessageBox.Show("تم حفظ بيانات الوردية", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End Sub

        Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
            ClearFields()
        End Sub

        Private Sub ClearFields()
            txtShiftName.Clear()
            dtpStartTime.Value = DateTime.Now
            dtpEndTime.Value = DateTime.Now
            chkActive.Checked = True
        End Sub

        Private Sub dgvShifts_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgvShifts.CellClick
            If e.RowIndex >= 0 Then
                Dim row As DataGridViewRow = dgvShifts.Rows(e.RowIndex)
                txtShiftName.Text = row.Cells("ShiftName").Value.ToString()
                dtpStartTime.Value = DateTime.Today.Add(TimeSpan.Parse(row.Cells("StartTime").Value.ToString()))
                dtpEndTime.Value = DateTime.Today.Add(TimeSpan.Parse(row.Cells("EndTime").Value.ToString()))
            chkActive.Checked = Convert.ToBoolean(row.Cells("IsActive").Value)
        End If
        End Sub

    End Class


