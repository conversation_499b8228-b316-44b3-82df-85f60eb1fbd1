@model AccountingSystem.Web.ViewModels.InvoiceToolSettingsViewModel
@{
    ViewData["Title"] = "إعدادات أدوات الفواتير";
    Layout = "_Layout";
    var invoiceTypes = ViewBag.InvoiceTypes as List<string> ?? new List<string>();
}

<div class="container-fluid" dir="rtl">
    <div class="page-header">
        <h1 class="page-title"><i class="fas fa-tools"></i> @ViewData["Title"]</h1>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show">@TempData["SuccessMessage"]<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show">@TempData["ErrorMessage"]<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>
        }
    </div>

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">اختر نوع الفاتورة لعرض الإعدادات</h3>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label for="invoiceTypeSelector" class="form-label">نوع الفاتورة</label>
                <select id="invoiceTypeSelector" class="form-select">
                    @foreach (var type in invoiceTypes)
                    {
                        <option value="@type" selected="@(type == Model.InvoiceType)">@type</option>
                    }
                </select>
            </div>
        </div>
    </div>

    <div id="settingsFormContainer">
        @await Html.PartialAsync("_SettingsForm", Model)
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#invoiceTypeSelector').change(function() {
                var selectedType = $(this).val();
                if (selectedType) {
                    $('#settingsFormContainer').html('<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>');
                    $.get('/InvoiceToolSettings/GetSettings?invoiceType=' + selectedType, function(data) {
                        $('#settingsFormContainer').html(data);
                    });
                }
            });
        });
    </script>
} 