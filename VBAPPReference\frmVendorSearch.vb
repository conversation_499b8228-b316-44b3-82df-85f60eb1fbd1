﻿Imports System.Data.SqlClient
Public Class frmVendorSearch
    Private Sub frmVendorSearch_Load(sender As Object, e As EventArgs) Handles MyBase.Load
    End Sub

    Sub SearchBox()
        Dim keyword As String = txtSearch.Text.Trim()
        If keyword = "" Then
            CustomersLoad()
            Return
        End If

        Dim ds As DataSet = New DataSet
        Dim cmd As New SqlClient.SqlCommand("Select VendorNo as [رقم المورد],VendorName as [الاسم التجاري],FirstName as [الاسم الأول],LastName as [الاسم الاخير] from tblVendors where CAST(VendorNo AS NVARCHAR) like @kw or VendorName LIKE @kw or FirstName LIKE @kw or LastName LIKE @kw order by VendorNo", New SqlClient.SqlConnection(Constr))
        cmd.Parameters.AddWithValue("@kw", "%" & keyword & "%")
        Dim da As New SqlClient.SqlDataAdapter(cmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Sub CustomersLoad()
        Dim ds As DataSet = New DataSet
        Dim usercmd As New SqlCommand("Select VendorNo as [رقم المورد],VendorName as [الاسم التجاري],FirstName as [الاسم الأول],LastName as [الاسم الاخير] from tblVendors order by VendorNo", Con)
        Dim da As New SqlDataAdapter(usercmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        SearchBox()
    End Sub

    Private Sub DataGridView1_CellDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If Val(DataGridView1.CurrentRow.Cells(0).Value) <> 0 Then
            VendNo = DataGridView1.CurrentRow.Cells(0).Value
            DataGridView1.DataSource = ""
            txtSearch.Text = ""
            Me.Close()
            If VendorSearchForm = "frmPurchaseInvoiceTrx" Then
                frmPurchaseInvoiceTrx.BringToFront()
                frmPurchaseInvoiceTrx.cmbxPartnerNo.SelectedValue = VendNo
                VendorSearchForm = ""

            ElseIf VendorSearchForm = "frmTrxExpenses" Then
                frmTrxExpenses.BringToFront()
                frmTrxExpenses.cmbxPartnerNo.SelectedValue = VendNo
                frmTrxExpenses.txtVendorInvoiceNo.Focus()
                VendorSearchForm = ""

            ElseIf VendorSearchForm = "frmVendorInvoices" Then
                frmVendorInvoices.BringToFront()
                frmVendorInvoices.cmbxPartnerNo.SelectedValue = VendNo
                VendorSearchForm = ""

            ElseIf VendorSearchForm = "frmPurchasingReturnInvoice" Then
                frmPurchasingReturnInvoice.BringToFront()
                frmPurchasingReturnInvoice.cmbxPartnerNo.SelectedValue = VendNo
                VendorSearchForm = ""
            ElseIf VendorSearchForm = "frmVendorStatement" Then
                frmVendorStatement.BringToFront()
                frmVendorStatement.cmbxPartnerNo.SelectedValue = VendNo
                VendorSearchForm = ""
            ElseIf VendorSearchForm = "frmVendorBalances" Then
                frmVendorBalances.BringToFront()
                frmVendorBalances.cmbxPartnerNo.SelectedValue = VendNo
                VendorSearchForm = ""

            End If
        End If
    End Sub
End Class