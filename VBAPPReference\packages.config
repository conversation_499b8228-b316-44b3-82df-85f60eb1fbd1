﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Augment" version="3.0.0" targetFramework="net48" />
  <package id="Azure.Core" version="1.45.0" targetFramework="net48" />
  <package id="Azure.Identity" version="1.13.2" targetFramework="net48" />
  <package id="CrystalDecisions.CrystalReports.Engine" version="1.0.0" targetFramework="net45" />
  <package id="CrystalDecisions.ReportSource" version="1.0.0" targetFramework="net45" />
  <package id="CrystalReports.AxShockwaveFlashObjects" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.Engine" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.ClientDoc" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.CommLayer" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.CommonControls" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.CommonObjectModel" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.Controllers" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.CubeDefModel" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.DataDefModel" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.DataSetConversion" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.ObjectFactory" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.Prompting" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.ReportDefModel" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportAppServer.XmlSerialize" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ReportSource" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.Shared" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.ShockwaveFlashObjects" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.Windows.Forms" version="13.0.4003" targetFramework="net45" />
  <package id="CrystalReports.WPF.ViewerShared" version="13.0.4003" targetFramework="net45" />
  <package id="Ensure.That" version="8.0.0" targetFramework="net48" />
  <package id="EPPlusFree" version="4.5.3.8" targetFramework="net472" />
  <package id="Genesis.QRCodeLib" version="1.0.0" targetFramework="net48" />
  <package id="Guna.UI2.WinForms" version="2.0.4.7" targetFramework="net472" />
  <package id="log4net" version="3.0.4" targetFramework="net48" />
  <package id="MaterialSkin.2" version="2.3.1" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.3" targetFramework="net48" />
  <package id="Microsoft.Bcl.Cryptography" version="9.0.3" targetFramework="net48" />
  <package id="Microsoft.Bcl.Memory" version="9.0.3" targetFramework="net48" />
  <package id="Microsoft.Bcl.TimeProvider" version="9.0.3" targetFramework="net48" />
  <package id="Microsoft.Data.SqlClient" version="6.0.1" targetFramework="net48" />
  <package id="Microsoft.Data.SqlClient.SNI" version="6.0.2" targetFramework="net48" />
  <package id="Microsoft.Extensions.Caching.Abstractions" version="9.0.3" targetFramework="net48" />
  <package id="Microsoft.Extensions.Caching.Memory" version="9.0.3" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="9.0.3" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="9.0.3" targetFramework="net48" />
  <package id="Microsoft.Extensions.Options" version="9.0.3" targetFramework="net48" />
  <package id="Microsoft.Extensions.Primitives" version="9.0.3" targetFramework="net48" />
  <package id="Microsoft.Identity.Client" version="4.69.1" targetFramework="net48" />
  <package id="Microsoft.Identity.Client.Extensions.Msal" version="4.69.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Abstractions" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Logging" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Protocols" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Protocols.OpenIdConnect" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Tokens" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.SqlServer.Assessment" version="1.1.17" targetFramework="net48" />
  <package id="Microsoft.SqlServer.Assessment.Authoring" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.SqlServer.SqlManagementObjects" version="172.64.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="QRCoder" version="1.6.0" targetFramework="net48" />
  <package id="RibbonWinForms" version="5.1.0-beta" targetFramework="net48" requireReinstallation="true" />
  <package id="System.Buffers" version="4.6.0" targetFramework="net48" />
  <package id="System.ClientModel" version="1.3.0" targetFramework="net48" />
  <package id="System.Configuration.ConfigurationManager" version="9.0.3" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="9.0.3" targetFramework="net48" />
  <package id="System.Formats.Asn1" version="9.0.3" targetFramework="net48" />
  <package id="System.IdentityModel.Tokens.Jwt" version="8.6.1" targetFramework="net48" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.FileSystem.AccessControl" version="5.0.0" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.3" targetFramework="net48" />
  <package id="System.Memory" version="4.6.0" targetFramework="net48" />
  <package id="System.Memory.Data" version="9.0.3" targetFramework="net48" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.6.0" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.1" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.0" targetFramework="net48" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="6.0.1" targetFramework="net48" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="net48" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Pkcs" version="9.0.3" targetFramework="net48" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.ProtectedData" version="9.0.3" targetFramework="net48" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.2" targetFramework="net48" />
  <package id="System.Security.Permissions" version="9.0.3" targetFramework="net48" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net472" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="9.0.3" targetFramework="net48" />
  <package id="System.Text.Json" version="9.0.3" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.0" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="Zen.Barcode.Core" version="2.0.0" targetFramework="net48" />
  <package id="Zen.Barcode.Core.Code128" version="3.1.0" targetFramework="net48" />
  <package id="Zen.Barcode.Rendering.Framework" version="3.1.10729.1" targetFramework="net48" />
  <package id="Zen.Barcode.Rendering.Framework.Web" version="3.1.10729.1" targetFramework="net48" />
  <package id="ZXing.Net" version="0.16.10" targetFramework="net48" requireReinstallation="true" />
</packages>