﻿
Imports System.Data.SqlClient
    Imports System.Globalization
    Imports System.Text
    Imports QRCoder

Public Class frmOpenStockInvoice
    Dim EnteredBefore As Int64 = 0
    Dim Cr As Int64
    Dim LableCode As Int64
    Dim txtPrice As Int64
    Dim QTY As Int64
    'Dim TrxNo As Int64
    Dim ForClose As Integer = 0
    Dim ForItemChange As Integer = 0
    Dim flag_cell_edited As Boolean
    Dim currentRow As Integer
    Dim currentColumn As Integer
    Dim ItemFailed As String = ""
    Dim x As Int64 = 0
    Dim PrintCopies, MaterialAccount, DiscountAccount As Int64
    Dim PrintOption, ReferenceMandatory, DefaultPrinter, MandatoryCustomerVATReg, PriceIncludeVATDef, NonVATInvoiceDef, PaymentType As String


    Private Sub frmOpenStockInvoice_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        InvNo = Val(txtInvoiceNo.Text)
        If InvNo <> 0 And Val(lblTotal.Text) = 0 Then
            Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set ReadyForUse = 'True' where TrxNo = " & Val(InvNo) & " and  TrxType ='رصيد افتتاحي'", Con)

            Con.Close()
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            Dim DeleteCMD As New SqlCommand("Delete tblStockMovement where TrxType = 'رصيد افتتاحي' and DocNo = " & Val(InvNo) & "", Con)
            Dim Delete2CMD As New SqlCommand("Update tblStockTrx set OutInvoiceType= NULL, OutInvoiceNo= NULL,OutInvoiceItemSN = NULL, OutCreatedBy = NULL,OutCreatedOn = NULL,UnitPrice=0,InStock = 1 where OutInvoiceType= 'رصيد افتتاحي' and OutInvoiceNo = " & Val(InvNo) & " and InStock = 0", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            DeleteCMD.ExecuteNonQuery()
            Delete2CMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        ElseIf InvNo <> 0 And Val(lblTotal.Text) <> 0 Then
            'If MsgBox("هل ترغب بحفظ التغييرات ؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
            ForClose = 1
            SaveTrx()
        End If

        'End If
    End Sub
    Private Sub frmTrxInvIn_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        InvNo = 0
        StoresLoad()
        LoadSettings()
        GetSerial()
        GetLineSerial()
        ForceGregorianForAllPickers(Me)

        LoadUserAuthorization(UserName) ' Pass the logged-in username
    End Sub
    Sub GetSerial()
        Try
            Dim PreUsedCMD As New SqlCommand("Select (Min(TrxNo)) as SN from tblStockMovHeader where ReadyForUse = 'True' and TrxType = 'رصيد افتتاحي'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = PreUsedCMD.ExecuteReader()
            If reader.Read Then
                InvNo = Val(reader.Item(0).ToString)
            Else
                InvNo = 0
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If InvNo = 0 Then
                Dim SelectCMD As New SqlCommand("Select (Max(TrxNo)) + 1 as SN from tblStockMovHeader where TrxType = 'رصيد افتتاحي'", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                Dim reader2 As SqlDataReader = SelectCMD.ExecuteReader()
                If reader2.Read Then
                    If reader2.Item("SN").ToString <> "" Then
                        InvNo = Val(reader2.Item("SN").ToString)
                    Else
                        InvNo = 1
                    End If
                End If
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                Dim InsertCMD As New SqlCommand("Insert into tblStockMovHeader (TrxType,TrxNo,ReadyForUse,CreatedBy,CreatedOn) values ('رصيد افتتاحي'," & Val(InvNo) & ",'False','" & UserName & "','" & Format(DateAndTime.Now, "yyyy/MM/dd hh:mm:ss tt") & "')", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                InsertCMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            Else
                Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set ReadyForUse = 'False' where TrxNo = " & Val(InvNo) & " and TrxType = 'رصيد افتتاحي' ", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                UpdateCMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If
            txtInvoiceNo.Text = InvNo
        Catch ex As Exception

        End Try
    End Sub
    Sub GetLineSerial()
        Try
            Dim SelectCMD As New SqlCommand("Select Max(LineSN) + 1 as SN from tblStockMovement where TrxType = 'رصيد افتتاحي' and DocNo = " & Val(txtInvoiceNo.Text) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                If reader.Item("SN").ToString <> "" Then
                    LineSN = Val(reader.Item("SN").ToString)
                Else
                    LineSN = 1
                End If
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            txtItemSN.Text = LineSN
            txtItemNo.Focus()
        Catch ex As Exception

        End Try
    End Sub

    Sub LoadUserAuthorization(ByVal username As String)
        Try
            Dim query As String = "SELECT GroupID, DefaultStore, StoreChange, DefaultCustomer, CustomerChange, DefaultCashier, CashierChange, ChangeInvoicePrice, MaxDiscountPercent 
                               FROM tblUsers WHERE Username = @Username"

            Dim cmd As New SqlCommand(query, Con)
            cmd.Parameters.AddWithValue("@Username", username)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            Dim reader As SqlDataReader = cmd.ExecuteReader()

            If reader.Read() Then
                Dim isAdmin As Boolean = (Not IsDBNull(reader("GroupID")) AndAlso Convert.ToInt32(reader("GroupID")) = 1)

                ' If the user is in GroupID = 1 (Admin), grant full access
                If isAdmin Then
                    cmbxStore.Enabled = True
                    'cmbxPartnerNo.Enabled = True
                    'cmbxCashier.Enabled = True

                Else
                    ' Apply Default Store
                    If Not IsDBNull(reader("DefaultStore")) Then
                        cmbxStore.SelectedItem = reader("DefaultStore").ToString()
                    End If
                    cmbxStore.Enabled = Convert.ToBoolean(reader("StoreChange")) ' Enable if allowed

                    ' Apply Default Customer
                    'If Not IsDBNull(reader("DefaultCustomer")) Then
                    '    cmbxPartnerNo.SelectedValue = Convert.ToInt32(reader("DefaultCustomer"))
                    'End If
                    'cmbxPartnerNo.Enabled = Convert.ToBoolean(reader("CustomerChange")) ' Enable if allowed

                    ' Apply Default Cashier
                    'If Not IsDBNull(reader("DefaultCashier")) Then
                    '    cmbxCashier.SelectedValue = Convert.ToInt32(reader("DefaultCashier"))
                    'End If
                    'cmbxCashier.Enabled = Convert.ToBoolean(reader("CashierChange")) ' Enable if allowed

                    ' Apply Price Change Authorization
                    txtUnitPrice.Enabled = Convert.ToBoolean(reader("ChangeInvoicePrice")) ' Enable if allowed

                    If Not IsDBNull(reader("MaxDiscountPercent")) Then
                        MaxDiscountPercent = Convert.ToDecimal(reader("MaxDiscountPercent"))
                    Else
                        MaxDiscountPercent = 0
                    End If


                End If
            End If

            reader.Close()

            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If

        Catch ex As Exception
            MessageBox.Show("Error loading user authorization: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub


    Sub LoadSettings()
        Dim CMD As New SqlCommand("Select * from tblToolsInvoice where InvoiceType = 'رصيد افتتاحي'", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        If reader.Read Then
            'ckbxVATIncluded.Enabled = Trim(reader.Item(1).ToString)
            'ckbxNonVatInvoice.Enabled = Trim(reader.Item(2).ToString)



            Store = Trim(reader.Item(12).ToString)
            PriceIncludeVATDef = Trim(reader.Item(13).ToString)
            NonVATInvoiceDef = Trim(reader.Item(14).ToString)
            MaterialAccount = Val(reader.Item(4).ToString)
            DiscountAccount = Val(reader.Item(5).ToString)
            MandatoryCustomerVATReg = Trim(reader.Item(8).ToString)
            DefaultPrinter = Trim(reader.Item(6).ToString)
            PrintOption = Trim(reader.Item(7).ToString)
            ReferenceMandatory = Trim(reader.Item(3).ToString)

        End If
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If


    End Sub

    Sub StoresLoad()
        Dim CMD As New SqlCommand("Select SN,Store from tblStores Order by Store", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxStore.Items.Clear()
        Do While reader.Read
            cmbxStore.Items.Add(reader.Item(1).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub

    Sub UnitsLoad()
        Dim CMD As New SqlCommand("SELECT UofM, 1 AS BaseFactor, AUofM, AUofMX, AUofM2, AUofMX2, AUofM3, AUofMX3 
                               FROM tblItems WHERE ItemNo = @ItemNo", Con)

        CMD.Parameters.AddWithValue("@ItemNo", Val(txtItemNo.Text))

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If

        Dim reader As SqlDataReader = CMD.ExecuteReader()

        ' Reset ComboBox before binding new data
        cmbxUnit.DataSource = Nothing
        cmbxUnit.Items.Clear()

        Dim units As New Dictionary(Of String, Decimal)

        If reader.Read() Then
            ' Add only non-empty values
            If Not IsDBNull(reader("UofM")) AndAlso reader("UofM").ToString().Trim() <> "" Then
                units.Add(reader("UofM").ToString(), 1) ' Base unit
            End If
            If Not IsDBNull(reader("AUofM")) AndAlso reader("AUofM").ToString().Trim() <> "" Then
                units.Add(reader("AUofM").ToString(), If(IsDBNull(reader("AUofMX")), 1, Convert.ToDecimal(reader("AUofMX"))))
            End If
            If Not IsDBNull(reader("AUofM2")) AndAlso reader("AUofM2").ToString().Trim() <> "" Then
                units.Add(reader("AUofM2").ToString(), If(IsDBNull(reader("AUofMX2")), 1, Convert.ToDecimal(reader("AUofMX2"))))
            End If
            If Not IsDBNull(reader("AUofM3")) AndAlso reader("AUofM3").ToString().Trim() <> "" Then
                units.Add(reader("AUofM3").ToString(), If(IsDBNull(reader("AUofMX3")), 1, Convert.ToDecimal(reader("AUofMX3"))))
            End If

            ' Bind to ComboBox
            cmbxUnit.DataSource = New BindingSource(units, Nothing)
            cmbxUnit.DisplayMember = "Key"  ' Show UofM
            cmbxUnit.ValueMember = "Value"  ' Store Conversion Factor
        End If

        reader.Close()

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub



    Sub SaveTrx()
        If Val(txtInvoiceNo.Text) <> 0 And Val(lblItemCount.Text) <> 0 Then
            If cmbxStore.Text.Trim = "" Then
                MsgBox("يجب إدخال المخزن للاستمرار", MsgBoxStyle.Critical, "نظام السلطان")
                Exit Sub
            End If
            InvNoForPrint = InvNo




            Dim UpdateCMD As New SqlCommand("Update tblStockMovHeader set TrxDate='" & Format(dtp1.Value.Date, "yyyy/MM/dd hh:mm:ss tt") & "',TrxNote = '" & Trim(txtNotes.Text) & "',TrxTotal=" & Val(lblTotal.Text) & ",TrxNetAmount = " & Val(lblTotal.Text) & ",ModifiedBy='" & UserName & "',ModifiedOn='" & Format(DateAndTime.Now, "yyyy/MM/dd hh:mm:ss tt") & "',VOIDSTTS=0,ReadyForUse='False',Store= '" & cmbxStore.Text.Trim & "' where TrxNo = " & Val(txtInvoiceNo.Text) & " and TrxType = 'رصيد افتتاحي'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            If ForItemChange = 0 Then
                Call UpdateTotals()
                'CreateUniversalJE(trxNo:=InvNo, referenceType:="رصيد افتتاحي")
            End If

            If ForClose = 1 Then
                Me.Close()
            ElseIf ForItemChange <> 1 Then
                Try
                    Using Con As New SqlConnection(Constr)
                        Using cmd As New SqlCommand("sp_CreateOpeningStockEntry", Con)
                            cmd.CommandType = CommandType.StoredProcedure
                            cmd.Parameters.AddWithValue("@DocNo", InvNo)

                            If Con.State <> ConnectionState.Open Then
                                Con.Open()
                            End If
                            cmd.ExecuteNonQuery()
                            If Con.State <> ConnectionState.Closed Then
                                Con.Close()
                            End If
                        End Using
                    End Using
                Catch ex As Exception
                    MessageBox.Show("خطأ أثناء ترحيل الفاتورة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    If Con.State <> ConnectionState.Closed Then
                        Con.Close()
                    End If
                End Try
                MsgBox("تم حفظ الفاتورة بنجاح", MsgBoxStyle.Information, "نظام السلطان")
                PrintType = "OpenStockInvoice"
                LoadSettings()
                ClearFields()
                If PrintOption = 0 Then
                    frmPrintPreview.MdiParent = frmMain
                    frmPrintPreview.Show()
                ElseIf PrintOption = 1 Then
                    If MsgBox("هل ترغب في طباعة الفاتورة؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
                        frmPrintPreview.MdiParent = frmMain
                        frmPrintPreview.Show()
                    End If
                End If
            End If

        End If
    End Sub

    Sub ClearFieldsWOGetSN()
        DGV1.DataSource = ""
        txtNotes.Clear()
        lblTotal.Text = ""
        lblItemCount.Text = ""
        lblQTY.Text = ""
        LoadSettings()
        txtItemSN.Text = 1
    End Sub
    Sub ClearFields()
        DGV1.DataSource = ""
        txtNotes.Clear()
        txtInvoiceNo.Clear()
        lblTotal.Text = ""
        lblItemCount.Text = ""
        lblQTY.Text = ""
        LoadSettings()
        GetSerial()
        txtItemSN.Text = 1
    End Sub

    Sub UpdateTotals()
        Dim grossTotal As Decimal = 0
        Dim qty As Decimal = 0

        Dim FillCMD As New SqlCommand("SELECT tblStockMovement.LineSN AS م, tblStockMovement.ItemNo AS [رقم الصنف], tblItems.ItemDescription AS [وصف الصنف], tblStockMovement.TrxQTY AS الكمية,tblItems.UofM AS [الوحدة], tblStockMovement.UnitPrice AS [سعر الوحدة],  FORMAT(tblStockMovement.TrxQTY * tblStockMovement.UnitPrice, 'N2') AS [السعر الإجمالي] FROM tblStockMovement INNER JOIN tblItems ON tblStockMovement.ItemNo = tblItems.ItemNo WHERE        (tblStockMovement.DocNo = " & Val(txtInvoiceNo.Text) & ") AND (tblStockMovement.TrxType = 'رصيد افتتاحي') ORDER BY م", Con)

        ' حساب إجمالي السعر الإجمالي (قبل الخصم)
        For Each r As DataGridViewRow In DGV1.Rows
            If Not r.IsNewRow Then
                grossTotal += r.Cells("السعر الإجمالي").Value
                qty += Val(r.Cells("الكمية").Value)
            End If
        Next
        lblTotal.Text = grossTotal
        ' التحقق من الخصم
        Dim discountPercent As Decimal = 0
        ' حساب مبلغ الخصم
        Dim discountAmount As Decimal = Math.Round((discountPercent / 100) * grossTotal, 2)
        Dim totalAfterDiscount As Decimal = grossTotal - discountAmount
        lblQTY.Text = qty
        lblItemCount.Text = DGV1.Rows.Count
        lblQTY.Text = qty
    End Sub





    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        If Val(lblItemCount.Text) = 0 And Val(lblTotal.Text) = 0 Then
            ClearFieldsWOGetSN()
        End If
    End Sub

    Private Sub DGV1_KeyDown(sender As Object, e As KeyEventArgs) Handles DGV1.KeyDown
        Try
            Dim iCol = DGV1.CurrentCell.ColumnIndex
            Dim iRow = DGV1.CurrentCell.RowIndex

            If e.KeyCode = Keys.Enter Then
                e.SuppressKeyPress = True


                If e.KeyCode = Keys.Enter Then
                    e.SuppressKeyPress = True
                    If iCol = DGV1.Columns.Count - 1 Then
                        If iRow < DGV1.Rows.Count - 1 Then
                            DGV1.CurrentCell = DGV1(0, iRow + 1)
                        End If
                    Else

                        If iCol = 1 Then
                            DGV1.CurrentCell = DGV1(iCol + 3, iRow)
                        Else
                            DGV1.CurrentCell = DGV1(iCol + 1, iRow)
                        End If
                    End If
                End If




            ElseIf e.KeyCode = Keys.Delete Then
                If DGV1.CurrentRow.Cells(0).Value <> 0 Then
                    If DGV1.Rows.Count <> 0 Then

                        x = DGV1.CurrentRow.Index
                        DGV1.Rows.Remove(DGV1.Rows(x))
                        x += 1
                        UpdateLineSN()

                    End If
                End If

            End If
        Catch ex As Exception

        End Try







    End Sub
    Sub UpdateLineSN()
        Dim DeleteCMD As New SqlCommand("Delete from tblStockMovement where DocNo =  " & Val(txtInvoiceNo.Text) & " and LineSN = " & Val(x) & " and TrxType = 'رصيد افتتاحي'", Con)
        Dim Delete2CMD As New SqlCommand("Delete from tblStockTrx where OutInvoiceNo =  " & Val(txtInvoiceNo.Text) & " and OutInvoiceItemSN = " & Val(x) & " and OutInvoiceType = 'رصيد افتتاحي'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        DeleteCMD.ExecuteNonQuery()
        Delete2CMD.ExecuteNonQuery()
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        '====================================================
        Dim UpdateCMD As New SqlCommand("Update tblStockMovement set LineSN = LineSN - 1 where DocNo = " & Val(txtInvoiceNo.Text) & " and LineSN > " & Val(x) & " and TrxType = 'رصيد افتتاحي'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        UpdateCMD.ExecuteNonQuery()

        'If reader.isclosed Then
        'Else
        '    reader.close()
        'End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        '====================================================
        Dim Update2CMD As New SqlCommand("Update tblStockTrx set OutInvoiceItemSN = OutInvoiceItemSN -1 where OutInvoiceNo = " & Val(txtInvoiceNo.Text) & " and OutInvoiceItemSN > " & Val(x) & " and OutInvoiceType = 'رصيد افتتاحي'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Update2CMD.ExecuteNonQuery()

        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Private Sub DGV1_DoubleClick(sender As Object, e As EventArgs) Handles DGV1.DoubleClick
        If DGV1.SelectedRows.Count = 1 And Val(DGV1.CurrentRow.Cells(0).Value) <> 0 Then
            txtItemSN.Text = Val(DGV1.CurrentRow.Cells(0).Value)
            txtItemNo.Text = DGV1.CurrentRow.Cells(1).Value
            txtQTY.Text = Val(DGV1.CurrentRow.Cells(3).Value)
            cmbxUnit.Text = Trim(DGV1.CurrentRow.Cells(4).Value)
            txtUnitPrice.Text = Val(DGV1.CurrentRow.Cells(5).Value)
            txtItemDescription.Text = Trim(DGV1.CurrentRow.Cells(2).Value)
            txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
            EnteredItemBySN = 0
        End If
    End Sub



    Sub CheckItem()
        If txtItemNo.Text <> "" Then
            Dim SearchCMD As New SqlCommand("Select ItemDescription,UofM,UnitPurchasePrice from tblItems where ItemNo = " & Val(txtItemNo.Text) & "", Con)
            OpenConnection()
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                ItemDescription = Trim(reader.Item(0).ToString)
                ItemUnit = Val(reader.Item(1).ToString)
                UnitPrice = Trim(reader.Item(2).ToString)
                txtItemDescription.Text = ItemDescription
                cmbxUnit.Text = ItemUnit
                If Val(txtQTY.Text) = 0 Then
                    txtQTY.Text = 1
                End If
                If Val(txtUnitPrice.Text) = 0 Then
                    txtUnitPrice.Text = UnitPrice
                End If
                If reader.IsClosed = False Then
                    reader.Close()
                End If
                CloseConnection()
                UnitsLoad()
                txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
                txtQTY.Focus()
            Else
                txtQTY.Clear()
                txtUnitPrice.Clear()
                txtTotal.Clear()
                SearchItemFor = "Sales"
                frmItemSearch.ShowDialog()
            End If
        Else
            txtItemDescription.Clear()
            txtQTY.Clear()
            txtUnitPrice.Clear()
            txtTotal.Clear()
            SearchItemFor = "Sales"
            frmItemSearch.ShowDialog()
        End If

    End Sub

    Private Sub txtQTY_Leave(sender As Object, e As EventArgs) Handles txtQTY.Leave, cmbxUnit.Leave
        txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
    End Sub

    Private Sub txtUnitPrice_Leave(sender As Object, e As EventArgs) Handles txtUnitPrice.Leave
        txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
    End Sub

    Private Sub txtItemNo_KeyDown(sender As Object, e As KeyEventArgs) Handles txtItemNo.KeyDown
        If e.KeyCode = Keys.Enter Then
            If txtItemNo.Text <> "" Then
                Dim SearchCMD As New SqlCommand("Select ItemDescription,UofM,UnitPurchasePrice from tblItems where ItemNo = " & Val(txtItemNo.Text) & "", Con)
                OpenConnection()
                Dim reader As SqlDataReader = SearchCMD.ExecuteReader
                If reader.Read Then
                    ItemDescription = Trim(reader.Item(0).ToString)
                    ItemUnit = Trim(reader.Item(1).ToString)
                    UnitPrice = Val(reader.Item(2).ToString)
                    txtItemDescription.Text = ItemDescription
                    cmbxUnit.Text = ItemUnit
                    If Val(txtQTY.Text) = 0 Then
                        txtQTY.Text = 1
                    End If
                    If Val(txtUnitPrice.Text) = 0 Then
                        txtUnitPrice.Text = UnitPrice
                    End If
                    If reader.IsClosed = False Then
                        reader.Close()
                    End If
                    CloseConnection()
                    UnitsLoad()
                    txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
                    txtQTY.Focus()
                    Dim selectedConversion As Decimal = Convert.ToDecimal(cmbxUnit.SelectedValue)

                Else
                    If reader.IsClosed = False Then
                        reader.Close()
                    End If
                    txtQTY.Clear()
                    txtUnitPrice.Clear()
                    txtTotal.Clear()
                    txtItemDescription.Clear()
                End If
            End If
        End If
    End Sub
    Private Sub txtItemNo_LostFocus(sender As Object, e As EventArgs) Handles txtItemNo.LostFocus
        If txtItemNo.Text <> "" Then
            Dim SearchCMD As New SqlCommand("Select ItemDescription,UofM,UnitPurchasePrice from tblItems where ItemNo = " & Val(txtItemNo.Text) & "", Con)
            OpenConnection()
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                ItemDescription = Trim(reader.Item(0).ToString)
                ItemUnit = Trim(reader.Item(1).ToString)
                UnitPrice = Val(reader.Item(2).ToString)
                txtItemDescription.Text = ItemDescription
                cmbxUnit.Text = ItemUnit
                If Val(txtQTY.Text) = 0 Then
                    txtQTY.Text = 1
                End If
                If Val(txtUnitPrice.Text) = 0 Then
                    txtUnitPrice.Text = UnitPrice
                End If
                If reader.IsClosed = False Then
                    reader.Close()
                End If
                CloseConnection()
                UnitsLoad()
                txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
                txtQTY.Focus()
                Dim selectedConversion As Decimal = Convert.ToDecimal(cmbxUnit.SelectedValue)

            Else
                If reader.IsClosed = False Then
                    reader.Close()
                End If
                txtQTY.Clear()
                txtUnitPrice.Clear()
                txtTotal.Clear()
                txtItemDescription.Clear()
            End If
        End If
    End Sub
    Sub RefreshGrid()
        Dim ds As DataSet = New DataSet
        Dim FillCMD As New SqlCommand("SELECT tblStockMovement.LineSN AS م, tblStockMovement.ItemNo AS [رقم الصنف], tblItems.ItemDescription AS [وصف الصنف], tblStockMovement.TrxQTY AS الكمية,tblItems.UofM AS [الوحدة], tblStockMovement.UnitPrice AS [سعر الوحدة],  FORMAT(tblStockMovement.TrxQTY * tblStockMovement.UnitPrice, 'N2') AS [السعر الإجمالي] FROM tblStockMovement INNER JOIN tblItems ON tblStockMovement.ItemNo = tblItems.ItemNo WHERE        (tblStockMovement.DocNo = " & Val(txtInvoiceNo.Text) & ") AND (tblStockMovement.TrxType = 'رصيد افتتاحي') ORDER BY م", Con)
        Dim da As New SqlDataAdapter(FillCMD)
        ds.Clear()
        da.Fill(ds)
        DGV1.DataSource = ds.Tables(0)
    End Sub
    Sub AddItems()

        If cmbxStore.Text.Trim = "" Then
            MsgBox("يجب إدخال المخزن للاستمرار", MsgBoxStyle.Critical, "نظام السلطان")
            Exit Sub
        End If

        If Val(txtItemSN.Text) <> 0 And Val(txtItemNo.Text) <> 0 And Trim(txtItemDescription.Text) <> "" And Val(txtQTY.Text) <> 0 And Val(txtUnitPrice.Text) <> 0 And Val(txtTotal.Text) <> 0 Then
            Dim itemNo As Long = Trim(txtItemNo.Text)
            Dim store As String = cmbxStore.Text.Trim
            Dim requiredQty As Decimal = Convert.ToDecimal(txtQTY.Text)
            Dim inputUofM As String = cmbxUnit.Text.Trim
            Dim vatPercentage As Decimal = 0
            Dim vatAmount As Decimal = 0
            Dim lineTotal As Decimal = 0
            'Dim isVatIncluded As Boolean = ckbxVATIncluded.Checked ' checkbox to determine inclusion
            Dim unitPrice As Decimal = Val(txtUnitPrice.Text)
            Dim qty As Decimal = Val(txtQTY.Text)

            ' === Get VAT Percentage from tblItems ===
            Dim vatCmd As New SqlCommand("SELECT ISNULL(Tax_Percent, 0) FROM tblItems WHERE ItemNo = @ItemNo", Con)
            vatCmd.Parameters.AddWithValue("@ItemNo", itemNo)
            If Con.State <> ConnectionState.Open Then Con.Open()
            vatPercentage = Convert.ToDecimal(vatCmd.ExecuteScalar())
            If Con.State <> ConnectionState.Closed Then Con.Close()
            lineTotal = unitPrice * qty

            ' === Check if line exists ===
            Dim CheckCMD As New SqlCommand("SELECT * FROM tblStockMovement WHERE DocNo = @DocNo AND TrxType = 'رصيد افتتاحي' AND LineSN = @LineSN", Con)
            CheckCMD.Parameters.AddWithValue("@DocNo", Val(txtInvoiceNo.Text))
            CheckCMD.Parameters.AddWithValue("@LineSN", Val(txtItemSN.Text))
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim reader As SqlDataReader = CheckCMD.ExecuteReader()

            If reader.Read() Then
                reader.Close()

                ' === Update Line
                Dim UpdateCMD As New SqlCommand("
                UPDATE tblStockMovement 
                SET UofMConversion = @Conv, UofM = @UofM, TrxQTY = @Qty, UnitPrice = @Price,
                    ItemNo = @ItemNo, ModifiedOn = GetDate(), ModifiedBy = @User, 
                    Store = @Store, VATAmount = @VAT,LineAmount= @LineAmount
                WHERE DocNo = @DocNo AND TrxType = 'رصيد افتتاحي' AND LineSN = @LineSN", Con)

                With UpdateCMD.Parameters
                    .AddWithValue("@Conv", Convert.ToDecimal(cmbxUnit.SelectedValue))
                    .AddWithValue("@UofM", inputUofM)
                    .AddWithValue("@Qty", requiredQty)
                    .AddWithValue("@Price", Val(txtUnitPrice.Text))
                    .AddWithValue("@ItemNo", itemNo)
                    .AddWithValue("@User", UserName)
                    .AddWithValue("@Store", store)
                    .AddWithValue("@VAT", vatAmount)
                    .AddWithValue("@LineAmount", lineTotal)
                    .AddWithValue("@DocNo", Val(txtInvoiceNo.Text))
                    .AddWithValue("@LineSN", Val(txtItemSN.Text))
                End With

                UpdateCMD.ExecuteNonQuery()

            Else
                reader.Close()

                ' === Insert New Line
                Dim InsertCMD As New SqlCommand("
                INSERT INTO tblStockMovement 
                (DocNo, ItemNo, LineSN, TrxType, TrxDate, TrxQTY, UnitPrice, CreatedBy, CreatedOn, Store, UofM, UofMConversion, VATAmount,LineAmount) 
                VALUES 
                (@DocNo, @ItemNo, @LineSN, 'رصيد افتتاحي', @TrxDate, @Qty, @Price, @User, GetDate(), @Store, @UofM, @Conv, @VAT,@LineAmount)", Con)

                With InsertCMD.Parameters
                    .AddWithValue("@DocNo", Val(txtInvoiceNo.Text))
                    .AddWithValue("@ItemNo", itemNo)
                    .AddWithValue("@LineSN", Val(txtItemSN.Text))
                    .AddWithValue("@TrxDate", Format(dtp1.Value.Date, "yyyy/MM/dd"))
                    .AddWithValue("@Qty", requiredQty)
                    .AddWithValue("@Price", Val(txtUnitPrice.Text))
                    .AddWithValue("@User", UserName)
                    .AddWithValue("@Store", store)
                    .AddWithValue("@UofM", inputUofM)
                    .AddWithValue("@Conv", Convert.ToDecimal(cmbxUnit.SelectedValue))
                    .AddWithValue("@VAT", vatAmount)
                    .AddWithValue("@LineAmount", lineTotal)
                End With

                InsertCMD.ExecuteNonQuery()
            End If

            If Con.State <> ConnectionState.Closed Then Con.Close()

            ' === Finalizing
            RefreshGrid()
            ForItemChange = 1
            SaveTrx()
            ForItemChange = 0
            UpdateTotals()

            txtItemNo.Clear()
            txtItemSN.Clear()
            txtItemDescription.Clear()
            txtQTY.Clear()
            txtUnitPrice.Clear()
            txtTotal.Clear()
            cmbxUnit.Text = ""
            GetLineSerial()
        End If
    End Sub


    Private Sub btnLineSave_Click(sender As Object, e As EventArgs) Handles btnLineSave.Click
        AddItems()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If MsgBox("هل ترغب بالتأكيد في حفظ الفاتورة", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
            EnteredItemBySN = 0
            SaveTrx()
        End If
    End Sub

    Private Sub btnLineDelete_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnLineDelete.Click
        If Val(txtItemSN.Text) <> 0 And Val(txtInvoiceNo.Text) <> 0 Then
            InvNo = Val(txtInvoiceNo.Text)
            If MsgBox("هل ترغب بحذف السطر بالتأكيد ؟", MsgBoxStyle.YesNoCancel, "نظام السلطان") = MsgBoxResult.Yes Then
                Dim DeleteCMD As New SqlCommand("Delete tblStockMovement where TrxType = 'رصيد افتتاحي' and DocNo = " & Val(InvNo) & " and LineSN = " & Val(txtItemSN.Text) & " ", Con)
                Dim Delete3CMD As New SqlCommand("Update tblStockMovement set LineSN = LineSN - 1 where LineSN > " & Val(txtItemSN.Text) & " and TrxType = 'رصيد افتتاحي' and DocNo = " & Val(InvNo) & "", Con)

                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If

                DeleteCMD.ExecuteNonQuery()
                'Delete2CMD.ExecuteNonQuery()
                Delete3CMD.ExecuteNonQuery()
                'Delete4CMD.ExecuteNonQuery()
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
                RefreshGrid()
                ForItemChange = 1
                SaveTrx()
                UpdateTotals()
                ForItemChange = 0
                txtItemNo.Clear()
                txtItemDescription.Clear()
                txtQTY.Clear()
                txtUnitPrice.Clear()
                txtTotal.Clear()
            End If
        End If
    End Sub

    Private Sub ckbxNonVatInvoice_CheckedChanged(sender As Object, e As EventArgs)
        UpdateTotals()
    End Sub
    Private Sub btnItemLookup_Click(sender As Object, e As EventArgs) Handles btnItemLookup.Click
        SearchItemFor = "OpenStock"
        frmItemSearch.ShowDialog()
    End Sub


    Private Sub cmbxUnit_TextChanged(sender As Object, e As EventArgs) Handles cmbxUnit.TextChanged
        If Val(txtItemNo.Text) <> 0 And cmbxUnit.Text.Trim <> "" Then
            Try
                Dim SearchCMD As New SqlCommand("SELECT tblItems.UnitPurchasePrice * UnitsPricesView.Coversion_to_Base AS ConvertedPrice FROM tblItems JOIN UnitsPricesView ON tblItems.ItemNo = UnitsPricesView.ItemNo WHERE tblItems.ItemNo = " & Val(txtItemNo.Text) & " AND UnitsPricesView.Sales_unit = '" & cmbxUnit.Text.Trim & "'", Con)
                OpenConnection()
                Dim SqlDataReader As SqlDataReader = SearchCMD.ExecuteReader
                If SqlDataReader.Read Then
                    UnitPrice = Val(SqlDataReader.Item(0).ToString)
                    If Val(txtQTY.Text) = 0 Then
                        txtQTY.Text = 1
                    End If
                    If Val(UnitPrice) <> 0 Then
                        txtUnitPrice.Text = UnitPrice
                    End If

                    CloseConnection()

                    txtTotal.Text = Val(txtQTY.Text) * Val(txtUnitPrice.Text)
                    txtQTY.Focus()

                    Dim selectedConversion As Decimal = Convert.ToDecimal(cmbxUnit.SelectedValue)

                Else
                    If SqlDataReader.IsClosed = False Then
                        SqlDataReader.Close()
                    End If

                    txtUnitPrice.Clear()
                    txtTotal.Clear()

                End If
            Catch ex As Exception

            End Try

        End If
    End Sub





End Class
