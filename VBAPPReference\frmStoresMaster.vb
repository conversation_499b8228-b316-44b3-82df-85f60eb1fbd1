﻿Imports System.Data.SqlClient
Public Class frmStoresMaster
    Private Sub frmPayMethod_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadPayMethods()
    End Sub


    Private Sub LoadPayMethods()
        Dim query As String = "SELECT SN as [الكود], Store as [الوصف] FROM tblStores order by SN"
        Dim dt As New DataTable()

        Using conn As New SqlConnection(ConStr)
            Using cmd As New SqlCommand(query, conn)
                conn.Open()
                dt.Load(cmd.ExecuteReader())
            End Using
        End Using

        DataGridView1.DataSource = dt
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If txtDescription.Text.Trim() = "" Then
            MessageBox.Show("يرجى إدخال وصف المستودع.")
            Exit Sub
        End If


        Dim description As String = txtDescription.Text.Trim()

        ' حالة: إضافة جديدة
        If txtCode.Text.Trim() = "" Then
            Dim newCode As Integer = GetNextPayMethodCode()

            Dim insertQuery As String = "INSERT INTO tblStores 
            (SN, Store, CreatedBy, CreatedOn)
            VALUES (@Code, @Text, @User, GETDATE())"

            Using conn As New SqlConnection(ConStr)
                Using cmd As New SqlCommand(insertQuery, conn)
                    cmd.Parameters.AddWithValue("@Code", newCode)
                    cmd.Parameters.AddWithValue("@Text", description)
                    cmd.Parameters.AddWithValue("@User", UserName)
                    conn.Open()
                    cmd.ExecuteNonQuery()
                End Using
            End Using

            MessageBox.Show("تمت إضافة المستودع بنجاح.")
        Else
            ' حالة: تعديل فقط (يُسمح فقط إن الكود موجود)
            Dim enteredCode As Integer = Convert.ToInt32(txtCode.Text.Trim())

            ' التحقق من وجود الكود
            Dim exists As Boolean = False
            Using conn As New SqlConnection(ConStr)
                Using cmd As New SqlCommand("SELECT COUNT(*) FROM tblStores WHERE SN = @Code", conn)
                    cmd.Parameters.AddWithValue("@Code", enteredCode)
                    conn.Open()
                    exists = Convert.ToInt32(cmd.ExecuteScalar()) > 0
                End Using
            End Using

            If Not exists Then
                MessageBox.Show("الكود المدخل غير موجود ولا يمكن إنشاء كود جديد يدويًا. يرجى ترك الحقل فارغًا.")
                Exit Sub
            End If

            ' تنفيذ التعديل
            Dim updateQuery As String = "UPDATE tblStores 
            SET Store = @Text, ModifiedBy = @User, ModifiedOn = GETDATE()
            WHERE SN = @Code"

            Using conn As New SqlConnection(ConStr)
                Using cmd As New SqlCommand(updateQuery, conn)
                    cmd.Parameters.AddWithValue("@Code", enteredCode)
                    cmd.Parameters.AddWithValue("@Text", description)
                    cmd.Parameters.AddWithValue("@User", UserName)
                    conn.Open()
                    cmd.ExecuteNonQuery()
                End Using
            End Using

            MessageBox.Show("تم تعديل المستودع بنجاح.")
        End If

        LoadPayMethods()
        ClearFields()
    End Sub



    Private Sub ClearFields()
        txtCode.Clear()
        txtDescription.Clear()
    End Sub

    Private Function GetNextPayMethodCode() As Integer
        Dim nextCode As Integer = 1 ' القيمة الابتدائية

        Using conn As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT ISNULL(MAX(SN), 0) + 1 FROM tblStores", conn)
            conn.Open()
            nextCode = Convert.ToInt32(cmd.ExecuteScalar())
        End Using

        Return nextCode
    End Function


    Private Sub DataGridView1_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If e.RowIndex >= 0 Then
            Dim row As DataGridViewRow = DataGridView1.Rows(e.RowIndex)
            txtCode.Text = row.Cells(0).Value.ToString()
            txtDescription.Text = row.Cells(1).Value.ToString()
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If txtCode.Text.Trim() = "" Then
            MessageBox.Show("يرجى تحديد المستودع الذي تريد حذفه.")
            Exit Sub
        End If

        Dim TrxSN As Integer = Convert.ToInt32(txtCode.Text.Trim())

        '  تحقق من وجود عمليات مرتبطة في جدول الحركات
        Dim trxCount As Integer = 0
        Using conn As New SqlConnection(ConStr)
            Dim checkCmd As New SqlCommand("SELECT COUNT(*) FROM tblPayMethodTrx WHERE Pay_mthd = @Code", conn)
            checkCmd.Parameters.AddWithValue("@Code", TrxSN)
            conn.Open()
            trxCount = Convert.ToInt32(checkCmd.ExecuteScalar())
            conn.Close()
        End Using

        If trxCount > 0 Then
            MessageBox.Show("لا يمكن حذف المستودع لأنه مستخدم في عمليات سابقة.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        ' ✅ تأكيد الحذف إذا لا توجد حركات مرتبطة
        Dim result = MessageBox.Show("هل أنت متأكد أنك تريد حذف المستودع المحدد؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning)
        If result <> DialogResult.Yes Then
            Exit Sub
        End If

        ' تنفيذ الحذف
        Dim deleteQuery As String = "DELETE FROM tblStores WHERE SN = @Code"
        Using conn As New SqlConnection(ConStr)
            Using cmd As New SqlCommand(deleteQuery, conn)
                cmd.Parameters.AddWithValue("@Code", TrxSN)
                conn.Open()
                cmd.ExecuteNonQuery()
            End Using
        End Using

        MessageBox.Show("تم حذف المستودع بنجاح.")
        LoadPayMethods()
        ClearFields()
    End Sub

End Class
