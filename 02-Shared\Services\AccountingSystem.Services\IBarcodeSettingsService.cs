using AccountingSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public interface IBarcodeSettingsService
    {
        Task<List<BarcodeSettings>> GetBarcodeSettingsAsync();
        Task<BarcodeSettings?> GetBarcodeSettingsByIdAsync(int id);
        Task<BarcodeSettings?> GetBarcodeSettingsByShopAsync(string shop);
        Task<BarcodeSettings> CreateBarcodeSettingsAsync(BarcodeSettings settings);
        Task<BarcodeSettings> UpdateBarcodeSettingsAsync(BarcodeSettings settings);
        Task DeleteBarcodeSettingsAsync(int id);
    }
} 