@model AccountingSystem.Services.ViewModels.ChartOfAccountEditViewModel
@{
    ViewData["Title"] = "تعديل الحساب";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الحساب
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post" id="editAccountForm">
                        <input type="hidden" asp-for="AccountCode" />
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="AccountCode" class="form-label"></label>
                                    <input asp-for="AccountCode" class="form-control" readonly />
                                    <span asp-validation-for="AccountCode" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="SegmentCode" class="form-label"></label>
                                    <input asp-for="SegmentCode" class="form-control" readonly />
                                    <span asp-validation-for="SegmentCode" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="AccountName" class="form-label"></label>
                                    <input asp-for="AccountName" class="form-control" />
                                    <span asp-validation-for="AccountName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="ParentAccountCode" class="form-label"></label>
                                    <select asp-for="ParentAccountCode" class="form-select" id="parentAccountSelect">
                                        <option value="">-- اختر الحساب الأب --</option>
                                        @foreach (var account in ViewBag.ParentAccounts)
                                        {
                                            <option value="@account.AccountCode">@account.AccountCode - @account.AccountName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="ParentAccountCode" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label asp-for="AccountType" class="form-label"></label>
                                    <select asp-for="AccountType" class="form-select">
                                        <option value="">-- اختر نوع الحساب --</option>
                                        @foreach (var type in ViewBag.AccountTypes)
                                        {
                                            <option value="@type">@type</option>
                                        }
                                    </select>
                                    <span asp-validation-for="AccountType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label asp-for="AccountNature" class="form-label"></label>
                                    <select asp-for="AccountNature" class="form-select">
                                        <option value="">-- اختر طبيعة الحساب --</option>
                                        @foreach (var nature in ViewBag.AccountNatures)
                                        {
                                            <option value="@nature">@nature</option>
                                        }
                                    </select>
                                    <span asp-validation-for="AccountNature" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label asp-for="OpeningBalance" class="form-label"></label>
                                    <input asp-for="OpeningBalance" class="form-control" type="number" step="0.01" />
                                    <span asp-validation-for="OpeningBalance" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input asp-for="IsPosting" class="form-check-input" />
                                        <label asp-for="IsPosting" class="form-check-label"></label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Notes" class="form-label"></label>
                            <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ التغييرات
                            </button>
                            <a href="@Url.Action("Index")" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i>
                                رجوع
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Set the selected parent account
            var parentCode = '@Model.ParentAccountCode';
            if (parentCode) {
                $('#parentAccountSelect').val(parentCode);
            }
        });
    </script>
} 