﻿Imports System.Data.SqlClient
Public Class frmUserPOSItems
    Dim currentUsername As String = ""
    Private Sub frmUserPOSItems_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        LoadUsers()
    End Sub

    Private Sub LoadUsers()
        cmbxUsers.Items.Clear()
        Using conn As New SqlConnection(ConStr)
            conn.Open()
            Dim cmd As New SqlCommand("SELECT Username FROM tblUsers", conn)
            Dim rdr = cmd.ExecuteReader()
            While rdr.Read()
                cmbxUsers.Items.Add(rdr("Username").ToString())
            End While
        End Using
    End Sub
    Private Sub cmbxUsers_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbxUsers.SelectedIndexChanged
        currentUsername = cmbxUsers.SelectedItem.ToString()
        LoadAvailableItems()
        LoadFavoriteItems()
    End Sub
    Private Sub LoadAvailableItems()
        dgvAvailableItems.Rows.Clear()

        ' تأكد من وجود الأعمدة فقط مرة واحدة
        If dgvAvailableItems.Columns.Count = 0 Then
            dgvAvailableItems.Columns.Add("ItemNo", "رقم الصنف")
            dgvAvailableItems.Columns.Add("ItemDescription", "اسم الصنف")
        End If

        Using conn As New SqlConnection(ConStr)
            conn.Open()

            ' استعلام لجلب أصناف المتجر الافتراضي الخاص بالمستخدم
            'Dim cmd As New SqlCommand("
            'SELECT T.ItemNo, T.ItemDescription
            'FROM tblItems T
            'INNER JOIN tblUsers U ON T.Shop = U.DefaultStore
            'WHERE U.Username = @Username", conn)
            Dim cmd As New SqlCommand("SELECT ItemNo,ItemDescription FROM tblItems order by ItemNo", conn)


            cmd.Parameters.AddWithValue("@Username", currentUsername)
            Dim rdr = cmd.ExecuteReader()

            While rdr.Read()
                dgvAvailableItems.Rows.Add(rdr("ItemNo").ToString(), rdr("ItemDescription").ToString())
            End While
        End Using
    End Sub


    Private Sub LoadFavoriteItems()
        dgvFavoriteItems.Rows.Clear()

        If dgvFavoriteItems.Columns.Count = 0 Then
            dgvFavoriteItems.Columns.Add("ItemNo", "رقم الصنف")
            dgvFavoriteItems.Columns.Add("ItemDescription", "اسم الصنف")
        End If

        Using conn As New SqlConnection(ConStr)
            conn.Open()
            Dim cmd As New SqlCommand("SELECT U.ItemNo, T.ItemDescription FROM tblUserPOSItems U INNER JOIN tblItems T ON U.ItemNo = T.ItemNo WHERE U.Username = @Username", conn)
            cmd.Parameters.AddWithValue("@Username", currentUsername)
            Dim rdr = cmd.ExecuteReader()
            While rdr.Read()
                dgvFavoriteItems.Rows.Add(rdr("ItemNo").ToString(), rdr("ItemDescription").ToString())
            End While
        End Using
    End Sub

    Private Sub btnAddToFavorites_Click(sender As Object, e As EventArgs) Handles btnAddToFavorites.Click
        Using conn As New SqlConnection(ConStr)
            conn.Open()
            For Each row As DataGridViewRow In dgvAvailableItems.SelectedRows
                Dim itemNo As Long = Convert.ToInt64(row.Cells("ItemNo").Value)

                ' تحقق لتجنب التكرار
                Dim checkCmd As New SqlCommand("SELECT COUNT(*) FROM tblUserPOSItems WHERE Username = @Username AND ItemNo = @ItemNo", conn)
                checkCmd.Parameters.AddWithValue("@Username", currentUsername)
                checkCmd.Parameters.AddWithValue("@ItemNo", itemNo)

                If CInt(checkCmd.ExecuteScalar()) = 0 Then
                    Dim insertCmd As New SqlCommand("INSERT INTO tblUserPOSItems (Username, ItemNo, CreatedBy, CreatedOn) VALUES (@Username, @ItemNo, @User, GETDATE())", conn)
                    insertCmd.Parameters.AddWithValue("@Username", currentUsername)
                    insertCmd.Parameters.AddWithValue("@ItemNo", itemNo)
                    insertCmd.Parameters.AddWithValue("@User", currentUsername)
                    insertCmd.ExecuteNonQuery()
                End If
            Next
        End Using

        MessageBox.Show("تمت إضافة العناصر المحددة إلى المفضلة", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
        LoadFavoriteItems() ' إعادة تحميل المفضلة
    End Sub

    Private Sub btnRemoveFromFavorites_Click(sender As Object, e As EventArgs) Handles btnRemoveFromFavorites.Click
        If dgvFavoriteItems.SelectedRows.Count = 0 Then
            MessageBox.Show("الرجاء تحديد عنصر واحد أو أكثر للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        If MessageBox.Show("هل أنت متأكد من حذف العناصر المحددة من المفضلة؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Exit Sub
        End If

        Using conn As New SqlConnection(ConStr)
            conn.Open()
            For Each row As DataGridViewRow In dgvFavoriteItems.SelectedRows
                Dim itemNo As Long = Convert.ToInt64(row.Cells("ItemNo").Value)

                Dim deleteCmd As New SqlCommand("DELETE FROM tblUserPOSItems WHERE Username = @Username AND ItemNo = @ItemNo", conn)
                deleteCmd.Parameters.AddWithValue("@Username", currentUsername)
                deleteCmd.Parameters.AddWithValue("@ItemNo", itemNo)
                deleteCmd.ExecuteNonQuery()
            Next
        End Using

        MessageBox.Show("تم حذف العناصر المحددة من المفضلة", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
        LoadFavoriteItems()
    End Sub
End Class