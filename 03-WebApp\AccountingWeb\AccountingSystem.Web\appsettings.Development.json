{"ConnectionStrings": {"DefaultConnection": "Server=174.138.185.119;Database=SULTDB;User Id=AppUser;Password=StrongP@ss123;TrustServerCertificate=true;", "AccountingDatabase": "Server=174.138.185.119;Database=SULTDB;User Id=AppUser;Password=StrongP@ss123;TrustServerCertificate=true;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "ApplicationSettings": {"EnableDetailedErrors": true, "EnableSensitiveDataLogging": true, "EnableDeveloperExceptionPage": true}, "DemoMode": false}