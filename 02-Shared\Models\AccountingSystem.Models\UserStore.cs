using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    [Table("UserStores")]
    public class UserStore
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Column("Username")]
        public string UserName { get; set; }

        [Required]
        [Column("StoreId")]
        public int StoreId { get; set; }
    }
} 