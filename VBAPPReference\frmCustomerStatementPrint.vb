﻿Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports CrystalDecisions.Windows.Forms
Imports System.Data.SqlClient

Public Class frmCustomerStatementPrint
    Private Sub frmCustomerStatementP_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.Size = New Size(frmRPTWidth, frmRPTHeight)

        Dim RPT As New rptCustomerStatement

        UpdateCRDataSource(RPT)
        RPT.Refresh()
        RPT.ParameterFields(0).CurrentValues.Clear()
        RPT.ParameterFields(0).CurrentValues.AddRange(frmCustomerStatement.dtpFrom.Value.Date, frmCustomerStatement.dtpTo.Value.Date, RangeBoundType.BoundInclusive, RangeBoundType.BoundInclusive)
        RPT.SetParameterValue(1, frmCustomerStatement.cmbxPartnerNo.SelectedValue)

        CrystalReportViewer2.ReportSource = RPT
    End Sub

End Class
