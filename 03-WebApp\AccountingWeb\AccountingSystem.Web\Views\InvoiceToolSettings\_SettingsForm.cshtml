@model AccountingSystem.Web.ViewModels.InvoiceToolSettingsViewModel
@{
    var printers = ViewBag.Printers as List<string> ?? new List<string>();
}

<form asp-controller="InvoiceToolSettings" asp-action="Update" method="post">
    @Html.AntiForgeryToken()
    <div class="card mt-4">
        <div class="card-header"><h3 class="card-title">إعدادات الفاتورة: @Model.InvoiceType</h3></div>
        <div class="card-body">
            <input type="hidden" asp-for="InvoiceType" />

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label asp-for="DefaultPrinter" class="form-label">الطابعة الافتراضية</label>
                    <select asp-for="DefaultPrinter" class="form-select">
                        <option value="">-- اختر طابعة --</option>
                        @foreach (var printer in printers)
                        {
                            <option value="@printer" selected="@(printer == Model.DefaultPrinter)">@printer</option>
                        }
                    </select>
                </div>

                <div class="col-md-6 mb-3">
                    <label asp-for="PrintOption" class="form-label">خيارات الطباعة</label>
                    <select asp-for="PrintOption" class="form-select">
                        <option value="0">طباعة تلقائية</option>
                        <option value="1">سؤال قبل الطباعة</option>
                    </select>
                </div>
            </div>

            <hr />

            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" asp-for="PriceIncludeVATDef">
                        <label class="form-check-label" asp-for="PriceIncludeVATDef">السعر يشمل الضريبة افتراضياً</label>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" asp-for="NonVATInvoiceDef">
                        <label class="form-check-label" asp-for="NonVATInvoiceDef">فاتورة غير خاضعة للضريبة افتراضياً</label>
                    </div>
                </div>
                 <div class="col-md-4 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" asp-for="MandatoryCustomerVATReg">
                        <label class="form-check-label" asp-for="MandatoryCustomerVATReg">الرقم الضريبي للعميل إجباري</label>
                    </div>
                </div>
            </div>

            <hr />

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label asp-for="PaymentType" class="form-label">نوع الدفع الافتراضي</label>
                    <select asp-for="PaymentType" class="form-select">
                        <option value="نقدي">نقدي</option>
                        <option value="شبكة">شبكة</option>
                        <option value="آجل">آجل</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                     <div class="form-check form-switch mt-4">
                        <input class="form-check-input" type="checkbox" asp-for="ReferenceMandatory">
                        <label class="form-check-label" asp-for="ReferenceMandatory">حقل المرجع إجباري</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer text-end">
            <button type="submit" class="btn btn-success">
                <i class="fas fa-save"></i> حفظ الإعدادات
            </button>
        </div>
    </div>
</form> 