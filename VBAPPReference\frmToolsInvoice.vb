﻿Imports System.Data.SqlClient
Public Class frmToolsInvoice
    Dim SNCOPY As String = ""
    Dim EnteredBefore As Int64 = 0
    Dim Cr As Int64
    Dim LableCode As Int64
    Dim txtPrice As Int64
    Dim txtQTY As Int64

    Dim MaterialAccount, DiscountAccount, AccountNo As Int64
    Private Sub frmTrxInvIn_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        '        frmMain.Enabled = True
        Cashier = ""
    End Sub
    Private Sub frmTrxInvIn_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        PrinterLoad()
        AccountsLoad()
        StoresLoad()
    End Sub
    Sub PrinterLoad()
        Try
            Dim InstalledPrinters As String
            For Each InstalledPrinters In System.Drawing.Printing.PrinterSettings.InstalledPrinters
                cmbxInvoicePrinter.Items.Add(InstalledPrinters)
            Next InstalledPrinters
        Catch ex As Exception

        End Try
    End Sub
    Sub StoresLoad()
        Dim CMD As New SQLCommand("Select Store from tblStores Order by Store", Con)
        
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxStores.Items.Clear()
        Do While reader.Read
            cmbxStores.Items.Add(reader.Item("Store").ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Sub AccountsLoad()
        Dim CMD As New SQLCommand("Select RootID from tblRoots order by RootID", Con)
        
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxMaterialAccount.Items.Clear()
        cmbxDiscountAccount.Items.Clear()
        cmbxCashier.Items.Clear()
        Do While reader.Read
            cmbxMaterialAccount.Items.Add(reader.Item(0).ToString)
            cmbxDiscountAccount.Items.Add(reader.Item(0).ToString)
            cmbxCashier.Items.Add(reader.Item(0).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Sub VendorLoad()
        Dim CMD As New SqlCommand("Select VendorNo from tblVendors Order by VendorNo", Con)

        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxAccountNo.Items.Clear()
        Do While reader.Read
            cmbxAccountNo.Items.Add(reader.Item(0).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Sub CustomerLoad()
        Dim CMD As New SQLCommand("Select CustomerNo from tblCustomers Order by CustomerNo", Con)
        
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxAccountNo.Items.Clear()
        Do While reader.Read
            cmbxAccountNo.Items.Add(reader.Item(0).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Sub OwnerLoad()
        Dim CMD As New SQLCommand("Select RootID from tblRoots order by RootID", Con)
        
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader
        cmbxAccountNo.Items.Clear()
        Do While reader.Read
            cmbxAccountNo.Items.Add(reader.Item(0).ToString)
        Loop
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    

    Private Sub cmbxAccountNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbxAccountNo.TextChanged
        If Val(cmbxAccountNo.Text) <> 0 Then
            Dim SearchCMD As New SQLCommand("select RootName from tblroots where RootID = " & Val(cmbxAccountNo.Text) & "", Con)
            
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                lblAccountName.Text = reader.Item(0).ToString
            Else
                lblAccountName.Text = ""
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Else
            lblAccountName.Text = ""
        End If
    End Sub

    Sub LoadSavedData()
        If cmbxInvoiceType.Text.Trim <> "" Then
            Dim CMD As New SQLCommand("Select * from tblToolsInvoice where InvoiceType = '" & cmbxInvoiceType.Text.Trim & "'", Con)
            
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CMD.ExecuteReader
            If reader.Read Then
                ckbxVATIncludedChangeable.Checked = Trim(reader.Item(1).ToString)
                ckbxNonVatInvoiceChangeable.Checked = Trim(reader.Item(2).ToString)
                ckbxReferenceMandatory.Checked = Trim(reader.Item(3).ToString)
                MaterialAccount = Val(reader.Item(4).ToString)
                DiscountAccount = Val(reader.Item(5).ToString)
                cmbxInvoicePrinter.Text = Trim(reader.Item(6).ToString)
                cmbxPrintOptions.SelectedIndex = Val(reader.Item(7).ToString)
                ckbxMandatoryVendorVATReg.Checked = Trim(reader.Item(8).ToString)
                AccountNo = Val(reader.Item(9).ToString)
                cmbxPaymentType.Text = Trim(reader.Item(10).ToString)
                Cashier = Val(reader.Item(11).ToString)
                cmbxStores.Text = Trim(reader.Item(12).ToString)
                ckbxPriceIncludeVATDef.Checked = Trim(reader.Item(13).ToString)
                ckbxNonVATInvoiceDef.Checked = Trim(reader.Item(14).ToString)
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
            cmbxMaterialAccount.Text = MaterialAccount
            cmbxDiscountAccount.Text = DiscountAccount
            cmbxAccountNo.Text = AccountNo
            cmbxCashier.Text = Cashier
        Else
            lblPartnerType.Text = "رقم الحساب"
            ClearData()
        End If
    End Sub
    Sub ClearData()

    End Sub
    Private Sub cmbxInvoiceType_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbxInvoiceType.SelectedIndexChanged
        If cmbxInvoiceType.Text.Trim <> "" Then
            If cmbxInvoiceType.Text.Trim = "مشتريات" Or cmbxInvoiceType.Text.Trim = "مرتجع مشتريات" Then

                lblPartnerType.Text = "رقم المورد"
                VendorLoad()
            ElseIf cmbxInvoiceType.Text.Trim = "مبيعات" Or cmbxInvoiceType.Text.Trim = "مرتجع مبيعات" Then

                lblPartnerType.Text = "رقم العميل"
                CustomerLoad()
            ElseIf cmbxInvoiceType.Text.Trim = "رصيد افتتاحي" Then

                lblPartnerType.Text = "رقم الشريك"
                OwnerLoad()
            End If
            LoadSavedData()
        Else
            lblPartnerType.Text = "رقم الحساب"
            ClearData()
        End If
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        ClearData()
    End Sub

    Private Sub cmbxMaterialAccount_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbxMaterialAccount.SelectedIndexChanged

    End Sub

    Private Sub cmbxMaterialAccount_TextChanged(sender As Object, e As EventArgs) Handles cmbxMaterialAccount.TextChanged
        If Val(cmbxMaterialAccount.Text) <> 0 Then
            Dim SearchCMD As New SQLCommand("select RootName from tblroots where RootID = " & Val(cmbxMaterialAccount.Text) & " ", Con)
            
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                lblMatAccountDescription.Text = reader.Item(0).ToString
            Else
                lblMatAccountDescription.Text = ""
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Else
            lblMatAccountDescription.Text = ""
        End If
    End Sub

    Private Sub cmbxDiscountAccount_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbxDiscountAccount.SelectedIndexChanged

    End Sub

    Private Sub cmbxDiscountAccount_TextChanged(sender As Object, e As EventArgs) Handles cmbxDiscountAccount.TextChanged
        If Val(cmbxDiscountAccount.Text) <> 0 Then
            Dim SearchCMD As New SQLCommand("select RootName from tblroots where RootID = " & Val(cmbxDiscountAccount.Text) & " ", Con)
            
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                lblDiscAccountDescription.Text = reader.Item(0).ToString
            Else
                lblDiscAccountDescription.Text = ""
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Else
            lblDiscAccountDescription.Text = ""
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If Trim(cmbxInvoiceType.Text) <> "" Then
            Dim CheckCMD As New SQLCommand("Select * from tblToolsInvoice where InvoiceType='" & Trim(cmbxInvoiceType.Text) & "'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = CheckCMD.ExecuteReader
            If reader.Read Then
                reader.Close()
                Dim UpdateCMD As New SQLCommand("Update tblToolsInvoice set VATIncludedChangeable='" & ckbxVATIncludedChangeable.Checked & "', NonVatInvoiceChangeable='" & ckbxNonVatInvoiceChangeable.Checked & "', ReferenceMandatory='" & ckbxReferenceMandatory.Checked & "', MaterialAccountNo=" & Val(cmbxMaterialAccount.Text) & ", DiscountAccountNo=" & Val(cmbxDiscountAccount.Text) & ", InvoicePrinter='" & cmbxInvoicePrinter.Text.Trim & "', PrintOption = " & cmbxPrintOptions.SelectedIndex & ", MandatoryVendorVATReg='" & ckbxMandatoryVendorVATReg.Checked & "', DefAccountNo=" & Val(cmbxAccountNo.Text) & ", DefPaymentType='" & cmbxPaymentType.Text.Trim & "', DefCashier=" & Val(cmbxCashier.Text) & ", DefStores='" & Trim(cmbxStores.Text) & "', DefPriceIncludeVAT='" & ckbxPriceIncludeVATDef.Checked & "', DefNonVATInvoice='" & ckbxNonVATInvoiceDef.Checked & "',ModifiedBy='" & UserName & "',ModifiedOn='" & Format(DateAndTime.Now, "yyyy/MM/dd hh:mm:ss tt") & "' where InvoiceType='" & Trim(cmbxInvoiceType.Text) & "'", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                UpdateCMD.ExecuteNonQuery()
                MessageBox.Show("تم تعديل البيانات بنجاح", "السلطان")
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            Else
                Dim InsertCMD As New SQLCommand("Insert Into tblToolsInvoice (InvoiceType,VATIncludedChangeable,NonVatInvoiceChangeable,ReferenceMandatory,MaterialAccountNo,DiscountAccountNo,InvoicePrinter,PrintOption,MandatoryVendorVATReg,DefAccountNo,DefPaymentType,DefCashier,DefStores,DefPriceIncludeVAT,DefNonVATInvoice,CreatedBy,CreatedOn) values ('" & Trim(cmbxInvoiceType.Text) & "','" & ckbxVATIncludedChangeable.Checked & "', '" & ckbxNonVatInvoiceChangeable.Checked & "', '" & ckbxReferenceMandatory.Checked & "', " & Val(cmbxMaterialAccount.Text) & ", " & Val(cmbxDiscountAccount.Text) & ", '" & cmbxInvoicePrinter.Text.Trim & "', " & Val(cmbxPrintOptions.SelectedIndex) & ", '" & ckbxMandatoryVendorVATReg.Checked & "', " & Val(cmbxAccountNo.Text) & ", '" & cmbxPaymentType.Text.Trim & "', " & Val(cmbxCashier.Text) & ", '" & Trim(cmbxStores.Text) & "', '" & ckbxPriceIncludeVATDef.Checked & "', '" & ckbxNonVATInvoiceDef.Checked & "','" & UserName & "','" & Format(DateAndTime.Now, "yyyy/MM/dd hh:mm:ss tt") & "')", Con)
                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                InsertCMD.ExecuteNonQuery()
                MessageBox.Show("تم حفظ البيانات بنجاح", "السلطان")
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If
        End If
    End Sub

    Private Sub cmbxCashier_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbxCashier.SelectedIndexChanged

    End Sub

    Private Sub cmbxCashier_TextChanged(sender As Object, e As EventArgs) Handles cmbxCashier.TextChanged
        If Val(cmbxCashier.Text) <> 0 Then
            Dim SearchCMD As New SQLCommand("select RootName from tblroots where RootID = " & Val(cmbxCashier.Text) & " ", Con)
            
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                lblCashierDescription.Text = reader.Item(0).ToString
            Else
                lblCashierDescription.Text = ""
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Else
            lblCashierDescription.Text = ""
        End If
    End Sub


    
End Class