﻿Imports System.Data.SqlClient
Public Class frmPOSSettings
    ' Properties for current POS/shop/user
    Private ShopID As Integer = 0 ' Set based on selected shop

    Private Sub frmPOSSettings_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadCustomers()
        LoadTemplates()
        LoadPaymentMethods()
        LoadCategories()
        LoadShops()
    End Sub

    Private Sub LoadShops()
        Dim dt As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT SN, Shop_Text FROM tblShops ORDER BY Shop_Text", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dt)
        End Using

        cmbShop.DataSource = dt
        cmbShop.DisplayMember = "Shop_Text"
        cmbShop.ValueMember = "SN"
        cmbShop.SelectedIndex = -1
    End Sub

    Private Sub cmbShop_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbShop.SelectedIndexChanged
        If cmbShop.SelectedValue IsNot Nothing Then
            If TypeOf cmbShop.SelectedValue Is Integer Then
                ShopID = CInt(cmbShop.SelectedValue)
            End If
            LoadSettings()
        End If
    End Sub

    Private Sub LoadCustomers()
        Dim dtable As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("Select RootID,RootName from tblRoots where ParentID = (Select AccountNo from tblGLConfig where EntryReferenceModule = 'عملاء') order by RootName", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dtable)
        End Using
        cmbDefaultCustomer.DataSource = dtable
        cmbDefaultCustomer.DisplayMember = "RootName"
        cmbDefaultCustomer.ValueMember = "RootID"
    End Sub

    Private Sub LoadTemplates()
        cmbTemplate.Items.Clear()
        cmbTemplate.Items.Add("فاتورة حرارية")
        cmbTemplate.Items.Add("A4")
        cmbTemplate.SelectedIndex = 0
    End Sub

    Private Sub LoadPaymentMethods()
        Dim dt As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT Pay_mthd, Pay_mthd_Text FROM tblPayMethod", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dt)
        End Using

        cmbDefaultPayment.DataSource = dt.Copy()
        cmbDefaultPayment.DisplayMember = "Pay_mthd_Text"
        cmbDefaultPayment.ValueMember = "Pay_mthd"

        chkPaymentMethods.Items.Clear()
        For Each row As DataRow In dt.Rows
            chkPaymentMethods.Items.Add(row("Pay_mthd_Text"), False)
        Next
    End Sub

    Private Sub LoadCategories()
        Dim dt As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT RootID, RootName FROM tblItemsCategory", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dt)
        End Using

        chkAllowedCategories.Items.Clear()
        For Each row As DataRow In dt.Rows
            chkAllowedCategories.Items.Add(row("RootName"), False)
        Next
    End Sub

    Private Sub LoadSettings()
        ' Clear defaults first
        cmbDefaultCustomer.SelectedIndex = -1
        cmbTemplate.SelectedIndex = -1
        cmbDefaultPayment.SelectedIndex = -1

        For i = 0 To chkPaymentMethods.Items.Count - 1
            chkPaymentMethods.SetItemChecked(i, False)
        Next

        For i = 0 To chkAllowedCategories.Items.Count - 1
            chkAllowedCategories.SetItemChecked(i, False)
        Next

        chkEnableNumericKeyboard.Checked = False
        chkShowImages.Checked = False
        chkShowPrintDialog.Checked = False
        chkAllowPartialPayment.Checked = False

        ' Load from DB
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT * FROM tblPOSSettings WHERE ShopID = @ShopID", con)
            cmd.Parameters.AddWithValue("@ShopID", ShopID)
            con.Open()
            Dim rdr = cmd.ExecuteReader()
            If rdr.Read() Then
                cmbDefaultCustomer.SelectedValue = rdr("DefaultCustomerID")
                cmbTemplate.Text = rdr("DefaultTemplate")
                cmbDefaultPayment.SelectedValue = rdr("DefaultPaymentMethodID")

                Dim enabledMethods = rdr("EnabledPaymentMethods").ToString().Split(",")
                For i = 0 To chkPaymentMethods.Items.Count - 1
                    chkPaymentMethods.SetItemChecked(i, enabledMethods.Contains(i.ToString()))
                Next

                Dim allowedCats = rdr("AllowedCategories").ToString().Split(",")
                For i = 0 To chkAllowedCategories.Items.Count - 1
                    chkAllowedCategories.SetItemChecked(i, allowedCats.Contains(i.ToString()))
                Next

                chkEnableNumericKeyboard.Checked = rdr("EnableNumericKeyboard")
                chkShowImages.Checked = rdr("ShowProductImages")
                chkShowPrintDialog.Checked = rdr("ShowPrintDialog")
                chkAllowPartialPayment.Checked = rdr("AllowPartialPayment")
            End If
        End Using
    End Sub


    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Dim enabledMethods As New List(Of String)()
        For i = 0 To chkPaymentMethods.Items.Count - 1
            If chkPaymentMethods.GetItemChecked(i) Then
                enabledMethods.Add(i.ToString())
            End If
        Next

        Dim allowedCats As New List(Of String)()
        For i = 0 To chkAllowedCategories.Items.Count - 1
            If chkAllowedCategories.GetItemChecked(i) Then
                allowedCats.Add(i.ToString())
            End If
        Next

        Using con As New SqlConnection(ConStr)
            Dim query As String = ""
            query &= "IF EXISTS (SELECT 1 FROM tblPOSSettings WHERE ShopID = @ShopID) "
            query &= "UPDATE tblPOSSettings SET " &
                     "DefaultCustomerID = @Customer, " &
                     "DefaultTemplate = @Template, " &
                     "DefaultPaymentMethodID = @Payment, " &
                     "EnabledPaymentMethods = @Methods, " &
                     "AllowedCategories = @Categories, " &
                     "EnableNumericKeyboard = @Keyboard, " &
                     "ShowProductImages = @Images, " &
                     "ShowPrintDialog = @PrintDialog, " &
                     "AllowPartialPayment = @Partial, " &
                     "ModifiedOn = @ModifiedOn, " &
                     "ModifiedBy = @ModifiedBy " &
                     "WHERE ShopID = @ShopID "
            query &= "ELSE "
            query &= "INSERT INTO tblPOSSettings (ShopID, DefaultCustomerID, DefaultTemplate, DefaultPaymentMethodID, " &
                     "EnabledPaymentMethods, AllowedCategories, EnableNumericKeyboard, ShowProductImages, ShowPrintDialog, AllowPartialPayment, CreatedOn, CreatedBy) " &
                     "VALUES (@ShopID, @Customer, @Template, @Payment, @Methods, @Categories, @Keyboard, @Images, @PrintDialog, @Partial, GETDATE(), @Username)"

            Dim cmd As New SqlCommand(query, con)

            cmd.Parameters.AddWithValue("@ShopID", ShopID)
            cmd.Parameters.AddWithValue("@Customer", cmbDefaultCustomer.SelectedValue)
            cmd.Parameters.AddWithValue("@Template", cmbTemplate.Text)
            cmd.Parameters.AddWithValue("@Payment", cmbDefaultPayment.SelectedValue)
            cmd.Parameters.AddWithValue("@Methods", String.Join(",", enabledMethods))
            cmd.Parameters.AddWithValue("@Categories", String.Join(",", allowedCats))
            cmd.Parameters.AddWithValue("@Keyboard", chkEnableNumericKeyboard.Checked)
            cmd.Parameters.AddWithValue("@Images", chkShowImages.Checked)
            cmd.Parameters.AddWithValue("@PrintDialog", chkShowPrintDialog.Checked)
            cmd.Parameters.AddWithValue("@Partial", chkAllowPartialPayment.Checked)
            cmd.Parameters.AddWithValue("@Username", UserName)
            cmd.Parameters.AddWithValue("@ModifiedOn", DateTime.Now)
            cmd.Parameters.AddWithValue("@ModifiedBy", UserName)

            con.Open()
            cmd.ExecuteNonQuery()
        End Using

        MessageBox.Show("تم حفظ الإعدادات بنجاح", "POS Settings", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
End Class
