﻿Imports SAPBusinessObjects.WPF.ViewerShared.EMFRender
Imports System.Data.SqlClient
Public Class frmPOSSessions

    Private Sub frmPOSSessions_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadShops()
        LoadDevices()
        LoadShifts()
        LoadStatusFilter()
        LoadSessions()
    End Sub

    Private Sub LoadShops()
        Dim dt As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT SN, Shop_Text FROM tblShops ORDER BY Shop_Text", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dt)
        End Using
        cmbShop.DataSource = dt
        cmbShop.DisplayMember = "Shop_Text"
        cmbShop.ValueMember = "SN"
        cmbShop.SelectedIndex = -1
    End Sub

    Private Sub LoadDevices()
        Dim dt As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT DeviceID, DeviceName FROM tblPOSDevices ORDER BY DeviceName", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dt)
        End Using
        cmbDevice.DataSource = dt
        cmbDevice.DisplayMember = "DeviceName"
        cmbDevice.ValueMember = "DeviceID"
        cmbDevice.SelectedIndex = -1
    End Sub

    Private Sub LoadShifts()
        Dim dt As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT ShiftID, ShiftName FROM tblPOSShifts ORDER BY ShiftName", con)
            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dt)
        End Using
        cmbShift.DataSource = dt
        cmbShift.DisplayMember = "ShiftName"
        cmbShift.ValueMember = "ShiftID"
        cmbShift.SelectedIndex = -1
    End Sub

    Private Sub LoadStatusFilter()
        cmbFilterStatus.Items.Clear()
        cmbFilterStatus.Items.AddRange(New String() {"All", "Open", "Closed"})
        cmbFilterStatus.SelectedIndex = 0
    End Sub

    Private Sub LoadSessions()
        Dim dt As New DataTable()
        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT s.SessionID, s.SessionSN, sh.Shop_Text AS ShopName, d.DeviceName, sf.ShiftName, s.OpenedBy, s.OpenTime, s.ClosedBy, s.CloseTime, s.Status, s.ClosingCash, s.Note FROM tblPOSSessions s Left Join tblShops sh ON s.ShopID = sh.SN LEFT JOIN tblPOSDevices d ON s.DeviceID = d.DeviceID Left Join tblPOSShifts sf ON s.ShiftID = sf.ShiftID WHERE (@ShopID Is NULL Or s.ShopID = @ShopID) And (@DeviceID Is NULL Or s.DeviceID = @DeviceID) AND (@ShiftID IS NULL OR s.ShiftID = @ShiftID) And (@Status = 'All' OR s.Status = @Status) ORDER BY s.SessionID DESC", con)
            cmd.Parameters.AddWithValue("@ShopID", If(cmbShop.SelectedIndex = -1, DBNull.Value, cmbShop.SelectedValue))
            cmd.Parameters.AddWithValue("@DeviceID", If(cmbDevice.SelectedIndex = -1, DBNull.Value, cmbDevice.SelectedValue))
            cmd.Parameters.AddWithValue("@ShiftID", If(cmbShift.SelectedIndex = -1, DBNull.Value, cmbShift.SelectedValue))
            cmd.Parameters.AddWithValue("@Status", cmbFilterStatus.Text)

            Dim da As New SqlDataAdapter(cmd)
            da.Fill(dt)
        End Using
        dgvSessions.DataSource = dt
    End Sub

    Private Sub btnNewSession_Click(sender As Object, e As EventArgs) Handles btnNewSession.Click
        If cmbShop.SelectedIndex = -1 OrElse cmbDevice.SelectedIndex = -1 OrElse cmbShift.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار الفرع والجهاز والوردية", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        Dim sessionSN As String = GenerateSessionSN(cmbShift.SelectedValue, cmbDevice.SelectedValue)
        Dim openedBy As String = UserName

        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("INSERT INTO tblPOSSessions (SessionSN, ShopID, DeviceID, ShiftID, OpenedBy, OpenTime, Status) VALUES (@SessionSN, @ShopID, @DeviceID, @ShiftID, @OpenedBy, GETDATE(), 'Open')", con)

            cmd.Parameters.AddWithValue("@SessionSN", sessionSN)
            cmd.Parameters.AddWithValue("@ShopID", cmbShop.SelectedValue)
            cmd.Parameters.AddWithValue("@DeviceID", cmbDevice.SelectedValue)
            cmd.Parameters.AddWithValue("@ShiftID", cmbShift.SelectedValue)
            cmd.Parameters.AddWithValue("@OpenedBy", openedBy)

            con.Open()
            cmd.ExecuteNonQuery()
        End Using

        LoadSessions()
        MessageBox.Show("تم فتح جلسة جديدة بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Function GenerateSessionSN(shiftId As Integer, deviceId As Integer) As String
        Dim datePart As String = DateTime.Now.ToString("yyyyMMdd")
        Dim seqNum As Integer = 1

        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("SELECT COUNT(*) FROM tblPOSSessions WHERE CAST(OpenTime AS DATE) = CAST(GETDATE() AS DATE) AND ShiftID = @ShiftID AND DeviceID = @DeviceID", con)

            cmd.Parameters.AddWithValue("@ShiftID", shiftId)
            cmd.Parameters.AddWithValue("@DeviceID", deviceId)

            con.Open()
            seqNum = Convert.ToInt32(cmd.ExecuteScalar()) + 1
        End Using

        Return $"{datePart}-{shiftId:D2}-{deviceId:D2}-{seqNum:D3}"
    End Function

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        LoadSessions()
    End Sub

    Private Sub btnCloseSession_Click(sender As Object, e As EventArgs) Handles btnCloseSession.Click
        If dgvSessions.SelectedRows.Count = 0 Then
            MessageBox.Show("يرجى اختيار جلسة لإغلاقها", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        Dim selectedRow As DataGridViewRow = dgvSessions.SelectedRows(0)
        Dim sessionId As Integer = CInt(selectedRow.Cells("SessionID").Value)
        Dim status As String = selectedRow.Cells("Status").Value.ToString()

        If status = "Closed" Then
            MessageBox.Show("هذه الجلسة مغلقة بالفعل", "معلومة", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If

        'Dim closingCash As Decimal = 0
        'Decimal.TryParse(InputBox("أدخل المبلغ النهائي في الدرج:", "إغلاق الجلسة"), closingCash)
        'If Not Decimal.TryParse(txtClosingCash.Text.Trim(), closingCash) Then
        '    MessageBox.Show("يرجى إدخال مبلغ صحيح", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        '    txtClosingCash.Focus()
        '    Return
        'End If
        Dim closingCash As Decimal = Convert.ToDecimal(txtClosingCash.Text.Trim)
        If closingCash = 0 Then
            If MessageBox.Show("هل أنت متأكد من أن المبلغ النهائي هو صفر؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                txtClosingCash.Focus()
                Exit Sub
            End If
        End If
        Dim note As String = txtNote.Text.Trim()
        Dim closedBy As String = UserName

        Using con As New SqlConnection(ConStr)
            Dim cmd As New SqlCommand("UPDATE tblPOSSessions SET CloseTime = GETDATE(), ClosedBy = @ClosedBy, ClosingCash = @ClosingCash, Status = 'Closed' WHERE SessionID = @SessionID", con)
            cmd.Parameters.AddWithValue("@ClosedBy", closedBy)
            cmd.Parameters.AddWithValue("@ClosingCash", closingCash)
            cmd.Parameters.AddWithValue("@SessionID", sessionId)

            con.Open()
            cmd.ExecuteNonQuery()
        End Using

        LoadSessions()
        MessageBox.Show("تم إغلاق الجلسة بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

End Class
