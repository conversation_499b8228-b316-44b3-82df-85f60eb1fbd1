using System;

class HashTest
{
    static void Main()
    {
        // Test common passwords
        string[] passwords = { "654321", "123456", "admin", "1234", "password" };
        
        Console.WriteLine("Password Hash Code Tests:");
        Console.WriteLine("========================");
        
        foreach (string password in passwords)
        {
            int hashCode = password.Trim().GetHashCode();
            Console.WriteLine($"Password: '{password}' -> Hash: {hashCode}");
        }
        
        Console.WriteLine("\nDatabase stored values:");
        Console.WriteLine("Admin: 1266883811");
        Console.WriteLine("abubaker: 1724376655");
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
