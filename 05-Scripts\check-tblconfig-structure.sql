-- Check the actual structure of tblConfig table
-- This will help us understand the correct column names

-- Check if tblConfig table exists and its structure
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'tblConfig'
ORDER BY ORDINAL_POSITION;

-- Check if there are any records in tblConfig
SELECT COUNT(*) as RecordCount FROM tblConfig;

-- Check sample data if any exists
SELECT TOP 5 * FROM tblConfig;

-- Check if there are any configuration tables with different names
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE '%config%' OR TABLE_NAME LIKE '%Config%'
ORDER BY TABLE_NAME;
