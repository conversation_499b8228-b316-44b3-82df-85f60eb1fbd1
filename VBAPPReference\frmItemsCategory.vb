﻿Imports System.Data.SqlClient
Imports System.Data
Public Class frmItemsCategory
    Dim sql As String
    Dim ds As New DataSet
    Dim dt As New DataTable
    Dim da As New SQLDataAdapter
    Dim dv As New DataView
    Dim cmd As New SQLCommand
    Dim nodeAuthor As TreeNode
    Dim nodeTitle As TreeNode
    Dim IsUpdate As Boolean
    Private Sub CreateTree(ByVal TV As TreeView)
        sql = "SELECT RootID, RootName, ParentID, RootLevel, convert(nvarchar(MAX),RootID) + ' : ' + RootName as MyRoot FROM tblItemsCategory Order By RootID"
        ds = New DataSet
        dt = New DataTable
        da = New SQLDataAdapter(sql, Con)
        da.Fill(ds, "tblItemsCategory")
        dv = New DataView(ds.Tables("tblItemsCategory"))
        dt = dv.ToTable

        Dim MaxLevel1 As Int64 = CInt(dt.Compute("MAX(RootLevel)", ""))

        Dim i, j As Int64

        For i = 0 To MaxLevel1
            Dim Rows1() As DataRow = dt.Select("RootLevel = " & i)
            For j = 0 To Rows1.Count - 1
                Dim ID1 As String = Rows1(j).Item("RootID").ToString
                Dim Name1 As String = Rows1(j).Item("RootName").ToString
                Dim Parent1 As String = Rows1(j).Item("ParentID").ToString
                Dim FullID As String = Rows1(j).Item("MyRoot").ToString

                If Parent1 = "-1" Then
                    TV.Nodes.Add(ID1, Name1)
                Else
                    Dim TreeNodes1() As TreeNode = TV.Nodes.Find(Parent1, True)
                    If TreeNodes1.Length > 0 Then
                        TreeNodes1(0).Nodes.Add(ID1, FullID)
                    End If
                End If
            Next
        Next
    End Sub

    Private Sub Button2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Button2.Click
        TV.Nodes.Clear()
        CreateTree(TV)
        TV.ExpandAll()
        TV.SelectedNode = TV.Nodes(0)
        For Each ctrl As Control In Me.GroupBox1.Controls
            If TypeOf ctrl Is TextBox Then
                ctrl.Text = ""
            End If
        Next
        IsUpdate = True
    End Sub


    Private Sub btnNew_Click(ByVal sender As Object, ByVal e As EventArgs) Handles جديدToolStripMenuItem.Click, btnNew.Click
        'Dim str As String = ""
        'Dim NameStr As String = ""
        'If TV.Nodes.Count > 0 Then
        '    If TV.SelectedNode.Text = "الدليل" Then
        '        txtParentAcc.Text = "0"
        '        txtParentName.Text = "الدليل"
        '        Exit Sub
        '    End If
        '    For i As Int64 = 0 To TV.SelectedNode.Text.Length - 1
        '        If TV.SelectedNode.Text.ToCharArray(i, 1) = " " Then
        '            If TV.SelectedNode.Text.ToCharArray(i + 1, 1) = ":" Then
        '                Exit For
        '            End If
        '        End If
        '        str += TV.SelectedNode.Text.ToCharArray(i, 1)
        '        NameStr = TV.SelectedNode.Text.ToString.Substring(str.Length + 2, TV.SelectedNode.Text.ToString.Length - str.Length - 2)
        '    Next
        '    txtAccID.Text = ""
        '    txtParentAcc.Text = str
        '    txtParentName.Text = NameStr
        '    txtAccName.Text = ""
        '    'ckbxBlock.Checked = False
        '    sql = "SELECT Top 1 RootID, RootName, ParentID, RootLevel FROM tblItemsCategory WHERE ParentID=" & CInt(txtParentAcc.Text) & " Order By RootID Desc"
        '    ds = New DataSet
        '    dt = New DataTable
        '    da = New SqlDataAdapter(sql, Con)
        '    da.Fill(ds, "tblItemsCategory")
        '    dv = New DataView(ds.Tables("tblItemsCategory"))
        '    dt = dv.ToTable
        '    If dt.Rows.Count > 0 Then
        '        txtAccID.Text = dt.Rows(0)(0) + 1
        '        txtLevel.Text = dt.Rows(0)(3)
        '    Else
        '        txtAccID.Text = txtParentAcc.Text & "1"
        '        sql = "SELECT Top 1 RootID, RootName, ParentID, RootLevel FROM tblItemsCategory WHERE RootID=" & CInt(txtParentAcc.Text) & " Order By RootID Desc"
        '        ds = New DataSet
        '        dt = New DataTable
        '        da = New SqlDataAdapter(sql, Con)
        '        da.Fill(ds, "tblItemsCategory")
        '        dv = New DataView(ds.Tables("tblItemsCategory"))
        '        dt = dv.ToTable
        '        txtLevel.Text = dt.Rows(0)(3)
        '    End If
        'End If
        'IsUpdate = False


        Dim str As String = ""
        Dim NameStr As String = ""

        ' Proceed if the tree has nodes
        If TV.Nodes.Count > 0 Then
            ' Check if "الدليل" is selected (Root Category)
            If TV.SelectedNode.Text = "الدليل" Then
                txtParentAcc.Text = "0"
                txtParentName.Text = "الدليل"
            Else
                ' Extract Parent ID and Name from Selected Node
                For i As Int64 = 0 To TV.SelectedNode.Text.Length - 1
                    If TV.SelectedNode.Text.ToCharArray(i, 1) = " " Then
                        If TV.SelectedNode.Text.ToCharArray(i + 1, 1) = ":" Then
                            Exit For
                        End If
                    End If
                    str += TV.SelectedNode.Text.ToCharArray(i, 1)
                    NameStr = TV.SelectedNode.Text.ToString.Substring(str.Length + 2, TV.SelectedNode.Text.Length - str.Length - 2)
                Next
                txtParentAcc.Text = str
                txtParentName.Text = NameStr
            End If

            ' Clear Text Fields for New Entry
            txtAccID.Text = ""
            txtAccName.Text = ""

            ' Prepare SQL Query to Get the Last Record Under the Selected Parent
            sql = "SELECT TOP 1 RootID, RootName, ParentID, RootLevel FROM tblItemsCategory WHERE ParentID = " & CInt(txtParentAcc.Text) & " ORDER BY RootID DESC"
            ds = New DataSet
            dt = New DataTable
            da = New SqlDataAdapter(sql, Con)
            da.Fill(ds, "tblItemsCategory")
            dv = New DataView(ds.Tables("tblItemsCategory"))
            dt = dv.ToTable

            ' ✅ Ensure Correct ID Generation
            If dt.Rows.Count > 0 Then
                txtAccID.Text = dt.Rows(0)("RootID") + 1
                txtLevel.Text = dt.Rows(0)("RootLevel")
            Else
                ' If "الدليل" is selected, start the first record at "1"
                If txtParentAcc.Text = "0" Then
                    txtAccID.Text = "1"
                    txtLevel.Text = "1"
                Else
                    ' Generate ID for first child category
                    txtAccID.Text = txtParentAcc.Text & "1"

                    ' Get Parent Level
                    sql = "SELECT RootLevel FROM tblItemsCategory WHERE RootID = " & CInt(txtParentAcc.Text)
                    dt = New DataTable
                    da = New SqlDataAdapter(sql, Con)
                    da.Fill(dt)

                    ' Prevent index out of range exception
                    If dt.Rows.Count > 0 Then
                        txtLevel.Text = dt.Rows(0)("RootLevel")
                    Else
                        txtLevel.Text = "1"
                    End If
                End If
            End If
        End If
        IsUpdate = False


    End Sub

    Private Sub TV_NodeMouseClick(ByVal sender As Object, ByVal e As TreeNodeMouseClickEventArgs) Handles TV.NodeMouseClick
        If e.Button = Windows.Forms.MouseButtons.Right Then
            TV.SelectedNode = e.Node
        End If
    End Sub


    Private Sub TV_AfterSelect(ByVal sender As Object, ByVal e As TreeViewEventArgs) Handles TV.AfterSelect
        If TV.Focused Then
            Dim str As String = ""
            Dim NameStr As String = ""
            If TV.SelectedNode.Text = "الدليل" Then
                Exit Sub
            End If
            For i As Int64 = 0 To TV.SelectedNode.Text.Length - 1

                
                If TV.SelectedNode.Text.ToCharArray(i, 1) = " " Then
                    If TV.SelectedNode.Text.ToCharArray(i + 1, 1) = ":" Then
                        Exit For
                    End If
                End If
                str += TV.SelectedNode.Text.ToCharArray(i, 1)
                NameStr = TV.SelectedNode.Text.ToString.Substring(str.Length + 2, TV.SelectedNode.Text.ToString.Length - str.Length - 2)
            Next
            txtAccID.Text = str
            txtAccName.Text = NameStr


        End If
    End Sub


    Private Sub تعديلToolStripMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles تعديلToolStripMenuItem.Click, bntUpdate.Click
        If txtAccID.Text = "" Then
            Exit Sub
        End If
        If isupdate = True Then
            'Dim UpdateCMD As New SQLCommand("Update tblItemsCategory set RootName = '" & txtAccName.Text.Trim & "',RootBlocked = '" & ckbxBlock.Checked & "',ModifiedBy = '" & UserName & "',ModifiedOn= GetDate() where RootID = " & CInt(txtAccID.Text) & "", Con)
            Dim UpdateCMD As New SQLCommand("Update tblItemsCategory set RootName = '" & txtAccName.Text.Trim & "',ModifiedBy = '" & UserName & "',ModifiedOn= GetDate() where RootID = " & CInt(txtAccID.Text) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If

            'cmd = New SQLCommand("UPDATE tblItemsCategory SET  RootName = @RootName WHERE RootId=" & CInt(txtAccID.Text), Con)
            'With cmd.Parameters
            '    .AddWithValue("@RootName", txtAccName.Text)
            '    .AddWithValue("@ParentID", txtParentAcc.Text)
            '    .AddWithValue("@RootLevel", txtLevel.Text)
            '    .AddWithValue("@RootBlocked", ckbxBlock.Checked)
            'End With
            'Con.Open()
            'cmd.ExecuteNonQuery()
            'Con.Close()
            Button2_Click(Nothing, Nothing)
        Else
            'Dim InsertCMD As New SQLCommand("Insert Into tblItemsCategory (RootID,RootName,ParentID,RootLevel,RootBlocked,CreatedBy,CreatedOn) Values (" & CInt(txtAccID.Text) & ",'" & txtAccName.Text.Trim & "'," & CInt(txtParentAcc.Text) & "," & CInt(txtLevel.Text) & ",'" & ckbxBlock.Checked & "','" & UserName & "',GetDate())", Con)
            Dim InsertCMD As New SQLCommand("Insert Into tblItemsCategory (RootID,RootName,ParentID,RootLevel,CreatedBy,CreatedOn) Values (" & CInt(txtAccID.Text) & ",'" & txtAccName.Text.Trim & "'," & CInt(txtParentAcc.Text) & "," & CInt(txtLevel.Text) & ",'" & UserName & "',GetDate())", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            InsertCMD.ExecuteNonQuery()
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If

            'cmd = New SQLCommand("INSERT INTO tblItemsCategory VALUES(RootID,RootName,ParentID,RootLevel,RootBlocked,CreatedBy,CreatedOn)", Con)
            'With cmd.Parameters
            '    .AddWithValue("@RootID", txtAccID.Text)
            '    .AddWithValue("@RootName", txtAccName.Text)
            '    .AddWithValue("@ParentID", txtParentAcc.Text)
            '    .AddWithValue("@RootLevel", txtLevel.Text)
            '    .AddWithValue("@RootBlocked", ckbxBlock.Checked)
            'End With
            'Con.Open()
            'cmd.ExecuteNonQuery()
            'Con.Close()
            Button2_Click(Nothing, Nothing)
        End If

    End Sub

    Private Sub حذفToolStripMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles حذفToolStripMenuItem.Click, btnDelete.Click
        If txtAccID.Text = "" Then
            Exit Sub
        End If
        If isupdate = True Then
            If TV.SelectedNode.Text = "الدليل" Or TV.SelectedNode.Text = "فئات الأصناف" Then
                Exit Sub
            End If
            If MsgBox("هل تريد حذف الفئة؟", MsgBoxStyle.YesNo, "دليل المواد") = MsgBoxResult.Yes Then
                cmd = New SQLCommand("DELETE FROM tblItemsCategory WHERE ParentID=" & CInt(txtAccID.Text), Con)
                Con.Open()
                cmd.ExecuteNonQuery()
                Con.Close()


                cmd = New SQLCommand("DELETE FROM tblItemsCategory WHERE RootId=" & CInt(txtAccID.Text), Con)
                Con.Open()
                cmd.ExecuteNonQuery()
                Con.Close()
                Button2_Click(Nothing, Nothing)
            End If
        End If

    End Sub

    Private Sub الغاءToolStripMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles الغاءToolStripMenuItem.Click, btnCancel.Click
        Button2_Click(Nothing, Nothing)
    End Sub

    Private Sub خروجToolStripMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs) Handles خروجToolStripMenuItem.Click
        If MsgBox("هل تريد الخروج", MsgBoxStyle.YesNo, "دليل المواد") = MsgBoxResult.Yes Then
            End
        End If
    End Sub

    Private Sub Form1_Load(ByVal sender As Object, ByVal e As EventArgs) Handles MyBase.Load
        Button2_Click(Nothing, Nothing)
    End Sub
End Class