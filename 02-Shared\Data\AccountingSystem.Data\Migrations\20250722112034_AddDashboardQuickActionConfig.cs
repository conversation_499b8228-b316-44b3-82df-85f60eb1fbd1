﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AccountingSystem.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddDashboardQuickActionConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "Email",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "FailedLoginAttempts",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "FullName",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "IsLocked",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "LastLoginDate",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "LastPasswordChange",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "ModifiedDate",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "RowVersion",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "tblSessionLogs");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "tblSessionLogs");

            migrationBuilder.DropColumn(
                name: "ModifiedDate",
                table: "tblSessionLogs");

            migrationBuilder.DropColumn(
                name: "RowVersion",
                table: "tblSessionLogs");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "tblGroupsAuth");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "tblGroupsAuth");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "tblGroupsAuth");

            migrationBuilder.DropColumn(
                name: "ModifiedDate",
                table: "tblGroupsAuth");

            migrationBuilder.DropColumn(
                name: "RowVersion",
                table: "tblGroupsAuth");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "tblGroupPermissions");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "tblGroupPermissions");

            migrationBuilder.DropColumn(
                name: "ModifiedDate",
                table: "tblGroupPermissions");

            migrationBuilder.DropColumn(
                name: "RowVersion",
                table: "tblGroupPermissions");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "tblGroupFormPermissions");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "tblGroupFormPermissions");

            migrationBuilder.DropColumn(
                name: "ModifiedDate",
                table: "tblGroupFormPermissions");

            migrationBuilder.DropColumn(
                name: "RowVersion",
                table: "tblGroupFormPermissions");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "tblForms");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "tblForms");

            migrationBuilder.DropColumn(
                name: "ModifiedDate",
                table: "tblForms");

            migrationBuilder.DropColumn(
                name: "RowVersion",
                table: "tblForms");

            migrationBuilder.RenameColumn(
                name: "UserID",
                table: "tblUsers",
                newName: "SN");

            migrationBuilder.AddColumn<byte>(
                name: "CashierChange",
                table: "tblUsers",
                type: "tinyint",
                nullable: false,
                defaultValue: (byte)0);

            migrationBuilder.AddColumn<byte>(
                name: "ChangeInvoicePrice",
                table: "tblUsers",
                type: "tinyint",
                nullable: false,
                defaultValue: (byte)0);

            migrationBuilder.AddColumn<byte>(
                name: "CustomerChange",
                table: "tblUsers",
                type: "tinyint",
                nullable: false,
                defaultValue: (byte)0);

            migrationBuilder.AddColumn<int>(
                name: "DefaultCashier",
                table: "tblUsers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "DefaultCustomer",
                table: "tblUsers",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DefaultStore",
                table: "tblUsers",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MaxDiscountPercent",
                table: "tblUsers",
                type: "decimal(5,2)",
                nullable: true);

            migrationBuilder.AddColumn<byte>(
                name: "StoreChange",
                table: "tblUsers",
                type: "tinyint",
                nullable: false,
                defaultValue: (byte)0);

            migrationBuilder.AlterColumn<int>(
                name: "VOIDSTTS",
                table: "tblStockMovHeader",
                type: "int",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "bit");

            migrationBuilder.AlterColumn<string>(
                name: "ReadyForUse",
                table: "tblStockMovHeader",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(10)",
                oldMaxLength: 10);

            migrationBuilder.AlterColumn<long>(
                name: "PartnerNo",
                table: "tblStockMovHeader",
                type: "bigint",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<long>(
                name: "ItemNo",
                table: "tblStockMovement",
                type: "bigint",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.CreateTable(
                name: "DashboardQuickActionConfigs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RoleName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    SidebarItemRoute = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsEnabled = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DashboardQuickActionConfigs", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DashboardQuickActionConfigs");

            migrationBuilder.DropColumn(
                name: "CashierChange",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "ChangeInvoicePrice",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "CustomerChange",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "DefaultCashier",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "DefaultCustomer",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "DefaultStore",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "MaxDiscountPercent",
                table: "tblUsers");

            migrationBuilder.DropColumn(
                name: "StoreChange",
                table: "tblUsers");

            migrationBuilder.RenameColumn(
                name: "SN",
                table: "tblUsers",
                newName: "UserID");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "tblUsers",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETDATE()");

            migrationBuilder.AddColumn<string>(
                name: "Email",
                table: "tblUsers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "FailedLoginAttempts",
                table: "tblUsers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "FullName",
                table: "tblUsers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "tblUsers",
                type: "bit",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsLocked",
                table: "tblUsers",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastLoginDate",
                table: "tblUsers",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastPasswordChange",
                table: "tblUsers",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedDate",
                table: "tblUsers",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "RowVersion",
                table: "tblUsers",
                type: "rowversion",
                rowVersion: true,
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "VOIDSTTS",
                table: "tblStockMovHeader",
                type: "bit",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<string>(
                name: "ReadyForUse",
                table: "tblStockMovHeader",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(10)",
                oldMaxLength: 10,
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "PartnerNo",
                table: "tblStockMovHeader",
                type: "int",
                nullable: true,
                oldClrType: typeof(long),
                oldType: "bigint",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "ItemNo",
                table: "tblStockMovement",
                type: "int",
                nullable: false,
                oldClrType: typeof(long),
                oldType: "bigint");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "tblSessionLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "tblSessionLogs",
                type: "bit",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedDate",
                table: "tblSessionLogs",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "RowVersion",
                table: "tblSessionLogs",
                type: "rowversion",
                rowVersion: true,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "tblGroupsAuth",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETDATE()");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "tblGroupsAuth",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "tblGroupsAuth",
                type: "bit",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedDate",
                table: "tblGroupsAuth",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "RowVersion",
                table: "tblGroupsAuth",
                type: "rowversion",
                rowVersion: true,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "tblGroupPermissions",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "GETDATE()");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "tblGroupPermissions",
                type: "bit",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedDate",
                table: "tblGroupPermissions",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "RowVersion",
                table: "tblGroupPermissions",
                type: "rowversion",
                rowVersion: true,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "tblGroupFormPermissions",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "tblGroupFormPermissions",
                type: "bit",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedDate",
                table: "tblGroupFormPermissions",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "RowVersion",
                table: "tblGroupFormPermissions",
                type: "rowversion",
                rowVersion: true,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "tblForms",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "tblForms",
                type: "bit",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedDate",
                table: "tblForms",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "RowVersion",
                table: "tblForms",
                type: "rowversion",
                rowVersion: true,
                nullable: true);
        }
    }
}
