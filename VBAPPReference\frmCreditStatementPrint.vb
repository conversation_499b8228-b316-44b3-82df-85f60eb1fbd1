﻿Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports CrystalDecisions.Windows.Forms
Imports System.Data.SqlClient

Public Class frmCreditStatementPrint

    Private Sub frmCreditStatementPrint_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed

    End Sub


    Private Sub frmCreditStatementPrint_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.Size = New Size(frmRPTWidth, frmRPTHeight)
        Dim ReportFormula As String = ""

        ReportFormula = "Date({VendorStatementByInvoices.InDate}) in ({?DateRange})"
        If frmCreditStatement.chbxTrxType.Checked = True Then
            ReportFormula += " and {@TrxType} = {?TrxType}"
        End If
        If frmCreditStatement.chbxPaymentType.Checked = True Then
            ReportFormula += " and {VendorStatementByInvoices.PaidStatus} = {?PaymentType}"
        End If
        If frmCreditStatement.chbxVendorNo.Checked = True Then
            ReportFormula += " and {VendorStatementByInvoices.InVendor} = {?VendorNo}"
        End If

        Dim RPT As New rptCreditStatement
        UpdateCRDataSource(RPT)
        RPT.RecordSelectionFormula = ReportFormula
        RPT.Refresh()

        RPT.ParameterFields(0).CurrentValues.Clear()
        RPT.ParameterFields(0).CurrentValues.AddRange(frmCreditStatement.dtpFrom.Value.Date, frmCreditStatement.dtpTo.Value.Date, RangeBoundType.BoundInclusive, RangeBoundType.BoundInclusive)

        If frmCreditStatement.chbxTrxType.Checked = True Then
            RPT.SetParameterValue(1, frmCreditStatement.cmbxTrxType.Text.Trim)
        End If
        If frmCreditStatement.chbxPaymentType.Checked = True Then
            RPT.SetParameterValue(3, frmCreditStatement.cmbxPaymentType.SelectedIndex)
        End If
        If frmCreditStatement.chbxVendorNo.Checked = True Then
            RPT.SetParameterValue(2, frmCreditStatement.cmbxVendorID.Text.Trim)
        End If

        CrystalReportViewer2.ReportSource = RPT
    End Sub


End Class
