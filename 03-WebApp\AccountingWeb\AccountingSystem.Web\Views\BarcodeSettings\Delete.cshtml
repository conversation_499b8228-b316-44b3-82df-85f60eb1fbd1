@model AccountingSystem.Models.BarcodeSettings

@{
    ViewData["Title"] = "Delete Barcode Settings";
}

<h1>@ViewData["Title"]</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Barcode Settings</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Shop)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Shop)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.BarcodeType)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.BarcodeType)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="ID" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div> 