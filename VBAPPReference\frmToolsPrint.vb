﻿Imports System.Data.SqlClient
Public Class frmToolsPrint
    Dim type As String = ""

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        Dim UpdateCMD As New SQLCommand("Update tblConfig set PrinterInvoice='" & cmbxPrinterInvoice.Text.Trim & "',PrinterLabel='" & cmbxPrinterLabel.Text.Trim & "',PrinterReports='" & cmbxPrinterReports.Text.Trim & "'", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        UpdateCMD.ExecuteReader()
        MsgBox("تم حفظ التعديلات", MsgBoxStyle.Information, "نظام السلطان")
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        PrinterInvoice = cmbxPrinterInvoice.Text
        PrinterLabel = cmbxPrinterLabel.Text
        PrinterReports = cmbxPrinterReports.Text
    End Sub

    Private Sub frmToolsPrint_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            Dim InstalledPrinters As String
            For Each InstalledPrinters In System.Drawing.Printing.PrinterSettings.InstalledPrinters
                cmbxPrinterInvoice.Items.Add(InstalledPrinters)
                cmbxPrinterLabel.Items.Add(InstalledPrinters)
                cmbxPrinterReports.Items.Add(InstalledPrinters)
            Next InstalledPrinters
            cmbxPrinterInvoice.Text = PrinterInvoice
            cmbxPrinterLabel.Text = PrinterLabel
            cmbxPrinterReports.Text = PrinterReports
        Catch ex As Exception

        End Try

    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub
End Class