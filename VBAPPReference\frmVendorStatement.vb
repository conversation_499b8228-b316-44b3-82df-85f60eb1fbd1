﻿Imports System.Data.SqlClient
Public Class frmVendorStatement
        Private Sub frmCustomerStatement_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        VendorsLoad()
        ForceGregorianForAllPickers(Me)

            cmbxPartnerNo.SelectedIndex = 0
        End Sub
        Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click

        If cmbxPartnerNo.SelectedValue <> 0 Then
                AccNo = cmbxPartnerNo.SelectedValue
                'dtp.CustomFormat = "dd/MM/yyyy"
                DateFrom = dtpFrom.Value.Date
                DateTo = dtpTo.Value.Date
                DateStrFrom = dtpFrom.Value.ToString("yyyy-MM-dd")
                DateStrTo = dtpTo.Value.ToString("yyyy-MM-dd")
            PrintType = "VendorStatement"
            frmPrintPreview.MdiParent = frmMain
                frmPrintPreview.Show()
            Else
                <PERSON>g<PERSON>("فضلا اختر الحساب", MsgBoxStyle.Critical, "نظام السلطان")
            End If

        End Sub
        Private Sub btnCustomerSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCustomerSearch.Click
        CustomerSearchForm = "frmVendorStatement"
        frmVendorSearch.ShowDialog()
    End Sub

    Sub VendorsLoad()
        Dim CMD As New SqlCommand("Select AccountCode,AccountName from tbl_Acc_Accounts where ParentAccountCode = (Select AccountNo from tblGLConfig where EntryReferenceModule = 'موردون') order by AccountCode", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "AccountName + ' - ' + CONVERT(AccountCode, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxPartnerNo.DataSource = dt
        cmbxPartnerNo.DisplayMember = "DisplayText" ' Show Name
        cmbxPartnerNo.ValueMember = "AccountCode" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
            Me.Close()
        End Sub
        'Private Sub cmbxPartnerNo_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbxPartnerNo.SelectedIndexChanged
        '    If cmbxPartnerNo.Text.Trim <> "" Then
        '        Dim SearchCMD As New SqlCommand("select CustomerName from tblCustomers where CustomerNo = " & Val(cmbxPartnerNo.Text) & " ", Con)

        '        If Con.State <> ConnectionState.Open Then
        '            Con.Open()
        '        End If
        '        Dim reader As SqlDataReader = SearchCMD.ExecuteReader
        '        If reader.Read Then
        '            lblCustomerDescription.Text = reader.Item("CustomerName").ToString
        '        Else
        '            lblCustomerDescription.Text = ""
        '        End If
        '        If reader.IsClosed Then
        '        Else
        '            reader.Close()
        '        End If
        '        If Con.State <> ConnectionState.Closed Then
        '            Con.Close()
        '        End If
        '    End If
        'End Sub
        'Private Sub cmbxVendors_Leave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbxPartnerNo.Leave
        '    If cmbxPartnerNo.Text.Trim <> "" Then
        '        Dim SearchCMD As New SqlCommand("select CustomerName from tblCustomers where CustomerNo = " & Val(cmbxPartnerNo.Text) & " ", Con)

        '        If Con.State <> ConnectionState.Open Then
        '            Con.Open()
        '        End If
        '        Dim reader As SqlDataReader = SearchCMD.ExecuteReader
        '        If reader.Read Then
        '            lblCustomerDescription.Text = reader.Item("CustomerName").ToString
        '        Else
        '            lblCustomerDescription.Text = ""
        '        End If
        '        If reader.IsClosed Then
        '        Else
        '            reader.Close()
        '        End If
        '        If Con.State <> ConnectionState.Closed Then
        '            Con.Close()
        '        End If
        '    End If
        'End Sub
    End Class