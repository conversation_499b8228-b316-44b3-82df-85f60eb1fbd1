using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    [Table("tblEmployees")]
    public class Employee
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)] // Not an IDENTITY column
        [Column("EmployeeNo")]
        public int Id { get; set; }

        [Column("Emp_Name")]
        [StringLength(255)]
        public string? Name { get; set; } // Made nullable

        [Column("Emp_Salary", TypeName = "money")]
        public decimal? Salary { get; set; }

        [Column("CreatedBy")]
        [StringLength(50)]
        public string? CreatedBy { get; set; } // Made nullable

        [Column("CreatedOn")]
        public DateTime? CreatedOn { get; set; }

        [Column("ModifiedBy")]
        [StringLength(50)]
        public string? ModifiedBy { get; set; } // Made nullable

        [Column("ModifiedOn")]
        public DateTime? ModifiedOn { get; set; }
    }
} 