﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-Shared", "02-Shared", "{DC4E06BF-EA79-DE12-7BF6-8A7621484D61}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{8992D3F8-993A-C27A-1BAC-31356B5E7DFB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AccountingSystem.Core", "02-Shared\Core\AccountingSystem.Core\AccountingSystem.Core.csproj", "{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Data", "Data", "{117F0391-77F6-ED2F-397E-5289470353FC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AccountingSystem.Data", "02-Shared\Data\AccountingSystem.Data\AccountingSystem.Data.csproj", "{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AccountingSystem.Models", "02-Shared\Models\AccountingSystem.Models\AccountingSystem.Models.csproj", "{B7C2F743-BFA6-44B9-9646-37D038239B55}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AccountingSystem.Services", "02-Shared\Services\AccountingSystem.Services\AccountingSystem.Services.csproj", "{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AccountingSystem.Web", "03-WebApp\AccountingWeb\AccountingSystem.Web\AccountingSystem.Web.csproj", "{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AccountingSystem.Tests", "03-WebApp\Tests\AccountingSystem.Tests\AccountingSystem.Tests.csproj", "{********-12B9-4100-B49C-6CEAF6D4E3DC}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Debug|x64.Build.0 = Debug|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Debug|x86.Build.0 = Debug|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Release|x64.ActiveCfg = Release|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Release|x64.Build.0 = Release|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Release|x86.ActiveCfg = Release|Any CPU
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01}.Release|x86.Build.0 = Release|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Debug|x64.Build.0 = Debug|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Debug|x86.Build.0 = Debug|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Release|Any CPU.Build.0 = Release|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Release|x64.ActiveCfg = Release|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Release|x64.Build.0 = Release|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Release|x86.ActiveCfg = Release|Any CPU
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4}.Release|x86.Build.0 = Release|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Debug|x64.Build.0 = Debug|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Debug|x86.Build.0 = Debug|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Release|Any CPU.Build.0 = Release|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Release|x64.ActiveCfg = Release|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Release|x64.Build.0 = Release|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Release|x86.ActiveCfg = Release|Any CPU
		{B7C2F743-BFA6-44B9-9646-37D038239B55}.Release|x86.Build.0 = Release|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Debug|x64.Build.0 = Debug|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Debug|x86.Build.0 = Debug|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Release|Any CPU.Build.0 = Release|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Release|x64.ActiveCfg = Release|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Release|x64.Build.0 = Release|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Release|x86.ActiveCfg = Release|Any CPU
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225}.Release|x86.Build.0 = Release|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Debug|x64.Build.0 = Debug|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Debug|x86.Build.0 = Debug|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Release|Any CPU.Build.0 = Release|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Release|x64.ActiveCfg = Release|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Release|x64.Build.0 = Release|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Release|x86.ActiveCfg = Release|Any CPU
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B}.Release|x86.Build.0 = Release|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Debug|x64.Build.0 = Debug|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Debug|x86.Build.0 = Debug|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Release|x64.ActiveCfg = Release|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Release|x64.Build.0 = Release|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Release|x86.ActiveCfg = Release|Any CPU
		{********-12B9-4100-B49C-6CEAF6D4E3DC}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{8992D3F8-993A-C27A-1BAC-31356B5E7DFB} = {DC4E06BF-EA79-DE12-7BF6-8A7621484D61}
		{A2B15CCD-DAF9-4B28-B564-1EB3F13DAA01} = {8992D3F8-993A-C27A-1BAC-31356B5E7DFB}
		{117F0391-77F6-ED2F-397E-5289470353FC} = {DC4E06BF-EA79-DE12-7BF6-8A7621484D61}
		{C6BB7D50-F7C7-4EEE-AB1D-789FC1AA2CD4} = {117F0391-77F6-ED2F-397E-5289470353FC}
		{B7C2F743-BFA6-44B9-9646-37D038239B55} = {117F0391-77F6-ED2F-397E-5289470353FC}
		{E1A08E1F-0619-4F8D-B1AD-135FA6AB0225} = {117F0391-77F6-ED2F-397E-5289470353FC}
		{FE725881-AEE0-4247-BB4F-6E248B3D5D2B} = {117F0391-77F6-ED2F-397E-5289470353FC}
		{********-12B9-4100-B49C-6CEAF6D4E3DC} = {117F0391-77F6-ED2F-397E-5289470353FC}
	EndGlobalSection
EndGlobal
