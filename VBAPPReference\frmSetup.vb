﻿Imports System.Data.SqlClient
Imports System.IO
Public Class frmSetup

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        If MsgBox("هل أنت متأكد من رغبتك حذف الشعار ؟", MsgBoxStyle.YesNo, "نظام السلطان") = MsgBoxResult.Yes Then
            Dim UpdateCMD As New SqlCommand("Update tblConfig set LogoFile= NULL", Con)
            OpenConnection()
            UpdateCMD.ExecuteNonQuery()
            CloseConnection()
            ConfigLoad()
        End If
    End Sub

    Private Sub frmSetup_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ConfigLoad()
    End Sub
    Sub ConfigLoad()
        Try
            Dim ds As DataSet = New DataSet
            Dim SearchCMD As New SqlCommand("Select * from tblConfig", Con)

            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SearchCMD.ExecuteReader
            If reader.Read Then
                'txtAutoBackupPath.Text = reader.Item("BackupLocation").ToString
                txtShopName.Text = reader.Item("StoreName").ToString
                txtVATRegNo.Text = reader.Item("VATRegNo").ToString
                'cmbxSalesPriceChangeDefault.Text = reader.Item("SalesPriceChangeDefault").ToString
                txtItemDescription1.Text = reader.Item("ItemDescription1").ToString
                txtItemDescription2.Text = reader.Item("ItemDescription2").ToString
                txtLVL1.Text = reader.Item("ItemLevel1").ToString
                txtLVL2.Text = reader.Item("ItemLevel2").ToString
                txtLVL3.Text = reader.Item("ItemLevel3").ToString
                txtLVL4.Text = reader.Item("ItemLevel4").ToString
                txtLVL5.Text = reader.Item("ItemLevel5").ToString
                txtReportFooter.Text = reader.Item("AddressFooter").ToString
                'txtReportFooter2.Text = reader.Item("ReportFooterLine2").ToString
                'cmbxCostType.Text = reader.Item("CostType").ToString
                Try
                    Dim pictureData As Byte() = DirectCast(reader.Item("LogoFile"), Byte())
                    Dim stream As New IO.MemoryStream(pictureData)
                    Me.PictureBox1.Image = Image.FromStream(stream)
                    stream.Dispose()
                Catch ex As Exception
                    Me.PictureBox1.Image = Nothing
                End Try
            Else
                txtVATRegNo.Text = ""
                txtShopName.Text = ""
                'cmbxSalesPriceChange.Text = ""
                'cmbxSalesPriceChangeDefault.Text = ""
                txtItemDescription1.Text = ""
                txtItemDescription2.Text = ""
                txtLVL1.Text = ""
                txtLVL2.Text = ""
                txtLVL3.Text = ""
                txtLVL4.Text = ""
                txtLVL5.Text = ""
                txtReportFooter.Text = ""
                'txtReportFooter2.Text = ""
                'cmbxCostType.Text = ""
                Me.PictureBox1.Image = Nothing
            End If
            If reader.IsClosed Then
            Else
                reader.Close()
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception
            MessageBox.Show(ex.ToString())
        End Try
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        Dim ofdRestore As New OpenFileDialog
        Try
            OpenFileDialog1.InitialDirectory = "c:\"
            ofdRestore.Filter = "JPEG files (*.jpg)|*.jpg|GIF files (*.gif)|*.gif|All files (*.*)|*.*"
            ofdRestore.ShowDialog()
            If ofdRestore.FileName <> "" Then
                txtLogo.Text = ofdRestore.FileName
            End If

        Catch ex As Exception
            MsgBox("لم يتم تحميل الصورة ", MsgBoxStyle.Critical, "النظام")
        End Try
    End Sub
    'Sub SaveLogo()
    '    'Dim ImageToSave As String = txtLogo.Text.Trim
    '    'If ImageToSave = "" Then Exit Sub
    '    'Try
    '    '    Dim fs As FileStream = New FileStream(ImageToSave, FileMode.Open, FileAccess.Read)
    '    '    Dim r As Binarydim reader As SqlDataReader New BinaryReader(fs)
    '    '    Dim FileByteArray(fs.Length - 1) As Byte
    '    '    r.Read(FileByteArray, 0, CInt(fs.Length))
    '    '    fs.Close()
    '    '    Dim UpdateCMD As New SQLCommand("Update tblConfig set LogoFile= @Picture", Con)
    '    '    UpdateCMD.Parameters.Add("@Picture", OdbcTYPE.varbinary).Value = FileByteArray
    '    '    OpenConnection()
    '    '    UpdateCMD.ExecuteNonQuery()
    '    '    CloseConnection()
    '    'Catch ex As Exception

    '    'End Try
    'End Sub
    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        ItemClassLevels = 0
        If txtLVL1.Text.Trim <> "" Then
            ItemClassLevels += 1
        End If
        If txtLVL2.Text.Trim <> "" Then
            ItemClassLevels += 1
        End If
        If txtLVL3.Text.Trim <> "" Then
            ItemClassLevels += 1
        End If
        If txtLVL4.Text.Trim <> "" Then
            ItemClassLevels += 1
        End If
        If txtLVL5.Text.Trim <> "" Then
            ItemClassLevels += 1
        End If

        Dim SearchCMD As New SQLCommand("Select * from tblConfig", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        'reader.Close()
        Dim reader As SqlDataReader = SearchCMD.ExecuteReader

        If reader.Read Then
            reader.Close()
            'Dim UpdateCMD As New SqlCommand("Update tblConfig set StoreName = '" & txtShopName.Text.Trim & "',BackupLocation = '" & Trim(txtAutoBackupPath.Text) & "',SalesPriceChange = '" & Trim(cmbxSalesPriceChange.Text) & "', SalesPriceChangeDefault = '" & Trim(cmbxSalesPriceChangeDefault.Text) & "',ModifiedBy = '" & UserName & "',ModifiedOn= '" & Format(Date.Now, "yyyy/MM/dd") & "',ReportFooterLine1 = '" & txtReportFooter.Text.Trim & "',ReportFooterLine2 = '" & txtReportFooter2.Text.Trim & "',ItemDescription1='" & txtItemDescription1.Text.Trim & "',ItemDescription2='" & txtItemDescription2.Text.Trim & "',ItemClassL1='" & txtLVL1.Text.Trim & "',ItemClassL2='" & txtLVL2.Text.Trim & "',ItemClassL3='" & txtLVL3.Text.Trim & "',ItemClassL4='" & txtLVL4.Text.Trim & "',ItemClassL5='" & txtLVL5.Text.Trim & "',ItemClassLevels=" & Val(ItemClassLevels) & ",CostType = '" & cmbxCostType.Text.Trim & "'", Con)
            Dim UpdateCMD As New SqlCommand("Update tblConfig set StoreName = '" & txtShopName.Text.Trim & "',VATRegNo = '" & Trim(txtVATRegNo.Text) & "', ModifiedBy = '" & UserName & "',ModifiedOn= '" & Format(Date.Now, "yyyy/MM/dd") & "',AddressFooter = '" & txtReportFooter.Text.Trim & "',ItemDescription1='" & txtItemDescription1.Text.Trim & "',ItemDescription2='" & txtItemDescription2.Text.Trim & "',ItemLevel1='" & txtLVL1.Text.Trim & "',ItemLevel2='" & txtLVL2.Text.Trim & "',ItemLevel3='" & txtLVL3.Text.Trim & "',ItemLevel4='" & txtLVL4.Text.Trim & "',ItemLevel5='" & txtLVL5.Text.Trim & "'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            UpdateCMD.ExecuteNonQuery()
            SaveLogo()
            MessageBox.Show("تم تعديل الاعدادات بنجاح", "نظام السلطان")
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Else
            reader.Close()
            Dim InsertCMD As New SqlCommand("Insert Into tblConfig (ShopName,VATRegNo,ReportFooterLine1,ModifiedBy,ModifiedOn,ItemDescription1,ItemDescription2,ItemLevel1,ItemLevel2,ItemLevel3,ItemLevel4,ItemLevel5) Values ('" & txtShopName.Text.Trim & "','" & Trim(txtVATRegNo.Text) & "','" & txtReportFooter.Text.Trim & "','" & UserName & "','" & Format(Date.Now, "yyyy/MM/dd") & "','" & txtItemDescription1.Text.Trim & "','" & txtItemDescription2.Text.Trim & "','" & txtLVL1.Text.Trim & "','" & txtLVL2.Text.Trim & "','" & txtLVL3.Text.Trim & "','" & txtLVL4.Text.Trim & "','" & txtLVL5.Text.Trim & "'," & Val(ItemClassLevels) & ")", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            InsertCMD.ExecuteNonQuery()
            SaveLogo()
            MessageBox.Show("تم حفظ الاعدادات بنجاح", "نظام السلطان")
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        End If
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
        If reader.IsClosed Then
        Else
            reader.Close()
        End If
        ConfigLoad()
    End Sub
    Sub SaveLogo()
        Dim ImageToSave As String = txtLogo.Text.Trim
        If ImageToSave = "" Then Exit Sub
        Try
            Dim fs As FileStream = New FileStream(ImageToSave, FileMode.Open, FileAccess.Read)
            Dim r As BinaryReader = New BinaryReader(fs)
            Dim FileByteArray(fs.Length - 1) As Byte
            r.Read(FileByteArray, 0, CInt(fs.Length))
            fs.Close()
            Dim UpdateCMD As New SqlCommand("Update tblConfig set LogoFile= @Picture", Con)
            UpdateCMD.Parameters.Add("@Picture", SqlDbType.VarBinary).Value = FileByteArray
            OpenConnection()
            UpdateCMD.ExecuteNonQuery()
            CloseConnection()
        Catch ex As Exception

        End Try
    End Sub


End Class