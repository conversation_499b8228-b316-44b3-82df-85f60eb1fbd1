﻿Imports System.Data.SqlClient
Public Class frmCustomers
    Dim RegionSN As Int64 = 0
    Dim RegionDes As String = ""
    Dim Salesman As String = ""
    Dim ParentAccountCode As String = ""
    Private Sub frmCustomers_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        ' frmCards.Enabled = True
    End Sub

    Private Sub frmStores_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        CustomersLoad()
        LoadParentAccountCode()
        ClearFields()
        FillComboBoxWithEmployees()
    End Sub
    Private Sub FillComboBoxWithEmployees()
        Dim CMD As New SqlCommand("Select EmployeeNo,Emp_Name from tblEmployees order by EmployeeNo", Con)
        If Con.State <> ConnectionState.Open Then
            Con.Open()
        End If
        Dim reader As SqlDataReader = CMD.ExecuteReader()
        Dim dt As New DataTable()
        dt.Load(reader)
        reader.Close()
        ' Add a computed column for display (RootName - RootID)
        dt.Columns.Add("DisplayText", GetType(String), "Emp_Name + ' - ' + CONVERT(EmployeeNo, 'System.String')")
        ' Bind the ComboBox to DataTable
        cmbxEmployee.DataSource = dt
        cmbxEmployee.DisplayMember = "DisplayText" ' Show Name
        cmbxEmployee.ValueMember = "EmployeeNo" ' Store ID
        If Con.State <> ConnectionState.Closed Then
            Con.Close()
        End If
    End Sub
    Private Sub FillComboBoxWithStatus()
        Dim query As String = "select Customer_Status from tblCustomerStatus order by Customer_Status"
        Dim Customer_Status As New List(Of String)()


        Using Con As New SqlConnection(Constr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Customer_Status.Add(category)
                        End While
                    End Using

                    cmbxStatus.DataSource = Customer_Status
                    cmbxStatus.SelectedIndex = -1


                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub

    Private Sub FillComboBoxWithPaymentMethod()
        Dim query As String = "select Payment_Method from tblPaymentMethod order by Payment_Method"
        Dim Payment_Method As New List(Of String)()


        Using Con As New SqlConnection(Constr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Payment_Method.Add(category)
                        End While
                    End Using

                    cmbxPaymentMethod.DataSource = Payment_Method
                    cmbxPaymentMethod.SelectedIndex = -1

                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub
    Private Sub FillComboBoxWithLocalKSA()
        Dim query As String = "select Local_KSA from tblLocalKSA order by Local_KSA"
        Dim Local_KSA As New List(Of String)()


        Using Con As New SqlConnection(Constr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Local_KSA.Add(category)
                        End While
                    End Using

                    cmbxLocal.DataSource = Local_KSA
                    cmbxLocal.SelectedIndex = -1


                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub

    Private Sub FillComboBoxWithShops()
        Dim query As String = "select Shop_Text from tblShops order by Shop_Text"
        Dim Shop_Text As New List(Of String)()


        Using Con As New SqlConnection(Constr)
            Using command As New SqlCommand(query, Con)
                Try
                    Con.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim category As String = reader(0).ToString()
                            Shop_Text.Add(category)
                        End While
                    End Using

                    cmbxShop.DataSource = Shop_Text
                    cmbxShop.SelectedIndex = -1

                Catch ex As Exception

                    MessageBox.Show("An error occurred while populating the ComboBox: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub

    Sub CustomersLoad()
        Dim ds As DataSet = New DataSet
        Dim usercmd As New SqlCommand("SELECT * from CustomersView", Con)
        Dim da As New SqlDataAdapter(usercmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Function GenerateNextCustomerAccountCode(parentCode As String) As String
        Dim nextSegment As String = "000001"
        Try
            Dim cmd As New SqlCommand("SELECT MAX(CAST(SUBSTRING(AccountCode, @StartIndex, 6) AS INT)) FROM tbl_Acc_Accounts WHERE ParentAccountCode = @ParentCode", Con)
            cmd.Parameters.AddWithValue("@ParentCode", parentCode)
            cmd.Parameters.AddWithValue("@StartIndex", parentCode.Length + 1)
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim result = cmd.ExecuteScalar()
            If result IsNot DBNull.Value AndAlso result IsNot Nothing Then
                nextSegment = (CInt(result) + 1).ToString("D6")
            End If
            Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في توليد كود الحساب: " & ex.Message)
            Con.Close()
        End Try
        Return parentCode & nextSegment
    End Function


    Sub CheckRegionBySN()
        Try
            Dim SelectCMD As New SqlCommand("Select RegionDescription from tblRegions where SN = " & Val(RegionSN) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                RegionDes = Trim(reader.Item(0).ToString)
            Else
                RegionDes = ""
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Sub CheckRegionByDesc()
        Try
            Dim SelectCMD As New SqlCommand("Select SN from tblRegions where RegionDescription = '" & Trim(RegionDes) & "'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                RegionSN = Val(reader.Item(0).ToString)
            Else
                RegionSN = 0
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Sub CheckEmpByName()
        Try
            Dim SelectCMD As New SqlCommand("Select EmpNo from tblEmployees where Emp_Name = '" & Trim(Salesman) & "'", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                EmpNo = Val(reader.Item(0).ToString)
            Else
                EmpNo = 0
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Sub CheckEmpByNo()
        Try
            Dim SelectCMD As New SqlCommand("Select Emp_Name from tblEmployees where EmpNo = " & Val(EmpNo) & "", Con)
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read Then
                Salesman = Trim(reader.Item(0).ToString)
            Else
                Salesman = ""
            End If
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If
        Catch ex As Exception

        End Try
    End Sub
    Sub LoadParentAccountCode()
        Try
            Dim SelectCMD As New SqlCommand("SELECT AccountNo FROM tblGLConfig WHERE EntryReferenceModule = 'عملاء'", Con)
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim reader As SqlDataReader = SelectCMD.ExecuteReader()
            If reader.Read() Then
                ParentAccountCode = reader("AccountNo").ToString()
            End If
            reader.Close()
            Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل الحساب الرئيسي للعملاء: " & ex.Message)
        End Try
    End Sub
    Sub InsertCustomerAccount(customerName As String, ByRef generatedAccountCode As String)
        generatedAccountCode = GenerateNextCustomerAccountCode(ParentAccountCode)
        Dim cmd As New SqlCommand("INSERT INTO tbl_Acc_Accounts (AccountCode, SegmentCode, AccountName, ParentAccountCode, AccountLevel, IsPosting, AccountType, AccountNature, OpeningBalance, Notes, CreatedBy, CreatedOn) VALUES (@AccountCode, @SegmentCode, @AccountName, @ParentAccountCode, @AccountLevel, @IsPosting, @AccountType, @AccountNature, @OpeningBalance, @Notes, @CreatedBy, GETDATE())", Con)
        cmd.Parameters.AddWithValue("@AccountCode", generatedAccountCode)
        cmd.Parameters.AddWithValue("@SegmentCode", generatedAccountCode.Substring(ParentAccountCode.Length))
        cmd.Parameters.AddWithValue("@AccountName", customerName)
        cmd.Parameters.AddWithValue("@ParentAccountCode", ParentAccountCode)
        cmd.Parameters.AddWithValue("@AccountLevel", generatedAccountCode.Split("."c).Length)
        cmd.Parameters.AddWithValue("@IsPosting", True)
        cmd.Parameters.AddWithValue("@AccountType", "Asset")
        cmd.Parameters.AddWithValue("@AccountNature", "Debit")
        cmd.Parameters.AddWithValue("@OpeningBalance", 0)
        cmd.Parameters.AddWithValue("@Notes", "حساب آلي للعميل")
        cmd.Parameters.AddWithValue("@CreatedBy", UserName)

        If Con.State <> ConnectionState.Open Then Con.Open()
        cmd.ExecuteNonQuery()
        Con.Close()
    End Sub

    Sub UpdateCustomerAccount(accountCode As String, customerName As String)
        Dim cmd As New SqlCommand("UPDATE tbl_Acc_Accounts SET AccountName = @AccountName, ModifiedBy = @ModifiedBy, ModifiedOn = GETDATE() WHERE AccountCode = @AccountCode", Con)
        cmd.Parameters.AddWithValue("@AccountCode", accountCode)
        cmd.Parameters.AddWithValue("@AccountName", customerName)
        cmd.Parameters.AddWithValue("@ModifiedBy", UserName)

        If Con.State <> ConnectionState.Open Then Con.Open()
        cmd.ExecuteNonQuery()
        Con.Close()
    End Sub



    Function GenerateCustomerNumber() As Integer
        Dim cmd As New SqlCommand("SELECT ISNULL(MAX(CustomerNo),0) + 1 FROM tblCustomers", Con)
        If Con.State <> ConnectionState.Open Then Con.Open()
        Dim nextNo As Integer = cmd.ExecuteScalar()
        Con.Close()
        Return nextNo
    End Function


    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        Dim existingCustomerNo As Integer = Val(txtCustomerNo.Text)
        Dim generatedAccountCode As String = ""

        If existingCustomerNo = 0 Then
            InsertCustomerAccount(txtCustomerName.Text.Trim(), generatedAccountCode)

            Dim InsertCMD As New SqlCommand("INSERT INTO tblCustomers (CustomerNo, CustomerName, FirstName, LastName, Mobile, Phone, Email, StreetAddress1, StreetAddress2, City, Region, PostalCode, PaymentMethod, CreditLimit, PaymentTerm, ContactPerson, CR, VATRegNo,Shop , Status, LocalCustomer,EmployeeNo,BuildingNo,AdditionalNo,District, Notes, CreatedBy, CreatedOn) VALUES (@CustomerNo, @CustomerName, @FirstName, @LastName, @Mobile, @Phone, @Email, @StreetAddress1, @StreetAddress2, @City, @Region, @PostalCode, @PaymentMethod, @CreditLimit, @PaymentTerm, @ContactPerson, @CR, @VATRegNo, @Shop, @Status, @LocalCustomer,@EmployeeNo,@BuildingNo,@AdditionalNo,@District, @Notes, @CreatedBy, GETDATE())", Con)
            InsertCMD.Parameters.AddWithValue("@CustomerNo", generatedAccountCode)
            InsertCMD.Parameters.AddWithValue("@CustomerName", txtCustomerName.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@FirstName", txtFirstName.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@LastName", txtLastName.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@Mobile", txtMobile.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@Phone", txtPhone.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@Email", txtEmail.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@StreetAddress1", txtStreetAddress1.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@StreetAddress2", txtStreetAddress2.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@City", txtCity.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@Region", txtRegion.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@PostalCode", txtPostalCode.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@PaymentMethod", cmbxPaymentMethod.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@CreditLimit", Val(txtCreditLimit.Text))
            InsertCMD.Parameters.AddWithValue("@PaymentTerm", txtPaymentTerm.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@ContactPerson", txtContacts.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@CR", txtCR.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@VATRegNo", txtVATReg.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@Shop", cmbxShop.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@Status", cmbxStatus.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@LocalCustomer", cmbxLocal.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@EmployeeNo", If(cmbxEmployee.SelectedValue, 0))
            InsertCMD.Parameters.AddWithValue("@BuildingNo", txtBuildingNo.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@AdditionalNo", txtAdditionalNo.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@District", txtDistrict.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@Notes", txtNotes.Text.Trim())
            InsertCMD.Parameters.AddWithValue("@CreatedBy", UserName)

            If Con.State <> ConnectionState.Open Then Con.Open()
            InsertCMD.ExecuteNonQuery()
            Con.Close()

            MsgBox("تم إضافة العميل بنجاح", MsgBoxStyle.Information, "نظام السلطان")

            If IsNewCustomer = "frmSalesInvoiceTrx" Then
                Me.Close()
                frmSalesInvoiceTrx.CustomerLoad()
                frmSalesInvoiceTrx.cmbxPartnerNo.SelectedValue = generatedAccountCode
                IsNewCustomer = "No"
            End If
        Else
            Dim UpdateCMD As New SqlCommand("UPDATE tblCustomers SET CustomerName=@CustomerName, FirstName=@FirstName, LastName=@LastName, Mobile=@Mobile, Phone=@Phone, Email=@Email, StreetAddress1=@StreetAddress1, StreetAddress2=@StreetAddress2, City=@City, Region=@Region, PostalCode=@PostalCode, PaymentMethod=@PaymentMethod, CreditLimit=@CreditLimit, PaymentTerm=@PaymentTerm, ContactPerson=@ContactPerson, CR=@CR, VATRegNo=@VATRegNo, Shop=@Shop, Status=@Status, LocalCustomer=@LocalCustomer,EmployeeNo=@EmployeeNo, Notes=@Notes, ModifiedBy=@ModifiedBy, ModifiedOn=GETDATE(), BuildingNo=@BuildingNo, AdditionalNo=@AdditionalNo, District=@District WHERE CustomerNo=@CustomerNo", Con)
            UpdateCMD.Parameters.AddWithValue("@CustomerNo", existingCustomerNo)
            UpdateCMD.Parameters.AddWithValue("@CustomerName", txtCustomerName.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@FirstName", txtFirstName.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@LastName", txtLastName.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@Mobile", txtMobile.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@Phone", txtPhone.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@Email", txtEmail.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@StreetAddress1", txtStreetAddress1.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@StreetAddress2", txtStreetAddress2.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@City", txtCity.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@Region", txtRegion.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@PostalCode", txtPostalCode.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@PaymentMethod", cmbxPaymentMethod.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@CreditLimit", Val(txtCreditLimit.Text))
            UpdateCMD.Parameters.AddWithValue("@PaymentTerm", txtPaymentTerm.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@ContactPerson", txtContacts.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@CR", txtCR.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@VATRegNo", txtVATReg.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@Shop", cmbxShop.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@Status", cmbxStatus.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@LocalCustomer", cmbxLocal.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@EmployeeNo", If(cmbxEmployee.SelectedValue, 0))
            UpdateCMD.Parameters.AddWithValue("@BuildingNo", txtBuildingNo.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@AdditionalNo", txtAdditionalNo.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@District", txtDistrict.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@Notes", txtNotes.Text.Trim())
            UpdateCMD.Parameters.AddWithValue("@ModifiedBy", UserName)



            If Con.State <> ConnectionState.Open Then Con.Open()
            UpdateCMD.ExecuteNonQuery()
            Con.Close()

            ' تحديث اسم الحساب في دليل الحسابات أيضًا
            Dim getAccountCmd As New SqlCommand("SELECT CustomerNo FROM tblCustomers WHERE CustomerNo = @CustomerNo", Con)
            getAccountCmd.Parameters.AddWithValue("@CustomerNo", existingCustomerNo)
            If Con.State <> ConnectionState.Open Then Con.Open()
            Dim accountCode As String = getAccountCmd.ExecuteScalar().ToString()
            Con.Close()

            UpdateCustomerAccount(accountCode, txtCustomerName.Text.Trim())

            MsgBox("تم تعديل بيانات العميل بنجاح", MsgBoxStyle.Information, "نظام السلطان")
        End If

        CustomersLoad()
        ClearFields()
    End Sub

    Dim EntryCheck As Int64 = 0

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If txtCustomerNo.Text.Trim <> "" Then
            If MessageBox.Show("هل تريد بالتأكيد حذف العميل وحسابه المرتبط؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.Yes Then
                Dim accountCode As String = ""
                ' أولاً، الحصول على كود الحساب المرتبط
                Dim getAccountCmd As New SqlCommand("SELECT CustomerNo FROM tblCustomers WHERE CustomerNo = @CustomerNo", Con)
                getAccountCmd.Parameters.AddWithValue("@CustomerNo", Val(txtCustomerNo.Text))
                If Con.State <> ConnectionState.Open Then Con.Open()
                Dim result = getAccountCmd.ExecuteScalar()
                If result IsNot Nothing Then accountCode = result.ToString()
                Con.Close()

                ' التحقق من عدم وجود حركات على الحساب
                Dim checkTrxCmd As New SqlCommand("SELECT COUNT(*) FROM tblGLTrx WHERE AccountNo = @AccountCode", Con)
                checkTrxCmd.Parameters.AddWithValue("@AccountCode", accountCode)
                If Con.State <> ConnectionState.Open Then Con.Open()
                Dim trxCount As Integer = CInt(checkTrxCmd.ExecuteScalar())
                Con.Close()

                If trxCount > 0 Then
                    MessageBox.Show("لا يمكن حذف العميل لوجود قيود على الحساب في اليومية العامة.")
                    Exit Sub
                End If

                ' الحذف من جدول العملاء
                Dim deleteCustomerCmd As New SqlCommand("DELETE FROM tblCustomers WHERE CustomerNo = @CustomerNo", Con)
                deleteCustomerCmd.Parameters.AddWithValue("@CustomerNo", Val(txtCustomerNo.Text))

                ' الحذف من دليل الحسابات
                Dim deleteAccountCmd As New SqlCommand("DELETE FROM tbl_Acc_Accounts WHERE AccountCode = @AccountCode", Con)
                deleteAccountCmd.Parameters.AddWithValue("@AccountCode", accountCode)

                If Con.State <> ConnectionState.Open Then Con.Open()
                deleteCustomerCmd.ExecuteNonQuery()
                deleteAccountCmd.ExecuteNonQuery()
                Con.Close()

                MessageBox.Show("تم حذف العميل والحساب بنجاح.")
                CustomersLoad()
                ClearFields()
            End If
        End If
    End Sub


    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        ClearFields()
    End Sub
    'Private Sub DataGridView1_CellClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellClick
    '    If Val(DataGridView1.CurrentRow.Cells(0).Value) <> 0 Then
    '        txtCustomerNo.Text = DataGridView1.CurrentRow.Cells(0).Value
    '    End If
    'End Sub
    Sub ClearFields()
        txtCustomerNo.Clear()
        txtCustomerName.Clear()
        txtFirstName.Clear()
        txtLastName.Clear()
        txtPhone.Clear()
        txtMobile.Clear()
        txtEmail.Clear()
        txtStreetAddress1.Clear()
        txtStreetAddress2.Clear()
        txtCity.Clear()
        txtPaymentTerm.Clear()
        txtContacts.Clear()
        txtCR.Clear()
        txtVATReg.Clear()
        txtRegion.Clear()
        txtPostalCode.Clear()
        txtCreditLimit.Clear()
        txtBuildingNo.Clear()
        txtAdditionalNo.Clear()
        txtDistrict.Clear()
        txtNotes.Clear()
        cmbxPaymentMethod.SelectedIndex = -1
        cmbxShop.SelectedIndex = -1
        cmbxStatus.SelectedIndex = -1
        cmbxLocal.SelectedIndex = -1
        cmbxEmployee.SelectedIndex = -1
        txtCustomerName.Focus()
        FillComboBoxWithLocalKSA()
        FillComboBoxWithPaymentMethod()
        FillComboBoxWithShops()
        FillComboBoxWithStatus()
        FillComboBoxWithEmployees()


    End Sub

    Private Sub txtCustomerNo_TextChanged(sender As Object, e As EventArgs) Handles txtCustomerNo.TextChanged
        Try
            If Val(txtCustomerNo.Text) <> 0 Then
                Dim SearchCMD As New SqlCommand("select * from tblCustomers where CustomerNo = " & Val(txtCustomerNo.Text) & " ", Con)

                If Con.State <> ConnectionState.Open Then
                    Con.Open()
                End If
                Dim reader As SqlDataReader = SearchCMD.ExecuteReader
                If reader.Read Then
                    txtCustomerName.Text = reader("CustomerName").ToString()
                    txtFirstName.Text = reader("FirstName").ToString()
                    txtLastName.Text = reader("LastName").ToString()
                    txtMobile.Text = reader("Mobile").ToString()
                    txtPhone.Text = reader("Phone").ToString()
                    txtEmail.Text = reader("Email").ToString()
                    txtStreetAddress1.Text = reader("StreetAddress1").ToString()
                    txtStreetAddress2.Text = reader("StreetAddress2").ToString()
                    txtCity.Text = reader("City").ToString()
                    txtRegion.Text = reader("Region").ToString()
                    txtPostalCode.Text = reader("PostalCode").ToString()
                    txtCreditLimit.Text = reader("CreditLimit").ToString()
                    txtPaymentTerm.Text = reader("PaymentTerm").ToString()
                    txtContacts.Text = reader("ContactPerson").ToString()
                    txtCR.Text = reader("CR").ToString()
                    txtVATReg.Text = reader("VATRegNo").ToString()
                    cmbxShop.Text = reader("Shop").ToString()
                    cmbxStatus.Text = reader("Status").ToString()
                    cmbxLocal.Text = reader("LocalCustomer").ToString()
                    txtBuildingNo.Text = reader("BuildingNo").ToString()
                    txtAdditionalNo.Text = reader("AdditionalNo").ToString()
                    txtDistrict.Text = reader("District").ToString()
                    txtNotes.Text = reader("Notes").ToString()
                    cmbxPaymentMethod.Text = reader("PaymentMethod").ToString()
                    cmbxEmployee.SelectedValue = If(IsDBNull(reader("EmployeeNo")), 0, CInt(reader("EmployeeNo")))

                Else
                    ClearFields()
                End If
                If reader.IsClosed Then
                Else
                    reader.Close()
                End If
                If Con.State <> ConnectionState.Closed Then
                    Con.Close()
                End If
            End If
        Catch ex As Exception
            Exit Sub
        End Try
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        SearchBox()
    End Sub
    Sub SearchBox()
        Dim keyword As String = txtSearch.Text.Trim()
        If keyword = "" Then
            CustomersLoad()
            Return
        End If

        Dim ds As DataSet = New DataSet
        Dim cmd As New SqlClient.SqlCommand("
        SELECT * from CustomersView where CAST([رقم العميل] AS NVARCHAR) like @kw or [اسم العميل] LIKE @kw or [الاسم الأول] LIKE @kw or [الاسم الاخير] LIKE @kw order by [رقم العميل]", New SqlClient.SqlConnection(Constr))
        cmd.Parameters.AddWithValue("@kw", "%" & keyword & "%")
        Dim da As New SqlClient.SqlDataAdapter(cmd)
        ds.Clear()
        da.Fill(ds)
        DataGridView1.DataSource = ds.Tables(0)
    End Sub

    Private Sub DataGridView1_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView1.CellDoubleClick
        If Val(DataGridView1.CurrentRow.Cells(0).Value) <> 0 Then
            txtCustomerNo.Text = DataGridView1.CurrentRow.Cells(0).Value
        End If
    End Sub
End Class


