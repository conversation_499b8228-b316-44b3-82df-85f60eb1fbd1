Imports System.Data.SqlClient

Public Class frmTestPOS
    Private Sub frmTestPOS_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Test database connection
        TestDatabaseConnection()
    End Sub

    Private Sub TestDatabaseConnection()
        Try
            ' Set up connection string like in frmLogin
            ConServerName = My.Settings.server
            ConDatabaseName = My.Settings.database
            ConUserName = My.Settings.id
            ConPassword = My.Settings.passowrd
            ConModel = "SQL"

            ConStr = "Server=" + ConServerName + ";Database=" + ConDatabaseName + ";User Id=" + ConUserName + ";Password=" + ConPassword + ";TrustServerCertificate=True"
            Con.ConnectionString = ConStr

            ' Test connection
            If Con.State <> ConnectionState.Open Then
                Con.Open()
            End If

            MessageBox.Show("Database connection successful!", "Test Result", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
            If Con.State <> ConnectionState.Closed Then
                Con.Close()
            End If

        Catch ex As Exception
            MessageBox.Show("Database connection failed: " & ex.Message, "Test Result", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnTestPOS_Click(sender As Object, e As EventArgs) Handles btnTestPOS.Click
        Try
            ' Set a test username
            UserName = "admin" ' or any valid username from your database
            
            ' Try to open frmPOS
            Dim posForm As New frmPOS()
            posForm.MdiParent = Me.MdiParent
            posForm.Show()
            
            MessageBox.Show("frmPOS opened successfully!", "Test Result", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            MessageBox.Show("Failed to open frmPOS: " & ex.Message & vbCrLf & vbCrLf & "Stack Trace: " & ex.StackTrace, "Test Result", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
