﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{957E1989-2EFE-431A-89CF-1ED762032442}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>ACCOUNTING.My.MyApplication</StartupObject>
    <RootNamespace>ACCOUNTING</RootNamespace>
    <AssemblyName>ALSULTAN</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>ALSULTAN.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <UseWinFormsOutOfProcDesigner>True</UseWinFormsOutOfProcDesigner>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>ALSULTAN.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <UseWinFormsOutOfProcDesigner>True</UseWinFormsOutOfProcDesigner>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Augment, Version=3.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Augment.3.0.0\lib\net471\Augment.dll</HintPath>
    </Reference>
    <Reference Include="AxShockwaveFlashObjects, Version=1.0.0.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <HintPath>..\packages\CrystalReports.AxShockwaveFlashObjects.13.0.4003\lib\net40\AxShockwaveFlashObjects.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Core, Version=1.45.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Core.1.45.0\lib\net472\Azure.Core.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Identity, Version=1.13.2.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Identity.1.13.2\lib\netstandard2.0\Azure.Identity.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.CrystalReports.Engine">
      <HintPath>c:\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win64_x64\dotnet\CrystalDecisions.CrystalReports.Engine.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportSource">
      <HintPath>c:\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win64_x64\dotnet\CrystalDecisions.ReportSource.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.Shared">
      <HintPath>c:\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win64_x64\dotnet\CrystalDecisions.Shared.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.VSDesigner, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=AMD64" />
    <Reference Include="CrystalDecisions.Windows.Forms">
      <HintPath>c:\program files (x86)\sap businessobjects\crystal reports for .net framework 4.0\common\sap businessobjects enterprise xi 4.0\win64_x64\dotnet\CrystalDecisions.Windows.Forms.dll</HintPath>
    </Reference>
    <Reference Include="Ensure.That, Version=8.0.0.86, Culture=neutral, PublicKeyToken=d7cf9132176ba30b, processorArchitecture=MSIL">
      <HintPath>..\packages\Ensure.That.8.0.0\lib\net451\Ensure.That.dll</HintPath>
    </Reference>
    <Reference Include="EPPlusFree, Version=4.5.3.8, Culture=neutral, PublicKeyToken=85bfa0499e2db48a, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlusFree.4.5.3.8\lib\net40\EPPlusFree.dll</HintPath>
    </Reference>
    <Reference Include="FlashControlV71, Version=1.0.3187.32366, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="Genesis.QRCodeLib, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Genesis.QRCodeLib.1.0.0\lib\net45\Genesis.QRCodeLib.dll</HintPath>
    </Reference>
    <Reference Include="Guna.UI2, Version=2.0.4.7, Culture=neutral, PublicKeyToken=8b9d14aa5142e261, processorArchitecture=MSIL">
      <HintPath>..\packages\Guna.UI2.WinForms.2.0.4.7\lib\net472\Guna.UI2.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=3.0.4.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.3.0.4\lib\net462\log4net.dll</HintPath>
    </Reference>
    <Reference Include="MaterialSkin, Version=2.3.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MaterialSkin.2.2.3.1\lib\net461\MaterialSkin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.3\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.Cryptography, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Cryptography.9.0.3\lib\net462\Microsoft.Bcl.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Memory.9.0.3\lib\net462\Microsoft.Bcl.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.TimeProvider, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.TimeProvider.9.0.3\lib\net462\Microsoft.Bcl.TimeProvider.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Data.SqlClient, Version=6.0.0.0, Culture=neutral, PublicKeyToken=23ec7fc2d6eaa4a5, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.SqlClient.6.0.1\lib\net462\Microsoft.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.Tools.Sql.BatchParser, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.Data.Tools.Sql.BatchParser.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Abstractions.9.0.3\lib\net462\Microsoft.Extensions.Caching.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Memory, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Memory.9.0.3\lib\net462\Microsoft.Extensions.Caching.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.3\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.3\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.9.0.3\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.9.0.3\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client, Version=4.69.1.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.4.69.1\lib\net472\Microsoft.Identity.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client.Extensions.Msal, Version=4.69.1.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.Extensions.Msal.4.69.1\lib\netstandard2.0\Microsoft.Identity.Client.Extensions.Msal.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Abstractions.8.6.1\lib\net472\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.8.6.1\lib\net472\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.8.6.1\lib\net472\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.8.6.1\lib\net472\Microsoft.IdentityModel.Protocols.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.OpenIdConnect.8.6.1\lib\net472\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.8.6.1\lib\net472\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Assessment, Version=1.100.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Assessment.1.1.17\lib\net462\Microsoft.SqlServer.Assessment.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Assessment.Types, Version=1.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Assessment.Authoring.1.1.0\lib\net462\Microsoft.SqlServer.Assessment.Types.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ConnectionInfo, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.ConnectionInfo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Dmf, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Dmf.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Dmf.Common, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Dmf.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.Collector, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.Collector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.CollectorEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.CollectorEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.HadrData, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.HadrData.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.HadrModel, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.HadrModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.RegisteredServers, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.RegisteredServers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.Sdk.Sfc, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.Sdk.Sfc.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.SqlScriptPublish, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.SqlScriptPublish.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.XEvent, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.XEvent.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.XEventDbScoped, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.XEventDbScoped.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.XEventDbScopedEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.XEventDbScopedEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.XEventEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Management.XEventEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.PolicyEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.PolicyEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.RegSvrEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.RegSvrEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ServiceBrokerEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.ServiceBrokerEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Smo, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Smo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Smo.Notebook, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.Smo.Notebook.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SmoExtended, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.SmoExtended.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SqlClrProvider, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.SqlClrProvider.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SqlEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.SqlEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SqlWmiManagement, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.SqlWmiManagement.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.WmiEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.64.0\lib\net472\Microsoft.SqlServer.WmiEnum.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="QRCoder, Version=1.6.0.0, Culture=neutral, PublicKeyToken=c4ed5b9ae8358a28, processorArchitecture=MSIL">
      <HintPath>..\packages\QRCoder.1.6.0\lib\net40\QRCoder.dll</HintPath>
    </Reference>
    <Reference Include="SAPBusinessObjects.WPF.ViewerShared, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <HintPath>..\packages\CrystalReports.WPF.ViewerShared.13.0.4003\lib\net40\SAPBusinessObjects.WPF.ViewerShared.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ShockwaveFlashObjects, Version=1.0.0.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <HintPath>..\packages\CrystalReports.ShockwaveFlashObjects.13.0.4003\lib\net40\ShockwaveFlashObjects.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ClientModel, Version=1.3.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ClientModel.1.3.0\lib\netstandard2.0\System.ClientModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.9.0.3\lib\net462\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Design" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.3\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Formats.Asn1, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Formats.Asn1.9.0.3\lib\net462\System.Formats.Asn1.dll</HintPath>
    </Reference>
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.8.6.1\lib\net472\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.AccessControl.5.0.0\lib\net461\System.IO.FileSystem.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.3\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.9.0.3\lib\net462\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=6.0.0.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.6.0.1\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Pkcs, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Pkcs.9.0.3\lib\net462\System.Security.Cryptography.Pkcs.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.ProtectedData, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.ProtectedData.9.0.3\lib\net462\System.Security.Cryptography.ProtectedData.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.2, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.2\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.9.0.3\lib\net462\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.3\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.3\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.6.0\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\assembly\GAC_MSIL\System.Web.Services\2.0.0.0__b03f5f7f11d50a3a\System.Web.Services.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Windows.Forms.Ribbon, Version=2.0.0.0, Culture=neutral, PublicKeyToken=928736e248aa81f9, processorArchitecture=MSIL">
      <HintPath>..\packages\RibbonWinForms.5.1.0-beta\lib\net48\System.Windows.Forms.Ribbon.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="Zen.Barcode.Core, Version=3.1.0.0, Culture=neutral, PublicKeyToken=b5ae55aa76d2d9de, processorArchitecture=MSIL">
      <HintPath>..\packages\Zen.Barcode.Core.2.0.0\lib\net45\Zen.Barcode.Core.dll</HintPath>
    </Reference>
    <Reference Include="Zen.Barcode.Design, Version=3.1.0.0, Culture=neutral, PublicKeyToken=b5ae55aa76d2d9de, processorArchitecture=MSIL">
      <HintPath>..\packages\Zen.Barcode.Rendering.Framework.Web.3.1.10729.1\lib\Zen.Barcode.Design.dll</HintPath>
    </Reference>
    <Reference Include="Zen.Barcode.Web, Version=3.1.0.0, Culture=neutral, PublicKeyToken=b5ae55aa76d2d9de, processorArchitecture=MSIL">
      <HintPath>..\packages\Zen.Barcode.Rendering.Framework.Web.3.1.10729.1\lib\Zen.Barcode.Web.dll</HintPath>
    </Reference>
    <Reference Include="Zen.Barcode.Web.Mvc, Version=3.1.0.0, Culture=neutral, PublicKeyToken=b5ae55aa76d2d9de, processorArchitecture=MSIL">
      <HintPath>..\packages\Zen.Barcode.Rendering.Framework.Web.3.1.10729.1\lib\Zen.Barcode.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="zxing, Version=0.16.10.0, Culture=neutral, PublicKeyToken=4e88037ac681fe60, processorArchitecture=MSIL">
      <HintPath>..\packages\ZXing.Net.0.16.10\lib\net48\zxing.dll</HintPath>
    </Reference>
    <Reference Include="zxing.presentation, Version=0.16.10.0, Culture=neutral, PublicKeyToken=4e88037ac681fe60, processorArchitecture=MSIL">
      <HintPath>..\packages\ZXing.Net.0.16.10\lib\net48\zxing.presentation.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CommunFunctions.vb" />
    <Compile Include="FormPermissionInfo.vb" />
    <Compile Include="connection.vb" />
    <Compile Include="frmAccountSearch.Designer.vb">
      <DependentUpon>frmAccountSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="frmAccountSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmActivation.Designer.vb">
      <DependentUpon>frmActivation.vb</DependentUpon>
    </Compile>
    <Compile Include="frmActivation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmBalanceUpload.Designer.vb">
      <DependentUpon>frmBalanceUpload.vb</DependentUpon>
    </Compile>
    <Compile Include="frmBalanceUpload.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmBarcodeSettings.Designer.vb">
      <DependentUpon>frmBarcodeSettings.vb</DependentUpon>
    </Compile>
    <Compile Include="frmBarcodeSettings.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCOAManagement.Designer.vb">
      <DependentUpon>frmCOAManagement.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCOAManagement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerBalances.Designer.vb">
      <DependentUpon>frmCustomerBalances.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerBalances.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerInvoices.Designer.vb">
      <DependentUpon>frmCustomerInvoices.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerInvoices.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerSearch.Designer.vb">
      <DependentUpon>frmCustomerSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomersUpload.Designer.vb">
      <DependentUpon>frmCustomersUpload.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomersUpload.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmDBCon.Designer.vb">
      <DependentUpon>frmDBCon.vb</DependentUpon>
    </Compile>
    <Compile Include="frmDBCon.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmGroupPermissions.Designer.vb">
      <DependentUpon>frmGroupPermissions.vb</DependentUpon>
    </Compile>
    <Compile Include="frmGroupPermissions.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmItemStock.Designer.vb">
      <DependentUpon>frmItemStock.vb</DependentUpon>
    </Compile>
    <Compile Include="frmItemStock.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmItemsTree.Designer.vb">
      <DependentUpon>frmItemsTree.vb</DependentUpon>
    </Compile>
    <Compile Include="frmItemsTree.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmItemsUpload.Designer.vb">
      <DependentUpon>frmItemsUpload.vb</DependentUpon>
    </Compile>
    <Compile Include="frmItemsUpload.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLoginNew.Designer.vb">
      <DependentUpon>frmLoginNew.vb</DependentUpon>
    </Compile>
    <Compile Include="frmLoginNew.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmMassUpload.Designer.vb">
      <DependentUpon>frmMassUpload.vb</DependentUpon>
    </Compile>
    <Compile Include="frmMassUpload.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmOpenStockInvoice.Designer.vb">
      <DependentUpon>frmOpenStockInvoice.vb</DependentUpon>
    </Compile>
    <Compile Include="frmOpenStockInvoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPurchaseInvoiceTrx.Designer.vb">
      <DependentUpon>frmPurchaseInvoiceTrx.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPurchaseInvoiceTrx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPayMethod.Designer.vb">
      <DependentUpon>frmPayMethod.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPayMethod.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPaySplit.Designer.vb">
      <DependentUpon>frmPaySplit.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPaySplit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPOS.Designer.vb">
      <DependentUpon>frmPOS.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPOS.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPOSDevices.Designer.vb">
      <DependentUpon>frmPOSDevices.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPOSDevices.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPOSSessions.Designer.vb">
      <DependentUpon>frmPOSSessions.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPOSSessions.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPOSSettings.Designer.vb">
      <DependentUpon>frmPOSSettings.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPOSSettings.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPOSShifts.Designer.vb">
      <DependentUpon>frmPOSShifts.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPOSShifts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPurchasingReturnInvoice.Designer.vb">
      <DependentUpon>frmPurchasingReturnInvoice.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPurchasingReturnInvoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSearchJE.Designer.vb">
      <DependentUpon>frmSearchJE.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSearchJE.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSearchPurchase.Designer.vb">
      <DependentUpon>frmSearchPurchase.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSearchPurchase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSearchStock.Designer.vb">
      <DependentUpon>frmSearchStock.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSearchStock.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSelectItem.Designer.vb">
      <DependentUpon>frmSelectItem.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSelectItem.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmShopsMaster.Designer.vb">
      <DependentUpon>frmShopsMaster.vb</DependentUpon>
    </Compile>
    <Compile Include="frmShopsMaster.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmStockAdjustment.Designer.vb">
      <DependentUpon>frmStockAdjustment.vb</DependentUpon>
    </Compile>
    <Compile Include="frmStockAdjustment.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmStockMovement.Designer.vb">
      <DependentUpon>frmStockMovement.vb</DependentUpon>
    </Compile>
    <Compile Include="frmStockMovement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmStockMovements.Designer.vb">
      <DependentUpon>frmStockMovements.vb</DependentUpon>
    </Compile>
    <Compile Include="frmStockMovements.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmStoresMaster.Designer.vb">
      <DependentUpon>frmStoresMaster.vb</DependentUpon>
    </Compile>
    <Compile Include="frmStoresMaster.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTafqeet.Designer.vb">
      <DependentUpon>frmTafqeet.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTafqeet.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTest.Designer.vb">
      <DependentUpon>frmTest.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTest.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTestPOS.Designer.vb">
      <DependentUpon>frmTestPOS.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTestPOS.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmToolsCOA.Designer.vb">
      <DependentUpon>frmToolsCOA.vb</DependentUpon>
    </Compile>
    <Compile Include="frmToolsCOA.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTrxExpenses.Designer.vb">
      <DependentUpon>frmTrxExpenses.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTrxExpenses.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmUserPOSItems.Designer.vb">
      <DependentUpon>frmUserPOSItems.vb</DependentUpon>
    </Compile>
    <Compile Include="frmUserPOSItems.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVendorBalances.Designer.vb">
      <DependentUpon>frmVendorBalances.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVendorBalances.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVendorInvoices.Designer.vb">
      <DependentUpon>frmVendorInvoices.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVendorInvoices.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVendors.Designer.vb">
      <DependentUpon>frmVendors.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVendors.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVendorSearch.Designer.vb">
      <DependentUpon>frmVendorSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVendorSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVendorStatement.Designer.vb">
      <DependentUpon>frmVendorStatement.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVendorStatement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVendorsUpload.Designer.vb">
      <DependentUpon>frmVendorsUpload.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVendorsUpload.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmZATCACustomerInfo.Designer.vb">
      <DependentUpon>frmZATCACustomerInfo.vb</DependentUpon>
    </Compile>
    <Compile Include="frmZATCACustomerInfo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module2.vb" />
    <Compile Include="AuthorizationModule.vb" />
    <Compile Include="rptCashStatement.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptCashStatement.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptCustomerBalances.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptCustomerBalances.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptPOSInvoiceNoQR.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPOSInvoiceNoQR.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptSalesInvoice-NoQR.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSalesInvoice-NoQR.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptVendorBalances.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptVendorBalances.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptVendorStatement.vb">
      <DependentUpon>rptVendorStatement.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptExpenses - Copy.vb">
      <DependentUpon>rptCustStatement.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptCustStatement.vb">
      <DependentUpon>rptCustStatement.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptJEStatement.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptJEStatement.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptPOSInvoice.vb">
      <DependentUpon>rptPOSInvoice.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptPOSInvoice-test.vb">
      <DependentUpon>rptPOSInvoice-test.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptPurchaseHeader.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPurchaseHeader.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptPurchaseLines.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPurchaseLines.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptSalesLines.vb">
      <DependentUpon>rptSalesLines.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptSalesHeader.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSalesHeader.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptSalesInvoice.vb">
      <DependentUpon>rptSalesInvoice.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptSalesInvoice - Backup.vb">
      <DependentUpon>rptSalesInvoice - Backup.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptSalesInvoice-Test.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSalesInvoice-Test.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptPurchaseReturnInvoice.vb">
      <DependentUpon>rptPurchaseReturnInvoice.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptPurchaseInvoice.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPurchaseInvoice.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptPurchaseReturnInvoice-1.vb">
      <DependentUpon>rptPurchaseReturnInvoice-1.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmSearchInvoices.Designer.vb">
      <DependentUpon>frmSearchInvoices.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSearchInvoices.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptNonSNStock2.vb">
      <DependentUpon>rptNonSNStock2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptNonSNStock.vb">
      <DependentUpon>rptNonSNStock.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmEmployees.Designer.vb">
      <DependentUpon>frmEmployees.vb</DependentUpon>
    </Compile>
    <Compile Include="frmEmployees.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptExpenses.vb">
      <DependentUpon>rptJEStatement.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptGLTrx.vb">
      <DependentUpon>rptGLTrx.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptCashRec.vb">
      <DependentUpon>rptCashRec.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptCashPay.vb">
      <DependentUpon>rptCashPay.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmJETrx.Designer.vb">
      <DependentUpon>frmJETrx.vb</DependentUpon>
    </Compile>
    <Compile Include="frmJETrx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCashPrintPreview.Designer.vb">
      <DependentUpon>frmCashPrintPreview.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCashPrintPreview.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCashStatement.Designer.vb">
      <DependentUpon>frmCashStatement.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCashStatement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCashStatementAllPrint.Designer.vb">
      <DependentUpon>frmCashStatementAllPrint.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCashStatementAllPrint.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCashStatementPrint.Designer.vb">
      <DependentUpon>frmCashStatementPrint.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCashStatementPrint.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangeMyPass.Designer.vb">
      <DependentUpon>frmChangeMyPass.vb</DependentUpon>
    </Compile>
    <Compile Include="frmChangeMyPass.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCompanies.Designer.vb">
      <DependentUpon>frmCompanies.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCompanies.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCreditStatement.Designer.vb">
      <DependentUpon>frmCreditStatement.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCreditStatement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCreditStatementPrint.Designer.vb">
      <DependentUpon>frmCreditStatementPrint.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCreditStatementPrint.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomers.Designer.vb">
      <DependentUpon>frmCustomers.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomers.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerStatement.Designer.vb">
      <DependentUpon>frmCustomerStatement.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerStatement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerStatementPrint.Designer.vb">
      <DependentUpon>frmCustomerStatementPrint.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerStatementPrint.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmDBMaint.Designer.vb">
      <DependentUpon>frmDBMaint.vb</DependentUpon>
    </Compile>
    <Compile Include="frmDBMaint.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmJEStatement.Designer.vb">
      <DependentUpon>frmJEStatement.vb</DependentUpon>
    </Compile>
    <Compile Include="frmJEStatement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmInvoiceReference.Designer.vb">
      <DependentUpon>frmInvoiceReference.vb</DependentUpon>
    </Compile>
    <Compile Include="frmInvoiceReference.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmItems.Designer.vb">
      <DependentUpon>frmItems.vb</DependentUpon>
    </Compile>
    <Compile Include="frmItems.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmItemsCategory.Designer.vb">
      <DependentUpon>frmItemsCategory.vb</DependentUpon>
    </Compile>
    <Compile Include="frmItemsCategory.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmItemSearch.Designer.vb">
      <DependentUpon>frmItemSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="frmItemSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLogin.Designer.vb">
      <DependentUpon>frmLogin.vb</DependentUpon>
    </Compile>
    <Compile Include="frmLogin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmMain.Designer.vb">
      <DependentUpon>frmMain.vb</DependentUpon>
    </Compile>
    <Compile Include="frmMain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPrintPreview.Designer.vb">
      <DependentUpon>frmPrintPreview.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPrintPreview.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSalesInvoiceTrx.Designer.vb">
      <DependentUpon>frmSalesInvoiceTrx.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesInvoiceTrx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSalesReturnInvoiceTrx.Designer.vb">
      <DependentUpon>frmSalesReturnInvoiceTrx.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesReturnInvoiceTrx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSetup.Designer.vb">
      <DependentUpon>frmSetup.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSetup.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmToolsChartOfAccounts.Designer.vb">
      <DependentUpon>frmToolsChartOfAccounts.vb</DependentUpon>
    </Compile>
    <Compile Include="frmToolsChartOfAccounts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmToolsGL.Designer.vb">
      <DependentUpon>frmToolsGL.vb</DependentUpon>
    </Compile>
    <Compile Include="frmToolsGL.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmToolsInvoice.Designer.vb">
      <DependentUpon>frmToolsInvoice.vb</DependentUpon>
    </Compile>
    <Compile Include="frmToolsInvoice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmToolsPrint.Designer.vb">
      <DependentUpon>frmToolsPrint.vb</DependentUpon>
    </Compile>
    <Compile Include="frmToolsPrint.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTrxCashPay.Designer.vb">
      <DependentUpon>frmTrxCashPay.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTrxCashPay.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTrxCashRec.Designer.vb">
      <DependentUpon>frmTrxCashRec.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTrxCashRec.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmUsers.Designer.vb">
      <DependentUpon>frmUsers.vb</DependentUpon>
    </Compile>
    <Compile Include="frmUsers.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module1.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="rptCustomerStatement.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptCustomerStatement.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptEmployeeSalary.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptEmployeeSalary.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptInAnalysis.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptInAnalysis.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptInAnalysis2.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptInAnalysis2.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptLoss.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptLoss.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptLossAnalysis.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptLossAnalysis.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptOpenStockInvoice.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptOpenStockInvoice.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptOutAnalysis.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptOutAnalysis.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptOutAnalysis2.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptOutAnalysis2.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptPartnersBalance.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPartnersBalance.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptPurchasing.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPurchasing.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptPurchasingItem.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPurchasingItem.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptSalesLoss.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSalesLoss.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptSalesNonBenfit.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSalesNonBenfit.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptSold.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSold.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptSoldItem.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSoldItem.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptSoldSearch.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSoldSearch.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptItemStock.vb">
      <DependentUpon>rptItemStock.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptStockMovements.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptStockMovements.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptStocks.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptStocks.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptStocks2.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptStocks2.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptStocks3.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptStocks3.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptStocksSearch.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptStocksSearch.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptStocksSearchAll.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptStocksSearchAll.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptStocksSearchOld.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptStocksSearchOld.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rptSummary.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSummary.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="SplashScreen1.Designer.vb">
      <DependentUpon>SplashScreen1.vb</DependentUpon>
    </Compile>
    <Compile Include="SplashScreen1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="‫rptCreditStatement.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>‫rptCreditStatement.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="‫rptDamage.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>‫rptDamage.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="‫rptVendorCreditPayments.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>‫rptVendorCreditPayments.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="frmAccountSearch.resx">
      <DependentUpon>frmAccountSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmActivation.resx">
      <DependentUpon>frmActivation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmBalanceUpload.resx">
      <DependentUpon>frmBalanceUpload.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmBarcodeSettings.resx">
      <DependentUpon>frmBarcodeSettings.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCOAManagement.resx">
      <DependentUpon>frmCOAManagement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerBalances.resx">
      <DependentUpon>frmCustomerBalances.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerInvoices.resx">
      <DependentUpon>frmCustomerInvoices.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerSearch.resx">
      <DependentUpon>frmCustomerSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomersUpload.resx">
      <DependentUpon>frmCustomersUpload.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmDBCon.resx">
      <DependentUpon>frmDBCon.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmGroupPermissions.resx">
      <DependentUpon>frmGroupPermissions.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmItemStock.resx">
      <DependentUpon>frmItemStock.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmItemsTree.resx">
      <DependentUpon>frmItemsTree.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmItemsUpload.resx">
      <DependentUpon>frmItemsUpload.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmLoginNew.resx">
      <DependentUpon>frmLoginNew.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmMassUpload.resx">
      <DependentUpon>frmMassUpload.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmOpenStockInvoice.resx">
      <DependentUpon>frmOpenStockInvoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPurchaseInvoiceTrx.resx">
      <DependentUpon>frmPurchaseInvoiceTrx.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPayMethod.resx">
      <DependentUpon>frmPayMethod.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPaySplit.resx">
      <DependentUpon>frmPaySplit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPOS.resx">
      <DependentUpon>frmPOS.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPOSDevices.resx">
      <DependentUpon>frmPOSDevices.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPOSSessions.resx">
      <DependentUpon>frmPOSSessions.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPOSSettings.resx">
      <DependentUpon>frmPOSSettings.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPOSShifts.resx">
      <DependentUpon>frmPOSShifts.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPurchasingReturnInvoice.resx">
      <DependentUpon>frmPurchasingReturnInvoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSearchJE.resx">
      <DependentUpon>frmSearchJE.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSearchPurchase.resx">
      <DependentUpon>frmSearchPurchase.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSearchStock.resx">
      <DependentUpon>frmSearchStock.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSelectItem.resx">
      <DependentUpon>frmSelectItem.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmShopsMaster.resx">
      <DependentUpon>frmShopsMaster.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmStockAdjustment.resx">
      <DependentUpon>frmStockAdjustment.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmStockMovement.resx">
      <DependentUpon>frmStockMovement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmStockMovements.resx">
      <DependentUpon>frmStockMovements.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmStoresMaster.resx">
      <DependentUpon>frmStoresMaster.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTafqeet.resx">
      <DependentUpon>frmTafqeet.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTest.resx">
      <DependentUpon>frmTest.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTestPOS.resx">
      <DependentUpon>frmTestPOS.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmToolsCOA.resx">
      <DependentUpon>frmToolsCOA.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTrxExpenses.resx">
      <DependentUpon>frmTrxExpenses.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmUserPOSItems.resx">
      <DependentUpon>frmUserPOSItems.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVendorBalances.resx">
      <DependentUpon>frmVendorBalances.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVendorInvoices.resx">
      <DependentUpon>frmVendorInvoices.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVendors.resx">
      <DependentUpon>frmVendors.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVendorSearch.resx">
      <DependentUpon>frmVendorSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVendorStatement.resx">
      <DependentUpon>frmVendorStatement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVendorsUpload.resx">
      <DependentUpon>frmVendorsUpload.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmZATCACustomerInfo.resx">
      <DependentUpon>frmZATCACustomerInfo.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCashStatement.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCashStatement.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCustomerBalances.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCustomerBalances.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPOSInvoiceNoQR.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPOSInvoiceNoQR.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesInvoice-NoQR.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesInvoice-NoQR.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptVendorBalances.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptVendorBalances.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptVendorStatement.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptVendorStatement.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCustStatement.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCustStatement.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPOSInvoice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPOSInvoice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPOSInvoice-test.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPOSInvoice-test.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPurchaseHeader.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPurchaseHeader.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPurchaseLines.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPurchaseLines.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesLines.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesLines.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesHeader.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesHeader.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesInvoice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesInvoice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesInvoice - Backup.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesInvoice - Backup.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesInvoice-Test.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesInvoice-Test.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPurchaseReturnInvoice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPurchaseReturnInvoice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPurchaseInvoice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPurchaseInvoice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPurchaseReturnInvoice-1.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPurchaseReturnInvoice-1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSearchInvoices.resx">
      <DependentUpon>frmSearchInvoices.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptNonSNStock2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptNonSNStock2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptNonSNStock.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptNonSNStock.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmEmployees.resx">
      <DependentUpon>frmEmployees.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptJEStatement.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptJEStatement.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptGLTrx.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptGLTrx.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCashRec.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCashRec.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCashPay.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCashPay.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmJETrx.resx">
      <DependentUpon>frmJETrx.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCashPrintPreview.resx">
      <DependentUpon>frmCashPrintPreview.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCashStatement.resx">
      <DependentUpon>frmCashStatement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCashStatementAllPrint.resx">
      <DependentUpon>frmCashStatementAllPrint.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCashStatementPrint.resx">
      <DependentUpon>frmCashStatementPrint.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmChangeMyPass.resx">
      <DependentUpon>frmChangeMyPass.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCompanies.resx">
      <DependentUpon>frmCompanies.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCreditStatement.resx">
      <DependentUpon>frmCreditStatement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCreditStatementPrint.resx">
      <DependentUpon>frmCreditStatementPrint.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomers.resx">
      <DependentUpon>frmCustomers.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerStatement.resx">
      <DependentUpon>frmCustomerStatement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerStatementPrint.resx">
      <DependentUpon>frmCustomerStatementPrint.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmDBMaint.resx">
      <DependentUpon>frmDBMaint.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmJEStatement.resx">
      <DependentUpon>frmJEStatement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmInvoiceReference.resx">
      <DependentUpon>frmInvoiceReference.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmItems.resx">
      <DependentUpon>frmItems.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmItemsCategory.resx">
      <DependentUpon>frmItemsCategory.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmItemSearch.resx">
      <DependentUpon>frmItemSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmLogin.resx">
      <DependentUpon>frmLogin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmMain.resx">
      <DependentUpon>frmMain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPrintPreview.resx">
      <DependentUpon>frmPrintPreview.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesInvoiceTrx.resx">
      <DependentUpon>frmSalesInvoiceTrx.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesReturnInvoiceTrx.resx">
      <DependentUpon>frmSalesReturnInvoiceTrx.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSetup.resx">
      <DependentUpon>frmSetup.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmToolsChartOfAccounts.resx">
      <DependentUpon>frmToolsChartOfAccounts.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmToolsGL.resx">
      <DependentUpon>frmToolsGL.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmToolsInvoice.resx">
      <DependentUpon>frmToolsInvoice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmToolsPrint.resx">
      <DependentUpon>frmToolsPrint.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTrxCashPay.resx">
      <DependentUpon>frmTrxCashPay.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTrxCashRec.resx">
      <DependentUpon>frmTrxCashRec.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmUsers.resx">
      <DependentUpon>frmUsers.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCustomerStatement.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCustomerStatement.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptEmployeeSalary.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptEmployeeSalary.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptInAnalysis.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptInAnalysis.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptInAnalysis2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptInAnalysis2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptLoss.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptLoss.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptLossAnalysis.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptLossAnalysis.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptOpenStockInvoice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptOpenStockInvoice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptOutAnalysis.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptOutAnalysis.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptOutAnalysis2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptOutAnalysis2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPartnersBalance.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPartnersBalance.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPurchasing.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPurchasing.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPurchasingItem.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPurchasingItem.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesLoss.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesLoss.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesNonBenfit.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesNonBenfit.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSold.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSold.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSoldItem.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSoldItem.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSoldSearch.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSoldSearch.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptItemStock.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptItemStock.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptStockMovements.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptStockMovements.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptStocks.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptStocks.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptStocks2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptStocks2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptStocks3.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptStocks3.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptStocksSearch.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptStocksSearch.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptStocksSearchAll.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptStocksSearchAll.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptStocksSearchOld.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptStocksSearchOld.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSummary.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSummary.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="SplashScreen1.resx">
      <DependentUpon>SplashScreen1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="‫rptCreditStatement.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>‫rptCreditStatement.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="‫rptDamage.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>‫rptDamage.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="‫rptVendorCreditPayments.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>‫rptVendorCreditPayments.vb</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="ClassDiagram1.cd" />
    <None Include="My Project\app.manifest" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{967B4E0D-AD0C-4609-AB67-0FA40C0206D8}" />
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="CrystalActiveXReportViewerLib13">
      <Guid>{B816E96D-D151-4000-BADB-53A2D8F3FC65}</Guid>
      <VersionMajor>13</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="Microsoft.Office.Core">
      <Guid>{2DF8D04C-5BFA-101B-BDE5-00AA0044DE52}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>8</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="Microsoft.Office.Interop.Excel">
      <Guid>{00020813-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>9</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="stdole">
      <Guid>{00020430-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="VBIDE">
      <Guid>{0002E157-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>5</VersionMajor>
      <VersionMinor>3</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Resources\Logos-1-03 %282%29.jpg" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <Import Project="..\packages\Microsoft.Data.SqlClient.SNI.6.0.2\build\net462\Microsoft.Data.SqlClient.SNI.targets" Condition="Exists('..\packages\Microsoft.Data.SqlClient.SNI.6.0.2\build\net462\Microsoft.Data.SqlClient.SNI.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Data.SqlClient.SNI.6.0.2\build\net462\Microsoft.Data.SqlClient.SNI.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Data.SqlClient.SNI.6.0.2\build\net462\Microsoft.Data.SqlClient.SNI.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>