using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Models;

namespace AccountingSystem.Web.Controllers
{
    /// <summary>
    /// Controller for managing web authorization (menu and quick action permissions)
    /// </summary>
    [Authorize]
    public class WebAuthorizationController : Controller
    {
        private readonly WebAuthorizationService _authService;

        public WebAuthorizationController(WebAuthorizationService authService)
        {
            _authService = authService;
        }

        /// <summary>
        /// Main authorization management page
        /// </summary>
        public async Task<IActionResult> Index()
        {
            var viewModel = new WebAuthorizationViewModel
            {
                Groups = await _authService.GetAllUserGroupsAsync(),
                MenuItems = await _authService.GetAllMenuItemsAsync(),
                QuickActions = await _authService.GetAllQuickActionsAsync()
            };

            return View(viewModel);
        }

        #region Menu Permission API Endpoints

        /// <summary>
        /// Get menu permissions for a specific group
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetGroupMenuPermissions(int groupId)
        {
            try
            {
                var permissions = await _authService.GetGroupMenuPermissionsAsync(groupId);
                return Json(new { success = true, permissions = permissions.MenuPermissions });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        /// <summary>
        /// Save menu permissions for a group
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveMenuPermissions([FromBody] SaveMenuPermissionsRequest request)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var success = await _authService.SaveMenuPermissionsAsync(
                    request.GroupId,
                    request.Permissions,
                    username
                );

                if (success)
                {
                    return Json(new { success = true, message = "تم حفظ صلاحيات القائمة بنجاح" });
                }
                else
                {
                    return Json(new { success = false, message = "حدث خطأ أثناء حفظ الصلاحيات" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"حدث خطأ: {ex.Message}" });
            }
        }

        #endregion

        #region Quick Action Permission API Endpoints

        /// <summary>
        /// Get quick action permissions for a specific group
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetGroupQuickActionPermissions(int groupId)
        {
            try
            {
                var permissions = await _authService.GetGroupQuickActionPermissionsAsync(groupId);
                return Json(new { success = true, quickActionIds = permissions.QuickActionIds });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        /// <summary>
        /// Save quick action permissions for a group
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveQuickActionPermissions([FromBody] SaveQuickActionPermissionsRequest request)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var success = await _authService.SaveQuickActionPermissionsAsync(
                    request.GroupId,
                    request.QuickActionIds,
                    username
                );

                if (success)
                {
                    return Json(new { success = true, message = "تم حفظ صلاحيات الإجراءات السريعة بنجاح" });
                }
                else
                {
                    return Json(new { success = false, message = "حدث خطأ أثناء حفظ الصلاحيات" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"حدث خطأ: {ex.Message}" });
            }
        }

        #endregion

        #region Helper Classes for Request Models

        public class SaveMenuPermissionsRequest
        {
            public int GroupId { get; set; }
            public List<MenuPermissionDto> Permissions { get; set; } = new List<MenuPermissionDto>();
        }

        public class SaveQuickActionPermissionsRequest
        {
            public int GroupId { get; set; }
            public List<int> QuickActionIds { get; set; } = new List<int>();
        }

        #endregion
    }
}

