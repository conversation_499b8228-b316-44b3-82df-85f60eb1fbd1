@using AccountingSystem.Services.ViewModels
@model List<AccountingSystem.Services.ViewModels.ChartOfAccountViewModel>

@functions {
    private List<ChartOfAccountViewModel> GetRootAccounts(List<ChartOfAccountViewModel> accounts)
    {
        return accounts.Where(a => string.IsNullOrEmpty(a.ParentAccountCode)).ToList();
    }

    private List<ChartOfAccountViewModel> GetChildren(List<ChartOfAccountViewModel> accounts, string parentCode)
    {
        return accounts.Where(a => a.ParentAccountCode == parentCode).ToList();
    }

    private void RenderAccountNode(ChartOfAccountViewModel account, List<ChartOfAccountViewModel> allAccounts, int level = 0)
    {
        var children = GetChildren(allAccounts, account.AccountCode);
        var hasChildren = children.Any();
        
        <div class="tree-node">
            <div class="tree-content" data-account-code="@account.AccountCode">
                @if (hasChildren)
                {
                    <div class="tree-toggle">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                }
                else
                {
                    <div class="tree-toggle" style="visibility: hidden;">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                }
                
                <div class="tree-icon @(account.AccountNature == "Debit" ? "bg-success" : "bg-warning")">
                    <i class="fas @(hasChildren ? "fa-folder" : "fa-file")"></i>
                </div>
                
                <div class="tree-text">
                    <div class="d-flex align-items-center">
                        <span class="fw-bold">@account.AccountCode</span>
                        <span class="ms-2">@account.AccountName</span>
                        <span class="account-badge @(account.AccountNature == "Debit" ? "account-type-debit" : "account-type-credit")">
                            @account.AccountNature
                        </span>
                    </div>
                    <small class="text-muted">
                        الرصيد: @account.OpeningBalance.ToString("N2")
                    </small>
                </div>
                
                <div class="tree-actions">
                    <a href="@Url.Action("Create", "ChartOfAccounts", new { parentCode = account.AccountCode })" 
                       class="btn btn-sm btn-outline-primary tree-action" 
                       title="إضافة حساب فرعي">
                        <i class="fas fa-plus"></i>
                    </a>
                    <a href="@Url.Action("Edit", "ChartOfAccounts", new { accountCode = account.AccountCode })" 
                       class="btn btn-sm btn-outline-secondary tree-action" 
                       title="تعديل">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button type="button" 
                            class="btn btn-sm btn-outline-danger tree-action tree-action-delete" 
                            data-account-code="@account.AccountCode"
                            data-account-name="@account.AccountName"
                            title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            
            @if (hasChildren)
            {
                <div class="tree-children" style="display: none;">
                    @foreach (var child in children.OrderBy(c => c.AccountCode))
                    {
                        RenderAccountNode(child, allAccounts, level + 1);
                    }
                </div>
            }
        </div>
    }
}

@{
    var rootAccounts = GetRootAccounts(Model);
}

@foreach (var account in rootAccounts.OrderBy(a => a.AccountCode))
{
    RenderAccountNode(account, Model);
} 