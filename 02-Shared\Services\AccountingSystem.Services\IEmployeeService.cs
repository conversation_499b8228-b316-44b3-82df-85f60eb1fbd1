using AccountingSystem.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem.Services
{
    public interface IEmployeeService
    {
        Task<List<Employee>> GetEmployeesAsync();
        Task<Employee> GetEmployeeByIdAsync(int id);
        Task<bool> CreateEmployeeAsync(Employee employee, string currentUser);
        Task<bool> UpdateEmployeeAsync(Employee employee, string currentUser);
        Task<bool> DeleteEmployeeAsync(int id);
        Task<int> GetNextEmployeeNoAsync();
    }
} 