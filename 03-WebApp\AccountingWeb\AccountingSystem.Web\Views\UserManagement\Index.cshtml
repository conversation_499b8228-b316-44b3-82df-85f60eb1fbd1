@model AccountingSystem.Web.Models.UserManagementViewModel
@{
    ViewData["Title"] = "إدارة المستخدمين";
    Layout = "_Layout";
}

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-users"></i>
                    إدارة المستخدمين
                </h1>
                <div class="page-options">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                        <i class="fas fa-plus"></i>
                        إضافة مستخدم جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">قائمة المستخدمين</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="usersTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>اسم المستخدم</th>
                                    <th>المجموعة</th>
                                    <th>المتجر الافتراضي</th>
                                    <th>العميل الافتراضي</th>
                                    <th>أقصى خصم %</th>
                                    <th>الخيارات</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in Model.Users)
                                {
                                    <tr>
                                        <td>@user.SN</td>
                                        <td>
                                            <strong>@user.Username</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@user.GroupName</span>
                                        </td>
                                        <td>@(user.DefaultStore ?? "-")</td>
                                        <td>@(user.DefaultCustomer ?? "-")</td>
                                        <td>
                                            @if (user.MaxDiscountPercent.HasValue)
                                            {
                                                <span class="badge bg-warning">@user.MaxDiscountPercent.Value%</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                <span title="يمكن تغيير المتجر" class="@((user.StoreChange ? "text-success" : "text-muted"))"><i class="fas fa-store"></i></span>
                                                <span title="يمكن تغيير العميل" class="@((user.CustomerChange ? "text-success" : "text-muted"))"><i class="fas fa-user"></i></span>
                                                <span title="يمكن تغيير الكاشير" class="@((user.CashierChange ? "text-success" : "text-muted"))"><i class="fas fa-cash-register"></i></span>
                                                <span title="يمكن تغيير سعر الفاتورة" class="@((user.ChangeInvoicePrice ? "text-success" : "text-muted"))"><i class="fas fa-tags"></i></span>
                                            </div>
                                        </td>
                                        <td>
                                            @if (user.CreatedOn.HasValue)
                                            {
                                                @user.CreatedOn.Value.ToString("yyyy/MM/dd")
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="editUser(@user.SN)" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-warning"
                                                        onclick="changePassword('@user.Username')" title="تغيير كلمة المرور">
                                                    <i class="fas fa-key"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteUser('@user.Username')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create User Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1" aria-labelledby="createUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form asp-action="CreateUser" method="post">
                <div class="modal-header">
                    <h5 class="modal-title" id="createUserModalLabel">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" dir="rtl">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="CreateUser.Username" class="form-label">اسم المستخدم</label>
                                <input asp-for="CreateUser.Username" class="form-control" required />
                                <span asp-validation-for="CreateUser.Username" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="CreateUser.GroupID" class="form-label">المجموعة</label>
                                <select asp-for="CreateUser.GroupID" class="form-select" required>
                                    <option value="">اختر المجموعة</option>
                                    @foreach (var group in Model.Groups)
                                    {
                                        <option value="@group.GroupID">@group.GroupName</option>
                                    }
                                </select>
                                <span asp-validation-for="CreateUser.GroupID" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="CreateUser.Password" class="form-label">كلمة المرور</label>
                                <input asp-for="CreateUser.Password" type="password" class="form-control" required />
                                <span asp-validation-for="CreateUser.Password" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="CreateUser.ConfirmPassword" class="form-label">تأكيد كلمة المرور</label>
                                <input asp-for="CreateUser.ConfirmPassword" type="password" class="form-control" required />
                                <span asp-validation-for="CreateUser.ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="CreateUser.DefaultStore" class="form-label">المتجر الافتراضي</label>
                                <select asp-for="CreateUser.DefaultStore" class="form-select">
                                    <option value="">اختر المتجر</option>
                                    @foreach (var store in Model.Stores)
                                    {
                                        <option value="@store.Value">@store.Text</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="CreateUser.DefaultCustomer" class="form-label">العميل الافتراضي</label>
                                <select asp-for="CreateUser.DefaultCustomer" class="form-select">
                                    <option value="">اختر العميل</option>
                                    @foreach (var customer in Model.Customers)
                                    {
                                        <option value="@customer.Value">@customer.Text</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="CreateUser.DefaultCashier" class="form-label">الكاشير الافتراضي</label>
                                <select asp-for="CreateUser.DefaultCashier" class="form-select">
                                    <option value="">اختر الكاشير</option>
                                    @foreach (var cashier in Model.Cashiers)
                                    {
                                        <option value="@cashier.Value">@cashier.Text</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="CreateUser.MaxDiscountPercent" class="form-label">أقصى نسبة خصم (%)</label>
                                <input asp-for="CreateUser.MaxDiscountPercent" type="number" step="0.01" min="0" max="100" class="form-control" />
                                <span asp-validation-for="CreateUser.MaxDiscountPercent" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check-group">
                                    <div class="form-check">
                                        <input asp-for="CreateUser.StoreChange" class="form-check-input" />
                                        <label asp-for="CreateUser.StoreChange" class="form-check-label">يمكن تغيير المتجر</label>
                                    </div>
                                    <div class="form-check">
                                        <input asp-for="CreateUser.CustomerChange" class="form-check-input" />
                                        <label asp-for="CreateUser.CustomerChange" class="form-check-label">يمكن تغيير العميل</label>
                                    </div>
                                    <div class="form-check">
                                        <input asp-for="CreateUser.CashierChange" class="form-check-input" />
                                        <label asp-for="CreateUser.CashierChange" class="form-check-label">يمكن تغيير الكاشير</label>
                                    </div>
                                    <div class="form-check">
                                        <input asp-for="CreateUser.ChangeInvoicePrice" class="form-check-input" />
                                        <label asp-for="CreateUser.ChangeInvoicePrice" class="form-check-label">يمكن تغيير سعر الفاتورة</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form asp-action="EditUser" method="post" id="editUserForm">
                @Html.AntiForgeryToken()
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">تعديل المستخدم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" dir="rtl" id="editUserBody">
                    <!-- Content will be loaded dynamically -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form asp-action="ChangePassword" method="post">
                <div class="modal-header">
                    <h5 class="modal-title" id="changePasswordModalLabel">تغيير كلمة المرور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" dir="rtl">
                    <input type="hidden" id="changePasswordUsername" name="Username" />

                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="changePasswordUsernameDisplay" readonly />
                    </div>

                    <div class="mb-3">
                        <label for="NewPassword" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" name="NewPassword" class="form-control" required />
                    </div>

                    <div class="mb-3">
                        <label for="ConfirmPassword" class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" name="ConfirmPassword" class="form-control" required />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key"></i>
                        تغيير كلمة المرور
                    </button>
                </div>
            </form>
            // Enable client-side validation for Create modal
            (function(){
                var form = document.querySelector('#createUserModal form');
                form.addEventListener('submit', function(e){
                    const pwd = form.querySelector('[name="CreateUser.Password"]').value;
                    const cpw = form.querySelector('[name="CreateUser.ConfirmPassword"]').value;
                    if(!pwd){ e.preventDefault(); alert('كلمة المرور مطلوبة'); return; }
                    if(pwd !== cpw){ e.preventDefault(); alert('كلمة المرور غير متطابقة'); }
                });
            })();

        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#usersTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
                },
                responsive: true,
                order: [[1, 'asc']], // Sort by username
                columnDefs: [
                    { orderable: false, targets: [7] } // Disable sorting on actions column
                ]
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });
            // Prepare dropdown data from server model
            var groups = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Model.Groups.Select(g => new { id = g.GroupID, text = g.GroupName })));
            var stores = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Model.Stores.Select(s => new { id = s.Value, text = s.Text })));
            var customers = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Model.Customers.Select(c => new { id = c.Value, text = c.Text })));
            var cashiers = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Model.Cashiers.Select(c => new { id = c.Value, text = c.Text })));


        function editUser(sn) {
            // Load user details via AJAX
            $.get('@Url.Action("GetUserDetails", "UserManagement")', { sn: sn })
                .done(function(data) {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }

                    // Build the edit form HTML
                    var editForm = `
                        <input type="hidden" name="SN" value="${data.sn}" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" name="Username" class="form-control" value="${data.username}" readonly />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المجموعة</label>
                                    <select name="GroupID" class="form-select" required></select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المتجر الافتراضي</label>
                                    <select name="DefaultStore" class="form-select"></select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">العميل الافتراضي</label>
                                    <select name="DefaultCustomer" class="form-select"></select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الكاشير الافتراضي</label>
                                    <select name="DefaultCashier" class="form-select"></select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">أقصى نسبة خصم (%)</label>
                                    <input type="number" name="MaxDiscountPercent" step="0.01" min="0" max="100" class="form-control" value="${data.maxDiscountPercent ?? ''}" inputmode="decimal" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check-group">
                                        <div class="form-check">
                                            <input type="hidden" name="StoreChange" value="false" />
                                            <input type="checkbox" name="StoreChange" class="form-check-input" value="true" ${data.storeChange ? 'checked' : ''} />
                                            <label class="form-check-label">يمكن تغيير المتجر</label>
                                        </div>
                                        <div class="form-check">
                                            <input type="hidden" name="CustomerChange" value="false" />
                                            <input type="checkbox" name="CustomerChange" class="form-check-input" value="true" ${data.customerChange ? 'checked' : ''} />

                                            <label class="form-check-label">يمكن تغيير العميل</label>
                                        </div>
                                        <div class="form-check">
                                            <input type="hidden" name="CashierChange" value="false" />
                                            <input type="checkbox" name="CashierChange" class="form-check-input" value="true" ${data.cashierChange ? 'checked' : ''} />
                                            <label class="form-check-label">يمكن تغيير الكاشير</label>
                                        </div>
                                        <div class="form-check">
                                            <input type="hidden" name="ChangeInvoicePrice" value="false" />
                                            <input type="checkbox" name="ChangeInvoicePrice" class="form-check-input" value="true" ${data.changeInvoicePrice ? 'checked' : ''} />
                                            <label class="form-check-label">يمكن تغيير سعر الفاتورة</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    $('#editUserBody').html(editForm);
                    // Populate selects after DOM injection
                    (function(){
                        var groupSelect = $('#editUserBody select[name="GroupID"]');
                        groupSelect.empty();
                        $.each(groups, function(_, g){
                            groupSelect.append(`<option value="${g.id}" ${g.id == data.groupID ? 'selected' : ''}>${g.text}</option>`);
                        });
                        var storeSelect = $('#editUserBody select[name="DefaultStore"]');
                        storeSelect.empty().append('<option value="">اختر المتجر</option>');
                        $.each(stores, function(_, s){
                            storeSelect.append(`<option value="${s.id}" ${s.id == data.defaultStore ? 'selected' : ''}>${s.text}</option>`);
                        });
                        var customerSelect = $('#editUserBody select[name="DefaultCustomer"]');
                        customerSelect.empty().append('<option value="">اختر العميل</option>');
                        $.each(customers, function(_, c){
                            customerSelect.append(`<option value="${c.id}" ${c.id == data.defaultCustomer ? 'selected' : ''}>${c.text}</option>`);
                        });
                        var cashierSelect = $('#editUserBody select[name="DefaultCashier"]');
                        cashierSelect.empty().append('<option value="">اختر الكاشير</option>');
                        $.each(cashiers, function(_, c){
                            cashierSelect.append(`<option value="${c.id}" ${c.id == data.defaultCashier ? 'selected' : ''}>${c.text}</option>`);
                        });
                        // Initialize Select2 if available
                        if ($.fn.select2) {
                            $('#editUserBody select').select2({ dropdownParent: $('#editUserModal'), width: '100%', dir: 'rtl' });
                        }
                    })();
                    $('#editUserModal').modal('show');
                })
                .fail(function() {
                    alert('حدث خطأ أثناء جلب بيانات المستخدم');
                });
        }

        function changePassword(username) {
            $('#changePasswordUsername').val(username);
            $('#changePasswordUsernameDisplay').val(username);
            $('#changePasswordModal').modal('show');
        }

        function deleteUser(username) {
            if (confirm('هل أنت متأكد من حذف المستخدم "' + username + '"؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                // Create a form and submit it
                var form = $('<form method="post" action="@Url.Action("DeleteUser", "UserManagement")"></form>');
                form.append('<input type="hidden" name="username" value="' + username + '" />');
                form.append('@Html.AntiForgeryToken()');
                $('body').append(form);
                        // Populate selects after DOM injection
                        (function(){
                            var groupSelect = $('#editUserBody select[name="GroupID"]');
                            groupSelect.empty();
                            $.each(groups, function(_, g){
                                groupSelect.append(`<option value="${g.id}" ${g.id == data.groupID ? 'selected' : ''}>${g.text}</option>`);
                            });
                            var storeSelect = $('#editUserBody select[name="DefaultStore"]');
                            storeSelect.empty().append('<option value="">اختر المتجر</option>');
                            $.each(stores, function(_, s){
                                storeSelect.append(`<option value="${s.id}" ${s.id == data.defaultStore ? 'selected' : ''}>${s.text}</option>`);
                            });
                            var customerSelect = $('#editUserBody select[name="DefaultCustomer"]');
                            customerSelect.empty().append('<option value="">اختر العميل</option>');
                            $.each(customers, function(_, c){
                                customerSelect.append(`<option value="${c.id}" ${c.id == data.defaultCustomer ? 'selected' : ''}>${c.text}</option>`);
                            });
                            var cashierSelect = $('#editUserBody select[name="DefaultCashier"]');
                            cashierSelect.empty().append('<option value="">اختر الكاشير</option>');
                            $.each(cashiers, function(_, c){
                                cashierSelect.append(`<option value="${c.id}" ${c.id == data.defaultCashier ? 'selected' : ''}>${c.text}</option>`);
                            });
                        })();

                form.submit();
                    // After injecting HTML, populate dynamic selects
                    var groupSelect = $('#editUserBody select[name="GroupID"]');
                    groupSelect.empty();
                    $.each(groups, function(_, g){
                        groupSelect.append(`<option value="${g.id}" ${g.id == data.groupID ? 'selected' : ''}>${g.text}</option>`);
                    });
                    var storeSelect = $('#editUserBody select[name="DefaultStore"]');
                    storeSelect.empty().append('<option value="">اختر المتجر</option>');
                    $.each(stores, function(_, s){
                        storeSelect.append(`<option value="${s.id}" ${s.id == data.defaultStore ? 'selected' : ''}>${s.text}</option>`);
                    });
                    var customerSelect = $('#editUserBody select[name="DefaultCustomer"]');
                    customerSelect.empty().append('<option value="">اختر العميل</option>');
                    $.each(customers, function(_, c){
                        customerSelect.append(`<option value="${c.id}" ${c.id == data.defaultCustomer ? 'selected' : ''}>${c.text}</option>`);
                    });
                    var cashierSelect = $('#editUserBody select[name="DefaultCashier"]');
                    cashierSelect.empty().append('<option value="">اختر الكاشير</option>');
                    $.each(cashiers, function(_, c){
                        cashierSelect.append(`<option value="${c.id}" ${c.id == data.defaultCashier ? 'selected' : ''}>${c.text}</option>`);
                    });

            }
        }
    </script>

    <style>
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #dee2e6;
        }

        .page-title {
            margin: 0;
            color: #2c3e50;
        }

        .form-check-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-check-group .form-check {
            margin-bottom: 0;
        }

        .btn-group .btn {
            margin-right: 2px;
        }

        .table th {
            background-color: #2c3e50;
            color: white;
            border-color: #34495e;
        }

        .badge {
            font-size: 0.75em;
        }

        .alert {
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .modal-content {
            border-radius: 10px;
        }

        .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
        }
    </style>
}
