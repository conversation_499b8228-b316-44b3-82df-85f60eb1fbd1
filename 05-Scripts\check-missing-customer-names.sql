-- Check customer names in sales invoices
-- This will help us understand why some invoices have missing customer names

-- Check invoices with missing customer names
SELECT 
    'Invoices with Missing Customer Names' as Section,
    TrxNo,
    TrxDate,
    PartnerName,
    PartnerNo,
    PartnerPhoneNo,
    Cashier,
    Store,
    TrxNetAmount,
    CASE 
        WHEN PartnerName IS NULL OR LTRIM(RTRIM(PartnerName)) = '' 
        THEN 'MISSING CUSTOMER NAME'
        ELSE 'CUSTOMER NAME OK'
    END as CustomerStatus
FROM tblStockMovHeader 
WHERE TrxType = 'مبيعات'
ORDER BY TrxNo DESC;

-- Check specific invoices mentioned in the issue
SELECT 
    'Specific Invoices Analysis' as Section,
    TrxNo,
    TrxDate,
    PartnerName,
    PartnerNo,
    PartnerPhoneNo,
    Cashier,
    Store,
    TrxNetAmount
FROM tblStockMovHeader 
WHERE TrxNo IN (89, 87, 86, 85, 84, 88, 95)
ORDER BY TrxNo DESC;

-- Check if there are customer records that should be linked
SELECT 
    'Customer Master Data' as Section,
    CustomerNo,
    CustomerName,
    Phone,
    Mobile,
    Status
FROM tblCustomers 
WHERE Status = 'Active'
ORDER BY CustomerName;

-- Check if PartnerNo field contains customer numbers that could be used to get names
SELECT 
    'PartnerNo Analysis' as Section,
    TrxNo,
    PartnerName,
    PartnerNo,
    PartnerPhoneNo,
    CASE 
        WHEN PartnerNo IS NOT NULL AND PartnerNo != '' 
        THEN 'HAS PARTNER NUMBER'
        ELSE 'NO PARTNER NUMBER'
    END as HasPartnerNo
FROM tblStockMovHeader 
WHERE TrxType = 'مبيعات' 
  AND (PartnerName IS NULL OR LTRIM(RTRIM(PartnerName)) = '')
ORDER BY TrxNo DESC;
