using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.Cookies;
using AccountingSystem.Data;
using AccountingSystem.Services;
using Microsoft.AspNetCore.Localization;
using System.Globalization;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews()
    .AddViewLocalization()
    .AddDataAnnotationsLocalization()
    .AddMvcOptions(options =>
    {
        // Ensure decimal binding is culture-tolerant for Arabic locales
        options.ModelBinderProviders.Insert(0, new AccountingSystem.Web.ModelBinders.InvariantDecimalModelBinderProvider());
    });

// Add Localization
builder.Services.AddLocalization(options => options.ResourcesPath = "Views");

// Add Entity Framework
builder.Services.AddDbContext<AccountingDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("AccountingDatabase")));

// Add Authentication
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/SimpleAccount/Login";
        options.LogoutPath = "/SimpleAccount/Logout";
        options.AccessDeniedPath = "/SimpleAccount/AccessDenied";
        options.ExpireTimeSpan = TimeSpan.FromHours(8);
        options.SlidingExpiration = true;
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
    });

// Add Session
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromHours(8);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Add Memory Cache
builder.Services.AddMemoryCache();

// Add custom services
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
builder.Services.AddScoped<IGlobalConfigurationService, GlobalConfigurationService>();
builder.Services.AddScoped<IApplicationStateService, ApplicationStateService>();
builder.Services.AddScoped<IChartOfAccountService, ChartOfAccountService>();
builder.Services.AddScoped<IPOSSessionService, POSSessionService>();
builder.Services.AddScoped<IPOSService, POSService>();
builder.Services.AddScoped<IGLConfigService, GLConfigService>();
builder.Services.AddScoped<IStoreServiceV2, StoreServiceV2>();
builder.Services.AddScoped<IWarehouseService, WarehouseService>();
builder.Services.AddScoped<IItemsCategoryService, ItemsCategoryService>();
builder.Services.AddScoped<IEmployeeService, EmployeeService>();
builder.Services.AddScoped<ICustomerService, CustomerService>();
builder.Services.AddScoped<IVendorService, VendorService>();
builder.Services.AddScoped<IInvoiceToolSettingService, InvoiceToolSettingService>();
builder.Services.AddScoped<IBarcodeSettingsService, BarcodeSettingsService>();
builder.Services.AddScoped<IItemsService, ItemsService>();
builder.Services.AddScoped<IPurchaseService, PurchaseService>();
builder.Services.AddScoped<IExpensesService, ExpensesService>();
builder.Services.AddScoped<WebAuthorizationService>();
builder.Services.AddScoped<WebAuthorizationHelperService>();

// Add logging
builder.Services.AddLogging();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

// Temporarily disable HTTPS redirection for development
// app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

// Localization configuration
var supportedCultures = new[] { new CultureInfo("ar"), new CultureInfo("en") };
app.UseRequestLocalization(new RequestLocalizationOptions
{
    DefaultRequestCulture = new RequestCulture("ar"),
    SupportedCultures = supportedCultures,
    SupportedUICultures = supportedCultures
});

app.UseSession();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=SimpleAccount}/{action=Login}/{id?}");

app.Run();
