﻿Imports System.IO
Imports System.Net.Mail
Imports System.Data.SqlClient
Public Class frmMain



    Private Sub frmMain_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        For Each ctr As Control In Me.Controls
            If TypeOf ctr Is MdiClient Then
                'ctr.BackColor = Color.Blue
                'ctr.BackgroundImage = My.Resources.Logos - 1 - 3(2) - Old
            End If
        Next
        lblUsername.Text = UserName
        lblDate.Text = Date.Now.Date

        ApplyUserAuthorization(Me, UserName, ConStr)

    End Sub



    Private Sub frmMain_FormClosed(ByVal sender As System.Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles MyBase.FormClosed
        Application.Exit()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Application.Exit()
    End Sub

    'Private Sub tsbSettingCOA_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSettingsCOA.Click
    '    frmToolsChartOfAccounts.MdiParent = Me
    '    frmToolsChartOfAccounts.Show()
    '    'frmToolsCOA.MdiParent = Me
    '    'frmToolsCOA.Show()
    'End Sub

    Private Sub tsbSettingIntegrationGL_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSettingsIntegrationGL.Click
        frmToolsGL.MdiParent = Me
        frmToolsGL.Show()
    End Sub

    Private Sub tsbSettingPrinters_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSettingsPrinters.Click
        frmToolsPrint.MdiParent = Me
        frmToolsPrint.Show()
    End Sub


    Private Sub tsbSettingInvoices_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSettingsInvoices.Click
        frmToolsInvoice.MdiParent = Me
        frmToolsInvoice.Show()
    End Sub

    Private Sub tsbSalesReturn_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSalesReturn.Click
        frmSalesReturnInvoiceTrx.MdiParent = Me
        frmSalesReturnInvoiceTrx.Show()
    End Sub
    Private Sub tsbSalesInvoice_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSalesInvoice.Click
        frmSalesInvoiceTrx.MdiParent = Me
        frmSalesInvoiceTrx.Show()
    End Sub

    Private Sub tsbMasterCustomers_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnMasterCustomers.Click
        IsNewCustomer = "No"
        frmCustomers.MdiParent = Me
        frmCustomers.Show()
    End Sub

    Private Sub tsbMasterItems_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnMasterItems.Click
        frmItems.MdiParent = Me
        frmItems.Show()
    End Sub

    Private Sub tsbSettingCOANew_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSettingsCOANew.Click
        frmCOAManagement.MdiParent = Me
        frmCOAManagement.Show()
    End Sub
    Private Sub tsbJEPayment_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnCashTrxPay.Click
        frmTrxCashPay.MdiParent = Me
        frmTrxCashPay.Show()
    End Sub

    Private Sub tsbSettingUsers_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSettingsUsers.Click
        frmUsers.MdiParent = Me
        frmUsers.Show()
    End Sub

    Private Sub tsbJEReceipt_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnCashTrxReceipt.Click
        frmTrxCashRec.MdiParent = Me
        frmTrxCashRec.Show()
    End Sub


    Private Sub tsbReportsJEStatmnt_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReportsStatementsJE.Click
        frmJEStatement.MdiParent = Me
        frmJEStatement.Show()
    End Sub

    Private Sub tsbSettingChangeMyPass_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSettingsChangeMyPass.Click
        frmChangeMyPass.MdiParent = Me
        frmChangeMyPass.Show()
    End Sub

    Private Sub tsbSettingBackup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSettingsBackup.Click
        frmDBMaint.MdiParent = Me
        frmDBMaint.Show()
    End Sub


    Private Sub tsbJE_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCashTrxJE.Click
        frmJETrx.MdiParent = Me
        frmJETrx.Show()
    End Sub


    Private Sub tsbSearchInvoices_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearchSales.Click
        frmSearchInvoices.MdiParent = Me
        frmSearchInvoices.Show()
    End Sub
    Private Sub btnPurchaseReturn_Click(sender As Object, e As EventArgs) Handles btnPurchaseReturn.Click
        frmPurchasingReturnInvoice.MdiParent = Me
        frmPurchasingReturnInvoice.Show()
    End Sub
    Private Sub tsbSettingConnection_Click(sender As Object, e As EventArgs) Handles btnSettingsConnection.Click
        frmDBCon.MdiParent = Me
        frmDBCon.Show()
    End Sub
    Public Sub ApplyMainFormPermissionsByUser(mainForm As Form, username As String)
        Try
            ' 1. جلب GroupID من اسم المستخدم
            Dim groupID As Integer = -1
            Using cmd As New SqlCommand("SELECT GroupID FROM tblUsers WHERE Username = @Username", Con)
                cmd.Parameters.AddWithValue("@Username", username)
                OpenConnection()
                Dim result = cmd.ExecuteScalar()
                If result IsNot Nothing Then groupID = Convert.ToInt32(result)
                CloseConnection()
            End Using

            ' 2. إذا المستخدم في المجموعة رقم 1 → لا نطبق صلاحيات
            If groupID = 1 Then Exit Sub

            ' 3. إخفاء جميع الأزرار المذكورة في جدول tblForms (بغض النظر عن وجودها أو لا)
            Dim sqlHide As String = "SELECT FormName FROM tblForms"
            Using cmd As New SqlCommand(sqlHide, Con)
                OpenConnection()
                Dim reader = cmd.ExecuteReader()
                While reader.Read()
                    Dim btnName As String = reader("FormName").ToString().Trim()
                    Dim ctrl = mainForm.Controls.Item(btnName)
                    If ctrl IsNot Nothing AndAlso TypeOf ctrl Is Button Then
                        ctrl.Visible = False
                    End If
                End While
                reader.Close()
                CloseConnection()
            End Using

            ' 4. إظهار الأزرار المصرح بها فقط من جدول tblGroupFormPermissions
            Dim sqlShow As String = "
            SELECT F.FormName 
            FROM tblGroupFormPermissions P
            INNER JOIN tblForms F ON F.FormID = P.FormID
            WHERE P.GroupID = @GroupID"
            Using cmd As New SqlCommand(sqlShow, Con)
                cmd.Parameters.AddWithValue("@GroupID", groupID)
                OpenConnection()
                Dim reader = cmd.ExecuteReader()
                While reader.Read()
                    Dim btnName As String = reader("FormName").ToString().Trim()
                    Dim ctrl = mainForm.Controls.Item(btnName)
                    If ctrl IsNot Nothing AndAlso TypeOf ctrl Is Button Then
                        ctrl.Visible = True
                    End If
                End While
                reader.Close()
                CloseConnection()
            End Using



        Catch ex As Exception
            MsgBox("⚠️ خطأ أثناء تحميل الصلاحيات: " & ex.Message)
            CloseConnection()
        End Try
    End Sub

    Private Sub tsbPurchasing_Click(sender As Object, e As EventArgs)

    End Sub

    'Private Sub TafqeetToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles btnTest.Click
    '    frmTafqeet.ShowDialog()
    'End Sub


    Private Sub tsbSettingGeneral_Click(sender As Object, e As EventArgs) Handles btnSettingsGeneral.Click
        frmSetup.MdiParent = Me
        frmSetup.Show()
    End Sub


    Private Sub tsbSettingActivation_Click(sender As Object, e As EventArgs) Handles btnSettingsActivation.Click
        frmActivation.MdiParent = Me
        frmActivation.Show()
    End Sub
    Private Sub tsbSettingGroups_Click(sender As Object, e As EventArgs) Handles btnSettingsGroups.Click
        frmGroupPermissions.MdiParent = Me
        frmGroupPermissions.Show()
    End Sub

    Private Sub tsbReportsCustomerStatmnt_Click(sender As Object, e As EventArgs) Handles btnReportsStatementsCustomers.Click
        frmCustomerStatement.MdiParent = Me
        frmCustomerStatement.Show()
    End Sub
    Private Sub tsbReportsVendorStatmnt_Click(sender As Object, e As EventArgs) Handles btnReportsStatementsVendor.Click
        frmVendorStatement.MdiParent = Me
        frmVendorStatement.Show()
    End Sub

    Private Sub tsbReportsCashierStatmnt_Click(sender As Object, e As EventArgs) Handles btnReportsStatementsCashier.Click
        frmCashStatement.MdiParent = Me
        frmCashStatement.Show()
    End Sub

    Private Sub tsbMasterItemsCategory_Click(sender As Object, e As EventArgs) Handles btnMasterItemsCategory.Click
        frmItemsCategory.MdiParent = Me
        frmItemsCategory.Show()
    End Sub


    Private Sub tsbMasterVendors_Click(sender As Object, e As EventArgs) Handles btnMasterVendors.Click
        frmVendors.MdiParent = Me
        frmVendors.Show()
    End Sub

    Private Sub btnStockTransfer_Click(sender As Object, e As EventArgs) Handles btnStockTransfer.Click
        frmStockMovement.MdiParent = Me
        frmStockMovement.Show()
    End Sub


    Private Sub tsbSettingBarcode_Click(sender As Object, e As EventArgs) Handles btnSettingsBarcode.Click
        frmBarcodeSettings.MdiParent = Me
        frmBarcodeSettings.Show()
    End Sub


    Private Sub tsbSettingPayMthd_Click(sender As Object, e As EventArgs) Handles btnSettingsPayMthd.Click
        frmPayMethod.MdiParent = Me
        frmPayMethod.Show()

    End Sub

    Private Sub tsbSettingPOSGeneral_Click(sender As Object, e As EventArgs) Handles btnSettingsPOSGeneral.Click
        frmPOSSettings.MdiParent = Me
        frmPOSSettings.Show()
    End Sub

    Private Sub tsbSettingPOSDevices_Click(sender As Object, e As EventArgs) Handles btnSettingsPOSDevices.Click
        frmPOSDevices.MdiParent = Me
        frmPOSDevices.Show()
    End Sub


    Private Sub tsbSettingPOSShifts_Click(sender As Object, e As EventArgs) Handles btnSettingsPOSShifts.Click
        frmPOSShifts.MdiParent = Me
        frmPOSShifts.Show()
    End Sub

    Private Sub tsbSettingPOSSessions_click(sender As Object, e As EventArgs) Handles btnSettingsPOSSessions.Click
        frmPOSSessions.MdiParent = Me
        frmPOSSessions.Show()
    End Sub

    Private Sub tsbsalespos_Click(sender As Object, e As EventArgs) Handles btnSalesPOS.Click
        frmPOS.MdiParent = Me
        frmPOS.Show()
    End Sub

    Private Sub tsbMasterUsersItems_Click(sender As Object, e As EventArgs) Handles btnMasterUsersItems.Click
        frmUserPOSItems.MdiParent = Me
        frmUserPOSItems.Show()
    End Sub

    Private Sub tsbSettingShops_Click(sender As Object, e As EventArgs) Handles btnSettingsShops.Click
        frmShopsMaster.MdiParent = Me
        frmShopsMaster.Show()
    End Sub


    Private Sub tsbSettingStores_Click(sender As Object, e As EventArgs) Handles btnSettingsStores.Click
        frmStoresMaster.MdiParent = Me
        frmStoresMaster.Show()
    End Sub

    Private Sub tsbPurcahseInvoice_Click(sender As Object, e As EventArgs) Handles btnPurchaseInvoice.Click
        frmPurchaseInvoiceTrx.MdiParent = Me
        frmPurchaseInvoiceTrx.Show()
    End Sub

    '

    Private Sub btnOpenStocke_Click(sender As Object, e As EventArgs) Handles btnStockOpen.Click
        frmOpenStockInvoice.MdiParent = Me
        frmOpenStockInvoice.Show()
    End Sub
    Private Sub tsbReportsStockBalance_Click(sender As Object, e As EventArgs) Handles btnReportsStockBalance.Click
        frmItemStock.MdiParent = Me
        frmItemStock.Show()
    End Sub

    Private Sub btnReportsStatementsCustomerBalance_Click(sender As Object, e As EventArgs) Handles btnReportsStatementsCustomerBalance.Click
        frmCustomerBalances.MdiParent = Me
        frmCustomerBalances.Show()
    End Sub
    Private Sub btnReportsStatementsVendorBalance_Click(sender As Object, e As EventArgs) Handles btnReportsStatementsVendorBalance.Click
        frmVendorBalances.MdiParent = Me
        frmVendorBalances.Show()
    End Sub
    Private Sub tsbMasterEmployees_Click(sender As Object, e As EventArgs) Handles btnMasterEmployees.Click
        frmEmployees.MdiParent = Me
        frmEmployees.Show()
    End Sub
    Private Sub tsbJEExpenses_Click(sender As Object, e As EventArgs) Handles btnCashTrxExpenses.Click
        frmTrxExpenses.MdiParent = Me
        frmTrxExpenses.Show()
    End Sub

    Private Sub btnSalesManageInvoices_Click(sender As Object, e As EventArgs) Handles btnSalesManageInvoices.Click
        frmCustomerInvoices.MdiParent = Me
        frmCustomerInvoices.Show()
    End Sub

    Private Sub btnPurchaseManageInvoices_Click(sender As Object, e As EventArgs) Handles btnPurchaseManageInvoices.Click
        frmVendorInvoices.MdiParent = Me
        frmVendorInvoices.Show()
    End Sub


    Private Sub btnSettingsUploadDataItems_Click(sender As Object, e As EventArgs) Handles btnSettingsUploadDataItems.Click
        Dim uploadForm As New frmMassUpload()
        uploadForm.ShowDialog()
    End Sub
    Private Sub btnSettingsUploadDataCustomers_Click(sender As Object, e As EventArgs) Handles btnSettingsUploadDataCustomers.Click
        frmCustomersUpload.MdiParent = Me
        frmCustomersUpload.Show()
    End Sub
    Private Sub btnSettingsUploadDataVendors_Click(sender As Object, e As EventArgs) Handles btnSettingsUploadDataVendors.Click
        frmVendorsUpload.MdiParent = Me
        frmVendorsUpload.Show()
    End Sub

    Private Sub btnSearchPurchases_Click(sender As Object, e As EventArgs) Handles btnSearchPurchases.Click
        frmSearchPurchase.MdiParent = Me
        frmSearchPurchase.Show()
    End Sub


    Private Sub btnSearchStock_Click(sender As Object, e As EventArgs) Handles btnSearchStock.Click
        frmSearchStock.MdiParent = Me
        frmSearchStock.Show()
    End Sub
    'Private Sub btnNewMain_Click(sender As Object, e As EventArgs) Handles btnNewMain.Click
    '    ' This button can be used to open a new main form or perform any other action
    '    Dim newMainForm As New frmMain_new()
    '    newMainForm.Show()
    'End Sub

    Private Sub btnReportsStockMovements_Click(sender As Object, e As EventArgs) Handles btnReportsStockMovements.Click
        frmStockMovements.MdiParent = Me
        frmStockMovements.Show()
    End Sub

    Private Sub btnSearchJE_Click(sender As Object, e As EventArgs) Handles btnSearchJE.Click
        frmSearchJE.MdiParent = Me
        frmSearchJE.Show()
    End Sub

    Private Sub btnStockAdjustment_Click(sender As Object, e As EventArgs) Handles btnStockAdjustment.Click
        frmStockAdjustment.MdiParent = Me
        frmStockAdjustment.Show()
    End Sub

    Private Sub btnSettingsUploadOpeningCustomers_Click(sender As Object, e As EventArgs) Handles btnSettingsUploadOpeningCustomers.Click
        frmBalanceUpload.MdiParent = Me
        frmBalanceUpload.Show()
    End Sub

End Class