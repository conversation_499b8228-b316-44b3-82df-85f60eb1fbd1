﻿' VB.NET: frmZATCACustomerInfo.vb
Imports System.Data.SqlClient

Public Class frmZATCACustomerInfo
    Public Property CustomerID As Integer
    Public Property CustomerName As String
    Public Property CustomerNumber As String
    Public Property CRNumber As String

    Private Sub frmZATCACustomerInfo_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        txtCustomerName.Text = CustomerName
        txtCRNumber.Text = CRNumber
        txtCustomerNumber.Text = CustomerNumber

        txtCustomerName.ReadOnly = True
        txtCRNumber.ReadOnly = True
        txtCustomerNumber.ReadOnly = True

        cmbCountryCode.Items.Add("SA")
        cmbCountryCode.SelectedIndex = 0
        LoadZATCACustomerInfo()

    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If String.IsNullOrWhiteSpace(txtVATNumber.Text) OrElse txtVATNumber.Text.Length <> 15 Then
            MessageBox.Show("VAT Number is required and must be 15 digits.", "Validation Error")
            Exit Sub
        End If

        If Not String.IsNullOrWhiteSpace(txtEmail.Text) AndAlso Not txtEmail.Text.Contains("@") Then
            MessageBox.Show("Invalid email format.", "Validation Error")
            Exit Sub
        End If

        SaveZATCACustomerInfo()
        Me.DialogResult = DialogResult.OK
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub
    Private Sub LoadZATCACustomerInfo()
        Using conn As New SqlConnection(Constr)
            conn.Open()
            Dim cmd As New SqlCommand("SELECT * FROM tblZATCACustomerInfo WHERE CustomerID = @CustomerID", conn)
            cmd.Parameters.AddWithValue("@CustomerID", CustomerID)

            Using reader As SqlDataReader = cmd.ExecuteReader()
                If reader.Read() Then
                    txtVATNumber.Text = reader("VATNumber").ToString()
                    cmbCountryCode.Text = reader("CountryCode").ToString()
                    txtBuildingNumber.Text = reader("BuildingNumber").ToString()
                    txtStreetName.Text = reader("StreetName").ToString()
                    txtCityName.Text = reader("CityName").ToString()
                    txtPostalCode.Text = reader("PostalCode").ToString()
                    txtEmail.Text = reader("Email").ToString()
                    txtPhoneNumber.Text = reader("PhoneNumber").ToString()
                End If
            End Using
        End Using
    End Sub

    Private Sub SaveZATCACustomerInfo()
        Using conn As New SqlConnection(Constr)
            conn.Open()
            Dim cmd As New SqlCommand("""
                INSERT INTO tblZATCACustomerInfo
                (CustomerID, CustomerNumber, CustomerName, CRNumber, VATNumber, CountryCode, BuildingNumber, StreetName, CityName, PostalCode, Email, PhoneNumber, CreatedBy)
                VALUES (@CustomerID, @CustomerNumber, @CustomerName, @CRNumber, @VATNumber, @CountryCode, @BuildingNumber, @StreetName, @CityName, @PostalCode, @Email, @PhoneNumber, @CreatedBy)
            """, conn)

            cmd.Parameters.AddWithValue("@CustomerID", CustomerID)
            cmd.Parameters.AddWithValue("@CustomerNumber", txtCustomerNumber.Text)
            cmd.Parameters.AddWithValue("@CustomerName", txtCustomerName.Text)
            cmd.Parameters.AddWithValue("@CRNumber", txtCRNumber.Text)
            cmd.Parameters.AddWithValue("@VATNumber", txtVATNumber.Text)
            cmd.Parameters.AddWithValue("@CountryCode", cmbCountryCode.Text)
            cmd.Parameters.AddWithValue("@BuildingNumber", txtBuildingNumber.Text)
            cmd.Parameters.AddWithValue("@StreetName", txtStreetName.Text)
            cmd.Parameters.AddWithValue("@CityName", txtCityName.Text)
            cmd.Parameters.AddWithValue("@PostalCode", txtPostalCode.Text)
            cmd.Parameters.AddWithValue("@Email", txtEmail.Text)
            cmd.Parameters.AddWithValue("@PhoneNumber", txtPhoneNumber.Text)
            cmd.Parameters.AddWithValue("@CreatedBy", UserName)

            cmd.ExecuteNonQuery()
        End Using
    End Sub
End Class
