using System;
using System.Text;

namespace AccountingSystem.Web.Utilities
{
    /// <summary>
    /// Utility class to generate VB.NET-compatible hash codes for password authentication
    /// This addresses the difference between VB.NET and C# GetHashCode() implementations
    /// </summary>
    public static class VBNetHashCompatibility
    {
        /// <summary>
        /// Generates a hash code that matches VB.NET's String.GetHashCode() behavior
        /// VB.NET uses a different algorithm than C# for string hash codes
        /// </summary>
        /// <param name="input">The string to hash</param>
        /// <returns>Hash code compatible with VB.NET</returns>
        public static int GetVBNetHashCode(string input)
        {
            if (string.IsNullOrEmpty(input))
                return 0;

            // VB.NET uses a different hashing algorithm
            // This implementation attempts to replicate VB.NET's behavior
            unchecked
            {
                int hash = 0;
                int len = input.Length;
                
                // VB.NET's string hash algorithm (simplified version)
                for (int i = 0; i < len; i++)
                {
                    hash = (hash << 5) - hash + input[i];
                }
                
                return hash;
            }
        }

        /// <summary>
        /// Alternative VB.NET hash implementation based on observed patterns
        /// </summary>
        /// <param name="input">The string to hash</param>
        /// <returns>Alternative hash code</returns>
        public static int GetVBNetHashCodeAlternative(string input)
        {
            if (string.IsNullOrEmpty(input))
                return 0;

            unchecked
            {
                int hash1 = 5381;
                int hash2 = hash1;

                for (int i = 0; i < input.Length; i += 2)
                {
                    hash1 = ((hash1 << 5) + hash1) ^ input[i];
                    if (i == input.Length - 1)
                        break;
                    hash2 = ((hash2 << 5) + hash2) ^ input[i + 1];
                }

                return hash1 + (hash2 * 1566083941);
            }
        }

        /// <summary>
        /// Attempts to match a password against stored VB.NET hash using multiple algorithms
        /// </summary>
        /// <param name="password">The password to verify</param>
        /// <param name="storedHash">The stored hash from database</param>
        /// <returns>True if password matches any VB.NET hash algorithm</returns>
        public static bool VerifyVBNetPassword(string password, string storedHash)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(storedHash))
                return false;

            var trimmedPassword = password.Trim();

            // Try multiple VB.NET hash algorithms
            var algorithms = new Func<string, int>[]
            {
                GetVBNetHashCode,
                GetVBNetHashCodeAlternative,
                // Also try C# hash for newer passwords
                s => s.GetHashCode()
            };

            foreach (var algorithm in algorithms)
            {
                var computedHash = algorithm(trimmedPassword).ToString();
                if (computedHash == storedHash)
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Generates password hash using the primary VB.NET algorithm
        /// </summary>
        /// <param name="password">Password to hash</param>
        /// <returns>Hash string compatible with VB.NET</returns>
        public static string HashPasswordForVBNet(string password)
        {
            if (string.IsNullOrEmpty(password))
                return string.Empty;

            return GetVBNetHashCode(password.Trim()).ToString();
        }

        /// <summary>
        /// Test method to verify hash compatibility with known values
        /// </summary>
        /// <param name="password">Password to test</param>
        /// <param name="expectedHash">Expected hash value</param>
        /// <returns>Test result information</returns>
        public static (bool IsMatch, string[] ComputedHashes) TestHashCompatibility(string password, string expectedHash)
        {
            var trimmedPassword = password.Trim();
            var computedHashes = new[]
            {
                $"VBNet Primary: {GetVBNetHashCode(trimmedPassword)}",
                $"VBNet Alternative: {GetVBNetHashCodeAlternative(trimmedPassword)}",
                $"C# Standard: {trimmedPassword.GetHashCode()}",
                $"Expected: {expectedHash}"
            };

            bool isMatch = VerifyVBNetPassword(password, expectedHash);
            return (isMatch, computedHashes);
        }

        /// <summary>
        /// Reverse engineering method to find the correct VB.NET hash algorithm
        /// by testing known password-hash pairs
        /// </summary>
        /// <param name="knownPairs">Dictionary of known password-hash pairs</param>
        /// <returns>Analysis results</returns>
        public static string AnalyzeHashPatterns(Dictionary<string, string> knownPairs)
        {
            var results = new StringBuilder();
            results.AppendLine("VB.NET Hash Pattern Analysis:");
            results.AppendLine("============================");

            foreach (var pair in knownPairs)
            {
                var password = pair.Key;
                var expectedHash = pair.Value;
                
                results.AppendLine($"\nPassword: '{password}'");
                results.AppendLine($"Expected Hash: {expectedHash}");
                
                var (isMatch, hashes) = TestHashCompatibility(password, expectedHash);
                results.AppendLine($"Match Found: {isMatch}");
                
                foreach (var hash in hashes)
                {
                    results.AppendLine($"  {hash}");
                }
            }

            return results.ToString();
        }
    }
}
