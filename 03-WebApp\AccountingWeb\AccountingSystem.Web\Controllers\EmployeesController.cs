using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Models;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    [Route("Employees")]
    public class EmployeesController : Controller
    {
        private readonly IEmployeeService _employeeService;
        private readonly ILogger<EmployeesController> _logger;

        public EmployeesController(IEmployeeService employeeService, ILogger<EmployeesController> logger)
        {
            _employeeService = employeeService;
            _logger = logger;
        }

        [HttpGet("")]
        public async Task<IActionResult> Index()
        {
            var employees = await _employeeService.GetEmployeesAsync();
            return View(employees);
        }

        [HttpPost("Create")]
        public async Task<IActionResult> CreateEmployee(Employee employee)
        {
            if (!ModelState.IsValid)
            {
                TempData["ErrorMessage"] = "البيانات المدخلة غير صالحة.";
                return RedirectToAction(nameof(Index));
            }

            var currentUser = User.Identity?.Name ?? "System";
            var success = await _employeeService.CreateEmployeeAsync(employee, currentUser);

            if (success)
            {
                TempData["SuccessMessage"] = "تمت إضافة الموظف بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في إضافة الموظف.";
            }

            return RedirectToAction(nameof(Index));
        }
        
        [HttpPost("Edit")]
        public async Task<IActionResult> EditEmployee(Employee employee)
        {
            if (!ModelState.IsValid)
            {
                TempData["ErrorMessage"] = "البيانات المدخلة غير صالحة.";
                return RedirectToAction(nameof(Index));
            }
            
            var currentUser = User.Identity?.Name ?? "System";
            var success = await _employeeService.UpdateEmployeeAsync(employee, currentUser);

            if (success)
            {
                TempData["SuccessMessage"] = "تم تحديث بيانات الموظف بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في تحديث بيانات الموظف.";
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpPost("Delete")]
        public async Task<IActionResult> DeleteEmployee(int id)
        {
            var success = await _employeeService.DeleteEmployeeAsync(id);

            if (success)
            {
                TempData["SuccessMessage"] = "تم حذف الموظف بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = "فشل في حذف الموظف.";
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpGet("Details/{id}")]
        public async Task<IActionResult> GetEmployeeDetails(int id)
        {
            var employee = await _employeeService.GetEmployeeByIdAsync(id);
            if (employee == null)
            {
                return NotFound();
            }
            return Json(employee);
        }

        [HttpGet("NextEmployeeNo")]
        public async Task<IActionResult> GetNextEmployeeNo()
        {
            var nextNo = await _employeeService.GetNextEmployeeNoAsync();
            return Json(new { nextNo });
        }
    }
} 