using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AccountingSystem.Services
{
    public class DatabaseSeederService
    {
        private readonly AccountingDbContext _context;
        private readonly ILogger<DatabaseSeederService> _logger;

        public DatabaseSeederService(AccountingDbContext context, ILogger<DatabaseSeederService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task SeedMenuDataAsync()
        {
            try
            {
                // Clear existing menu data to recreate with new structure
                if (await _context.WebFormMenus.AnyAsync())
                {
                    _logger.LogInformation("Clearing existing menu data to recreate...");
                    var existingMenus = await _context.WebFormMenus.ToListAsync();
                    _context.WebFormMenus.RemoveRange(existingMenus);

                    var existingPermissions = await _context.WebGroupMenuPermissions.ToListAsync();
                    _context.WebGroupMenuPermissions.RemoveRange(existingPermissions);

                    await _context.SaveChangesAsync();
                }

                _logger.LogInformation("Seeding menu data...");

                // Seed admin group first
                await SeedAdminGroupAsync();

                // Seed admin users
                await SeedAdminUsersAsync();

                // Create main menu items exactly as shown in screenshots
                var dashboardMenu = new WebFormMenu
                {
                    DisplayName = "الصفحة الرئيسية",
                    DisplayNameEn = "Dashboard",
                    Route = "/SimpleDashboard/Index",
                    Icon = "fas fa-home",
                    DisplayOrder = 1,
                    ModuleName = "Dashboard",
                    IsContainer = false,
                    IsActive = true
                };
                _context.WebFormMenus.Add(dashboardMenu);

                // Sales Section (Container)
                var salesContainer = new WebFormMenu
                {
                    DisplayName = "المبيعات",
                    DisplayNameEn = "Sales",
                    Route = "#",
                    Icon = "fas fa-shopping-cart",
                    DisplayOrder = 2,
                    ModuleName = "Sales",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(salesContainer);

                // Purchase Section (Container)
                var purchaseContainer = new WebFormMenu
                {
                    DisplayName = "المشتريات",
                    DisplayNameEn = "Purchase",
                    Route = "#",
                    Icon = "fas fa-shopping-cart",
                    DisplayOrder = 3,
                    ModuleName = "Purchase",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(purchaseContainer);

                // Cash Movements Section (Container)
                var cashContainer = new WebFormMenu
                {
                    DisplayName = "الحركات النقدية",
                    DisplayNameEn = "Cash Movements",
                    Route = "#",
                    Icon = "fas fa-money-bill-wave",
                    DisplayOrder = 4,
                    ModuleName = "Cash",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(cashContainer);

                // Master Data Section (Container)
                var masterDataContainer = new WebFormMenu
                {
                    DisplayName = "البيانات الأساسية",
                    DisplayNameEn = "Master Data",
                    Route = "#",
                    Icon = "fas fa-database",
                    DisplayOrder = 5,
                    ModuleName = "MasterData",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(masterDataContainer);

                // Reports Section (Container)
                var reportsContainer = new WebFormMenu
                {
                    DisplayName = "التقارير",
                    DisplayNameEn = "Reports",
                    Route = "#",
                    Icon = "fas fa-chart-line",
                    DisplayOrder = 6,
                    ModuleName = "Reports",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(reportsContainer);

                // Settings Section (Container)
                var settingsContainer = new WebFormMenu
                {
                    DisplayName = "الإعدادات",
                    DisplayNameEn = "Settings",
                    Route = "#",
                    Icon = "fas fa-cog",
                    DisplayOrder = 7,
                    ModuleName = "Settings",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(settingsContainer);

                // Purchase Section (Container)
                var purchaseContainer = new WebFormMenu
                {
                    DisplayName = "المشتريات",
                    DisplayNameEn = "Purchase",
                    Route = "#",
                    Icon = "fas fa-truck",
                    DisplayOrder = 3,
                    ModuleName = "Purchase",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(purchaseContainer);

                // Cash Section (Container)
                var cashContainer = new WebFormMenu
                {
                    DisplayName = "الخزينة والبنوك",
                    DisplayNameEn = "Cash & Banks",
                    Route = "#",
                    Icon = "fas fa-university",
                    DisplayOrder = 4,
                    ModuleName = "Cash",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(cashContainer);

                // Master Data Section (Container)
                var masterDataContainer = new WebFormMenu
                {
                    DisplayName = "البيانات الأساسية",
                    DisplayNameEn = "Master Data",
                    Route = "#",
                    Icon = "fas fa-database",
                    DisplayOrder = 5,
                    ModuleName = "MasterData",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(masterDataContainer);

                // Reports Section (Container)
                var reportsContainer = new WebFormMenu
                {
                    DisplayName = "التقارير",
                    DisplayNameEn = "Reports",
                    Route = "#",
                    Icon = "fas fa-chart-bar",
                    DisplayOrder = 6,
                    ModuleName = "Reports",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(reportsContainer);

                // Settings Section (Container)
                var settingsContainer = new WebFormMenu
                {
                    DisplayName = "الإعدادات",
                    DisplayNameEn = "Settings",
                    Route = "#",
                    Icon = "fas fa-cog",
                    DisplayOrder = 7,
                    ModuleName = "Settings",
                    IsContainer = true,
                    IsActive = true
                };
                _context.WebFormMenus.Add(settingsContainer);

                // Save containers first to get their IDs
                await _context.SaveChangesAsync();

                // Add Sales sub-items (exactly as shown in screenshot)
                var salesSubItems = new[]
                {
                    new WebFormMenu { DisplayName = "فواتير المبيعات", DisplayNameEn = "Sales Invoices", Route = "/Sales/Invoice", Icon = "fas fa-file-invoice", ParentMenuId = salesContainer.Id, DisplayOrder = 1, ModuleName = "Sales", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "نقاط البيع", DisplayNameEn = "POS", Route = "/POS/Index", Icon = "fas fa-cash-register", ParentMenuId = salesContainer.Id, DisplayOrder = 2, ModuleName = "Sales", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "جلسات نقاط البيع", DisplayNameEn = "POS Sessions", Route = "/POSSessions/Index", Icon = "fas fa-clock", ParentMenuId = salesContainer.Id, DisplayOrder = 3, ModuleName = "Sales", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "مرتجع المبيعات", DisplayNameEn = "Sales Return", Route = "/Sales/Return", Icon = "fas fa-undo", ParentMenuId = salesContainer.Id, DisplayOrder = 4, ModuleName = "Sales", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "بحث المبيعات", DisplayNameEn = "Sales Search", Route = "/Sales/Search", Icon = "fas fa-search", ParentMenuId = salesContainer.Id, DisplayOrder = 5, ModuleName = "Sales", IsContainer = false, IsActive = true }
                };
                _context.WebFormMenus.AddRange(salesSubItems);

                // Add Purchase sub-items (exactly as shown in screenshot)
                var purchaseSubItems = new[]
                {
                    new WebFormMenu { DisplayName = "إنشاء فاتورة مشتريات", DisplayNameEn = "Create Purchase Invoice", Route = "/Purchase/Create", Icon = "fas fa-plus", ParentMenuId = purchaseContainer.Id, DisplayOrder = 1, ModuleName = "Purchase", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "عرض فواتير المشتريات", DisplayNameEn = "View Purchase Invoices", Route = "/Purchase/Index", Icon = "fas fa-list", ParentMenuId = purchaseContainer.Id, DisplayOrder = 2, ModuleName = "Purchase", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "مرتجع المشتريات", DisplayNameEn = "Purchase Return", Route = "/Purchase/Return", Icon = "fas fa-undo", ParentMenuId = purchaseContainer.Id, DisplayOrder = 3, ModuleName = "Purchase", IsContainer = false, IsActive = true }
                };
                _context.WebFormMenus.AddRange(purchaseSubItems);

                // Add Cash Movements sub-items (exactly as shown in screenshot)
                var cashSubItems = new[]
                {
                    new WebFormMenu { DisplayName = "سندات القبض", DisplayNameEn = "Receipt Vouchers", Route = "/Cash/Receipt", Icon = "fas fa-file-invoice", ParentMenuId = cashContainer.Id, DisplayOrder = 1, ModuleName = "Cash", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "سندات الصرف", DisplayNameEn = "Payment Vouchers", Route = "/Cash/Payment", Icon = "fas fa-money-bill-wave", ParentMenuId = cashContainer.Id, DisplayOrder = 2, ModuleName = "Cash", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "القيود اليومية", DisplayNameEn = "Journal Entries", Route = "/Journal/Entry", Icon = "fas fa-file-alt", ParentMenuId = cashContainer.Id, DisplayOrder = 3, ModuleName = "Cash", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "المصروفات", DisplayNameEn = "Expenses", Route = "/Expenses/Create", Icon = "fas fa-receipt", ParentMenuId = cashContainer.Id, DisplayOrder = 4, ModuleName = "Cash", IsContainer = false, IsActive = true }
                };
                _context.WebFormMenus.AddRange(cashSubItems);

                // Add Master Data sub-items (exactly as shown in screenshot)
                var masterDataSubItems = new[]
                {
                    new WebFormMenu { DisplayName = "العملاء", DisplayNameEn = "Customers", Route = "/Customers/Index", Icon = "fas fa-users", ParentMenuId = masterDataContainer.Id, DisplayOrder = 1, ModuleName = "MasterData", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "الموردين", DisplayNameEn = "Vendors", Route = "/Vendors/Index", Icon = "fas fa-truck", ParentMenuId = masterDataContainer.Id, DisplayOrder = 2, ModuleName = "MasterData", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "الأصناف", DisplayNameEn = "Items", Route = "/Items/Index", Icon = "fas fa-boxes", ParentMenuId = masterDataContainer.Id, DisplayOrder = 3, ModuleName = "MasterData", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "تصنيفات الأصناف", DisplayNameEn = "Item Categories", Route = "/ItemCategories/Index", Icon = "fas fa-tags", ParentMenuId = masterDataContainer.Id, DisplayOrder = 4, ModuleName = "MasterData", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "الموظفين", DisplayNameEn = "Employees", Route = "/Employees/Index", Icon = "fas fa-users", ParentMenuId = masterDataContainer.Id, DisplayOrder = 5, ModuleName = "MasterData", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "دليل الحسابات", DisplayNameEn = "Chart of Accounts", Route = "/ChartOfAccounts/Index", Icon = "fas fa-users", ParentMenuId = masterDataContainer.Id, DisplayOrder = 6, ModuleName = "MasterData", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "إدارة المتاجر", DisplayNameEn = "Store Management", Route = "/Stores/Index", Icon = "fas fa-store", ParentMenuId = masterDataContainer.Id, DisplayOrder = 7, ModuleName = "MasterData", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "إدارة المستودعات", DisplayNameEn = "Warehouse Management", Route = "/Warehouses/Index", Icon = "fas fa-warehouse", ParentMenuId = masterDataContainer.Id, DisplayOrder = 8, ModuleName = "MasterData", IsContainer = false, IsActive = true }
                };
                _context.WebFormMenus.AddRange(masterDataSubItems);

                // Add Reports sub-items
                var reportsSubItems = new[]
                {
                    new WebFormMenu { DisplayName = "التقارير المالية", DisplayNameEn = "Financial Reports", Route = "/Reports/Financial", Icon = "fas fa-chart-line", ParentMenuId = reportsContainer.Id, DisplayOrder = 1, ModuleName = "Reports", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "تقارير المخزون", DisplayNameEn = "Inventory Reports", Route = "/Reports/Inventory", Icon = "fas fa-warehouse", ParentMenuId = reportsContainer.Id, DisplayOrder = 2, ModuleName = "Reports", IsContainer = false, IsActive = true }
                };
                _context.WebFormMenus.AddRange(reportsSubItems);

                // Add Settings sub-items (exactly as shown in screenshot)
                var settingsSubItems = new[]
                {
                    new WebFormMenu { DisplayName = "إدارة المستخدمين", DisplayNameEn = "User Management", Route = "/UserManagement/Index", Icon = "fas fa-users-cog", ParentMenuId = settingsContainer.Id, DisplayOrder = 1, ModuleName = "Settings", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "صلاحيات المجموعات (VB.NET)", DisplayNameEn = "Group Permissions (VB.NET)", Route = "/GroupPermissions/Index", Icon = "fas fa-shield-alt", ParentMenuId = settingsContainer.Id, DisplayOrder = 2, ModuleName = "Settings", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "صلاحيات النظام الإلكتروني", DisplayNameEn = "Web Authorization", Route = "/WebAuthorization/Index", Icon = "fas fa-shield-alt", ParentMenuId = settingsContainer.Id, DisplayOrder = 3, ModuleName = "Settings", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "ربط الحسابات", DisplayNameEn = "GL Configuration", Route = "/GLConfig/Index", Icon = "fas fa-link", ParentMenuId = settingsContainer.Id, DisplayOrder = 4, ModuleName = "Settings", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "إعدادات الفواتير", DisplayNameEn = "Invoice Settings", Route = "/InvoiceSettings/Index", Icon = "fas fa-cog", ParentMenuId = settingsContainer.Id, DisplayOrder = 5, ModuleName = "Settings", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "إعدادات النظام", DisplayNameEn = "System Settings", Route = "/SystemSettings/Index", Icon = "fas fa-cogs", ParentMenuId = settingsContainer.Id, DisplayOrder = 6, ModuleName = "Settings", IsContainer = false, IsActive = true },
                    new WebFormMenu { DisplayName = "إعدادات الباركود", DisplayNameEn = "Barcode Settings", Route = "/BarcodeSettings/Index", Icon = "fas fa-barcode", ParentMenuId = settingsContainer.Id, DisplayOrder = 7, ModuleName = "Settings", IsContainer = false, IsActive = true }
                };
                _context.WebFormMenus.AddRange(settingsSubItems);

                await _context.SaveChangesAsync();

                // Grant permissions to admin group (GroupID = 1)
                await SeedAdminPermissionsAsync();

                _logger.LogInformation("Menu data seeded successfully!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error seeding menu data");
                throw;
            }
        }

        private async Task SeedAdminPermissionsAsync()
        {
            try
            {
                // Check if admin permissions already exist
                if (await _context.WebGroupMenuPermissions.AnyAsync(p => p.GroupID == 1))
                {
                    _logger.LogInformation("Admin permissions already exist, skipping.");
                    return;
                }

                // Get all menu items
                var allMenus = await _context.WebFormMenus.ToListAsync();

                // Grant full permissions to admin group (GroupID = 1)
                var adminPermissions = allMenus.Select(menu => new WebGroupMenuPermission
                {
                    GroupID = 1,
                    MenuId = menu.Id,
                    CanView = true,
                    CanAdd = true,
                    CanEdit = true,
                    CanDelete = true,
                    CanPrint = true,
                    CreatedBy = "System"
                }).ToList();

                _context.WebGroupMenuPermissions.AddRange(adminPermissions);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Granted permissions for {adminPermissions.Count} menu items to admin group.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error seeding admin permissions");
                throw;
            }
        }

        private async Task SeedAdminUsersAsync()
        {
            try
            {
                // Ensure admin users are assigned to admin group
                var adminUsers = new[] { "admin", "barbaa" }; // Add other admin usernames as needed

                foreach (var username in adminUsers)
                {
                    var user = await _context.Users.FirstOrDefaultAsync(u => u.Username == username);
                    if (user != null && user.GroupID != 1)
                    {
                        user.GroupID = 1; // Assign to admin group
                        _logger.LogInformation($"Assigned user {username} to admin group");
                    }
                    else if (user == null)
                    {
                        // Create the user if it doesn't exist (for barbaa)
                        if (username == "barbaa")
                        {
                            var newUser = new User
                            {
                                Username = username,
                                Password = "barbaa", // Simple password for demo - should be hashed in production
                                GroupID = 1,
                                CreatedBy = "System",
                                CreatedOn = DateTime.Now
                            };
                            _context.Users.Add(newUser);
                            _logger.LogInformation($"Created admin user {username}");
                        }
                    }
                }

                _logger.LogInformation("Admin users seeded successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error seeding admin users");
                throw;
            }
        }
    }
}
